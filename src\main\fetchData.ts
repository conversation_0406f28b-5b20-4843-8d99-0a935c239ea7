const http = require('http');

function fetchDataSync(url: string): any {
	return new Promise(function (resolve, reject) {
		const timestamp = Date.now();
		http
			.get(`${url}?timestamp=${timestamp}`, (res) => {
				let list: any = [];
				res.on('data', (chunk) => {
					list.push(chunk);
				});
				res.on('end', () => {
					if (list.length) {
						list = list.join();
						try {
							list = JSON.parse(list);
						} catch (error) {
							reject(error);
						}
					}
					resolve(list);
				});
			})
			.on('error', (err) => {
				reject(err);
			});
	});
}
export default fetchDataSync;
