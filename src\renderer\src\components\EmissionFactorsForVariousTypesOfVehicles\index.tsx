import React, { useEffect, useState } from 'react';
import '@animxyz/core';
import Box from '../../baseUI/Box';
import { XyzTransitionGroup } from '@animxyz/react';
import background from '../../images/background';
import { EmissionFactorsForVariousTypesOfVehiclesContent } from './style';

const EmissionFactorsForVariousTypesOfVehicles = () => {
	const [isVisible, setIsVisible] = useState(false);

	useEffect(() => {
		// setTimeout(() => {
		setIsVisible(true);
		// })
	}, []);
	return (
		<EmissionFactorsForVariousTypesOfVehiclesContent>
			{' '}
			各类车辆排放因子
			<XyzTransitionGroup
				className="item-group"
				appear
				mode="out-in"
				xyz="fade left-100%"
			>
				{isVisible && (
					<div className="square">
						<Box>
							<img src={background.mode1} className="left-bg" />
						</Box>
					</div>
				)}
			</XyzTransitionGroup>
		</EmissionFactorsForVariousTypesOfVehiclesContent>
	);
};

export default EmissionFactorsForVariousTypesOfVehicles;
