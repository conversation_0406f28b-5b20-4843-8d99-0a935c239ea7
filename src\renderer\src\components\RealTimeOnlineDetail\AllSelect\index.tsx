import React, { useEffect, useState } from 'react';
import { Button, Dropdown } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';

import { TimeTypeRadioContent, LabelContent } from './style';

enum IType {
	ALL = 'all',
	BI_IN = 'bjin',
	BI_OUT = 'biout',
}

const getTypeTitle = (type: IType): string => {
	switch (type) {
		case IType.ALL:
			return '全部';
		case IType.BI_IN:
			return '京内';
		case IType.BI_OUT:
			return '京外';
		default:
			return '';
	}
};

const list = [
	{ id: IType.ALL, label: getTypeTitle(IType.ALL) },
	{ id: IType.BI_IN, label: getTypeTitle(IType.BI_IN) },
	{ id: IType.BI_OUT, label: getTypeTitle(IType.BI_OUT) },
];

export default ({ defaultValue = IType.ALL, change }) => {
	const [type, setType] = useState<IType>(defaultValue);
	const [label, setLabel] = useState(getTypeTitle(type));
	const items: MenuProps['items'] = list.map((item) => {
		return {
			label: (
				<LabelContent
					data-title={item.id}
					onClick={(e: any) => {
						setType(e.target.dataset.title);
					}}
				>
					{item.label}
				</LabelContent>
			),
			key: item.id,
		};
	});

	useEffect(() => {
		const label = getTypeTitle(type);

		setLabel(label);
		change && change({ label, type });
	}, [type]);

	return (
		<TimeTypeRadioContent>
			<Dropdown
				menu={{ items }}
				placement="bottomLeft"
				overlayClassName="time_select_overlay"
			>
				<Button>
					{label} <DownOutlined rev={undefined} />{' '}
				</Button>
			</Dropdown>
		</TimeTypeRadioContent>
	);
};
