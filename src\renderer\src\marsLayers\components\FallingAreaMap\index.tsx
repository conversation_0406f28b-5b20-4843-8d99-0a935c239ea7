import { useContext, useEffect, useState } from 'react';
import { geoCenter2D } from '@renderer/components/AreaViolation/AreaMap/data';
import RegistriesDistributionEcharts2d from './components/RegistriesDistributionEcharts2d';
import RegistriesDistributionEcharts3d from './components/RegistriesDistributionEcharts3d';
import { PageContext } from '@renderer/context';

type Props = {
	data: Array<any>;
	layerId: string;
	getMaterialColor: (
		num: number,
		layerId: string,
		data: Array<any>,
	) => Array<string>;
	sceneMode: number;
	currentPosition: any;
	targetId: number;
	regionYOY: Array<any>;
	position?: string;
	enableClick?: boolean;
};

const FallingAreaMap = (props: Props) => {
	const {
		data,
		layerId,
		getMaterialColor,
		sceneMode,
		currentPosition,
		targetId,
		type,
		regionYOY,
		streetShipId,
		position = 'left',
		enableClick = true,
	} = props;
	const { currentTopNavMenu } = useContext(PageContext);
	const [customerBatteryCityData2d, setCustomerBatteryCityData2d] =
		useState<any>([]);
	const [customerBatteryCityData3d, setCustomerBatteryCityData3d] =
		useState<any>([]);
	const [dynamicEffectData, setDynamicEffectData] = useState<any>([]);

	// 动态计算柱形图的高度（定一个max）
	function lineMaxHeight() {
		// const maxValue = Math.max(...customerBatteryCityData.map((item) => item.value))
		return 0.1;
	}
	// 柱状体的主干
	function lineData(arr) {
		return arr
			?.filter((item) => geoCenter2D[item.name]) // 过滤掉没有有效坐标的数据
			?.map((item) => ({
				coords: [
					geoCenter2D[item.name],
					[
						geoCenter2D[item.name][0],
						geoCenter2D[item.name][1] + 1 * lineMaxHeight(),
					],
				],
			}));
	}
	// 柱状体的顶部
	function scatterData(arr) {
		return arr
			?.filter((item) => geoCenter2D[item.name])
			?.map((item) => {
				return [
					geoCenter2D[item.name][0],
					geoCenter2D[item.name][1] + lineMaxHeight(),
					item,
				];
			});
	}
	// 柱状体的底部
	function scatterData2(arr) {
		return arr
			?.filter((item) => geoCenter2D[item.name])
			?.map((item) => {
				return {
					name: item.name,
					value: geoCenter2D[item.name],
				};
			});
	}

	// 获取模块颜色或3D高度
	const colors = ['rgb(15, 239, 145)', '#f0db38', '#cb16bc', '#f61a1a'];
	// const colors = [
	//   'rgba(4, 189, 184)',
	//   'rgba(55, 100, 253)',
	//   'rgba(141, 71, 255)',
	//   'rgba(246, 7, 247)'
	// ]
	const heights = [1, 2, 3, 4, 5];
	const getColorOrHeight = (num: number, data: Array<any>, type: string) => {
		const valueList = data.map((item) => item.value);
		const maxValue = Math.max(...valueList);
		const minValue = Math.min(...valueList);

		const scale = (num - minValue) / (maxValue - minValue);
		if (type === 'color') {
			const colorIndex = Math.floor(scale * (colors.length - 1));
			return colors[Math.max(0, Math.min(colorIndex, colors.length - 1))];
		}
		if (type === 'height') {
			const heightIndex = Math.floor(scale * (heights.length - 1));
			return heights[Math.max(0, Math.min(heightIndex, heights.length - 1))];
		}
		return null;
	};

	// 新增颜色模块id
	const addColorModuleId = ['排放量', '排放强度'];

	// 判断是否为新增颜色
	const isNewColor = (value, currentTopNavMenu, data) => {
		// if (!addColorModuleId.includes(currentTopNavMenu)) {
		//   return getColorOrHeight(value, data, 'color')
		// }
		return getMaterialColor(value, layerId, data);
	};

	useEffect(() => {
		// if (!data?.length) return
		let sortData: any;
		if (Array.isArray(data)) {
			sortData = [...data];
		}
		sortData?.sort((a, b) => Number(a.value) - Number(b.value)).reverse();
		// if (sceneMode === 2) {
		const customerBatteryCityData = sortData?.map((item) => {
			return {
				...item,
				itemStyle: {
					areaColor: isNewColor(item.value, currentTopNavMenu, sortData),
					// borderColor: '#3Fdaff',
					// borderWidth: 1
				},
			};
		});
		setCustomerBatteryCityData2d(customerBatteryCityData);
		setDynamicEffectData([
			lineData(customerBatteryCityData),
			scatterData(customerBatteryCityData),
			scatterData2(customerBatteryCityData),
		]);
		// } else {
		//   const customerBatteryCityData = sortData.map((item) => {
		//     return {
		//       ...item,
		//       itemStyle: {
		//         color: isNewColor(item.value, currentTopNavMenu, sortData)
		//         // borderColor: '#3Fdaff',
		//         // borderWidth: 1
		//       },
		//       height: getColorOrHeight(item.value, sortData, 'height'),
		//       isSelected: false
		//     }
		//   })
		//   setCustomerBatteryCityData3d(customerBatteryCityData)
		// }
	}, [data, sceneMode, targetId]);

	return (
		<>
			{/* {sceneMode === 2 ? ( */}
			<RegistriesDistributionEcharts2d
				data={data}
				layerId={layerId}
				targetId={targetId}
				currentPosition={currentPosition}
				customerBatteryCityData={customerBatteryCityData2d}
				dynamicEffectData={dynamicEffectData}
				type={type}
				regionYOY={regionYOY}
				streetShipId={streetShipId}
				position={position}
				enableClick={enableClick}
			/>
			{/* ) : (
        <RegistriesDistributionEcharts3d
          data={data}
          layerId={layerId}
          targetId={targetId}
          currentPosition={currentPosition}
          customerBatteryCityData={customerBatteryCityData3d}
          type={type}
          regionYOY={regionYOY}
        />
      )} */}
		</>
	);
};

export default FallingAreaMap;
