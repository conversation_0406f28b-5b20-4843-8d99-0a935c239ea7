import React, { useState, useContext, useEffect } from 'react';
import LevelTwoTitle from '@renderer/components/LevelTwoTitle';
import ScrollList from '@renderer/components/ScrollList';
import icon from '@renderer/images/icon/point.png';
import LayoutContainer from './style';
type listProps = {
	violation_type_id: string;
	violation_type: string;
	vin: string;
};
type columnProps = {
	plate_number: string;
	violation_project: string;
	violation_time: string;
	violation_place: string;
};
type enterpriseProps = {
	name: string;
	color: string;
	violation_nums: string;
	rate: number | string | any;
};
const UserProfile = (props) => {
	const { dataList } = props;
	// const [enterprise, setEnterprise] = useState<Array<enterpriseProps>>([])
	const [show, setShow] = useState(false);
	const [details, setDetails] = useState<columnProps>();
	const column = [
		{
			align: 'center',
			dataIndex: 'user_type_id',
			key: 'user_type_id',
			title: '编号',
			with: '100px',
		},
		{
			align: 'center',
			dataIndex: 'user_type',
			key: 'user_type',
			title: '用车企业',
		},
		{
			align: 'center',
			dataIndex: 'vin',
			key: 'vin',
			title: '用车数量',
		},
	];
	// const enterpriseRankingMore = dataList.slice()
	// let useNum = 5
	// for (let index = 0; index < 20; index++) {
	//   const element = enterpriseRankingMore[index]
	//   const newElement = { ...element, user_type_id: useNum }
	//   enterpriseRankingMore.push(newElement)
	//   useNum++
	// }
	const onliClick = (e) => {
		setShow(true);
		setDetails(e);
	};
	// const [violatioData, setViolatioData] = useState<Array<listProps>>([])
	return (
		<LayoutContainer>
			<div className="middle-container">
				<LevelTwoTitle title="用车大户推送"></LevelTwoTitle>
				<div className="table">
					<ul className="column-ul">
						{column.map((item, index) => {
							return (
								<li className="column-li" key={index}>
									{item.title}{' '}
								</li>
							);
						})}
					</ul>
					<ScrollList onliClick={onliClick} data={dataList} column={column} />
				</div>
			</div>
			<div className="right-container">
				<LevelTwoTitle title="用车大户TOP 5"></LevelTwoTitle>
				<div className="enterprise">
					{dataList?.map((item, index) => {
						return (
							<div key={index} className="enterprise-item">
								<div className="up">
									<span style={{ color: item.color }}>{index + 1}</span>
									<span>{item.user_type}</span>
								</div>

								<div className="down">
									<img src={icon}></img>
									<span>用车数量:</span>
									<span>{item.vin}</span>
									<span>用车率:</span>
									<span>{item.rate || 1 * 100}%</span>
								</div>
							</div>
						);
					})}
				</div>
			</div>
		</LayoutContainer>
	);
};

export default UserProfile;
