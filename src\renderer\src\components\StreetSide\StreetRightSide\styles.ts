import styled, { keyframes } from 'styled-components';
import imgs from '@renderer/images/background';
import icon from '@renderer/images/icon';

const slideInFromRight = keyframes`
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
`;

const AreaRightSideStyled = styled.div`
	width: 21.7%;
	height: 1080px;
	position: absolute;
	right: 0;
	bottom: 0;
	background: url(${imgs.areaRightSide}) no-repeat;
	background-size: 100% 100%;
	z-index: 10;
	animation: ${slideInFromRight} 1s ease-in-out;
	display: flex;
	justify-content: center;
	align-items: center;
	/* backdrop-filter: blur(5px); */
	.Streetcontent {
		width: 93.5%;
		height: 95%;
		backdrop-filter: blur(15px);
		border-radius: 45px 0 0 45px;
		box-shadow: -25px 0 25px 25px rgb(3 27 60 / 54%);
		.slidingLayer {
			display: flex;
			flex-direction: column;
			background: url(${icon.right}) no-repeat;
			background-size: 100% 100%;
			width: 90%;
			height: 70px;
			margin: 0 10px 0 0;
			.dropDown1 {
				position: absolute;
				right: 6%;
				top: 1%;
			}
			.dropDown2 {
				position: absolute;
				right: 6%;
				top: 28%;
			}
			.dropDown3 {
				position: absolute;
				right: 6%;
				top: 70%;
			}
			:where(.css-dev-only-do-not-override-mu9r37).ant-btn-default {
				width: 120px;
				height: 35px;
				background-color: transparent;
				border: 0;
				background: url(${icon.selectRegionBg});
				background-repeat: no-repeat;
				background-size: 100% 100%;
				font-size: 18px;
				margin: 0 10px;
			}
			display: flex;
			align-items: center;
		}
		.slidtext {
			width: 100%;
			height: 100%;
			overflow-wrap: break-word;
			color: rgba(255, 255, 255, 1);
			font-size: 24px;
			font-family: PingFangSC-Regular;
			text-align: left;
			white-space: nowrap;
			line-height: 38px;
			margin: 12px 0 0 70px;
		}
	}
`;
const LayoutContainer = styled.div`
	margin-left: 10px;
	height: 25%;
	display: flex;
	flex-direction: row;
	position: relative;
	.container {
		width: 100%;
		margin-top: 14px;
		display: flex;
		flex-direction: row;
		.describe {
			width: 40%;
			margin-top: 0;
			margin-right: 14px;
			dl {
				color: #fff;
				padding-left: 0;
				margin: 0 0 0 0;
				dt {
					font-size: 34px;
					color: #d8e2ef;
					letter-spacing: 4px;
				}
				dd {
					position: relative;
					bottom: 5px;
					font-size: 28px;
					margin: 5px 0 0 0;
					font-family: 'electronicFont';
					span {
						font-size: 24px;
						color: #d8e2ef;
						margin-left: 5px;
					}
				}
				&.no1 {
					dd {
						color: #5ce2aa;
					}
				}
				&.no2 {
					margin-top: 13px;
					padding-left: 50px;
					dd {
						color: #f5803e;
						font-size: 64px;
						padding-bottom: 10px;
					}
				}
				&.no3 {
					dd {
						color: #f9ae83;
						font-size: 28px;
					}
				}
			}
		}
		.echarts-container {
			display: flex;
			flex-direction: row;
			position: relative;
			flex: 1;
			padding-right: 20px;
			.echarts {
				width: 100%;
				height: 100%;
			}
		}
	}
	.echarts-line {
		width: 100%;
		display: flex;
	}
`;

export { LayoutContainer, AreaRightSideStyled };
