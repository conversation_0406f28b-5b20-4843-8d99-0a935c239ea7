import { useState, useEffect, useContext, useRef } from 'react';
import { PageContext } from '@renderer/context';
import { getClueSpotDetails } from '@renderer/api';
import { VehicleHistoricalTrajectoryStyled, LayoutContainer } from './styles';

export default function ViolationInfoPanel(props) {
	const { setCurrentViolationClueCode, currentViolationClueCode } =
		useContext(PageContext);
	const [data, setData] = useState(null);

	const closeViolationRecord = () => {
		setCurrentViolationClueCode('');
		setData(null);
	};

	useEffect(() => {
		if (currentViolationClueCode !== '')
			getClueSpotDetails({
				clue_code: currentViolationClueCode,
			})
				.then((res) => {
					console.log('=================getClueSpotDetails===================');
					console.log(res);
					console.log('=================getClueSpotDetails===================');
					if (res.length) {
						setData(res[0]);
					} else {
						setData(null);
					}
				})
				.catch((error) => {
					setData(null);
				});
	}, [currentViolationClueCode]);
	return data ? (
		<VehicleHistoricalTrajectoryStyled>
			<LayoutContainer>
				<h3>违规记录</h3>
				<span className="violation-record-close" onClick={closeViolationRecord}>
					x
				</span>
				<div className="cumulative">
					<div>时间</div>
					<div>{data.monitor_time}</div>
				</div>
				<div className="cumulative">
					<div>区域名称</div>
					<div>{data.county_name}</div>
				</div>
				<div className="cumulative">
					<div>地址</div>
					<div>{data.county_name}</div>
				</div>
				<div className="cumulative">
					<div>记录</div>
					<div>{data.record}</div>
				</div>
				<div className="cumulative">
					<div>建议</div>
					<div>{data.suggest}</div>
				</div>
			</LayoutContainer>
		</VehicleHistoricalTrajectoryStyled>
	) : null;
}
