import { useCallback, useState } from 'react';
import { isEqual } from 'lodash';

import { useIpc<PERSON><PERSON><PERSON> } from '@renderer/hooks';
import type { ModuleList } from '@renderer/types';

export const useFetchModuleData = () => {
	const [modules, setModules] = useState<ModuleList>([]);

	const listener = useCallback((_event, data: ModuleList) => {
		setModules((modules) => {
			const moduleList = data.map((item, i) => {
				if (isEqual(modules[i]?.id, item.id)) {
					return { ...item, isChange: false };
				} else {
					return { ...item, isChange: true };
				}
			});
			console.log('moduleList', moduleList);
			return moduleList;
		});
	}, []);

	useIpcRenderer('data:modules', listener);

	return modules;
};
