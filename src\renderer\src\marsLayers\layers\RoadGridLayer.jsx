// 道路网格
import React, { useEffect, useContext, useState, useRef } from 'react';
import {
	Cartesian3,
	PolylineCollection,
	Color,
	Cartesian2,
	Entity,
} from 'mars3d-cesium';
import * as mars3d from 'mars3d';
import { Radio, DatePicker, Input } from 'antd';
import moment from 'moment';
import dayjs from 'dayjs';
import { getVehicleFlowEmissionGrid } from '@renderer/api';
import { MapContext } from '@renderer/context';
import NumericInput from '../components/NumericInput';
import { wgs84togcj02 } from '@renderer/utils';
import { PageContext } from '@renderer/context';
import styled from 'styled-components';
import icon from '@renderer/images/icon';
import { COUNTY_ID_NAME_MAP } from '@renderer/marsLayers/components/AreaStreetStyles';

const COLORS = {
	0: 'rgba(0, 255, 0)',
	1: 'rgba(50, 255, 0)',
	2: 'rgba(100, 255, 0)',
	3: 'rgba(150, 255, 0)',
	4: 'rgba(200, 255, 0)',
	5: 'rgba(255, 255, 0)',
	6: 'rgba(255, 180, 0)',
	7: 'rgba(255, 120, 0)',
	8: 'rgba(255, 60, 0)',
	9: 'rgba(255, 0, 0)',
	'-': 'rgb(102,102,102)',
};

const LEVELNAME = {
	0: '一级',
	1: '二级',
	2: '三级',
	3: '四级',
	4: '五级',
	5: '六级',
	6: '七级',
	7: '八级',
	8: '九级',
	9: '十级',
};

const getLevelList = (arr) => {
	let num_max = Math.max(...arr);
	let num_min = Math.min(...arr);
	let diff = num_max - num_min;
	return [
		{ max: num_min + diff * 0.1, min: num_min, level: 0 },
		{ max: num_min + diff * 0.2, min: num_min + diff * 0.1, level: 1 },
		{ max: num_min + diff * 0.3, min: num_min + diff * 0.2, level: 2 },
		{ max: num_min + diff * 0.4, min: num_min + diff * 0.3, level: 3 },
		{ max: num_min + diff * 0.5, min: num_min + diff * 0.4, level: 4 },
		{ max: num_min + diff * 0.6, min: num_min + diff * 0.5, level: 5 },
		{ max: num_min + diff * 0.7, min: num_min + diff * 0.6, level: 6 },
		{ max: num_min + diff * 0.8, min: num_min + diff * 0.7, level: 7 },
		{ max: num_min + diff * 0.9, min: num_min + diff * 0.8, level: 8 },
		{ max: num_max, min: num_min + diff * 0.9, level: 9 },
	];
};

const getLevel = (value, arr) => {
	return arr.filter((item) => {
		return item.min <= value && item.max >= value;
	})[0].level;
};

export default (props) => {
	const { visible, map, layerId } = props;
	const { countyId } = useContext(PageContext);
	const Polygon = useRef();
	const [selectedButton, setSelectedButton] = useState('default');
	const [showPop, setShowPop] = useState(false);
	const [inputMax, setInputMax] = useState(null);
	const [cameraHeight, setCameraHeight] = useState(0);
	const [valueMax, setValueMax] = useState('--');
	const [customDateGrid, setCustomDateGrid] = useState({});

	const getVehicleFlowEmissionGridData = (params) => {
		getVehicleFlowEmissionGrid(params)
			.then((res) => {
				// console.log(res, 'getVehicleFlowEmissionGrid +++++++++ getVehicleFlowEmissionGrid?????')
				if (res.length) {
					drawPolygonLayer(res);
				}
			})
			.catch((err) => {
				console.log('err', JSON.stringify(err));
			});
	};

	const drawPolygonLayer = (data) => {
		// const values = data.map((item) => item.value);
		// const list = [...getLevelList(values)];

		const gridData = data.sort((a, b) => a.value - b.value);
		const length = gridData.length;
		const chunkSize = Math.ceil(length / 10);

		for (let i = 0; i < length; i++) {
			const level = Math.floor(i / chunkSize);
			gridData[i]['level'] = level;
		}

		const PolygonData = gridData.map((item) => {
			const level = item.level;
			// const positions = mars3d.Cesium.Cartesian3.fromDegreesArray(roadGeometryData[item[0]]['geo'].flat())
			// const geojson = {
			//   "type": "LineString",
			//   "coordinates": roadGeometryData[item[0]]['geo']
			// }
			const positions = [
				wgs84togcj02(item.LONGITUDE_UP_LEFT, item.LATITUDE_UP_LEFT),
				wgs84togcj02(item.LONGITUDE_UP_RIGHT, item.LATITUDE_UP_RIGHT),
				wgs84togcj02(item.LONGITUDE_DOWN_RIGHT, item.LATITUDE_DOWN_RIGHT),
				wgs84togcj02(item.LONGITUDE_DOWN_LEFT, item.LATITUDE_DOWN_LEFT),
			];
			return {
				positions,
				style: {
					color: COLORS[level],
					outline: true,
					outlineColor: '#ffffff',
					outlineOpacity: 1.0,
					outlineStyle: {
						width: 1.5,
					},
					opacity: 0.6,
				},
				attr: {},
			};
		});
		const graphic = new mars3d.graphic.RectangleCombine({
			instances: PolygonData,
			// 高亮时的样式
			highlight: {
				type: mars3d.EventType.click,
				color: mars3d.Cesium.Color.WHITE,
			},
		});
		Polygon.current.addGraphic(graphic);
	};

	// 网格时间自定义切换
	const handleClickReference = (value) => {
		setSelectedButton(value);
		if (value === 'custom') {
			setShowPop(true);
		} else {
			setShowPop(false);
		}
	};

	const onChange = (date, dateString) => {
		setCustomDateGrid(dateString);
	};

	useEffect(() => {
		if (!visible) return;
		Polygon.current = new mars3d.layer.GraphicLayer();
		map.addLayer(Polygon.current);
		if (selectedButton === 'custom' && !Object.keys(customDateGrid).length)
			return;
		const currentTime = moment()
			.subtract(1, 'day')
			.format('YYYY-MM-DD 00:00:00');
		const factor = layerId === 'vehicleEmissionsHotspotGrid' ? 1 : 2;
		let params;
		if (selectedButton === 'default') {
			params = {
				start_time: currentTime,
				end_time: currentTime,
				factor,
				target: 2,
			};
		} else {
			params = {
				start_time: customDateGrid,
				end_time: customDateGrid,
				factor,
				target: 2,
			};
		}
		params.area_name = COUNTY_ID_NAME_MAP[countyId] || '';
		getVehicleFlowEmissionGridData(params);
		return () => {
			if (Polygon.current) {
				Polygon.current.clearDrawing();
				Polygon.current.clear();
				Polygon.current.enabledEvent = false;
				map.removeLayer(Polygon.current);
			}
		};
	}, [visible, customDateGrid, countyId]);

	return (
		<>
			<HotspotGridTimeStyled>
				<div className="selectAssembly">
					<p>时间选择：</p>
					<Radio.Button
						value="default"
						onClick={() => handleClickReference('default')}
						style={{
							color: selectedButton === 'default' ? '#3ADAFF' : '#e2e5e5',
						}}
					>
						昨日
					</Radio.Button>
					<Radio.Button
						value="custom"
						onClick={() => handleClickReference('custom')}
						style={{
							color: selectedButton === 'custom' ? '#3ADAFF' : '#e2e5e5',
						}}
					>
						自定义
					</Radio.Button>
					{/* <NumericInput onChange={setInputMax} height={cameraHeight} value={valueMax} /> */}
				</div>
				{showPop && (
					<div className="customDate">
						<p>自定义时间: </p>
						<DatePicker
							onChange={onChange}
							format="YYYY-MM-DD 00:00:00"
							showTime={false}
							defaultValue={dayjs(`${dayjs()}`)}
						/>
					</div>
				)}
			</HotspotGridTimeStyled>
			<RoadGridLayerStyled>
				<ul className="road-network-legend">
					{Object.values(LEVELNAME).map((item, i) => {
						return (
							<li>
								<i style={{ backgroundColor: COLORS[i] }} />
								{item}
							</li>
						);
					})}
				</ul>
			</RoadGridLayerStyled>
		</>
	);
};

const RoadGridLayerStyled = styled.div`
	.road-network-legend {
		display: flex;
		flex-direction: column;
		position: absolute;
		bottom: 5px;
		left: 500px;
		z-index: 4;
		margin-bottom: 0;
		padding: 10px 15px;
		background-color: rgba(0, 0, 0, 0.4);
		li {
			width: auto;
			align-items: center;
			display: flex;
			margin-top: 5px;
			color: #fff;
			i {
				width: 20px;
				height: 20px;
				display: inline-block;
				background-color: #fff;
				margin-right: 5px;
			}
		}
	}
`;
const HotspotGridTimeStyled = styled.div`
	position: absolute;
	right: 6%;
	top: 8%;
	width: 360px;
	height: auto;
	background: url(${icon.heatGrid});
	background-repeat: no-repeat;
	background-size: 100% 100%;
	z-index: 4;
	padding: 10px 0;
	.selectAssembly {
		width: 100%;
		padding: 10px;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
			white-space: nowrap;
		}
		.ant-radio-button-wrapper {
			width: 80px;
			border: 0;
			border-radius: 10px;
			font-size: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			white-space: nowrap;
			span:hover {
				color: #3adaff;
			}
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			> .ant-input:last-child,
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			.ant-input-group-addon:last-child {
			width: 100px;
			background-color: transparent;
			border: 0;
			font-size: 16px;
			padding-left: 2px;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			> .ant-input:last-child:focus,
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			.ant-input-group-addon:last-child:focus {
			border: 1px solid #007bff;
			outline: none;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			> .ant-input:first-child,
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			.ant-input-group-addon:first-child {
			background-color: transparent;
			border: 0;
			font-size: 16px;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group-wrapper {
			background: rgb(58, 218, 255, 0.1) !important;
			border-radius: 10px;
		}
		input {
			padding: 4px 0;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group-wrapper {
			width: auto;
		}
	}
	.customDate {
		height: 60px;
		display: flex;
		margin-top: -6px;
		padding: 20px;
		align-items: center;
		justify-content: center;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
			padding-right: 10px;
		}
		.ant-picker {
			background: rgba(0, 65, 94) !important;
			border-radius: 4px;
			border: 0;
			font-size: 18px !important;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			height: 34px;
			color: #ffffff !important;
			display: flex;
			align-items: center;
		}
		.ant-picker-input {
			font-size: 20px;
			color: #e2f0ff !important;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-picker .ant-picker-suffix {
			margin-right: 10px;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-picker .ant-picker-clear {
			display: none !important;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-picker
			.ant-picker-input
			> input {
			text-align: center !important;
			line-height: 0 !important;
			font-size: 18px !important;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-picker
			.ant-picker-input-placeholder
			> input {
			color: #fff !important;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-picker {
			padding: 0 !important;
		}
	}
`;
