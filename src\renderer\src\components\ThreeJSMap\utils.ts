import * as THREE from 'three';

// GeoJSON数据类型定义
export interface GeoJSONFeature {
	type: 'Feature';
	properties: {
		name: string;
		[key: string]: any;
	};
	geometry: {
		type: 'Polygon' | 'MultiPolygon';
		coordinates: number[][][] | number[][][][];
	};
}

export interface GeoJSONData {
	type: 'FeatureCollection';
	features: GeoJSONFeature[];
}

export interface MapData {
	name: string;
	value: number;
	[key: string]: any;
}

// 坐标转换工具类
export class CoordinateConverter {
	private centerLon: number;
	private centerLat: number;
	private scale: number;

	constructor(centerLon = 116.4, centerLat = 39.9, scale = 100) {
		this.centerLon = centerLon;
		this.centerLat = centerLat;
		this.scale = scale;
	}

	// 经纬度转Three.js坐标
	lonLatToThreeJS(lon: number, lat: number): [number, number, number] {
		return [
			(lon - this.centerLon) * this.scale,
			0,
			-(lat - this.centerLat) * this.scale,
		];
	}

	// 批量转换坐标
	convertCoordinates(coordinates: number[][]): [number, number, number][] {
		return coordinates.map((coord) => this.lonLatToThreeJS(coord[0], coord[1]));
	}

	// 计算边界框
	calculateBounds(geoJsonData: GeoJSONData): {
		minLon: number;
		maxLon: number;
		minLat: number;
		maxLat: number;
	} {
		let minLon = Infinity;
		let maxLon = -Infinity;
		let minLat = Infinity;
		let maxLat = -Infinity;

		geoJsonData.features.forEach((feature) => {
			const processCoordinates = (coords: number[][]) => {
				coords.forEach((coord) => {
					minLon = Math.min(minLon, coord[0]);
					maxLon = Math.max(maxLon, coord[0]);
					minLat = Math.min(minLat, coord[1]);
					maxLat = Math.max(maxLat, coord[1]);
				});
			};

			if (feature.geometry.type === 'Polygon') {
				feature.geometry.coordinates.forEach((ring) =>
					processCoordinates(ring),
				);
			} else if (feature.geometry.type === 'MultiPolygon') {
				feature.geometry.coordinates.forEach((polygon) =>
					polygon.forEach((ring) => processCoordinates(ring)),
				);
			}
		});

		return { minLon, maxLon, minLat, maxLat };
	}

	// 自动调整中心点和缩放
	autoFit(geoJsonData: GeoJSONData) {
		const bounds = this.calculateBounds(geoJsonData);
		this.centerLon = (bounds.minLon + bounds.maxLon) / 2;
		this.centerLat = (bounds.minLat + bounds.maxLat) / 2;

		const lonRange = bounds.maxLon - bounds.minLon;
		const latRange = bounds.maxLat - bounds.minLat;
		const maxRange = Math.max(lonRange, latRange);

		// 调整缩放以适应视图
		this.scale = 10 / maxRange;
	}
}

// 几何体创建工具
export class GeometryCreator {
	private converter: CoordinateConverter;

	constructor(converter: CoordinateConverter) {
		this.converter = converter;
	}

	// 创建区域形状
	createRegionShapes(coordinates: number[][][]): THREE.Shape[] {
		const shapes: THREE.Shape[] = [];

		coordinates.forEach((polygon) => {
			const convertedCoords = this.converter.convertCoordinates(polygon);
			const shape = new THREE.Shape();

			convertedCoords.forEach((coord, index) => {
				if (index === 0) {
					shape.moveTo(coord[0], coord[2]);
				} else {
					shape.lineTo(coord[0], coord[2]);
				}
			});

			shapes.push(shape);
		});

		return shapes;
	}

	// 创建挤出几何体
	createExtrudedGeometry(
		shapes: THREE.Shape[],
		depth: number,
		bevelEnabled = false,
	): THREE.ExtrudeGeometry[] {
		return shapes.map((shape) => {
			const extrudeSettings = {
				depth,
				bevelEnabled,
				bevelThickness: bevelEnabled ? 0.01 : 0,
				bevelSize: bevelEnabled ? 0.01 : 0,
				bevelSegments: bevelEnabled ? 3 : 0,
			};

			return new THREE.ExtrudeGeometry(shape, extrudeSettings);
		});
	}

	// 创建平面几何体（2D模式）
	createFlatGeometry(shapes: THREE.Shape[]): THREE.ShapeGeometry[] {
		return shapes.map((shape) => new THREE.ShapeGeometry(shape));
	}
}

// 颜色工具类
export class ColorUtils {
	// 预定义颜色方案
	static readonly COLOR_SCHEMES = {
		blue: [
			'#f7fbff',
			'#deebf7',
			'#c6dbef',
			'#9ecae1',
			'#6baed6',
			'#4292c6',
			'#2171b5',
			'#08519c',
			'#08306b',
		],
		red: [
			'#fff5f0',
			'#fee0d2',
			'#fcbba1',
			'#fc9272',
			'#fb6a4a',
			'#ef3b2c',
			'#cb181d',
			'#a50f15',
			'#67000d',
		],
		green: [
			'#f7fcf5',
			'#e5f5e0',
			'#c7e9c0',
			'#a1d99b',
			'#74c476',
			'#41ab5d',
			'#238b45',
			'#006d2c',
			'#00441b',
		],
		purple: [
			'#fcfbfd',
			'#efedf5',
			'#dadaeb',
			'#bcbddc',
			'#9e9ac8',
			'#807dba',
			'#6a51a3',
			'#54278f',
			'#3f007d',
		],
		orange: [
			'#fff5eb',
			'#fee6ce',
			'#fdd0a2',
			'#fdae6b',
			'#fd8d3c',
			'#f16913',
			'#d94801',
			'#a63603',
			'#7f2704',
		],
		viridis: [
			'#440154',
			'#482777',
			'#3f4a8a',
			'#31678e',
			'#26838f',
			'#1f9d8a',
			'#6cce5a',
			'#b6de2b',
			'#fee825',
		],
	};

	// 计算数值范围
	static getValueRange(data: MapData[]): { min: number; max: number } {
		if (data.length === 0) return { min: 0, max: 1 };
		const values = data.map((d) => d.value);
		return {
			min: Math.min(...values),
			max: Math.max(...values),
		};
	}

	// 根据数值获取颜色
	static getColorByValue(
		value: number,
		min: number,
		max: number,
		colorScale: string[],
	): string {
		if (max === min) return colorScale[0];
		const ratio = (value - min) / (max - min);
		const index = Math.floor(ratio * (colorScale.length - 1));
		return colorScale[Math.min(index, colorScale.length - 1)];
	}

	// 插值颜色
	static interpolateColor(
		value: number,
		min: number,
		max: number,
		colorScale: string[],
	): string {
		if (max === min) return colorScale[0];

		const ratio = (value - min) / (max - min);
		const scaledValue = ratio * (colorScale.length - 1);
		const index = Math.floor(scaledValue);
		const fraction = scaledValue - index;

		if (index >= colorScale.length - 1) {
			return colorScale[colorScale.length - 1];
		}

		const color1 = new THREE.Color(colorScale[index]);
		const color2 = new THREE.Color(colorScale[index + 1]);

		return '#' + color1.lerp(color2, fraction).getHexString();
	}

	// 生成图例数据
	static generateLegendData(
		data: MapData[],
		colorScale: string[],
	): Array<{ color: string; value: number; label: string }> {
		const { min, max } = this.getValueRange(data);
		const step = (max - min) / (colorScale.length - 1);

		return colorScale.map((color, index) => ({
			color,
			value: min + step * index,
			label: (min + step * index).toFixed(2),
		}));
	}
}

// 数据处理工具
export class DataProcessor {
	// 数据归一化
	static normalizeData(data: MapData[]): MapData[] {
		const { min, max } = ColorUtils.getValueRange(data);
		const range = max - min;

		if (range === 0) return data;

		return data.map((item) => ({
			...item,
			normalizedValue: (item.value - min) / range,
		}));
	}

	// 数据分组
	static groupDataByValue(
		data: MapData[],
		groups: number,
	): Array<{ min: number; max: number; items: MapData[] }> {
		const { min, max } = ColorUtils.getValueRange(data);
		const step = (max - min) / groups;

		const result: Array<{ min: number; max: number; items: MapData[] }> = [];

		for (let i = 0; i < groups; i++) {
			const groupMin = min + step * i;
			const groupMax = min + step * (i + 1);

			const items = data.filter(
				(item) =>
					item.value >= groupMin &&
					(i === groups - 1 ? item.value <= groupMax : item.value < groupMax),
			);

			result.push({ min: groupMin, max: groupMax, items });
		}

		return result;
	}

	// 匹配GeoJSON和数据
	static matchGeoJSONWithData(
		geoJsonData: GeoJSONData,
		mapData: MapData[],
	): Array<{ feature: GeoJSONFeature; data: MapData | null }> {
		return geoJsonData.features.map((feature) => {
			const data = mapData.find((d) => d.name === feature.properties.name);
			return { feature, data };
		});
	}

	// 验证数据完整性
	static validateData(
		geoJsonData: GeoJSONData,
		mapData: MapData[],
	): {
		valid: boolean;
		missingInGeoJSON: string[];
		missingInData: string[];
	} {
		const geoJSONNames = new Set(
			geoJsonData.features.map((f) => f.properties.name),
		);
		const dataNames = new Set(mapData.map((d) => d.name));

		const missingInGeoJSON = mapData
			.filter((d) => !geoJSONNames.has(d.name))
			.map((d) => d.name);

		const missingInData = geoJsonData.features
			.filter((f) => !dataNames.has(f.properties.name))
			.map((f) => f.properties.name);

		return {
			valid: missingInGeoJSON.length === 0 && missingInData.length === 0,
			missingInGeoJSON,
			missingInData,
		};
	}
}

// 性能优化工具
export class PerformanceUtils {
	// 简化几何体
	static simplifyGeometry(
		geometry: THREE.BufferGeometry,
		tolerance = 0.01,
	): THREE.BufferGeometry {
		// 这里可以实现几何体简化算法
		// 暂时返回原几何体
		return geometry;
	}

	// 批量创建材质
	static createMaterialPool(
		colors: string[],
	): Map<string, THREE.MeshLambertMaterial> {
		const pool = new Map<string, THREE.MeshLambertMaterial>();

		colors.forEach((color) => {
			if (!pool.has(color)) {
				pool.set(
					color,
					new THREE.MeshLambertMaterial({
						color: new THREE.Color(color),
						transparent: true,
						opacity: 0.8,
					}),
				);
			}
		});

		return pool;
	}

	// 实例化几何体管理
	static createInstancedMesh(
		geometry: THREE.BufferGeometry,
		material: THREE.Material,
		count: number,
	): THREE.InstancedMesh {
		return new THREE.InstancedMesh(geometry, material, count);
	}
}
