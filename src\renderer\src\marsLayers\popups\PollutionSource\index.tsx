// 污染源弹窗
import React, { useState, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';

import PopContainer from '@/marsLayers/popups/common/PopContainer';

const Popup = (info: any) => {
	let { openCount } = info;

	const data = [
		{ label: '企业类型', value: info?.industry_type || '--' },
		{ label: '所属镇街', value: info?.town_name || '--' },
		{ label: '位置', value: info?.address || '--', isRow: true },
		{
			label: '统一社会信用代码',
			value: info?.unified_social_credit || '--',
			isRow: true,
		},
		{ label: '经度', value: info?.lng || '--', filter: true },
		{ label: '纬度', value: info?.lat || '--', filter: true },
	];

	const sensorP = (
		<PopContainer
			text={info?.point_name}
			size="small"
			data={data}
		></PopContainer>
	);

	return ReactDOM.createPortal(
		sensorP,
		document.querySelector(`#${info.LayerId}Popup`),
	);
};

export default Popup;
