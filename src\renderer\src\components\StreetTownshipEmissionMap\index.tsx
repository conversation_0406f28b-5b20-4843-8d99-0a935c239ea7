// 街乡镇排放
import { useEffect, useContext, useState } from 'react';
import LevelTwoTitle from '@renderer/components/LevelTwoTitle';
import TimeTypeRadio from '@renderer/baseUI/TimeTypeRadio2';
import {
	StreetDeregulationWrapStyled,
	StreetDeregulationListStyled,
} from '../StreetDeregulation/style';
import { getStatisticsNox } from '@renderer/api';
import { useAuth } from '@renderer/hooks/useAuth';

interface TimeData {
	start_time: string;
	end_time: string;
	timeType: string;
}

interface Props {
	setLayerData: any;
	setCurrentPosition: any;
}

enum TimeType {
	Hour = 'hour',
	Day = 'day',
}

const VIO_LATION_DATA_SIZE = 10;
const violatioData = Array.from({ length: VIO_LATION_DATA_SIZE }).map(() => ({
	adm_name: '',
	count: '',
	name: '',
	rank_desc: '',
}));

const StreetTownshipEmissionMap = (props: Props) => {
	const { setLayerData, setCurrentPosition } = props;
	const [timeData, setTimeData] = useState<any>({});
	const { token } = useAuth();
	const [dataCreate, setDateCreate] = useState<any>(violatioData); // 排放强度数据
	const [emissionIntensity, setEmissionIntensity] = useState<any>(violatioData); // 排放量数据
	const [isContentExpanded, setIsContentExpanded] = useState(true);
	const [isButtonRotated, setIsButtonRotated] = useState(false);

	const getCurrentPosition = (data) => {
		setCurrentPosition({
			adm_name: data.TOWN_NAME,
			count: data.NOX_EMISSIONS,
			name: data.COUNTY_NAME,
			rank_desc: data.rank,
		});
	};

	const buildRequestParams = (timeData: TimeData, token: string) => {
		const startTime = timeData.start_time;
		const endTime = timeData.end_time;
		let timeType: TimeType = TimeType.Hour; // 设置默认值
		switch (timeData.timeType) {
			case '1':
			case '2':
				timeType = TimeType.Hour;
				break;
			case '3':
				timeType = TimeType.Day;
				break;
		}
		return {
			startTime,
			endTime,
			time_type: timeType,
			token,
		};
	};

	useEffect(() => {
		setEmissionIntensity(violatioData);
		setDateCreate(violatioData);
		const req = buildRequestParams(timeData, token);
		//街乡镇排放
		getStatisticsNox(req).then((res: any) => {
			if (!res.length) return;
			if (res[0]?.sorted_emission_intensity.length)
				setEmissionIntensity(res[0].sorted_emission_intensity);
			if (res[0]?.sorted_by_nox_emission.length)
				setDateCreate(res[0].sorted_by_nox_emission);
			const data = res[0].data?.map((item, index) => {
				let color;
				const num = Number(item.EMISSION_INTENSITY);
				// 30以下：绿色 30-60：黄色 60-90：橙色 90-120：红色 120以上：绿色
				if (num < 30) {
					color = '#00FFFF';
				} else if (num >= 30 && num < 60) {
					color = '#00BFFF';
				} else if (num >= 60 && num < 90) {
					color = '#4169E1';
				} else if (num >= 90 && num < 120) {
					color = '#0000CD';
				} else if (num >= 120) {
					color = '#191970';
				}
				return {
					value: item.EMISSION_INTENSITY,
					name: item.TOWN_NAME,
					areaName: item.COUNTY_NAME,
					itemStyle: {
						areaColor: color,
					},
				};
			});
			setLayerData(data);
		});
	}, [timeData]);

	const onbtnclickedhandle = () => {
		setIsContentExpanded(!isContentExpanded);
		setIsButtonRotated(!isButtonRotated);
	};

	const contentStyle = {
		maxHeight: isContentExpanded ? '825px' : '0',
		opacity: isContentExpanded ? 1 : 0,
		transition: 'max-height 0.3s ease, opacity 0.3s ease',
	};

	return (
		<StreetDeregulationWrapStyled>
			<div
				className={`buttonclicked ${isButtonRotated ? 'rotated' : ''}`}
				onClick={onbtnclickedhandle}
			></div>
			<TimeTypeRadio defaultValue="1" timeType={(data) => setTimeData(data)} />
			<StreetDeregulationListStyled style={contentStyle}>
				<div className="middle-container">
					<div className="Violation">
						<LevelTwoTitle
							className="Violation1"
							title="全市重型车活动排放量前10"
						></LevelTwoTitle>
						{/* <span>TOP</span> */}
					</div>
					<div className="table">
						{dataCreate?.length && (
							<dl>
								<dt>
									<span className="no1">排名</span>
									<span className="no2">街乡镇</span>
									<span className="no3">所属区</span>
									<span className="no4">排放量</span>
								</dt>
								{dataCreate?.map((item, key) => {
									return (
										<dd
											key={key}
											onClick={() => {
												getCurrentPosition(item);
											}}
										>
											<span className={`top${key} no1`}>{key + 1}</span>
											<span className="no2">{item.TOWN_NAME || '--'}</span>
											<span className="no3">{item.COUNTY_NAME || '--'}</span>
											<span className="no4">
												{Number(item.NOX_EMISSIONS)
													? Number(item.NOX_EMISSIONS).toFixed(1)
													: '--'}
											</span>
										</dd>
									);
								})}
							</dl>
						)}
					</div>
				</div>
				<div className="right-container">
					<div className="Violation">
						<LevelTwoTitle
							className="Violation1"
							title="全市重型车活动排放强度前10"
						></LevelTwoTitle>
					</div>
					<div className="table">
						{emissionIntensity?.length && (
							<dl>
								<dt>
									<span className="no1">排名</span>
									<span className="no2">街乡镇</span>
									<span className="no3">所属区</span>
									<span className="no4">排放强度</span>
								</dt>
								{emissionIntensity?.map((item, key) => {
									return (
										<dd
											key={key}
											onClick={() => {
												getCurrentPosition(item);
											}}
										>
											<span className={`top${key} no1`}>{key + 1}</span>
											<span className="no2">{item.TOWN_NAME || '--'}</span>
											<span className="no3">{item.COUNTY_NAME || '--'}</span>
											<span className="no4">
												{Number(item.EMISSION_INTENSITY)
													? Number(item.EMISSION_INTENSITY).toFixed(1)
													: '--'}
											</span>
										</dd>
									);
								})}
							</dl>
						)}
					</div>
				</div>
			</StreetDeregulationListStyled>
		</StreetDeregulationWrapStyled>
	);
};
export default StreetTownshipEmissionMap;
