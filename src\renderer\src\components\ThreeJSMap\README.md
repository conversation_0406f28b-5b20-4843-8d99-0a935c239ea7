# Three.js 3D 地图可视化组件

这是一个基于 Three.js 的 3D 地图可视化组件，可以根据 GeoJSON 数据和对应的数值数据渲染 3D 地图，并根据数据值显示不同的颜色等级。

## 功能特性

- 🗺️ **3D 地图渲染**: 基于 Three.js 的高性能 3D 渲染
- 🎨 **颜色分级**: 根据数据值自动分配颜色等级
- 🖱️ **交互操作**: 支持鼠标拖拽、缩放、点击、悬停
- 📊 **数据可视化**: 支持挤出高度表示数据大小
- 🎛️ **控制面板**: 内置相机控制和视角切换
- 📈 **图例显示**: 自动生成颜色图例
- 💡 **工具提示**: 悬停显示详细信息
- 🎯 **事件回调**: 支持点击和悬停事件处理

## 组件结构

```
ThreeJSMap/
├── index.tsx                 # 基础地图组件
├── ThreeJSMapEnhanced.tsx   # 增强版地图组件
├── MapExample.tsx           # 使用示例
├── styles.ts                # 样式定义
├── utils.ts                 # 工具函数
└── README.md               # 说明文档
```

## 快速开始

### 1. 基础使用

```tsx
import ThreeJSMap from '@renderer/components/ThreeJSMap';

const MyMapComponent = () => {
	const geoJsonData = {
		type: 'FeatureCollection',
		features: [
			{
				type: 'Feature',
				properties: { name: '区域1' },
				geometry: {
					type: 'Polygon',
					coordinates: [
						[
							[116.4, 39.9],
							[116.5, 39.9],
							[116.5, 40.0],
							[116.4, 40.0],
							[116.4, 39.9],
						],
					],
				},
			},
			// 更多区域...
		],
	};

	const mapData = [
		{ name: '区域1', value: 100 },
		// 更多数据...
	];

	return (
		<ThreeJSMap
			geoJsonData={geoJsonData}
			mapData={mapData}
			width={800}
			height={600}
		/>
	);
};
```

### 2. 增强版使用

```tsx
import ThreeJSMapEnhanced from '@renderer/components/ThreeJSMap/ThreeJSMapEnhanced';

const EnhancedMapComponent = () => {
	const handleRegionClick = (regionName: string, data: any) => {
		console.log('点击区域:', regionName, data);
	};

	const handleRegionHover = (regionName: string | null, data: any) => {
		console.log('悬停区域:', regionName);
	};

	return (
		<ThreeJSMapEnhanced
			geoJsonData={geoJsonData}
			mapData={mapData}
			width={1200}
			height={700}
			colorScale={[
				'#313695',
				'#4575b4',
				'#74add1',
				'#abd9e9',
				'#fee090',
				'#fdae61',
				'#f46d43',
				'#d73027',
				'#a50026',
			]}
			extrudeHeight={0.2}
			cameraPosition={[0, 8, 8]}
			showLegend={true}
			showControls={true}
			enableTooltip={true}
			onRegionClick={handleRegionClick}
			onRegionHover={handleRegionHover}
		/>
	);
};
```

## API 参考

### ThreeJSMapEnhanced Props

| 属性             | 类型                       | 默认值      | 描述                   |
| ---------------- | -------------------------- | ----------- | ---------------------- |
| `geoJsonData`    | `GeoJSONData`              | -           | GeoJSON 格式的地理数据 |
| `mapData`        | `MapData[]`                | -           | 对应的数值数据         |
| `width`          | `number`                   | `800`       | 地图宽度               |
| `height`         | `number`                   | `600`       | 地图高度               |
| `colorScale`     | `string[]`                 | 默认蓝色系  | 颜色等级数组           |
| `extrudeHeight`  | `number`                   | `0.1`       | 基础挤出高度           |
| `cameraPosition` | `[number, number, number]` | `[0, 5, 5]` | 相机初始位置           |
| `showLegend`     | `boolean`                  | `true`      | 是否显示图例           |
| `showControls`   | `boolean`                  | `true`      | 是否显示控制按钮       |
| `enableTooltip`  | `boolean`                  | `true`      | 是否启用工具提示       |
| `onRegionClick`  | `function`                 | -           | 区域点击回调           |
| `onRegionHover`  | `function`                 | -           | 区域悬停回调           |

### 数据格式

#### GeoJSON 数据格式

```typescript
interface GeoJSONData {
	type: 'FeatureCollection';
	features: GeoJSONFeature[];
}

interface GeoJSONFeature {
	type: 'Feature';
	properties: {
		name: string; // 区域名称，用于与mapData匹配
		[key: string]: any;
	};
	geometry: {
		type: 'Polygon' | 'MultiPolygon';
		coordinates: number[][][] | number[][][][];
	};
}
```

#### 数值数据格式

```typescript
interface MapData {
	name: string; // 区域名称，需与GeoJSON中的properties.name匹配
	value: number; // 数值，用于颜色分级和高度计算
	[key: string]: any; // 其他自定义属性
}
```

## 工具函数

### CoordinateConverter

坐标转换工具，用于将经纬度坐标转换为 Three.js 坐标系。

```typescript
const converter = new CoordinateConverter(116.4, 39.9, 100);
const threeJSCoord = converter.lonLatToThreeJS(116.5, 40.0);
```

### ColorUtils

颜色处理工具，提供多种颜色方案和颜色计算方法。

```typescript
// 获取预定义颜色方案
const blueScheme = ColorUtils.COLOR_SCHEMES.blue;

// 根据数值获取颜色
const color = ColorUtils.getColorByValue(value, min, max, colorScale);

// 生成图例数据
const legendData = ColorUtils.generateLegendData(mapData, colorScale);
```

### DataProcessor

数据处理工具，用于数据验证、匹配和处理。

```typescript
// 验证数据完整性
const validation = DataProcessor.validateData(geoJsonData, mapData);

// 匹配GeoJSON和数据
const matched = DataProcessor.matchGeoJSONWithData(geoJsonData, mapData);
```

## 交互操作

- **鼠标拖拽**: 旋转视角（轨道模式）或平移视图（顶视图模式）
- **鼠标滚轮**: 缩放视图
- **点击区域**: 选择区域，触发`onRegionClick`回调
- **悬停区域**: 显示工具提示，触发`onRegionHover`回调
- **重置视角**: 点击控制面板中的"重置视角"按钮
- **切换视图**: 在轨道视图和顶视图之间切换

## 颜色方案

组件提供多种预定义颜色方案：

- `blue`: 蓝色系（适合表示水资源、人口等）
- `red`: 红色系（适合表示温度、危险等级等）
- `green`: 绿色系（适合表示环境、健康等）
- `purple`: 紫色系（适合表示经济、科技等）
- `orange`: 橙色系（适合表示能源、活跃度等）
- `viridis`: 科学可视化常用色系

## 性能优化

- 使用材质池减少材质创建开销
- 支持几何体简化
- 实例化渲染支持（适用于大量相似几何体）
- 自动 LOD（细节层次）管理

## 注意事项

1. **坐标系统**: 组件默认使用 WGS84 坐标系，如果数据使用其他坐标系需要预先转换
2. **数据匹配**: 确保 GeoJSON 中的`properties.name`与 mapData 中的`name`字段完全匹配
3. **性能考虑**: 对于大量区域（>1000 个），建议使用数据简化或分层加载
4. **浏览器兼容**: 需要支持 WebGL 的现代浏览器

## 示例

查看 `MapExample.tsx` 文件获取完整的使用示例，包括：

- 多种数据类型切换
- 颜色方案切换
- 交互事件处理
- 控制面板集成

## 扩展开发

组件采用模块化设计，可以轻松扩展：

1. **自定义材质**: 修改材质创建逻辑添加特殊效果
2. **动画效果**: 添加数据变化时的过渡动画
3. **多层数据**: 支持多个数据层的叠加显示
4. **导出功能**: 添加截图或数据导出功能
