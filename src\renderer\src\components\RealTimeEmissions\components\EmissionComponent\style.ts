import styled from 'styled-components';

const LayoutContainer = styled.div`
  /* margin-top: 10px; */
  margin-left: 30px;
  display: flex;
  flex-direction: row;
  //justify-content: space-between;
  position: relative;
  .left-container {
    width: 254px;
    display: flex;
    flex-direction: row;
    .describe {
      display:flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      /* margin-left: 10px; */
      margin-top: 0;
      margin-bottom: 20px;
      /* text-align: center; */
      margin-right: 14px;
      dl {
        color: #fff;
        padding-left: 0;
        margin: 0 0 0 0;
        /* display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center; */
        dt {
          font-size: 14px;
          color: #d8e2ef;
          letter-spacing: 4px;
        }
        dd {
          position: relative;
          bottom: 5px;
          font-size: 28px;
          margin: 5px 0 0 0;
          /* font-weight: bold; */
          // font-family: 'electronicFont';
          span {
            font-size: 14px;
            color: #d8e2ef;
            margin-left: 5px;
          }
        }
        &.no1 {
          dt{
            font-size: 16px;
          }
          dd {
            display: inline-block;
            white-space: nowrap;
            color: #5ce2aa;
            font-size: 24px;
            padding-left: 20px;
            padding-top: 10px;
          }
        }
        &.no2 {
          margin-top: 13px;
          dt{
            padding-left: 20px;
            font-size: 16px;
          }
          dd {
            color: rgb(103,161,255);
            font-size: 64px;
            padding-bottom: 10px;
            /* border-bottom: 1px #3488b9 dotted; */
          }
        }
        &.no3 {
          dt{
            font-size: 16px;
          }
          dd {
            color: #f9ae83;
            font-size: 24px;
            display: inline-block;
            white-space: nowrap;
            padding-left: 20px;
            padding-top: 10px;
          }
        }
      }
      .describe-bottom {
        display: flex;
        width: 100%;
        flex-direction: row;
        justify-content: center;
        margin-top: 18px;
        dl {
          padding: 0 30px;
          &:nth-child(1) {
            padding-left: 0;
            position: relative;
            &::after {
              content: '';
              position: absolute;
              right: 0;
              top: 0;
              display: block;
              width: 1px;
              height: 50px;
              border-right: #3488b9 1px dashed;
            }
          }
          &:nth-child(2) {
            padding-right: 0;
          }
        }
      }
      img {
        width: 16px;
        height: 16px;
      }
      span:nth-child(2) {
        margin-left: 5px;
        color: #e2f0ff;
        font-size: 14px;
        // font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
      }
      span:nth-child(3) {
        margin-left: 10px;
        color: #4ce6ff;
        font-size: 14px;
      }
      span:nth-child(4) {
        margin-left: 10px;
        color: #e2f0ff;
        font-size: 14px;
        // font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
      }
      span:nth-child(5) {
        margin-left: 10px;
        color: #4ce6ff;
        font-size: 14px;
      }
    }
  }
  
  }
`;
export default LayoutContainer;
