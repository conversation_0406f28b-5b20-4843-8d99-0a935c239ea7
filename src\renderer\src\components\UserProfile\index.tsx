import React, { useEffect, useState, useContext } from 'react';
import Box from '../../baseUI/Box';
import Container from './style';
import { UserProfileComponent } from './components/UserProfileComponent';
import imgs from '../../images/users';
import {
	getUserCarTop,
	getCompany,
	getVehicleUser,
	getAffiliationInfoByID,
} from '@renderer/api';
import UserProfileEcharts from './components/UserProfileEcharts';
import { PageContext } from '@renderer/context';
/**
 * 用户概况
 */
type overviewProps = {
	img: string;
	name: string;
	color: string;
	key: string;
	num?: string;
};
type detailItemProps = {
	name: string;
	vehicle_type_nums: number;
	user_nums: number;
	rate: number;
};
type resProps = {
	// overview: { total_vehicle_type_nums: number; total_user_nums: number; total_company_nums: number }
	detail: detailItemProps[];
};
type userCarProps = {
	name: string;
	nums: number;
	user_type_id?: string;
	vin?: number;
	delete?: string;
};
type listProps = {
	violation_type_id: string;
	violation_type: string;
	vin: string;
};
const ManufacturersAndUsers = (props) => {
	const { countyId } = useContext(PageContext);
	// const color = ['#FF6B45', '#01E2FF', '#03FF9F', '#F09E38', '#ffa800', '#EC6B9E', '#CEE2E9;']
	const [overview, setOverview] = useState({});
	const [moreData, setMoreData] = useState<any>(props);

	const [userCar, setUserCar] = useState<any>([]);
	const [nameList, setNameList] = useState<any>([]);
	const [titleData, setTilleData] = useState<Array<overviewProps>>([
		{
			img: imgs.group,
			name: '厂商数量',
			color: ' #01E2FF',
			key: 'total_company_nums',
		},
		{
			img: imgs.car,
			name: '车型数量',
			color: '#01E2FF',
			key: 'total_vehicle_type_nums',
		},
		{
			img: imgs.user,
			name: '用户数量',
			color: '#01E2FF',
			key: 'total_user_nums',
		},
	]);

	const [options, setOption] = useState({});

	useEffect(() => {
		// getCompany({
		//   countyId
		// }).then((res) => {
		//   console.log('==============getCompany======================');
		//   console.log(res);
		//   console.log('==============getCompany======================');
		//   const overview = (res as resProps).overview
		//   const data = titleData.map((item) => {
		//     return { ...item, num: overview[item.key] }
		//   })
		//   setOverview(data)
		// })
		// let sortColor = ''
		// getUserCarTop({ top: 5, countyId }).then((res) => {
		//   console.log('==============getUserCarTop======================');
		//   console.log(res);
		//   console.log('==============getUserCarTop======================');
		//   if ((res as userCarProps[]).length >= 5) {
		//     const newItem = (res as userCarProps[]).slice(0, 5).map((item, index) => {
		//       if (index == 0) {
		//         sortColor = '#FE2C46'
		//       } else if (index == 1) {
		//         sortColor = '#FF6703'
		//       } else if (index == 2) {
		//         sortColor = '#FFAC0B'
		//       } else {
		//         sortColor = '#9195A3'
		//       }
		//       return { ...item, color: sortColor }
		//     })
		//     const enterpriseRankingItem = newItem.map((item, index) => {
		//       const { name, nums, ...rest } = item
		//       return { user_type_id: index + 1, user_type: name, vin: nums, ...rest }
		//     })
		//     setUserCar(newItem)
		//     setMoreData({
		//       ...moreData,
		//       data: {
		//         dataList: enterpriseRankingItem
		//       }
		//     })
		//   }
		// })

		getVehicleUser({ top: 10 }).then((res) => {
			const data = {
				user_num: res?.user_num || 0,
				vehicle_rate: res?.vehicle_rate * 100 || 0,
				big_user: res?.big_user || 0,
			};
			setOverview(data);
			const ids = res.rate.map((item) => item.name);
			getAffiliationInfoByID({
				affiliation_ids: ids.toString(),
			})
				.then((o) => {
					if (o.length) {
						const carData = o.map((item, i) => {
							return {
								nums: res.rate[i].value,
								name: item.AFFILIATION,
							};
						});
						const nameList = o.map((item) => item.AFFILIATION);
						setUserCar(carData);
						setNameList(nameList);
					}
				})
				.catch((error) => {
					console.log('error', error);
				});
		});
	}, [countyId]);

	// useEffect(() => {
	//   const option = {
	//     tooltip: {
	//       trigger: 'item'
	//     },
	//     // legend: {
	//     //   top: '5%',
	//     //   left: 'center'
	//     // },
	//     color: color,
	//     series: [
	//       {
	//         //name: 'Access From',
	//         type: 'pie',
	//         radius: ['50%', '70%'],
	//         avoidLabelOverlap: false,
	//         itemStyle: {
	//           borderWidth: 5,
	//           borderRadius: 0,
	//           shadowBlur: 3
	//           // borderColor: color[i],
	//           //shadowColor: color[i],
	//         },
	//         label: {
	//           show: false,
	//           position: 'center'
	//         },
	//         // emphasis: {
	//         //   label: {
	//         //     show: true,
	//         //     fontSize: 40,
	//         //     fontWeight: 'bold'
	//         //   }
	//         // },
	//         labelLine: {
	//           show: false
	//         },
	//         data: userData
	//       }
	//     ]
	//   }
	//   setOption(option)
	// }, [userData])
	//more={moreData}
	return (
		<Box title="用户概况">
			<Container>
				{userCar.length ? (
					<>
						<div className="left">
							<UserProfileComponent data={overview} />
						</div>

						<div className="right">
							<UserProfileEcharts userCar={userCar} nameList={nameList} />
						</div>
					</>
				) : null}
			</Container>
		</Box>
	);
};
export default ManufacturersAndUsers;
