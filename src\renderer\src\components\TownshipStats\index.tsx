import { useContext, useEffect, useRef, useState } from 'react';
import * as echarts from 'echarts';
import axios from 'axios';
import dayjs from 'dayjs';
import MapContext from '@renderer/context';
import { useAuth } from '@renderer/hooks/useAuth';
import {
	getViolationTownList,
	getViolationRegisterTownList,
} from '@renderer/api';
import { TownshipStatsStyle } from './styles';

const TownshipStats = (props: any) => {
	const { setLayerData, regionName } = props;
	const divRef = useRef<any>(null);
	const myChartRef = useRef<any>(null);
	const { token } = useAuth();
	const [regionFinal, setRegionFinal] = useState(regionName);
	let currentIndex = -1;

	const [currentTimeData, setCurrentTimeData] = useState<any>({
		start_time: dayjs().format('YYYY-MM-DD 00:00:00'),
		end_time: dayjs().format('YYYY-MM-DD HH:00:00'),
		time_type: '1',
	});
	const [previousTimeData, setPreviousTimeData] = useState<any>({
		start_time: dayjs().add(7, 'day').format('YYYY-MM-DD HH:00:00'),
		end_time: dayjs().format('YYYY-MM-DD HH:00:00'),
		time_type: '2',
	});
	const [allData, setAllData] = useState<any>([]);
	const [townJsonData, setTownJsonData] = useState<any>(null);
	const [optionsData, setOptionsData] = useState<any>([]);
	const t = 1; //流入流出控制
	let r = 1; //右侧流入流出文字控制
	const colors = [
		'#1DE9B6',
		'#EEDD78',
		'#FFFF00',
		'#FFDB5C',
		'#37A2DA',
		'#04B9FF',
	];
	const year = ['当前周期', '上一周期'];
	const mapData: any = [[], []];
	/*柱子Y名称*/
	const categoryData: any = [];
	const barData: any = [];

	const getTownJson = async () => {
		try {
			const response = await axios.get(`file:///public/data/townJson.json`);
			setTownJsonData(response.data);
		} catch (error) {
			console.error(error);
			throw error;
		}
	};

	function truncateString(str, maxLength) {
		if (str.length <= maxLength) {
			return str; // 如果字符串长度不超过 maxLength，则直接返回原字符串
		} else {
			return str.slice(0, maxLength) + '...'; // 截取字符串前 maxLength 个字符，并添加省略号
		}
	}

	const changeLayerData = (arr) => {
		const data = arr.map((item, index) => {
			let color;
			const num = Number(item.count);
			// 30以下：绿色 30-60：黄色 60-90：橙色 90-120：红色 120以上：绿色
			if (num < 30) {
				color = '#00FFFF';
			} else if (num >= 30 && num < 60) {
				color = '#00BFFF';
			} else if (num >= 60 && num < 90) {
				color = '#4169E1';
			} else if (num >= 90 && num < 120) {
				color = '#0000CD';
			} else if (num >= 120) {
				color = '#191970';
			}

			return {
				value: item.count,
				name: item.adm_name,
				areaName: item.name,
				itemStyle: {
					areaColor: color,
				},
			};
		});
		setLayerData(data);
	};

	useEffect(() => {
		getTownJson();
		const req = {
			start_time: currentTimeData.start_time,
			end_time: currentTimeData.end_time,
			time_type: currentTimeData.timeType,
			token,
		};
		const req2 = {
			start_time: previousTimeData.start_time,
			end_time: previousTimeData.end_time,
			time_type: previousTimeData.timeType,
			token,
		};
		Promise.all([
			getViolationRegisterTownList(req),
			getViolationRegisterTownList(req2),
		]).then((res) => {
			changeLayerData(res[1]);
			const data = res.map((item) => {
				return item;
			});
			console.log(data);

			setAllData(res);
		});
	}, []);

	useEffect(() => {
		if (!allData.length || !townJsonData) return;
		townJsonData.features.map((item) => {
			const name = item.properties.name;
			const currentValue = allData[0].find((i) => i.adm_name === name)?.count;
			const previousValue = allData[1].find((i) => i.adm_name === name)?.count;
			if (currentValue) {
				mapData[0].push({
					year: '当日',
					name: name,
					value: currentValue,
					value1: currentValue,
				});
			}
			if (previousValue) {
				mapData[1].push({
					year: '七天',
					name: name,
					value: previousValue,
					value1: previousValue,
				});
			}
		});

		for (let i = 0; i < mapData.length; i++) {
			mapData[i].sort(function sortNumber(a, b) {
				return a.value - b.value;
			});
			barData.push([]);
			categoryData.push([]);
			for (let j = 0; j < mapData[i].length; j++) {
				barData[i].push(mapData[i][j].value1);
				categoryData[i].push(mapData[i][j].name);
			}
		}
		const arr: any = [];
		for (let n = 0; n < year.length; n++) {
			arr.push({
				xAxis: {
					type: 'value',
					scale: true,
					position: 'top',
					min: 0,
					boundaryGap: false,
					splitLine: {
						show: false,
					},
					axisLine: {
						show: false,
					},
					axisTick: {
						show: false,
					},
					axisLabel: {
						margin: 1,
						textStyle: {
							color: '#aaa',
						},
					},
				},
				// 街乡镇名称
				yAxis: {
					type: 'category',
					//  name: 'TOP 20',
					nameGap: 16,
					axisLine: {
						show: true,
						lineStyle: {
							color: '#ddd',
						},
					},
					axisTick: {
						show: false,
						lineStyle: {
							color: '#ddd',
						},
					},
					axisLabel: {
						interval: 10,
						textStyle: {
							fontSize: '18px',
							color: '#ddf3fa',
						},
						formatter: (value) => {
							return truncateString(value, 4);
						},
					},
					data: categoryData[n],
				},
				series: [
					//柱状图
					{
						zlevel: 1,
						type: 'bar',
						barMaxWidth: 8,
						symbol: 'none',
						itemStyle: {
							normal: {
								color: colors[n],
								barBorderRadius: [0, 30, 30, 0],
							},
						},
						data: barData[n],
					},
				],
			});
			if (r === 0) {
				r = 1;
			} else {
				r = 0;
			}
		}
		setOptionsData(arr);
	}, [allData, townJsonData]);

	useEffect(() => {
		if (!myChartRef.current) return;
		const option = myChartRef.current.getOption();
		if (regionName && regionName.trim().length > 0) {
			myChartRef.current.dispatchAction({
				type: 'timelinePlayChange',
				// 播放状态，true 为自动播放
				playState: false,
			});
			// 取消之前高亮的图形
			myChartRef.current.dispatchAction({
				type: 'downplay',
				seriesIndex: 0,
				dataIndex: currentIndex,
			});
			currentIndex = option.yAxis[0].data.indexOf(regionName);

			// 高亮当前图形
			myChartRef.current.dispatchAction({
				type: 'highlight',
				seriesIndex: 0,
				dataIndex: currentIndex,
			});
			// 显示 tooltip
			myChartRef.current.dispatchAction({
				type: 'showTip',
				seriesIndex: 0,
				dataIndex: currentIndex,
			});
			setRegionFinal(regionName);
		} else {
			console.log(regionFinal);

			myChartRef.current.dispatchAction({
				type: 'timelinePlayChange',
				// 播放状态，true 为自动播放
				playState: true,
			});
			// 取消之前高亮的图形
			myChartRef.current.dispatchAction({
				type: 'downplay',
				seriesIndex: 0,
				dataIndex: option.yAxis[0].data.indexOf(regionFinal),
			});
			// 显示 tooltip
			myChartRef.current.dispatchAction({
				type: 'hideTip',
			});
		}
	}, [regionName]);

	useEffect(() => {
		myChartRef.current = echarts.init(divRef.current);
		const option = {
			timeline: {
				data: year,
				axisType: 'category',
				autoPlay: true,
				playInterval: 3500,
				left: '10%',
				right: '10%',
				bottom: '3%',
				width: '80%',
				//  height: null,
				label: {
					normal: {
						textStyle: {
							color: '#ddd',
						},
					},
					emphasis: {
						textStyle: {
							color: '#fff',
						},
					},
				},
				symbolSize: 10,
				lineStyle: {
					color: '#FFFFFF',
				},
				checkpointStyle: {
					borderColor: '#FFFFFF',
					borderWidth: 2,
				},
				controlStyle: {
					showNextBtn: true,
					showPrevBtn: true,
					normal: {
						color: '#d9d9d9',
						borderColor: '#bebebe',
					},
					emphasis: {
						color: '#FFFFFF',
						borderColor: '#aaa',
					},
				},
			},
			baseOption: {
				animation: true,
				animationDuration: 1000,
				animationEasing: 'cubicInOut',
				animationDurationUpdate: 1000,
				animationEasingUpdate: 'cubicInOut',
				grid: {
					right: '5%',
					top: '10%',
					bottom: '11%',
					width: '70%',
				},
				tooltip: {
					trigger: 'axis', // hover触发器
					axisPointer: {
						// 坐标轴指示器，坐标轴触发有效
						type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
						shadowStyle: {
							color: 'rgba(150,150,150,0.1)', //hover颜色
						},
					},
				},
			},
			options: optionsData,
		};

		myChartRef.current.setOption(option);
		myChartRef.current.on('timelinechanged', (e) => {
			if (e.currentIndex === 1) {
				changeLayerData(allData[0]);
			} else {
				changeLayerData(allData[1]);
			}
		});
		return () => {
			if (myChartRef.current) {
				myChartRef.current.off('timelinechanged');
				myChartRef.current.dispose();
			}
		};
	}, [optionsData]);

	return <TownshipStatsStyle ref={divRef}></TownshipStatsStyle>;
};

export default TownshipStats;
