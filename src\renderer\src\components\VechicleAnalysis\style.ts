import { background } from '@renderer/images/background';
import styled from 'styled-components';

export const VechicleAnalysisContent = styled.div`
  color: #fff;
  width: 100%;
  height: 100%;
  .container {
    width: 100%;
    height: 100%;
    display: flex;
    .container-left {
      display: flex;
      flex-direction: column;
      flex: 1;
      width: 100%;
      height: 100%;
      .echart-left {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
      }
    }
    .container-right {
      flex: 1.3;
      margin-left: 20px;
      height: 80%;
      .register-area {
        display: flex;
        height: 100%;
        margin-top: 5px;
        align-items: center;
        justify-content: center;
        margin-bottom: 5px;
        .map {
          width: 200px;
          height: 170px;
        }
        .list {
          // width: 250px;
          width: 100%;
          height: 100%;
          margin-right: 20px;
          margin-bottom:30px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
          // background: linear-gradient(180deg, rgba(13, 130, 234, 0.31) 0%, rgba(9, 30, 59, 0) 100%);
          border-radius: 4px 4px 0px 0px;
          .list-item {
            display: flex;
            width: 124px;
            height: 18px;
            line-height: 18x;
            padding: 0 8px;
            font-size: 12px;
            border: 1px solid #3488b9;
            justify-content: space-between;

          }
          .list-item:hover {
            background: rgba(58, 218, 255, 0.5);
            cursor: pointer;
          }

        
      }
    }

  }





`;
