import styled from 'styled-components';
type Style = {
	data: {
		progress?: number;
		bgColor: string;
		color: string;
	};
};
export const ProgressBarContainer = styled.div<Style>`
	.progress {
		width: 119px;
		display: flex;
		flex-direction: column;
		.name {
			margin: auto;
			height: 20px;
			line-height: 25px;
			font-size: 18px;
			font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #ffffff;
		}
		.bar {
			margin-top: 6px;
			width: 98px;
			height: 6px;
			background: ${(props) => props.data.bgColor};
			border-radius: 3px;
		}
		.bar::before {
			display: flex;
			height: 6px;
			content: '';
			color: #ffffff;
			background-color: ${(props) => props.data.color};
			text-align: center;
			white-space: nowrap;
			overflow: hidden;
			border-radius: 3px;
			width: ${(props) => `calc( 1% * ${props.data.progress})`};
		}
		.num {
			padding-top: 10px;
			display: flex;
			flex-direction: row;
			justify-content: center;
			height: 10px;
			line-height: 10px;
			span:nth-child(1) {
				display: flex;
				flex-direction: row;
				font-size: 20px;
				font-weight: bold;
				color: #ffffff;
				padding-right: 5px;
			}
			/* span:nth-child(1)::after{
      content: "辆";
      color: #FFFFFF;
      font-size: 16px;
      padding-right: 5px;
    } */
			span:nth-child(2) {
				font-weight: bold;
				color: ${(props) => props.data.color};
				font-size: 16px;
			}
			span:nth-child(3) {
				font-weight: bold;
				color: ${(props) => props.data.color};
				font-size: 16px;
			}
		}
	}
`;
