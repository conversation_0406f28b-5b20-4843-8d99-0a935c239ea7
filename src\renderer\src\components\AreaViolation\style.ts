import styled from 'styled-components';
import iconSelect from '@renderer/images/icon/select.png';
import iconSelectUn from '@renderer/images/icon/selectUn.png';

const AreaViolationStyled = styled.div`
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #fff;

	.echarts-map {
		width: 30%;
	}

	.echarts-line {
		width: 70%;
		padding-top: 10px;

		.echarts-title {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 20px;
			font-size: 14px;
			line-height: 20px;
			color: #fff;
			cursor: pointer;

			span {
				width: 70px;
				display: flex;
				justify-content: center;
				flex-shrink: 0;
				margin: 0 10px;
			}
		}
		.title-select {
			background: url(${iconSelect}) no-repeat center center;
			background-size: contain;
		}
		.title-un-select {
			background: url(${iconSelectUn}) no-repeat center center;
			background-size: contain;
		}
	}
`;
export default AreaViolationStyled;
