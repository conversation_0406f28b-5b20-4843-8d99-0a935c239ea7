import { useState } from 'react';
// import LevelTwoTitle from '@renderer/components/LevelTwoTitle'
// import ScrollList from '@renderer/components/ScrollList'
import ScrollListWidthTitle from './components/ScrollListWidthTitle';
import LayoutContainer from './style';
import DailyActivityLevelEcharts from './components/DailyActivityLevelEcharts';
type columnProps = {
	plate_number: string;
	violation_project: string;
	violation_time: string;
	violation_place: string;
};
const DailyActivityLevel = (props) => {
	const { dataList } = props;
	const [show, setShow] = useState(false);
	const [details, setDetails] = useState<columnProps>();
	const column = [
		{
			align: 'center',
			dataIndex: 'vehicle_type',
			key: 'vehicle_type',
			title: '行业',
		},
		{
			align: 'center',
			dataIndex: 'nums',
			key: 'nums',
			title: '在线车辆',
		},
		{
			align: 'center',
			dataIndex: 'total_nox',
			key: 'total_nox',
			title: '总排放量',
		},
		{
			align: 'center',
			dataIndex: 'total_distance',
			key: 'total_distance',
			title: '总里程',
		},
		{
			align: 'center',
			dataIndex: 'total_oil',
			key: 'total_oil',
			title: '总油耗',
		},
		{
			align: 'center',
			dataIndex: 'emissions_factor',
			key: 'emissions_factor',
			title: '排放因子',
		},
	];
	let optionTable = {
		columns: column,
		width: '100%',
		height: '305px',
		fontSize: 20,
		thclassname: 'th_row',
		tablebgcolor: 'rgba(9,30,59,0)',
		trheight: '40px',
		thheight: '40px',
		customwidth: true,
		rowbgcolor: [
			'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
		],
		thbgcolor: '#081F38',
	};
	// const dataListMore = dataList
	const echartsData: any = [];
	dataList?.map((item) => {
		const obj = {
			name: item.vehicle_type,
			value: item.nums,
		};
		echartsData.push(obj);
	});
	// let useNum = 5
	// for (let index = 0; index < 20; index++) {
	//   const element = dataListMore[index]
	//   const newElement = { ...element, user_type_id: useNum }
	//   dataListMore.push(newElement)
	//   useNum++
	// }
	const onliClick = (e) => {
		setShow(true);
		setDetails(e);
	};
	return (
		<LayoutContainer>
			{dataList ? (
				<>
					<div className="middle-container">
						<div className="table">
							<ScrollListWidthTitle
								columns={column}
								data={dataList}
								{...optionTable}
							></ScrollListWidthTitle>
						</div>
					</div>
					<div className="right-container">
						<DailyActivityLevelEcharts echartsData={echartsData} />
					</div>
				</>
			) : (
				<div style={{ margin: '10px', color: '#fff' }}>暂无数据</div>
			)}
		</LayoutContainer>
	);
};

export default DailyActivityLevel;
