/**
 * 实时在线整体情况
 */
import { useState, useEffect } from 'react';
import { ActivityLevelContent } from './style';
import Box from '../../baseUI/Box';
import TimeTypeRadio from '@renderer/components/OnlineTypeRadio';
import { RealTimeCumulativeOnline } from '../RealTimeCumulativeOnline';
import { getActivityLevel } from '@renderer/api';
type dataProps = {
	vehicle_type: string;
	in_beijing: Boolean;
	nums: number;
};
type props = {
	total_online: number;
	beijing_in: number;
	beijing_out: number;
	total_online_rate: number;
	beijing_in_rate: number;
	beijing_out_rate: number;
	data: Array<dataProps>;
};
type echartProps = {
	length: number;
	value: string;
	name: string;
	rate: number;
};
const ActivityLevel = () => {
	const [data, setData] = useState<props>();
	const [echartData, setEchartData] = useState<echartProps>();
	// const [accumulate, setAccumulate] = useState<any>([])
	const [onlineType, setOnlineType] = useState('realTime');
	const [typeOptions, setTypeOptions] = useState([
		{
			label: '实时在线',
			value: 'realTime',
		},
		{
			label: '累计上线',
			value: 'Accumulate',
		},
	]);
	useEffect(() => {
		getActivityLevel({ type: onlineType }).then((res) => {
			let result = res as any;
			setData(result);
			let data = (res as any).data.map((item) => {
				return {
					name: item.industry,
					num: item.nums,
					value: item.rate,
				};
			});
			setEchartData(data);
		});
	}, [onlineType]);

	return (
		<ActivityLevelContent>
			<Box
				title="实时在线整体情况"
				width="100%"
				titlewidth="95%"
				subTitle={
					<TimeTypeRadio
						type={'realTime'}
						typeOptions={typeOptions}
						onlineType={(e) => setOnlineType(e)}
					/>
				}
			>
				<div className="container">
					<RealTimeCumulativeOnline
						name={onlineType}
						onlineType={onlineType}
						data={data}
						echartData={echartData}
					/>
				</div>
			</Box>
		</ActivityLevelContent>
	);
};

export default ActivityLevel;
