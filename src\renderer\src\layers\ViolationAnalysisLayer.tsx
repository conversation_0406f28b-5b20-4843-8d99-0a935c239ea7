// 违规分析
import React, { useEffect, useContext, useState, useRef } from 'react';
import { Radio } from 'antd';
import moment from 'moment';
import * as echarts from 'echarts';
import { getViolationsIndexesYoy } from '@renderer/api';
import imgs from '@renderer/images/background';
import { getPop } from '@renderer/components/AreaViolation/echartsIo';
import { PageContext, MapContext } from '@renderer/context';
import {
	formatNumber,
	useViolationTimeReferenceParams,
} from '@renderer/hooks/index';
import CustomPopUps from '@renderer/baseUI/TimeTypeRadioCustom/components/CustomPopUps';
import EmissionProportion from '@renderer/marsLayers/components/EmissionProportion';
import {
	COUNTY_ID_NAME_MAP,
	VaUnitList,
	getTitles,
} from '@renderer/marsLayers/components/AreaStreetStyles';
import {
	AreaViolationWrapStyled,
	AreaViolationStyled,
	ReferenceStyled,
} from '@renderer/marsLayers/components/AreaStreetStyles/styles';

export default (props) => {
	const { visible, layerId } = props;
	const { sceneMode } = useContext(MapContext);
	const {
		currentTopNavMenu, // 菜单栏当前标签名称
		setCurrentTopNavMenu,
		regionData, // 区域地图数据
		setRegionData,
		selectRegionDate, // 当前选择的时间参数
		regionalIndicators, // 获取的区域地图和同比数据
		setRegionalIndicators,
		hideModeMenu, // 图层设定的hideModeMenu属性对应变量
		currentSelectedId, // 当前图层模板id
		setCurrentSelectedId,
		currentIndustryList, // 当前行业列表
		currentTopNavMenuList, // 当前选择菜单项名称列表
		currentIdList, // 当前选择菜单id列表
	} = useContext(PageContext);
	const divChartRef = useRef(null);
	const myChartRef = useRef(null);
	const [customDateYoy, setCustomDateYoy] = useState({});
	const [selectedButton, setSelectedButton] = useState('default');
	const [showPop, setShowPop] = useState(false);
	const [regionYOY, setRegionYOY] = useState([]);
	const [proportionData, setProportionData] = useState([]); // 区域右下角数据占比
	const [regionTitle, setRegionTitle] = useState('');

	// 初始化柱状图
	const setUpOption1 = (data) => {
		const combinedData = data?.adm?.map((adm, index) => ({
			adm,
			count: formatNumber(data.count[index]),
			dod: formatNumber(data.dod[index]),
			per: formatNumber(data.per[index]),
		}));
		combinedData?.sort((a, b) => a.count - b.count);
		const sortedAdmData = combinedData?.map((item) => item.adm);
		const sortedCountData = combinedData?.map((item) => item.count);
		const sortedDodData = combinedData?.map((item) => item.dod);
		const sortedPerData = combinedData?.map((item) => item.per);
		const option = getPop({
			...data,
			adm: sortedAdmData,
			count: sortedCountData,
			dod: sortedDodData,
			per: sortedPerData,
			text: getTitles(selectRegionDate.timeType, ''),
			unit: '辆',
		});
		const regionYOY = combinedData?.map((item) => {
			return {
				name: item.adm,
				per: item.per,
			};
		});
		setRegionYOY(regionYOY);
		return option;
	};

	// 柱状图默认自定义切换
	const handleClickReference = (value) => {
		setSelectedButton(value);
		if (value === 'custom') {
			setShowPop(true);
		} else {
			setShowPop(false);
		}
	};

	// 获取接口数据
	const getCustomLineData = (params) => {
		getViolationsIndexesYoy(params)
			.then((res) => {
				if (res?.count?.length > 0) {
					const mapData = res?.adm?.map((item, idx) => {
						const value = formatNumber(res?.count[idx]);
						return {
							name: COUNTY_ID_NAME_MAP[item],
							value,
						};
					});
					setRegionData(mapData);
					const newData = { ...res };
					newData.adm = res?.adm?.map((id) => COUNTY_ID_NAME_MAP[id]);
					setRegionalIndicators(newData);
				} else {
					setRegionData([]);
					setRegionalIndicators([]);
				}
			})
			.catch((err) => {
				setRegionData([]);
				setRegionalIndicators([]);
			});
	};

	useEffect(() => {
		setCurrentTopNavMenu(VaUnitList[0].name);
	}, []);

	useEffect(() => {
		if (!regionData?.length) {
			setProportionData([]);
			return;
		}
		setProportionData(regionData);
	}, [regionData]);

	useEffect(() => {
		if (selectedButton !== 'custom' || !Object.keys(customDateYoy).length)
			return;
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		const { benchmark_start_time, benchmark_end_time, benchmark_time_type } =
			useViolationTimeReferenceParams(
				customDateYoy,
				selectRegionDate.customDate,
			);
		let industry_name = '';
		if (currentIndustryList.length) industry_name = currentIndustryList[0];
		const params = {
			start_time,
			end_time,
			time_type: time_type === 'hour' ? 2 : 1,
			benchmark_start_time,
			benchmark_end_time,
			benchmark_time_type,
			topic: currentIdList.toString(),
			industry_name,
		};
		getCustomLineData(params);
	}, [
		customDateYoy,
		selectedButton,
		currentTopNavMenu,
		selectRegionDate,
		currentIndustryList,
		currentTopNavMenuList,
		currentIdList,
	]);

	useEffect(() => {
		if (
			!hideModeMenu ||
			(selectedButton === 'custom' && Object.keys(customDateYoy)?.length > 0)
		)
			return;
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		const { benchmark_start_time, benchmark_end_time, benchmark_time_type } =
			useViolationTimeReferenceParams(
				customDateYoy,
				selectRegionDate.customDate,
			);
		let industry_name = '';
		if (currentIndustryList.length) industry_name = currentIndustryList[0];
		const params = {
			start_time,
			end_time,
			time_type: time_type === 'hour' ? 2 : 1,
			benchmark_start_time,
			benchmark_end_time,
			benchmark_time_type,
			topic: currentIdList.toString(),
			industry_name,
		};
		getCustomLineData(params);
	}, [
		hideModeMenu,
		selectedButton,
		selectRegionDate,
		currentTopNavMenu,
		currentIndustryList,
		currentTopNavMenuList,
		currentIdList,
	]);

	useEffect(() => {
		if (!visible || !hideModeMenu) return;
		setCurrentSelectedId(layerId);
		myChartRef.current = echarts.init(divChartRef.current);
		myChartRef.current.setOption(setUpOption1(regionalIndicators));
		return () => {
			if (myChartRef.current) {
				myChartRef.current.dispose();
			}
			setCurrentSelectedId('');
		};
	}, [visible, regionalIndicators]);

	return visible && hideModeMenu ? (
		<>
			<AreaViolationWrapStyled>
				<ReferenceStyled style={{ right: '30%' }}>
					<div className="selectAssembly">
						<p>环比基准：</p>
						<Radio.Button
							value="default"
							onClick={() => handleClickReference('default')}
							style={{
								borderRadius: '6px',
								border: '1px solid #3ADAFF',
								background:
									selectedButton === 'default'
										? 'radial-gradient(72% 90% at 50% 50%, #3BC3F4 0%, rgba(52,112,190,0) 100%)'
										: undefined,
							}}
						>
							默认
						</Radio.Button>
						<Radio.Button
							value="custom"
							onClick={() => handleClickReference('custom')}
							style={{
								borderRadius: '6px',
								border: '1px solid #3ADAFF',
								background:
									selectedButton === 'custom'
										? 'radial-gradient(72% 90% at 50% 50%, #3BC3F4 0%, rgba(52,112,190,0) 100%)'
										: undefined,
							}}
						>
							自定义
						</Radio.Button>
					</div>
					<CustomPopUps
						top={'50px'}
						right={'0'}
						showPop={showPop}
						setShowPop={setShowPop}
						setTimeType={null}
						setCustomDate={setCustomDateYoy}
					/>
					<div
						ref={divChartRef}
						style={{ width: '100%', height: '540px', flex: 1 }}
					></div>
				</ReferenceStyled>
				<AreaViolationStyled>
					<div className="echarts-line" style={{ right: '30%' }}>
						<div className="echarts-title1">
							<span>总次数</span>
						</div>

						<img src={imgs.jiantou3} alt="" />

						<EmissionProportion
							emTitle={'总次数'}
							data={proportionData}
							regionData={regionData}
						/>
					</div>
				</AreaViolationStyled>
			</AreaViolationWrapStyled>
		</>
	) : null;
};
