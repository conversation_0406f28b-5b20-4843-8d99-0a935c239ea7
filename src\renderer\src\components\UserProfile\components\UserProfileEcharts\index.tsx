import styled from 'styled-components';
import Echarts from '@renderer/components/echarts';

type Props = {
	width?: string;
	height?: string;
	userCar: Array<any>;
	nameList: Array<any>;
};

export const Container = styled.div<Props>`
	width: ${(props) => props.width || '440px'};
	height: ${(props) => props.height || '215px'};
`;

const UserProfileEcharts = (props: Props) => {
	const { userCar, nameList } = props;
	const data = userCar.map((car) => car.nums);
	const Carname = userCar.map((car) => car.name);
	// data[0] = 99991
	const roundToCeiling = (number) => {
		const base = Math.pow(10, Math.floor(Math.log10(number)));
		const floor = Math.floor((number + base - 1) / base) * base;
		return floor;
	};
	const frameData = data.map(() => {
		return roundToCeiling(data[0]);
	});
	// const frameData2 = data.map((item) => {
	//   return item
	// })
	// frameData2.reverse()

	// const data = [70, 34, 60, 78, 69]
	const titlename = ['TOP 1', 'TOP 2', 'TOP 3', 'TOP 4', 'TOP 5'];
	const valdata = [];
	const option = {
		tooltip: {
			trigger: 'item',
			textStyle: {
				color: '#483D8B',
				fontSize: 12,
				fontWeight: 'bold',
			},
			itemWidth: 10,
			itemHeight: 10,
			formatter: (params) => {
				return `${Carname[params.dataIndex]}: ${params.value}`;
			},
		},
		grid: {
			top: 10,
			left: 100,
			right: 50,
			bottom: 25,
		},
		xAxis: {
			name: '{a|(辆)}', // 使用富文本标签设置 x 轴名称
			nameTextStyle: {
				rich: {
					a: {
						color: '#CCCCCC',
						fontSize: 14,
						padding: [28, 0, 0, 10], // 调整上边距
					},
				},
			},
			show: true,
			//设置title 刻度标签样式
			axisLabel: {
				show: true,
				color: '#E8F4FF',
				textStyle: {
					fontSize: 14,
				},
				// formatter: function (value, index) {
				//   if (index === data.length + 1) {
				//     return '(辆)'
				//   }
				//   return value
				// }
			},
			//设置底部轴线样式
			axisLine: {
				show: false,
			},
			//设置网格线颜色
			splitLine: {
				show: false,
			},
		},
		yAxis: [
			//左侧Y轴设置
			{
				//数据
				data: Carname,
				//显示
				show: true,
				//是否是反向坐标轴
				inverse: true,
				//轴线样式
				axisLine: {
					show: false,
					lineStyle: {
						color: ['rgba(160, 192, 252, 0.2)'],
					},
				},
				//设置网格线
				splitLine: {
					show: false,
				},
				//坐标轴刻度
				axisTick: {
					show: false,
				},
				//设置title 刻度标签样式
				axisLabel: {
					color: '#E8F4FF',
					textStyle: {
						fontSize: 14,
					},
					formatter: function (params) {
						let val = '';
						if (params.length > 6) {
							val = params.substr(0, 6) + '...';
							return val;
						} else return params;
					},
				},
			},
			//右侧侧Y轴设置
			{
				show: false,
				inverse: true,
				data: valdata,
				axisLabel: {
					textStyle: {
						fontSize: 12,
						color: 'red',
					},
				},
				axisLine: {
					show: false,
				},
				splitLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
			},
		],
		series: [
			{
				name: '条',
				type: 'bar',
				yAxisIndex: 0,
				data: data,
				barWidth: 16,
				itemStyle: {
					normal: {
						color: '#80ACFA',
					},
				},
				label: {
					normal: {
						show: true,
						color: '#ffffff',
						position: 'right',
						formatter: function (params) {
							const scales = [100, 1000, 10000, 100000];
							const index = scales.findIndex((scale) => data[0] < scale);
							const format =
								params.data < scales[index] / 10 ? '{outside|' : '{inside|';
							return format + params.data + '}';
						},
						// formatter: '{outside|{@[0]}}{inside|{@[6]}}',
						rich: {
							inside: {
								color: 'white',
								width: 0,
								padding: [0, 0, -2, -45],
							},
							outside: {
								color: 'white',
								width: 0,
								align: 'left',
								padding: [0, 0, -3, 0],
							},
						},
					},
				},
				z: 2,
			},
			{
				name: '框',
				type: 'bar',
				yAxisIndex: 1,
				barGap: '-100%',
				data: frameData,
				barWidth: 16,
				itemStyle: {
					normal: {
						color: 'rgba(160, 192, 252, 0.1)',
					},
				},
				z: 1,
				tooltip: {
					show: false,
				},
			},
			// {
			//   name: '框',
			//   type: 'bar',
			//   yAxisIndex: 1,
			//   barGap: '-100%',
			//   data: frameData2,
			//   barWidth: 16,
			//   itemStyle: {
			//     normal: {
			//       color: 'rgba(160, 192, 252, 0.15)'
			//     }
			//   },
			//   z: 0,
			//   tooltip: {
			//     show: false
			//   }
			// }
		],
	};

	return (
		<Container {...props}>
			<Echarts option={option}></Echarts>
		</Container>
	);
};

export default UserProfileEcharts;
