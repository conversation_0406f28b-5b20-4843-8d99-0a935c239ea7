import React, { useContext, useEffect, useRef } from 'react';
import * as mars3d from 'mars3d';
// import 'mars3d-cesium'
import { MapContext } from '../context';
import graWall from '@renderer/images/layer/grawall.png';
import {
	geoJson,
	geoBeijing,
} from '@renderer/marsLayers/components/BeiJingMap/data';

// const Cesium = mars3d.Cesium
let isFirst = true;
export default ({ level, ids, map, visible }) => {
	if (!map) return null;
	const { selectRegion } = useContext(MapContext);
	const addBeijingAreaMask = () => {
		geoJson.features.forEach((item, idx) => {
			map.addLayer(
				new mars3d.layer.GeoJsonLayer({
					id: 'regionWall' + idx,
					data: item,
					mask: true, // 标识为遮罩层【重点参数】
					show: false,
					symbol: {
						type: 'wallP',
						styleOptions: {
							setHeight: -2000,
							diffHeight: 3000,
							materialType: mars3d.MaterialType.LineFlow,
							image: graWall,
							color: '#36cee5',
						},
					},
				}),
			);
			map.addLayer(
				new mars3d.layer.GeoJsonLayer({
					id: 'regionPolyline' + idx,
					data: item,
					show: false,
					symbol: {
						type: 'polylineP',
						styleOptions: {
							color: '#3Fdaff',
							width: 2,
							addHeight: 1000,
						},
					},
				}),
			);
		});
	};

	const addBeijingMask = () => {
		fetch(
			`https://gis-service-api.airqualitychina.cn/v1/china-border?adm_level=${level}&adm_ids=${ids}&is_include_sub=0&coordinate=gcj02`,
			// `https://geoserver-air.i2value.cn/geoserver/hotgrid/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=hotgrid:china_regions_gcj&maxFeatures=50&outputFormat=application%2Fjson&cql_filter=COUNTYID=13`,
		)
			.then((res) => res.json())
			.then((res) => {
				res = res.result;
				console.log('==========res===========res=======res========');
				console.log(res);
				console.log('========res===res===========res=========res=====');
				map.addLayer(
					new mars3d.layer.GeoJsonLayer({
						data: geoBeijing,
						mask: true, // 标识为遮罩层【重点参数】
						// symbol: {
						//   styleOptions: {
						//     fill: true,
						//     color: 'rgb(2,26,79)',
						//     opacity: 0.9,
						//     outline: true,
						//     outlineColor: '#39E09B',
						//     outlineWidth: 8,
						//     outlineOpacity: 0.8,
						//     arcType: Cesium.ArcType.GEODESIC,
						//     clampToGround: true
						//   }
						// }
						symbol: {
							type: 'wallP',
							styleOptions: {
								setHeight: -2000,
								diffHeight: 3000,
								materialType: mars3d.MaterialType.LineFlow,
								image: graWall,
								color: '#36cee5',
							},
						},
						// flyTo: true
					}),
				);

				map.addLayer(
					new mars3d.layer.GeoJsonLayer({
						data: geoBeijing,
						symbol: {
							type: 'polylineP',
							styleOptions: {
								color: '#3Fdaff',
								width: 2,
								addHeight: 1000,
							},
						},
					}),
				);

				map.addLayer(
					new mars3d.layer.GeoJsonLayer({
						url: 'https://geo.datav.aliyun.com/areas_v3/bound/110000.json',
						mask: true,
						symbol: {
							styleOptions: {
								fill: true,
								color: '#102744',
								opacity: 0.9,
								clampToGround: true, // 是否贴地
							},
						},
					}),
				);
			});
	};

	useEffect(() => {
		if (isFirst) {
			// 添加北京的遮罩
			addBeijingMask();
			// 添加北京各个区域的遮罩
			addBeijingAreaMask();
			isFirst = false;
		}
		if (visible) {
			geoJson.features.forEach((item, idx) => {
				if (item.properties.name === selectRegion.name) {
					map.getLayerById('regionWall' + idx).show = true;
					map.getLayerById('regionPolyline' + idx).show = true;
				} else {
					map.getLayerById('regionWall' + idx).show = false;
					map.getLayerById('regionPolyline' + idx).show = false;
				}
			});
		} else {
			map.getLayers().forEach((item) => {
				if (/(regionWall|regionPolyline)/.test(item.options.id)) {
					map.getLayerById(item.options.id).show = false;
				}
			});
		}
	}, [map, visible, selectRegion]);

	return null;
};
