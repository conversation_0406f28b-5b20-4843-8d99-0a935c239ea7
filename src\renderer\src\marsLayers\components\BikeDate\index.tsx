import { useState } from 'react';
import { BikeDateStyled } from './styles';
import { Radio } from 'antd';
import CustomPopUps from '@renderer/baseUI/TimeTypeRadioCustom/components/CustomPopUps';

const BikeDate = () => {
	const [selectedButton, setSelectedButton] = useState('default');
	const [showPop, setShowPop] = useState(false);
	const [customDate, setCustomDate] = useState({});

	const handleClickReference = (value) => {
		setSelectedButton(value);
		if (value === 'custom') {
			setShowPop(true);
		} else {
			setShowPop(false);
		}
	};

	return (
		<BikeDateStyled>
			<div className="selectAssembly">
				<Radio.Button
					value="default"
					onClick={() => handleClickReference('default')}
					style={{
						color: selectedButton === 'default' ? '#3ADAFF' : '#e2e5e5',
					}}
				>
					实时
				</Radio.Button>
				<Radio.Button
					value="custom"
					onClick={() => handleClickReference('custom')}
					style={{ color: selectedButton === 'custom' ? '#3ADAFF' : '#e2e5e5' }}
				>
					自定义
				</Radio.Button>
			</div>
			<CustomPopUps
				top={'50px'}
				right={'0'}
				showPop={showPop}
				setShowPop={setShowPop}
				setTimeType={null}
				setCustomDate={setCustomDate}
			/>
		</BikeDateStyled>
	);
};

export default BikeDate;
