import React, { useEffect, useState } from 'react';
// import DelayUI from '@renderer/baseUI/DelayUI'
import { useFont } from '@renderer/hooks';
import LayoutContainer from './style';
import RealTimeOnlineEcharts from '../RealTimeOnlineEcharts';

interface Props {
	name: string;
	onlineType: string;
	data: any;
	echartData: any;
	rate: Record<string, number>;
}

export const RealTimeCumulativeOnline: React.FC<Props> = (props) => {
	const { name, data, echartData, onlineType } = props;
	const [real, setReal] = useState(true);

	const switchOnline = () => {
		if (name === 'realTime') {
			setReal(true);
		} else {
			setReal(false);
		}
	};
	useEffect(() => {
		switchOnline();
	}, [name, real]);
	return (
		<>
			<LayoutContainer>
				<div className={'left-container' + ' ' + useFont()}>
					<div className="describe-left">
						<div className="describe-title">
							京外在线
							<div className="describe-title-left"></div>
						</div>
						<div>
							<span style={{ fontSize: '26px', fontWeight: 600 }}>
								{data?.beijing_out}
							</span>
							<span>辆</span>
						</div>
						<div>{data?.beijing_out_rate}%</div>
					</div>
					<div className="describe-center">
						<div>{data?.total_online}</div>
						<span>辆</span>
					</div>
					<div className="describe-right">
						<div className="describe-title">
							京内在线
							<div className="describe-title-right"></div>
						</div>
						<div>
							<span style={{ fontSize: '26px', fontWeight: 600 }}>
								{data?.beijing_in}
							</span>
							<span>辆</span>
						</div>
						<div>{data?.beijing_in_rate}%</div>
					</div>
				</div>
				<div className="container-middle">
					{echartData?.length > 0 ? (
						<RealTimeOnlineEcharts data={echartData} />
					) : (
						<div>暂无数据</div>
					)}
					<div className="background"></div>
				</div>
			</LayoutContainer>
		</>
	);
};
