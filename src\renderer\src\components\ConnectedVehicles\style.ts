import styled from 'styled-components';
import titleBg from '../../images/networkedData/titleBg.png';
import imags from '../../images/networkedData/index';
import background from '@renderer/images/background';

const Container = styled.div`
	display: flex;
	position: relative;
	width: 100%;
	margin-left: 8px;
	margin-top: 16px;

	.up-container {
		flex: 1;
		min-width: 280px;
		position: relative;
	}

	.right {
		flex: 1;
		position: relative;

		.background {
			width: 56%;
			height: 100%;
			position: absolute;
			top: -10px;
			right: 42%;
			background-image: url(${background.echarts_b});
			background-repeat: no-repeat;
			background-size: 100%;
			z-index: -99;
		}
	}
`;
export { Container };
