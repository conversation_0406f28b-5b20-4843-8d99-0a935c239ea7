import { useState, useEffect, useContext } from 'react';
import { Button, Input } from 'antd';
import * as mars3d from 'mars3d';
import SearchBox from '../SearchBox';
import { MapContext } from '@renderer/context';
import VehicleSearchPanelStyle from './style';
const { Search } = Input;
const orientation = {
	heading: mars3d.Cesium.Math.toRadians(0),
	pitch: mars3d.Cesium.Math.toRadians(-35),
	roll: 0.0,
};

export default function VehicleSearchPanel({ map }) {
	const { setCurrentInfo, currentInfo } = useContext(MapContext);
	const [displaySearchList, setDisplaySearchList] = useState(false);
	const onSearch = (e) => {
		// console.log('eeeeeeeeeeeeeeee', e)
		setCurrentInfo({
			VIN: e,
		});
		// if (e && data?.length) {
		//   const o = data.find((obj) => obj.VIN === e)
		//   if (o?.LAT && o?.LON) {
		//     const point =
		//       sceneMode === 3
		//         ? [Number(o.LON), Number(o.LAT), 800]
		//         : [Number(o.LON), Number(o.LAT), 800]
		//     const orientationPoint = sceneMode === 3 ? orientation : {}
		//     map.flyToPoint(point, {
		//       orientation: orientationPoint,
		//       duration: 4,
		//       complete: () => {
		//         setCurrentInfo(o)
		//       }
		//     })
		//   }
		// }
	};

	const more = () => {
		setDisplaySearchList(true);
	};

	useEffect(() => {}, []);

	return (
		currentInfo?.VIN && (
			<>
				<VehicleSearchPanelStyle>
					<div className="vinSearch">
						<Search placeholder="请输入VIN" allowClear onSearch={onSearch} />
					</div>
					{/* <div className="moreSearch" onClick={more}>
          <span>更多</span>
        </div> */}
				</VehicleSearchPanelStyle>
				{/* <SearchBox
        displaySearchList={displaySearchList}
        setDisplaySearchList={setDisplaySearchList}
        map={map}
        vehicleSearchData={data?.slice(0, 10)}
      /> */}
			</>
		)
	);
}
