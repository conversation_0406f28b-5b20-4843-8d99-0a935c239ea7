import styled from 'styled-components';
import searchBg from '@renderer/images/icon/searchBg.png';

const CustomPopUpsStyle = styled.div`
	position: absolute;
	right: ${(props) => props.right || '540px'};
	top: ${(props) => props.top || '20px'};
	z-index: 10;
	width: 368px;
	height: 286px;
	font-size: 20px;
	color: #e2f0ff;
	/* overflow: hidden; */
	/* background-color: #00153cc5; */
	padding: 10px 0 0 15px;
	/* border: 2px solid rgba(19, 20, 20, 0.7); */
	background: linear-gradient(
		133deg,
		#0b2945b5 0%,
		#0b2945ba 45%,
		#0f334abd 100%
	);
	border-radius: 4px;
	border: 2px solid;
	border-image: linear-gradient(
			307deg,
			rgba(200, 200, 200, 0),
			rgba(1, 184, 255, 1),
			rgba(151, 151, 151, 0)
		)
		2 2;
	.close {
		width: 20px;
		height: 20px;
		position: absolute;
		right: 8px;
		top: 8px;
		transition: all 0.2s;
		&:hover {
			scale: calc(1.1);
			cursor: pointer;
		}
	}
	p {
		margin-bottom: 0;
	}
	.content {
		width: 100%;
		height: 200px;
		padding: 10px;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		.item {
			margin-top: 10px;
			display: flex;
			align-items: center;
			p {
				margin-right: 10px;
			}
		}
	}
	.search {
		width: 120px;
		height: 45px;
		float: right;
		margin-right: 30px;
		background: url(${searchBg}) no-repeat;
		background-size: 100% 100%;
		line-height: 45px;
		text-align: center;
		cursor: pointer;
	}
	.settings-select {
		.ant-select-selector {
			background: rgba(64, 146, 171, 0.16);
			border-radius: 4px;
			border: 1px solid #3fdaff;
			font-size: 18px !important;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			height: 32px;
			color: #ffffff !important;
			display: flex;
			align-items: center;
		}
		.ant-select-selection-overflow {
			text-overflow: ellipsis;
			white-space: nowrap;
			height: 49px;
			align-items: center;
			flex-wrap: nowrap;
			overflow: hidden;
		}
		.ant-select-selection-overflow-item {
			background-color: transparent;
			border: 0;
			.ant-select-selection-item {
				background-color: transparent;
				border: 0;
			}
			.ant-select-selection-item-remove {
				color: #fff;
				font-size: 20px;
			}
		}
	}
	.ant-select-arrow {
		color: rgba(255, 255, 255, 1);
		font-size: 18px;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-select-single.ant-select-open
		.ant-select-selection-item {
		color: #ffffff !important;
	}
	.ant-picker {
		background: rgba(64, 146, 171, 0.16);
		border-radius: 4px;
		border: 1px solid #3fdaff;
		font-size: 18px !important;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		height: 32px;
		color: #ffffff !important;
		display: flex;
		align-items: center;
	}
	.ant-picker-input {
		font-size: 20px;
		color: #e2f0ff !important;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-picker .ant-picker-suffix {
		display: none !important;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-picker .ant-picker-clear {
		display: none !important;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-picker
		.ant-picker-input
		> input {
		text-align: center !important;
		line-height: 0 !important;
		font-size: 18px !important;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-picker
		.ant-picker-input-placeholder
		> input {
		color: #fff !important;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-picker {
		padding: 0 !important;
	}
`;

export default CustomPopUpsStyle;
