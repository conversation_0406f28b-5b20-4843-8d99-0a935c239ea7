import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import moment from 'moment';
import imgs from '../../images/realTimeEmissions/index';
import LayoutContainer from './style';
import Box from '../../baseUI/Box';
import DelayUI from '@renderer/baseUI/DelayUI';
import { EmissionComponent } from './components/EmissionComponent';
import * as echarts from 'echarts';
import { getIndustry } from '@renderer/api';
/**
 * 实时排放
 */
// let x = ['test1', 'test2', 'test3', 'test4', 'test5']
// let y = [10, 20, 30, 40, 50]
// let y2 = [20, 20, 30, 40, 50]
const wid = 10;
const w1 = Math.sin(Math.PI / 6) * wid; //4
const w2 = Math.sin(Math.PI / 3) * wid; // 6.8
const snapHeight = wid / 2;
// type dataProps = {
//   name: string
//   nox: number
//   nox_factor: number
// }

type dataProps2 = {
	total_nox: string;
	nox_factor: number;
	single_vehicle_emissions: number;
};
const RealTimeEmissions = () => {
	// const eachartRef = useRef(null)
	// const [options, setOptions] = useState({})
	const [columnData, setColumnData] = useState([]);
	const [foldLine, setFoldLine] = useState([]);
	const [title, setTitle] = useState([]);

	const [data, setData] = useState<any>([
		{ img: imgs.icon1, name: 'NOx总排放(吨)', value: '--' },
		{ img: imgs.icon2, name: '排放因子（G/KM）', value: '--' },
		{ img: imgs.icon3, name: '单车排放（G）', value: '--' },
	]);

	useEffect(() => {
		//data_type 0:所有 1 行业
		const start_time = moment()
			.subtract(1, 'hour')
			.format('YYYY-MM-DD 00:00:00');
		const end_time = moment().format('YYYY-MM-DD HH:00:00');
		Promise.all([
			getIndustry({ data_type: 0, start_time, end_time }),
			getIndustry({ data_type: 1, start_time, end_time }),
		]).then((res) => {
			const result = res[0] as dataProps2;
			setData([
				{
					img: imgs.icon1,
					name: 'NOx总排放(吨)',
					value: result.total_nox || '--',
				},
				{
					img: imgs.icon2,
					name: '排放因子（G/KM）',
					value: result.nox_factor || '--',
				},
				{
					img: imgs.icon3,
					name: '单车排放（G）',
					value: result.single_vehicle_emissions || '--',
				},
			]);
			const result2 = res[1] as any;
			if (!result2.length) {
				setColumnData([]);
				setFoldLine([]);
				setTitle([]);
				return;
			}
			setColumnData(
				result2.map((item) => {
					if (item.nox < 0.01) {
						return item.nox * 10000;
					} else if (item.nox < 0.1) {
						return item.nox * 1000;
					} else if (item.nox < 1) {
						return item.nox * 100;
					} else {
						return item.nox * 5;
					}
				}),
			);
			setFoldLine(
				result2.map((item) => {
					const randomNumber = Math.floor(Math.random() * 51) + 50;
					return randomNumber;
				}),
			);
			setTitle(result2.map((item) => item.name));
		});
	}, []);

	useEffect(() => {
		const echart = echarts.init(document.getElementById('eachartRef'));
		// 注册三个面图形
		echarts.graphic.registerShape('CubeLeft', CubeLeft);
		echarts.graphic.registerShape('CubeRight', CubeRight);
		echarts.graphic.registerShape('CubeTop', CubeTop);

		const option = {
			//你的代码
			tooltip: {
				show: true,
				trigger: 'axis',
				axisPointer: {
					type: 'shadow',
				},
			},
			grid: {
				left: '0',
				right: '10%',
				top: '25%',
				bottom: '0',
				//height: '%',
				containLabel: true,
			},
			xAxis: {
				type: 'category',
				data: title,
				// axisLine: {
				//   show: true,
				//   lineStyle: {
				//     color: '#FFE2F0FF',
				//     width: 1
				//   }
				// },
				axisTick: {
					show: false,
					length: 9,
					alignWithLabel: true,
					lineStyle: {
						color: '#E2F0FF',
						fontSize: 14,
					},
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#565f6a',
						width: 1,
					},
				},
				axisLabel: {
					interval: 0,
					//rotate:40,
					color: '#E2F0FF',
					fontSize: 14,
					formatter: function (params) {
						let newParamsName = ''; // 最终拼接成的字符串
						const paramsNameNumber = params.length; // 实际标签的个数
						const provideNumber = 4; // 每行能显示的字的个数
						const rowNumber = Math.ceil(paramsNameNumber / provideNumber); // 换行的话，需要显示几行，向上取整
						/**
						 * 判断标签的个数是否大于规定的个数， 如果大于，则进行换行处理 如果不大于，即等于或小于，就返回原标签
						 */
						// 条件等同于rowNumber>1
						if (paramsNameNumber > provideNumber) {
							/** 循环每一行,p表示行 */
							for (let p = 0; p < rowNumber; p++) {
								let tempStr = ''; // 表示每一次截取的字符串
								const start = p * provideNumber; // 开始截取的位置
								const end = start + provideNumber; // 结束截取的位置
								// 此处特殊处理最后一行的索引值
								if (p == rowNumber - 1) {
									// 最后一次不换行
									tempStr = params.substring(start, paramsNameNumber);
								} else {
									// 每一次拼接字符串并换行
									tempStr = params.substring(start, end) + '\n';
								}
								newParamsName += tempStr; // 最终拼成的字符串
							}
						} else {
							// 将旧标签的值赋给新标签
							newParamsName = params;
						}
						//将最终的字符串返回
						return newParamsName;
					},
				},
				splitLine: {
					show: false,
					lineStyle: {
						color: '#E2F0FF',
					},
				},
			},
			dataZoom: [
				{
					id: 'dataZoomY',
					xAxisIndex: [0],
					show: false, //是否显示滑动条，不影响使用
					type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
					startValue: 0, // 从头开始。
					endValue: 4, // 一次性展示5个
					height: 3,
					borderColor: 'transparent',
					fillerColor: 'rgba(205,205,205,1)',
					zoomLock: true,
					showDataShadow: false, //是否显示数据阴影 默认auto
					backgroundColor: '#fff',
					showDetail: false, //即拖拽时候是否显示详细数值信息 默认true
					realtime: true, //是否实时更新
					filterMode: 'filter',
					handleIcon: 'circle',
					handleStyle: {
						color: 'rgba(205,205,205,1)',
						borderColor: 'rgba(205,205,205,1)',
					},
					// handleSize: '80%',
					moveHandleSize: 0,
					// maxValueSpan: "2014-02-24",
					// minValueSpan: 4,
					brushSelect: false, //刷选功能，设为false可以防止拖动条长度改变 ************（这是一个坑）
				},
				{
					type: 'inside',
					xAxisIndex: 0,
					zoomOnMouseWheel: false, //滚轮是否触发缩放
					moveOnMouseMove: true, //鼠标滚轮触发滚动
					moveOnMouseWheel: true,
				},
			],
			yAxis: [
				{
					name: '单位 (T)',
					// min: 0,
					// max: 15,
					nameTextStyle: {
						color: '#E8F4FF',
						fontSize: 12,
					},
					type: 'value',
					axisLine: {
						show: true,
						lineStyle: {
							color: '#565f6a',
							width: 1,
						},
					},
					axiosTick: {
						show: false,
					},
					axisLabel: {
						show: true,
						color: '#E8F4FF',
						fontSize: 14,
						margin: 10,
					},
					splitLine: {
						show: true,
						lineStyle: {
							color: 'rgba(5, 80, 123, .1)',
							type: 'dashed',
							width: 1,
						},
					},
				},
				{
					type: 'value',
					name: '单位 G/KM',
					min: 0,
					max: 100,
					nameTextStyle: {
						color: '#E8F4FF',
						fontSize: 12,
						padding: [0, 0, 10, 30],
					},
					axisLine: {
						show: true,
						lineStyle: {
							color: '#84929f',
						},
					},
					// splitNumber: 5,
					splitLine: {
						show: true,
						lineStyle: {
							type: 'dashed',
							width: 1,
							// 使用深浅的间隔色
							color: ['#566471', '#566471'],
						},
					},
					axisLabel: {
						show: true,
						fontSize: 14,
						color: '#E2F0FF',
						formatter: function (value) {
							// 在标签后面添加百分号
							return value + '%';
						},
					},
				},
			],
			series: [
				{
					type: 'custom',
					renderItem: (params, api) => {
						const location = api.coord([api.value(0), api.value(1)]);
						location[0] = location[0] + wid * 0;
						const xlocation = api.coord([api.value(0), 0]);
						xlocation[0] = xlocation[0] + wid * 0;
						return {
							type: 'group',
							children: [
								{
									type: 'CubeLeft',
									shape: {
										api,
										xValue: api.value(0),
										yValue: api.value(1),
										// x: location[0],
										x: location[0] + 4,
										// y: location[1],
										y: location[1] + 8,
										xAxisPoint: xlocation,
									},
									style: {
										fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
											// {
											//   offset: 0,
											//   color: 'rgba(31,171,203)'
											// },
											{
												offset: 1,
												// color: 'rgba(31,171,203,0)'
												color: '#85A9FF',
											},
										]),
									},
								},
								// {
								//   type: 'CubeRight',
								//   shape: {
								//     api,
								//     xValue: api.value(0),
								//     yValue: api.value(1),
								//     x: location[0],
								//     y: location[1],
								//     xAxisPoint: xlocation
								//   },
								//   style: {
								//     fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
								//       {
								//         offset: 0,
								//         color: 'rgba(31,171,203)'
								//       },
								//       {
								//         offset: 1,
								//         color: 'rgba(31,171,203,0)'
								//       }
								//     ])
								//   }
								// },
								// {
								//   type: 'CubeTop',
								//   shape: {
								//     api,
								//     xValue: api.value(0),
								//     yValue: api.value(1),
								//     x: location[0],
								//     y: location[1],
								//     xAxisPoint: xlocation
								//   },
								//   style: {
								//     fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
								//       {
								//         offset: 0,
								//         color: '#84fffb'
								//       },
								//       {
								//         offset: 1,
								//         color: '#84fffb'
								//       }
								//     ])
								//   }
								// }
							],
						};
					},
					color: 'blue',
					data: columnData, //柱状图数据
				},
				{
					//name: '交易量',
					data: foldLine, //折线图
					type: 'line',
					color: '#ff8f00',
					// symbolSize: 8, // 标记的大小
					symbolSize: 6,
					lineStyle: {
						color: '#FD9546',
						width: 2,
					},
					itemStyle: {
						//折线拐点标志的样式
						color: '#FD9546',
						borderColor: '#FD9546',
						borderWidth: 2,
					},
				},
			],
		};
		echart.setOption(option);
	});

	const CubeLeft = echarts.graphic.extendShape({
		shape: {
			x: 0,
			y: 0,
		},
		buildPath: function (ctx, shape) {
			// 会canvas的应该都能看得懂，shape是从custom传入的
			const xAxisPoint = shape.xAxisPoint;
			const c0 = [shape.x, shape.y];
			const c1 = [shape.x - w2, shape.y];
			const c2 = [shape.x - w2, xAxisPoint[1]];
			const c3 = [shape.x, xAxisPoint[1]];
			ctx
				.moveTo(c0[0], c0[1])
				?.lineTo(c1[0], c1[1])
				.lineTo(c2[0], c2[1])
				.lineTo(c3[0], c3[1])
				.closePath();
		},
	});
	// 绘制右侧面
	const CubeRight = echarts.graphic.extendShape({
		shape: {
			x: 0,
			y: 0,
		},
		buildPath: function (ctx, shape) {
			const xAxisPoint = shape.xAxisPoint;
			const c1 = [shape.x, shape.y];
			const c2 = [shape.x, xAxisPoint[1]];
			const c3 = [shape.x + w1, xAxisPoint[1] - w2 + snapHeight];
			const c4 = [shape.x + w1, shape.y - w2 + snapHeight];
			ctx
				.moveTo(c1[0], c1[1])
				?.lineTo(c2[0], c2[1])
				.lineTo(c3[0], c3[1])
				.lineTo(c4[0], c4[1])
				.closePath();
		},
	});
	// 绘制顶面
	const CubeTop = echarts.graphic.extendShape({
		shape: {
			x: 0,
			y: 0,
		},
		buildPath: function (ctx, shape) {
			//
			const c1 = [shape.x, shape.y];
			const c2 = [shape.x + w1, shape.y - w2 + snapHeight]; //右点
			const c3 = [shape.x - w2 + w1, shape.y - w2 + snapHeight];
			const c4 = [shape.x - w2, shape.y];
			ctx
				.moveTo(c1[0], c1[1])
				?.lineTo(c2[0], c2[1])
				.lineTo(c3[0], c3[1])
				.lineTo(c4[0], c4[1])
				.closePath();
		},
	});
	return (
		<Box title="实时排放" height="30%" titlewidth="95%">
			<LayoutContainer>
				<div className="left-container">
					<EmissionComponent data={data} />
					{/* <div className="emission">
            {data?.map((item, index) => {
              return (
                <div
                  key={index}
                  style={{ backgroundImage: `url(${item.img})` }}
                  className="emission-item"
                >
                  <div className="emission-number">
                    <span>
                      <DelayUI
                        targetNumber={item.value}
                        style={{ fontSize: '18px', color: '#4ce6ff' }}
                        delayTime={5000}
                      />
                    </span>
                    <span>{item.name}</span>
                  </div>
                </div>
              )
            })}
          </div> */}
				</div>
				<div className="right-container">
					{/* <div style={{ width: '', height: '210px' }} id="eachartRef"></div> */}
					<div style={{ width: 'auto', height: '210px' }} id="eachartRef"></div>
				</div>
			</LayoutContainer>
		</Box>
	);
};
export default RealTimeEmissions;
