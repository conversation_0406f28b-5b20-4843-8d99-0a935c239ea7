import {
	AreaStyle,
	AreaStyleP,
	StreetStyle,
	UnitStyle,
	UnitIndustryStyle,
} from './styles';
import humidityColor from '@renderer/images/layer/humidityColor.png';
import humidityColor2 from '@renderer/images/layer/humidityColor2.png';
import dataBase from '@renderer/images/layer/datacolumn.png';

// 北京各区id
export const COUNTY_ID_NAME_MAP = {
	1: '全市',
	2: '海淀区',
	3: '朝阳区',
	4: '大兴区',
	5: '房山区',
	6: '石景山区',
	7: '东城区',
	8: '西城区',
	9: '平谷区',
	10: '密云区',
	11: '怀柔区',
	12: '顺义区',
	13: '门头沟区',
	14: '通州区',
	15: '昌平区',
	16: '延庆区',
	17: '丰台区',
	18: '开发区',
};

// 默认北京各区排序
export const beijingRegions = [
	'东城区',
	'西城区',
	'朝阳区',
	'海淀区',
	'丰台区',
	'石景山区',
	'门头沟区',
	'房山区',
	'通州区',
	'顺义区',
	'昌平区',
	'大兴区',
	'怀柔区',
	'平谷区',
	'密云区',
	'延庆区',
];

export const degreeList = [
	{ id: 1, name: '优', color: '#53CB44' },
	{ id: 2, name: '良', color: '#CCD339' },
	{ id: 3, name: '轻度', color: '#D1A13C' },
	{ id: 4, name: '中度', color: '#CD523B' },
	{ id: 5, name: '重度', color: '#B82C2A' },
	{ id: 6, name: '严重', color: '#740000' },
];

export const unitList = [
	{ id: 5, name: '在线车辆', unit: '辆' },
	{ id: 2, name: '排放量', unit: '千克' },
	{ id: 3, name: '里程', unit: '千米' },
	{ id: 1, name: '油耗', unit: '升' },
	{ id: 4, name: '排放强度', unit: '克/千米' },
	{ id: 6, name: '违规', unit: '辆' },
];

export const VaUnitList = [
	{ id: 1, name: 'MIL灯点亮', unit: '辆' },
	{ id: 2, name: 'DPF故障', unit: '辆' },
	{ id: 3, name: '尿素消耗异常', unit: '辆' },
	{ id: 4, name: 'OBD数据不全', unit: '辆' },
	{ id: 5, name: '超过7天不在线', unit: '辆' },
	{ id: 6, name: 'NOx超排', unit: '辆' },
];

const selectLegend = (layerId, name) => {
	if (name === '排放量' || name === '排放强度') {
		return layerId == 'streetTownship' ? (
			<StreetStyle>
				<img src={dataBase} alt="" />
			</StreetStyle>
		) : (
			<AreaStyleP>
				<img src={humidityColor2} alt="" />
			</AreaStyleP>
		);
	} else {
		return layerId == 'streetTownship' ? null : ( // </StreetStyle> //   <img src={dataBase} alt="" /> // <StreetStyle>
			<AreaStyleP>
				<img src={humidityColor2} alt="" />
			</AreaStyleP>
		);
		// return (
		//   <AreaStyle>
		//     <div className="degree">
		//       {degreeList.map((item) => {
		//         return (
		//           // <div key={item.id} className="degreeContent">
		//           //   <div className="degreeItem" style={{ backgroundColor: item.color }}></div>
		//           //   <div className="itemName">{item.name}</div>
		//           // </div>
		//           null
		//         )
		//       })}
		//     </div>
		//   </AreaStyle>
		// )
	}
};

// 描述地图图例
const legendArea = (layerId, currentName) => {
	return selectLegend(layerId, currentName);
};

// 地图数据单位
const unitArea = (currentTopNavMenu) => {
	return (
		<UnitStyle>
			<div className="unitArea">
				单位: &nbsp;{unitList?.find((i) => i.name === currentTopNavMenu)?.unit}
			</div>
		</UnitStyle>
	);
};

// 地图数据单位
const unitIndustry = (currentTopNavMenu) => {
	return (
		<UnitIndustryStyle>
			<div className="unitArea">
				单位: &nbsp;{unitList?.find((i) => i.name === currentTopNavMenu)?.unit}
			</div>
		</UnitIndustryStyle>
	);
};

const unitViolationAnalysis = (currentTopNavMenu) => {
	return (
		<UnitStyle>
			<div className="unitArea">
				单位: &nbsp;
				{VaUnitList?.find((i) => i.name === currentTopNavMenu)?.unit}
			</div>
		</UnitStyle>
	);
};

// 获取柱状图文案
const getTitles = (timeType, name) => {
	switch (timeType) {
		case 'day':
			return [`当日`, `昨日`, '环比'];
		case '7day':
		case 'custom':
			return [`当前周期`, `上一周期`, '环比'];
		case '24hours':
			return [`昨日`, `前日`, '环比'];
		default:
			return [`当前周期`, `上一周期`, '环比'];
	}
};

const violationTypeList = [
	{ id: 1, name: 'dpf异常', active: true },
	{ id: 2, name: '尿素空异常', active: true },
	{ id: 3, name: 'Mil灯异常', active: true },
	{ id: 4, name: 'NOx排放异常', active: true },
	{ id: 5, name: 'OBD监测项不全', active: true },
];
export {
	legendArea,
	unitArea,
	getTitles,
	unitViolationAnalysis,
	violationTypeList,
	unitIndustry,
};
