body {
	display: flex;
	flex-direction: column;
	font-family: 'TimesNewRoman', Roboto, -apple-system, BlinkMacSystemFont,
		'Helvetica Neue', 'Segoe UI', 'Oxygen', 'Ubuntu', 'Cantarell', 'Open Sans',
		sans-serif;
}
#root {
	width: 100vw;
	height: 100vh;
	overflow: hidden;
	background-color: rgb(24 38 50);
}
* {
	padding: 0;
	margin: 0;
}

ul {
	list-style: none;
}
#cesiumContainer {
	width: 100%;
	height: 100%;
}

.time_select_overlay .ant-dropdown-menu {
	background: #22252f !important;
	color: #e2e6ed;
	border: 1px solid #373e53;
	margin-top: 3px;
}

.time_select_popup .ant-picker-year-panel {
	border: 1px solid #373e53;
}
.sewage-treatment-table {
	padding: 10px;
}

.venntooltip {
  position: absolute;
  text-align: center;
  width: 128px;
  height: 16px;
  background: #333;
  color: #ddd;
  padding: 2px;
  border: 0px;
  border-radius: 8px;
  opacity: 0;
}
