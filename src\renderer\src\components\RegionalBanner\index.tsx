import { useContext, useEffect, useState } from 'react';
import { Input, Space, Button, Dropdown, message } from 'antd';
import type { MenuProps } from 'antd';
import moment from 'moment';
import _debounce from 'lodash/debounce';
import { XyzTransitionGroup } from '@animxyz/react';
import { SearchOutlined, DownOutlined } from '@ant-design/icons';
import { PageContext } from '@renderer/context';
import {
	useAuth,
	formatNumber,
	useSpaceTimeParams,
} from '@renderer/hooks/index';
import { COUNTY_ID_NAME_MAP } from '@renderer/marsLayers/components/AreaStreetStyles';
import industryClassification from '@renderer/components/AreaSide/components/IndustryClassification';
import TimeTypeRadio from '@renderer/baseUI/TimeTypeRadioCustom';
import icon from '@renderer/images/icon';
import RegionalBannerStyled from './styles';
import { getTargetList } from '@renderer/utils/util';
import LayerChoose from '@renderer/components/LayerChoose';

interface CustomDate {
	start_time: string;
	end_time: string;
	time_type: string;
}

let bannerTimeout;

const RegionalBanner = ({ currentSelectedId }) => {
	const {
		hideModeMenu,
		setCurrentTopNavMenu,
		regionName,
		setRegionName,
		setRegionData,
		selectRegionDate,
		setSelectRegionDate,
		setRegionalIndicators,
		setStreetName,
		streetShipData,
		currentIndustry,
		setCurrentIndustry,
		setCurrentIndustryList,
		setCurrentTopNavMenuList, // 多选框选择名称列表
	} = useContext(PageContext);
	const [streetNames, setStreetNames] = useState(''); //模糊查询记忆输入名字
	const [index, setIndex] = useState(0); //模糊查询记忆输入下标
	const [currentIdList, setCurrentIdList] = useState([5]); // 多选框选中id列表
	const [currentId, setCurrentId] = useState(5);
	const [currentRegion, setCurrentRegion] = useState('全市');
	// const [currentIndustry, setCurrentIndustry] = useState('全部')
	const [navList, setNavList] = useState(getTargetList(currentSelectedId));
	const [timeType, setTimeType] = useState('24hours');
	const [customDate, setCustomDate] = useState<CustomDate>({
		start_time: moment().subtract(8, 'day').format('YYYY-MM-DD 00:00:00'),
		end_time: moment().subtract(1, 'day').format('YYYY-MM-DD 23:59:59'),
		time_type: '3',
	});
	const [hideBanner, setHideBanner] = useState(false);
	const [searchText, setSearchText] = useState('');
	const [messageApi, contextHolder] = message.useMessage();
	const [hasSearchRegion, setHasSearchRegion] = useState(false);
	const [hasDropdown, setHasDropdown] = useState(false);
	const [hasDropdownIndustry, setHasDropdownIndustry] = useState(false);
	const [navChooseId, setNavChooseId] = useState<any>();

	const typeOptions = [
		{
			label: '当日',
			value: 'day',
		},
		{
			label: '昨日',
			value: '24hours',
		},
		{
			label: '7天',
			value: '7day',
		},
		// TODO 取消自定义标签
		// {
		// 	label: '自定义',
		// 	value: 'custom',
		// },
	];

	const items: MenuProps['items'] = Object.values(COUNTY_ID_NAME_MAP).map(
		(item, idx) => {
			// if (item === '北京市') return null
			return {
				label: item,
				key: `${idx + 1}`,
			};
		},
	);

	const handleMenuClick: MenuProps['onClick'] = (e) => {
		setCurrentRegion(COUNTY_ID_NAME_MAP[e.key]);
		setRegionName(COUNTY_ID_NAME_MAP[e.key]);
	};

	const menuProps = {
		items,
		onClick: handleMenuClick,
	};

	const industryItems: MenuProps['items'] = Object.keys(
		industryClassification(),
	).map((item, idx) => {
		return {
			label: item,
			key: `${idx}`,
		};
	});

	const industryHandleMenuClick: MenuProps['onClick'] = (e) => {
		const label = Object.keys(industryClassification())[e.key];
		setCurrentIndustry(label);
		setCurrentIndustryList(industryClassification()[label]);
	};

	const menuPropsIndustry = {
		items: industryItems,
		onClick: industryHandleMenuClick,
	};

	const onSearch = (e) => {
		if (searchText.trim() === '') {
			warning('输入的值为空');
			return;
		}
		if (!Object.keys(selectRegionDate).length) return;
		const data = streetShipData?.features;
		// 清除非中文字符
		const cleanedInput = searchText.replace(/[^\u4e00-\u9fa5]/g, '');

		// 构建正则表达式
		const regex = new RegExp(cleanedInput);

		// 获取所有匹配的 item
		const matchedItems = data.filter((item) =>
			regex.test(item.properties.name),
		);
		//
		if (streetNames === cleanedInput) {
			setIndex(index + 1);

			if (index === matchedItems.length - 1) {
				setIndex(0);
			}
		} else {
			setStreetNames(cleanedInput);
			setIndex(0);
		}
		if (matchedItems.length) {
			setStreetName(matchedItems[index].properties.name);
		} else {
			// setStreetName(null)
			warning('街道不存在');
		}
		// // 获取所有匹配的 item
		// const matchedItems = data.filter((item) =>
		// 	regex.test(item.properties.name),
		// );
		// console.log('matchedItems', matchedItems);
		// if (matchedItems) {
		// 	setStreetName(matchedItems[0].properties.name);
		// } else {
		// 	// setStreetName(null)
		// 	warning('街道不存在');
		// }

		// // 找到第一个匹配的 item
		// const firstMatchedItem = data.find((item) =>
		// 	regex.test(item.properties.name),
		// );
		// if (firstMatchedItem) {
		// 	setStreetName(firstMatchedItem.properties.name);
		// } else {
		// 	// setStreetName(null)
		// 	warning('街道不存在');
		// }
		// const obj = data.find((i) => i.properties.name === searchText);
		// if (obj) {
		// 	setStreetName(obj.properties.name);
		// } else {
		// 	// setStreetName(null)
		// 	warning('街道不存在');
		// }
	};
	const inputChange = _debounce((e) => {
		// clearInterval(bannerTimeout)
		// if (bannerTimeout) {
		//   bannerTimeout = null
		// }
		setSearchText(e.target.value);
	}, 100);

	const inputFocus = () => {
		// clearInterval(bannerTimeout)
		// if (bannerTimeout) {
		//   bannerTimeout = null
		// }
	};

	const warning = (text) => {
		messageApi.open({
			type: 'warning',
			content: text,
			duration: 2,
			className: 'custom-class',
			style: {
				position: 'absolute',
				left: '50%',
				width: '200px',
				height: '50px',
				marginTop: '20vh',
				background: `url(${icon.selectRegionBg})`,
				backgroundRepeat: 'no-repeat',
				backgroundSize: '100% 100%',
			},
		});
	};

	const clickHnadlerNav = (e) => {
		setCurrentId(e.id);
		setCurrentTopNavMenu(e.name);
	};

	// const onMouseLeave = () => {
	//   bannerTimeOut()
	// }

	const inputBlur = () => {
		// bannerTimeOut()
	};

	useEffect(() => {
		const list = navList.map((item) => {
			if (item.id === currentId) {
				item.active = true;
			} else {
				item.active = false;
			}
			return item;
		});
		setNavList(list);
	}, [currentId]);

	useEffect(() => {
		if (!regionName) {
			setCurrentRegion('全市');
			return;
		}
		if (regionName === '全市') {
			setRegionName(null);
			return;
		}
		if (regionName) {
			setCurrentRegion(regionName);
		}
	}, [regionName]);

	useEffect(() => {
		setSelectRegionDate({
			timeType,
			customDate: useSpaceTimeParams(timeType, customDate),
		});
	}, [timeType, customDate]);

	useEffect(() => {
		if (!hideModeMenu) return;
		// setHideBanner(false)
		// bannerTimeOut()
		return () => {
			// if (bannerTimeout) {
			//   bannerTimeout = null
			// }
			setSearchText('');
			setRegionName(null);
		};
	}, [hideModeMenu]);

	useEffect(() => {
		if (currentSelectedId == '') return;
		let _hasSearchRegion = false,
			_hasDropdown = false;
		// _hasDropdownIndustry = false,
		// _currentTopNavMenu = '在线车辆',
		// navId = 'region'
		// console.log('======', _currentTopNavMenu, currentSelectedId, '??? ViolationAnalysis')
		switch (currentSelectedId) {
			case 'area':
			case 'industry':
			case 'userEnterprise':
				_hasSearchRegion = false;
				_hasDropdown = false;
				break;
			case 'streetTownship':
				_hasSearchRegion = true;
				_hasDropdown = true;
				break;
			// case 'road':
			//   _hasSearchRegion = false
			//   _hasDropdown = false
			//   break
			// case 'ViolationAnalysis':
			//   _hasSearchRegion = false
			//   _hasDropdownIndustry = true
			//   _currentTopNavMenu = 'MIL灯点亮'
			//   navId = 'violation'
			//   break
			// default:
			//   break
		}
		// setNavChooseId(navId)
		// setCurrentId(1)
		// setCurrentTopNavMenu(_currentTopNavMenu)
		setHasSearchRegion(_hasSearchRegion);
		setHasDropdown(_hasDropdown);
		// setHasDropdownIndustry(_hasDropdownIndustry)
	}, [currentSelectedId]);

	return (
		<>
			{hideModeMenu && (
				<RegionalBannerStyled>
					<XyzTransitionGroup xyz="fade up-100%">
						{contextHolder}
						{/* {!hideBanner && ( */}
						<div
							className={`top`}
							// onMouseLeave={onMouseLeave}
							// onMouseMove={() => {
							//   if (bannerTimeout) {
							//     // console.log('******清除定时器******')
							//     clearTimeout(bannerTimeout)
							//   }
							//   setHideBanner(false)
							// }}
						>
							{hasSearchRegion && (
								<div
									className="searchRegion"
									// style={{
									//   display: currentSelectedId === 'area' || 'road' ? 'none' : 'inline-block',
									//   zIndex: currentSelectedId === 'area' || 'road' ? '-1' : '0'
									// }}
								>
									<Space.Compact size="large">
										<Input
											onFocus={inputFocus}
											onBlur={inputBlur}
											addonBefore={
												<SearchOutlined
													style={{ cursor: 'pointer' }}
													onClick={onSearch}
												/>
											}
											onChange={inputChange}
											onPressEnter={onSearch}
											placeholder="输入街乡镇"
										/>
									</Space.Compact>
								</div>
							)}
							{/* {navChooseId == 'violation' ? (
                  <LayerChoose
                    className="violation-nav"
                    data={violationTypeOptions}
                    onChange={onChange}
                  />
                ) : ( */}
							<div className="topNav">
								{navList.map((item) => {
									return (
										<div
											className="item"
											key={item.id}
											style={{
												background: item.active
													? `url(${icon.topNavBg}) 0 0 / 100% 100% no-repeat`
													: `url(${icon.topNavFrame}) 0 0 / 100% 100% no-repeat`,
											}}
											onClick={() => clickHnadlerNav(item)}
										>
											{item.name}
											{item.active}
										</div>
									);
								})}
							</div>
							{/* )} */}

							{hasDropdown && (
								<Dropdown menu={menuProps}>
									<Button>
										<Space>
											{currentRegion}
											<DownOutlined />
										</Space>
									</Button>
								</Dropdown>
							)}
							{/* {hasDropdownIndustry && (
                  <Dropdown menu={menuPropsIndustry}>
                    <Button>
                      <Space>
                        {currentIndustry}
                        <DownOutlined />
                      </Space>
                    </Button>
                  </Dropdown>
                )} */}
							<div className="selectTime">
								<TimeTypeRadio
									right={'5%'}
									top={'80%'}
									type={timeType}
									typeOptions={typeOptions}
									timeType={(data) => setTimeType(data)}
									setTimeType={setTimeType}
									setCustomDate={setCustomDate}
									customStartTime={selectRegionDate.customDate}
								/>
							</div>
						</div>
						{/* )} */}
					</XyzTransitionGroup>
					{/* <XyzTransitionGroup xyz="fade up-100%">
            {hideBanner && (
              <div className="bannerMenu" onClick={openBanner}>
                {currentSelectedId !== 'ViolationAnalysis' &&
                  navList.find((i) => i.active === true)?.name}
              </div>
            )}
          </XyzTransitionGroup> */}
				</RegionalBannerStyled>
			)}
		</>
	);
};

export default RegionalBanner;
