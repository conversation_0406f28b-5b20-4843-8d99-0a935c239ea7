// 区域不同行业里程特征
import React, { useState } from 'react';

import MileageCharacteristics from '@renderer/components/MileageCharacteristics';
import { getCountyByTime } from '@renderer/api';

let colorList = [
	'#29BCFD', // 轻
	'#2D7BE5', // 中
	'#85A9FF', // 重
	'#73DEBD',
	'#26C978',
	'#8CDF6C',
	'#FBD657',
	'#F56679',
	'#E07BCE',
	'#9D50E0',
	'#634FDA',
];

// let my_data = {
//   xData: [
//     "海淀",
//     "朝阳",
//     "密云",
//     "昌平",
//     "顺义",
//     "石景山",
//     "丰台",
//     "通州",
//     "房山",
//   ],
//   legend: [
//     "公交车",
//     "其他",
//     "冷藏车",
//     "加油车",
//     "医疗废弃物转运车",
//     "垃圾车",
//     "客车",
//   ],
//   data: [
//     [0, 0, 0, 0, 0, 0, 0, 0, 0],
//     [0, 0, 0, 0, 0, 0, 0, 0, 0],
//     [0, 0, 0, 0, 0, 0, 0, 0, 0],
//     [0, 0, 0, 0, 0, 0, 0, 0, 0],
//     [0, 0, 0, 0, 0, 0, 0, 0, 0],
//     [0, 0, 0, 0, 0, 0, 0, 0, 0],
//     [0, 0, 0, 0, 0, 0, 0, 0, 0],
//   ],
// };

function MileageCharacteristicsIndustriesRegion() {
	const [data, setData] = useState(null);

	const onTimeChange = (time, type) => {
		getCountyByTime({
			time: time.start_time,
			time_type: type,
			target: '1',
			legend: 'industry',
		})
			.then((res: any) => {
				setData(res);
			})
			.catch(() => {
				setData(null);
			});
	};

	return (
		<MileageCharacteristics
			title="区域不同行业里程特征"
			defaultValue="year"
			barWidth=""
			data={data}
			colors={colorList}
			onTimeChange={onTimeChange}
			interval={1}
		/>
	);
}

export default MileageCharacteristicsIndustriesRegion;
