import { useEffect, useState, FC } from 'react';
import { layer, Util, Map } from 'mars3d';

import { getMeteorAllPic } from '@renderer/api';
import { useFetchMeteorPictureData, useMap } from '@renderer/hooks';

const { ImageLayer } = layer;

interface IMeteorImageLayerProps {
	layerId: string;
	visible: boolean;
}

export const MeteorImageLayer: FC<IMeteorImageLayerProps> = ({
	layerId,
	visible,
}) => {
	const map = useMap();
	const beijingExtents = [115.4168938, 39.442185, 117.5082261, 41.0592191];
	const [imageLayer, setImageLayer] = useState<layer.ImageLayer>();
	const [url, setParams] = useFetchMeteorPictureData();

	useEffect(() => {
		if (url === undefined) return;
		const layer = new ImageLayer({
			url,
			bbox: beijingExtents,
			alpha: 0.5,
			zIndex: 9,
		});

		map.addLayer(layer);

		return () => {
			map.removeLayer(layer);
		};
	}, [map, url]);

	return null;
};
