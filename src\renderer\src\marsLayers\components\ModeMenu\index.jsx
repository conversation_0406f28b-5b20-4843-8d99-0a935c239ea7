import { useState, useEffect, useContext } from 'react';
import { Tabs } from 'antd';
import '@animxyz/core';
import { XyzTransitionGroup } from '@animxyz/react';
import { MapContext, PageContext } from '@renderer/context';
import { useAuth } from '@renderer/hooks';
// import { getRealTimeVehicleStatisticsQuantity } from '@renderer/api';
import layers from '@renderer/images/icon/layers';
// import { Spin } from 'antd'
import { carData } from './data';
import { ModeMenuContent } from './style';
import '../../../../style.scss';
let timeout;
function ModeMenu(props) {
	const { parsentFlash, openTrajectory } = props;
	const { selectMode, setSelectMode, switchingPointsId, hideModeMenu } =
		useContext(PageContext);
	const { token } = useAuth();
	const [isVisible, setIsVisible] = useState(false);
	const [targetId, setTargetId] = useState(switchingPointsId);
	const [mouseStatus, setMouseStatus] = useState(false);
	const [items, setItems] = useState([]);
	const [loading, setLoading] = useState(true);

	const [childList, setChildList] = useState(carData);
	const [nameList, setNameList] = useState({
		all: '全部',
		freight: '货运',
		passenger: '客运',
		workingVehicle: '工程',
		other: '其他',
	});
	const targetValues = {
		vehicleOnline: 1,
		violatingVehicles: 2,
	};
	const timeOut = () => {
		timeout = setTimeout(() => {
			setIsVisible(false);
		}, 3000);
	};
	let nums =
		JSON.parse(sessionStorage.getItem('zcJsonData'))?.carNumber || 190000;
	// let interval = null
	let updatedItems = [];

	useEffect(() => {
		setTargetId(targetValues[switchingPointsId]);
		setLoading(true);
		getCarData(carData);
		if (!isVisible) return;
		if (timeout) clearTimeout(timeout);
		// getInterfaceData(targetValues[switchingPointsId])
	}, [switchingPointsId]);

	useEffect(() => {
		setIsVisible(!parsentFlash);
		if (!parsentFlash) {
			// getInterfaceData(targetId)
			setTimeout(() => {
				setLoading(false);
				setIsVisible(false);
			}, 5000);
		}
	}, [parsentFlash]);

	useEffect(() => {
		setSelectMode({
			key: 'all',
			child: childList['all'],
		});
		// getCarData()
	}, []);

	useEffect(() => {
		if (!mouseStatus && !loading) {
			timeOut();
		}
	}, [mouseStatus, loading]);

	// useEffect(() => {
	//   if (isVisible) {
	//     interval = setInterval(() => {
	//       if (loading) return
	//       getInterfaceData(targetId)
	//     }, 10000)
	//   }
	//   return () => {
	//     clearInterval(interval)
	//   }
	// }, [isVisible, targetId, loading])

	const onChange = (key) => {
		// if (!loading) {
		setSelectMode({
			key,
			child: childList[key],
		});
		// }
	};

	const handleIsVisible = () => {
		// setMouseStatus(false)
		// getInterfaceData(targetId)
		setIsVisible(!isVisible);
	};

	const onMouseLeave = () => {
		if (loading) return;
		timeOut();
		setMouseStatus(false);
	};

	const getCarData = (data) => {
		updatedItems = [];
		for (let key in layers) {
			updatedItems.push({
				key,
				label: (
					<div className="layersButton">
						<img
							src={layers[key]}
							width="24px"
							height="24px"
							alt={nameList[key]}
							title={nameList[key]}
						/>
						<div>{nameList[key] || '--'}</div>
					</div>
				),
				children: (
					<div className="children">
						{data[key]?.map((item, index) => (
							<div className="item" key={index}>
								<div style={{ background: item.color }}></div>
								<div>
									{item.label} {item.num}
								</div>
							</div>
						))}
					</div>
				),
			});
		}
		setItems(updatedItems);
	};

	// const getInterfaceData = (target = targetId) => {
	// 	getRealTimeVehicleStatisticsQuantity({ token, nums, target })
	// 		.then((res) => {
	// 			const updatedChildList = {
	// 				all: [],
	// 				freight: [],
	// 				passenger: [],
	// 				workingVehicle: [],
	// 				other: [],
	// 			};
	// 			res.forEach((item) => {
	// 				item.data.forEach((item2) => {
	// 					let obj = { label: item2.type, num: item2.value };
	// 					switch (item.label) {
	// 						case '货运':
	// 							updatedChildList.freight.push(obj);
	// 							break;
	// 						case '客运':
	// 							updatedChildList.passenger.push(obj);
	// 							break;
	// 						case '工程':
	// 							updatedChildList.workingVehicle.push(obj);
	// 							break;
	// 						case '其它':
	// 							updatedChildList.other.push(obj);
	// 							break;
	// 					}
	// 					updatedChildList.all.push(obj);
	// 				});
	// 			});
	// 			setChildList(updatedChildList);
	// 			const colors = [
	// 				'#2ec7c9',
	// 				'#b6a2de',
	// 				'#5ab1ef',
	// 				'#ffb980',
	// 				'#d87a80',
	// 				'#c14089',
	// 				'#e5cf0d',
	// 				'#97b552',
	// 				'#95706d',
	// 				'#dc69aa',
	// 				'#07a2a4',
	// 				'#9a7fd1',
	// 				'#588dd5',
	// 				'#f5994e',
	// 				'#c05050',
	// 				'#59678c',
	// 				'#c9ab00',
	// 				'#7eb00a',
	// 				'#91cc75',
	// 			];
	// 			updatedChildList.all.forEach((item, index) => {
	// 				item.color = colors[index];
	// 			});
	// 			updatedItems = [];
	// 			for (let key in layers) {
	// 				updatedItems.push({
	// 					key,
	// 					label: (
	// 						<div className="layersButton">
	// 							<img
	// 								src={layers[key]}
	// 								width="24px"
	// 								height="24px"
	// 								alt={nameList[key]}
	// 								title={nameList[key]}
	// 							/>
	// 							<div>{nameList[key] || '--'}</div>
	// 						</div>
	// 					),
	// 					children: (
	// 						<div className="children">
	// 							{updatedChildList[key]?.map((item, index) => (
	// 								<div className="item" key={index}>
	// 									<div style={{ background: item.color }}></div>
	// 									<div>
	// 										{item.label}（{item.num}）
	// 									</div>
	// 								</div>
	// 							))}
	// 						</div>
	// 					),
	// 				});
	// 			}
	// 			setItems(updatedItems);
	// 			setLoading(false);
	// 			// if (change) setMouseStatus(true)
	// 			// if (timeout) clearTimeout(timeout)
	// 			// timeout = setTimeout(() => {
	// 			//   setIsVisible(false)
	// 			// }, 3000)
	// 		})
	// 		.catch(() => setIsVisible(false));
	// };

	return (
		<ModeMenuContent hidden={hideModeMenu || openTrajectory ? true : false}>
			<XyzTransitionGroup xyz="fade up-100%">
				{!parsentFlash && !isVisible && (
					<div className="layermenuButton" onClick={handleIsVisible}></div>
				)}
			</XyzTransitionGroup>
			<XyzTransitionGroup xyz="fade up-100%">
				{isVisible && (
					<div
						className="layerMenuContent"
						onMouseLeave={() => onMouseLeave()}
						onMouseMove={() => {
							if (timeout) {
								clearTimeout(timeout);
							}
							setIsVisible(true);
							// setMouseStatus(false)
						}}
						// onMouseEnter={() => {
						//   if (timeout) {
						//     clearTimeout(timeout)
						//   }
						//   timeout = setTimeout(() => {
						//     setIsVisible(false)
						//   }, 3000)
						// }}
					>
						<Tabs
							activeKey={selectMode.key}
							type="card"
							items={items}
							onChange={onChange}
						/>
						{/* <ul
              className="five-point
"
            >
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li></li>
            </ul> */}
					</div>
				)}
			</XyzTransitionGroup>
		</ModeMenuContent>
	);
}
export default ModeMenu;
