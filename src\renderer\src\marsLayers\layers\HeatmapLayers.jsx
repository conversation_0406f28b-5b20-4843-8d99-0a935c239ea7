import React, { useEffect, useContext, useState } from 'react';
import * as mars3d from 'mars3d';
import 'mars3d-heatmap';
import { Radio, DatePicker, Input } from 'antd';
import moment from 'moment';
import dayjs from 'dayjs';
import { wgs84togcj02 } from '@renderer/utils';

import styled from 'styled-components';
import { MapContext } from '@renderer/context';
import { getVehicleFlowEmissionGrid } from '@renderer/api';
import NumericInput from '../components/NumericInput';
import { readDbData } from '@renderer/utils/getData';
import { ManageLayerData } from '@renderer/utils/getData';
import icon from '@renderer/images/icon';
import { PageContext } from '../../context';

const HotspotGridTimeStyled = styled.div`
	position: absolute;
	right: 6%;
	top: 8%;
	width: 500px;
	height: auto;
	background: url(${icon.heatGrid});
	background-repeat: no-repeat;
	background-size: 100% 100%;
	z-index: 2;
	padding: 10px 0;
	.selectAssembly {
		width: 100%;
		padding: 10px;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
			white-space: nowrap;
		}
		.ant-radio-button-wrapper {
			width: 80px;
			border: 0;
			border-radius: 10px;
			font-size: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			white-space: nowrap;
			span:hover {
				color: #3adaff;
			}
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			> .ant-input:last-child,
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			.ant-input-group-addon:last-child {
			width: 100px;
			background-color: transparent;
			border: 0;
			font-size: 16px;
			padding-left: 2px;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			> .ant-input:last-child:focus,
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			.ant-input-group-addon:last-child:focus {
			border: 1px solid #007bff;
			outline: none;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			> .ant-input:first-child,
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
			.ant-input-group-addon:first-child {
			background-color: transparent;
			border: 0;
			font-size: 16px;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group-wrapper {
			background: rgb(58, 218, 255, 0.1) !important;
			border-radius: 10px;
		}
		input {
			padding: 4px 0;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-input-group-wrapper {
			width: auto;
		}
	}
	.customDate {
		height: 60px;
		display: flex;
		margin-top: -6px;
		padding: 20px;
		align-items: center;
		justify-content: center;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
			padding-right: 10px;
		}
		.ant-picker {
			background: rgba(0, 65, 94) !important;
			border-radius: 4px;
			border: 0;
			font-size: 18px !important;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			height: 34px;
			color: #ffffff !important;
			display: flex;
			align-items: center;
		}
		.ant-picker-input {
			font-size: 20px;
			color: #e2f0ff !important;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-picker .ant-picker-suffix {
			margin-right: 10px;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-picker .ant-picker-clear {
			display: none !important;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-picker
			.ant-picker-input
			> input {
			text-align: center !important;
			line-height: 0 !important;
			font-size: 18px !important;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-picker
			.ant-picker-input-placeholder
			> input {
			color: #fff !important;
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-picker {
			padding: 0 !important;
		}
	}
`;

let heatmap = null;
let heatmapGrid = null;
let gridListener = null;

const heatConfig = {
	radius: 12,
	maxOpacity: 0.9,
	minOpacity: 0.1,
	blur: 0.9,
	gradient: {
		'.3': '#7CFC00',
		'.5': '#FFFF00',
		'.7': '#FF8000',
		'.95': '#FF0000',
	},
};

export default (props) => {
	const { visible, map, layerId } = props;
	const { timeBarEndTime } = useContext(MapContext);
	const { regionName, LayerDataDB } = useContext(PageContext);
	const [selectedButton, setSelectedButton] = useState('default');
	const [showPop, setShowPop] = useState(false);
	const [currentGridId, setCurrentGridId] = useState(false);
	const [customDateGrid, setCustomDateGrid] = useState({});
	const [heatGridData, setHeatGridData] = useState([]);
	const [inputMax, setInputMax] = useState(null);
	const [valueMax, setValueMax] = useState('--');
	const [cameraHeight, setCameraHeight] = useState(0);

	let bbox = {
		xmin: 114.18791503888905,
		xmax: 118.45912727123806,
		ymin: 39.41272295607264,
		ymax: 41.127507988758694,
	};
	const autoRadiusConfig = {
		trafficVolumeThermodynamics: {
			enabled: true,
			min: 5000,
			max: 150000,
			maxRadius: 20,
			minRadius: 10,
		},
		vehicleEmissionsThermodynamics: {
			enabled: true,
			min: 5000,
			max: 100000,
			maxRadius: 5,
			minRadius: 2,
		},
	};
	let jsonData = sessionStorage.getItem('zcJsonData');
	if (jsonData) {
		jsonData = JSON.parse(jsonData);
		autoRadiusConfig.trafficVolumeThermodynamics = jsonData?.autoRadiusConfig
			?.trafficVolumeThermodynamics
			? jsonData?.autoRadiusConfig?.trafficVolumeThermodynamics
			: autoRadiusConfig.trafficVolumeThermodynamics;
		autoRadiusConfig.vehicleEmissionsThermodynamics = jsonData?.autoRadiusConfig
			?.vehicleEmissionsThermodynamics
			? jsonData?.autoRadiusConfig?.vehicleEmissionsThermodynamics
			: autoRadiusConfig.vehicleEmissionsThermodynamics;
	}

	const setHeatmap = (list) => {
		let resultData = list.map((item) => {
			const lnlaArray = wgs84togcj02(item.x, item.y);
			return {
				lat: lnlaArray[1],
				lng: lnlaArray[0],
				value: item.value,
			};
		});

		if (heatmap) {
			heatmap.setPositions(resultData, true);
			return;
		}
		heatmap = new mars3d.layer.HeatLayer({
			positions: resultData,
			rectangle: bbox,
			heatStyle: heatConfig,
			// 以下为矩形矢量对象的样式参数
			style: {
				// arc: true, // 是否为曲面
				height: 1000,
			},
			redrawZoom: true,
			redrawRatio: 1,
		});
		// 热力图 图层
		map.addLayer(heatmap);
	};

	const setHeatmapGrid = (list, max) => {
		let resultData = list.map((item) => {
			return {
				lng: item[0],
				lat: item[1],
				value: item[2],
			};
		});
		if (heatmapGrid) {
			map.removeLayer(heatmapGrid);
			heatmapGrid = null;
		}
		const attribute = {
			id: 'heatGridLayer',
			positions: resultData,
			rectangle: bbox,
			min: 0,
			heatStyle: heatConfig,
			// 以下为矩形矢量对象的样式参数
			style: {
				// arc: true, // 是否为曲面
				height: 1000,
			},
			redrawZoom: true,
		};
		if (max) {
			attribute.max = max;
		}
		heatmapGrid = new mars3d.layer.HeatLayer(attribute);
		// 热力图 图层
		map.addLayer(heatmapGrid);
	};

	const getLayersData = () => {
		let startTime = moment(timeBarEndTime)
			.subtract(1, 'h')
			.format('YYYY-MM-DD HH:00:00');
		let endTime = moment(timeBarEndTime).format('YYYY-MM-DD HH:00:00');
		const dateObj = {
			startTime,
			endTime,
			// dateIdx: moment(timeBarEndTime).format('YYYYMMDDHH')
		};
		const layerData = new ManageLayerData({
			layerId,
			jsonData,
			DB: LayerDataDB,
		});
		layerData.getData(dateObj, setLayer);
	};

	const setLayer = (data) => {
		if (data?.length) setHeatmap(data);
	};

	const readData = async () => {
		try {
			let data = await readDbData({
				layerId,
				endTime: timeBarEndTime,
				localforage: LayerDataDB,
			});
			if (data?.length) {
				setHeatmap(data);
			} else {
				getLayersData();
			}
		} catch (error) {
			// console.log('readData catch------0000', error);
			// getLayersData()
		}
	};

	// 网格时间自定义切换
	const handleClickReference = (value) => {
		setSelectedButton(value);
		if (value === 'custom') {
			setShowPop(true);
		} else {
			setShowPop(false);
		}
	};

	const onChange = (date, dateString) => {
		setCustomDateGrid(dateString);
	};

	function calculateMax(cameraHeight, maxValue) {
		const maxScaleFactor = 1.8; // 最大值比例系数，可以根据需要调整
		const k = layerId === 'trafficVolumeHotspotGrid' ? 10 : 0.0001; // 参数用于调节相机高度与 max 值之间的关系
		const b = layerId === 'trafficVolumeHotspotGrid' ? 100 : 0; // 参数用于调节相机高度与 max 值之间的关系

		// 计算基本的 max 值
		let max = k * cameraHeight + b;

		// 确保 max 不会超过 maxValue 的 maxScaleFactor
		max = Math.min(max, maxValue * maxScaleFactor);

		// 最小值限制为0
		max = Math.max(max, 0);

		return max;
	}

	const getCurrentCameraHeight = () => {
		// 获取当前相机的高度
		const height = map.camera.positionCartographic.height;
		const trafficVolumeHeight = height < 5000 || height > 200000;
		const vehicleEmissionsHeight = height < 5000 || height > 100000;
		if (layerId === 'trafficVolumeHotspotGrid' && trafficVolumeHeight) {
			return;
		} else if (
			layerId === 'vehicleEmissionsHotspotGrid' &&
			vehicleEmissionsHeight
		) {
			return;
		} else {
			const max = calculateMax(height, valueMax);
			setHeatmapGrid(heatGridData, max);
			setCameraHeight(height);
			setValueMax(Number(max).toFixed(0));
		}
	};

	useEffect(() => {
		if (valueMax === '--' || cameraHeight) return;
		if (gridListener) {
			gridListener();
			gridListener = null;
		}
		gridListener = map.camera.changed.addEventListener(getCurrentCameraHeight);
	}, [valueMax]);

	useEffect(() => {
		if (
			!visible ||
			layerId === 'trafficVolumeThermodynamics' ||
			layerId === 'vehicleEmissionsThermodynamics'
		)
			return;
		if (selectedButton === 'custom' && !Object.keys(customDateGrid).length)
			return;
		const currentTime = moment()
			.subtract(1, 'day')
			.format('YYYY-MM-DD 00:00:00');
		const factor = layerId === 'vehicleEmissionsHotspotGrid' ? 1 : 2;
		let params;
		if (selectedButton === 'default') {
			params = {
				start_time: currentTime,
				end_time: currentTime,
				factor,
				target: 1,
			};
		} else {
			params = {
				start_time: customDateGrid,
				end_time: customDateGrid,
				factor,
				target: 1,
			};
		}
		params.area_name = regionName ? regionName : '';
		getVehicleFlowEmissionGrid(params)
			.then((res) => {
				if (res?.length) {
					let maxValue = -Infinity;
					res?.forEach((subArray) => {
						if (subArray.length >= 3) {
							maxValue = Math.max(maxValue, subArray[2]);
						}
					});
					setValueMax(maxValue);
					setHeatGridData(res);
					setHeatmapGrid(res, maxValue);
				}
			})
			.catch((err) => {});
	}, [visible, customDateGrid, selectedButton]);

	useEffect(() => {
		if (!heatmapGrid || !heatGridData) return;
		setHeatmapGrid(heatGridData, inputMax);
	}, [inputMax]);

	useEffect(() => {
		if (
			layerId === 'vehicleEmissionsHotspotGrid' ||
			layerId === 'trafficVolumeHotspotGrid'
		)
			return;
		if (!visible || !timeBarEndTime) return;
		readData();
		// if (heatmap) {
		//   heatmap.setPositions(getRandomPoints(), true)
		//   return
		// }
		// heatmap = new mars3d.layer.HeatLayer({
		//   positions: getRandomPoints(),
		//   rectangle: bbox,
		//   heatStyle: {
		//     radius: 40,
		//     blur: 0.85
		//   },
		//   // 以下为矩形矢量对象的样式参数
		//   style: {
		//     // arc: true, // 是否为曲面
		//     height: 200.0
		//   }
		// })
		// //热力图 图层
		// map.addLayer(heatmap)
	}, [visible, timeBarEndTime]);

	useEffect(() => {
		const isHotGridId =
			layerId === 'vehicleEmissionsHotspotGrid' ||
			layerId === 'trafficVolumeHotspotGrid';
		setCurrentGridId(isHotGridId);
		return () => {
			if (heatmap) {
				map.removeLayer(heatmap);
				heatmap = null;
			}
			if (heatmapGrid) {
				map.removeLayer(heatmapGrid);
				heatmapGrid = null;
			}
			if (gridListener) {
				gridListener();
				gridListener = null;
			}
		};
	}, []);

	return (
		currentGridId && (
			<HotspotGridTimeStyled>
				<div className="selectAssembly">
					<p>时间选择：</p>
					<Radio.Button
						value="default"
						onClick={() => handleClickReference('default')}
						style={{
							color: selectedButton === 'default' ? '#3ADAFF' : '#e2e5e5',
						}}
					>
						昨日
					</Radio.Button>
					<Radio.Button
						value="custom"
						onClick={() => handleClickReference('custom')}
						style={{
							color: selectedButton === 'custom' ? '#3ADAFF' : '#e2e5e5',
						}}
					>
						自定义
					</Radio.Button>
					<NumericInput
						onChange={setInputMax}
						height={cameraHeight}
						value={valueMax}
					/>
				</div>
				{showPop && (
					<div className="customDate">
						<p>自定义时间: </p>
						<DatePicker
							onChange={onChange}
							format="YYYY-MM-DD 00:00:00"
							showTime={false}
							defaultValue={dayjs(`${dayjs()}`)}
						/>
					</div>
				)}
			</HotspotGridTimeStyled>
		)
	);
};
