import React, { useState, useEffect, useRef, useMemo } from 'react';
import ReactDOM from 'react-dom';

import PopContainer from '@/marsLayers/popups/common/PopContainer';
import PollutantListCon from '@/marsLayers/popups/common/PollutantList';
import BarEchart from '@/marsLayers/popups/common/BarEchart';
import { sensorStationWindow } from '@/api';
const SensorPopup = (info: any) => {
	let { openCount, LayerId } = info;
	const [selectedPollutantId, setSelectedPollutantId] = useState();
	const [echartData, setEchartData] = useState<any>({
		time: [],
		data: [],
	});
	const echarsRef = useRef();

	const [infoData, setInfoData] = useState(null);
	const [type, setType] = useState(null);
	const multiline = [
		{ label: '点位类型', value: type || '--' },
		{ label: '镇街', value: info?.town_name || '--' },
	];
	const aloneRow = [
		{ label: '地址', value: info?.name || '--' },
		{ label: '更新时间', value: info?.time || '--' },
	];

	useEffect(() => {
		sensorStationWindow({
			station_id: info?.devsite_code,
			pollutant_id: 134004,
		})
			.then((res) => {
				setEchartData(res);
				setInfoData(res.data6);
				setSelectedPollutantId(res.data6[0]?.pollutant_id);
				setType(res.type);
			})
			.catch();
	}, []);
	useEffect(() => {
		setEchartData({
			time: [],
			data: [],
		});
		let req = {
			station_id: info?.devsite_code,
			pollutant_id: selectedPollutantId,
		};
		sensorStationWindow(req)
			.then((res) => {
				setEchartData(res);
			})
			.catch();
	}, [openCount, selectedPollutantId]);

	const sensorP = (
		<PopContainer text={info?.name} multiline={multiline} aloneRow={aloneRow}>
			<PollutantListCon
				pollutantId={selectedPollutantId}
				data={infoData || []}
				changePollutantId={setSelectedPollutantId}
			/>
			{echartData ? (
				<BarEchart
					inputRef={echarsRef}
					xAxisData={echartData.time}
					seriesData={echartData.data}
					yAxisName={String(selectedPollutantId)}
				></BarEchart>
			) : (
				<div style={{ height: '250px' }}></div>
			)}
		</PopContainer>
	);

	return ReactDOM.createPortal(
		sensorP,
		document.querySelector(`#${LayerId}Popup`),
	);
};

export default SensorPopup;
