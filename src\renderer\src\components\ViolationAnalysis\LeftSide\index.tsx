import { useEffect, useState, useContext } from 'react';
import Echarts from '@renderer/components/echarts';
import { Space, Button, Dropdown } from 'antd';
// import type { MenuProps } from 'antd'
import { DownOutlined } from '@ant-design/icons';
import {
	getRegionalIndustryProportion,
	getRegionalAnalysis,
	getIndustryOnlineSituation,
	getStreetTownshipIndustriesProportion,
	getSTIStats,
	getViolationsIndustryViolations,
	getViolationsRegionViolationsRanking,
	getViolationsTownAnalyse,
	getViolationsHistory,
} from '@renderer/api';
import {
	getIper,
	getIndustry,
	getOnlineSituationEcharts,
	getHistoryViolationsEcharts,
} from '@renderer/components/SideExpansionField/components/IndustryProportion';
import industryClassification from '@renderer/components/SideExpansionField/components/IndustryClassification';
import { PageContext } from '@renderer/context';
import {
	COUNTY_ID_NAME_MAP,
	unitList,
	VaUnitList,
} from '@renderer/marsLayers/components/AreaStreetStyles';
import {
	formatNumber,
	calculatePercentage,
	tranNumber,
} from '@renderer/hooks/index';
import { LeftSideStyled, LayoutContainer } from './styles';
import moment from 'moment';
import { INDUSTRY_COLOR } from '@renderer/constants/color';
import VennModule from '../components/VennModule';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const LeftSide = (props: Props) => {
	const { currentSelectedId, streetShipData } = props;
	const {
		currentTopNavMenu,
		regionName, // 选中的区县名称
		streetName, // 选中的街乡镇名称
		currentIndustry,
		selectRegionDate, // 当前选择的时间参数
		currentTopNavMenuList, // 当前选择菜单项名称列表
		currentIndustryList, // 当前行业列表
		currentIdList, // 当前选择菜单id列表
	} = useContext(PageContext);
	if (selectRegionDate.customDate.start_time == '') return;

	const [dataSum, setDataSum] = useState(0);
	const [optionsIP, setOptionsIP] = useState({}); //行业占比echarts
	const [optionsRA, setOptionsRA] = useState({}); //区域分析echarts
	const [optionsOS, setOptionsOS] = useState({}); //上线情况echarts
	const [title1, setTitle1] = useState(null);

	function separateNumberAndChinese(str) {
		if (str === '0.00' || str === 0) {
			return { number: parseFloat(str), chinese: '' };
		}
		const matches = str?.match(/([\d.]+)(\D+)/);
		if (matches) {
			const number = parseFloat(matches[1]);
			const chinese = matches[2];
			return { number, chinese };
		} else {
			return { number: parseFloat(str), chinese: '' };
		}
	}

	const setUpOption2 = (data) => {
		const unit = '辆';
		let sumCountsByAdm: any = [];
		let name;
		sumCountsByAdm = data?.reduce((acc, curr) => {
			curr.data.forEach((count, index) => {
				acc[index] = (acc[index] || 0) + count;
			});
			return acc;
		}, []);
		if (regionName) {
			name = data[0]?.county?.map((name) => name);
		} else {
			name = data[0]?.county?.map((id) => COUNTY_ID_NAME_MAP[id]);
		}

		const admCounts = sumCountsByAdm?.map((total, index) => {
			return {
				name: name[index],
				value: total >= 1 ? Number(total.toFixed(1)) : Number(total.toFixed(2)),
			};
		});
		const sortedAdmCounts = admCounts.sort((a, b) => a.value - b.value);
		const tradeAdm = sortedAdmCounts.map((item) => item.name);
		const tradeCount = sortedAdmCounts.map((item) => item.value);

		const option = getIndustry(
			{
				...data,
				name: tradeAdm,
				originalName: name,
				total: tradeCount,
				unit,
			},
			currentSelectedId,
		);
		setOptionsRA(option);
	};

	const getIndustryDataResult = (res) => {
		if (res?.data?.length > 0) {
			const carData = res?.data?.map((item) => {
				return {
					name: Object.keys(item)[0],
					value: formatNumber(Object.values(item)[0]),
				};
			});
			setOptionsIP(getIper(calculatePercentage(carData)));
			setDataSum(res?.sum);
		} else {
			setDataSum(0);
			setOptionsIP(getIper([]));
		}
	};

	// 区域-街乡镇行业占比
	const getIndustryData = (id, params) => {
		getViolationsIndustryViolations(params)
			.then((res) => {
				getIndustryDataResult(res);
			})
			.catch((err) => {
				setDataSum(0);
				setOptionsIP(getIper([]));
			});
	};

	// 区域-街乡镇 行业数据统计
	const getIndustryStatistics = (id, params) => {
		if (params?.county_id) {
			getViolationsTownAnalyse(params)
				.then((res) => {
					const arr = res.map((item) => {
						return {
							...item,
							county: item.town_name,
						};
					});
					setUpOption2(arr);
				})
				.catch((err) => {});
		} else {
			getViolationsRegionViolationsRanking(params)
				.then((res) => {
					setUpOption2(res);
				})
				.catch((err) => {});
		}
	};

	const getOnlineSituationResult = (res) => {
		if (res) {
			const xData = res.times.map((i) => moment(i).format('MM-DD'));
			const series = [
				{
					name: '总和',
					data: res.sum,
					type: 'line',
					symbol: 'circle',
					symbolSize: 12,
					smooth: true, // 设置为平滑曲线
					lineStyle: {
						color: '#C664FF', // 线条颜色
						width: 3, // 线条宽度
						// opacity: 0.8, // 透明度
					},
					itemStyle: {
						borderColor: '#C664FF',
						color: '#DCCFFF ',
						borderWidth: 3,
						// opacity: 0.8,
					},
				},
			];
			res.data.map((i) => {
				series.push({
					name: i.name,
					data: i.values,
					type: 'bar',
					stack: 'total',
					color: INDUSTRY_COLOR[i.name],
				});
			});
			setOptionsOS(
				getHistoryViolationsEcharts({
					xData,
					series,
				}),
			);
		} else {
			setOptionsOS(getHistoryViolationsEcharts({}));
		}
	};

	// 区域-街乡镇 上线情况
	const getOnlineSituation = (id, params) => {
		getViolationsHistory(params)
			.then((res) => {
				getOnlineSituationResult(res);
			})
			.catch((err) => {
				setOptionsOS(getOnlineSituationEcharts({}));
			});
	};

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		const params: any = {
			start_time,
			end_time,
			time_type,
			topic: currentIdList.toString(),
		};
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		getIndustryStatistics(currentSelectedId, params);
	}, [
		selectRegionDate,
		currentTopNavMenu,
		currentSelectedId,
		regionName,
		currentIdList,
	]);

	useEffect(() => {
		setTitle1(streetName ? streetName : regionName);
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		const params: any = {
			start_time,
			end_time,
			// time_type,
			topic: currentIdList.toString(),
		};
		if (streetName) {
			params.town_id = streetShipData?.features.find(
				(i) => i.properties.name === streetName,
			).properties.region;
		}
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		// if ('county_id' in params && 'town_id' in params) return
		getIndustryData(currentSelectedId, params);
	}, [
		selectRegionDate,
		currentTopNavMenu,
		regionName,
		streetName,
		currentSelectedId,
		currentIdList,
	]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		const params: any = {
			start_time,
			end_time,
			time_type,
			topic: currentIdList.toString(),
		};
		if (title1) {
			params['county_name'] = title1;
		}
		if (currentIndustry && currentIndustry !== '全部') {
			params.industry_name = currentIndustryList.join(',');
		}
		getOnlineSituation(currentSelectedId, params);
	}, [
		currentIndustry,
		selectRegionDate,
		currentSelectedId,
		currentIdList,
		title1,
	]);

	return (
		<LeftSideStyled>
			<div className="Areacontent">
				<div className="slidingLayer">
					<span className="slidtext">
						{title1 ? title1 : '全市'}行业违规情况
					</span>
				</div>
				<LayoutContainer style={{ height: '20%' }}>
					<div className={'container'}>
						<div className="describe">
							<dl className="no2">
								<dt>总量</dt>
								<br />
								<dd>
									{separateNumberAndChinese(tranNumber(dataSum, 2)).number}
									<span style={{ fontSize: '36px', color: '#f5803e' }}>
										{separateNumberAndChinese(tranNumber(dataSum, 2)).chinese}
										&nbsp;
									</span>
									<span>
										{
											VaUnitList?.find((i) => i.name === currentTopNavMenu)
												?.unit
										}
									</span>
								</dd>
							</dl>
						</div>
						<div className="echarts-container">
							<div className="echarts">
								<Echarts option={optionsIP}></Echarts>
							</div>
						</div>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">区域违规排行</span>
				</div>
				<LayoutContainer style={{ height: '35%' }}>
					<div className="echarts-line">
						<Echarts
							option={optionsRA}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">历史违规情况</span>
				</div>
				<LayoutContainer style={{ height: '25%' }}>
					<div className="echarts-line">
						<Echarts
							option={optionsOS}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
			</div>
		</LeftSideStyled>
	);
};

export default LeftSide;
