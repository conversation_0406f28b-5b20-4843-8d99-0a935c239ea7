import * as mars3d from 'mars3d';
type MapProps = {
	addLayer: (layer: object) => void;
	removeLayer: (layer: object, boolean) => void;
};

type LayerProp = {
	addGraphic?: (o: object) => void;
	bindPopup: (func: (o: any) => string, o?: object) => void;
	on: (o: any, func: (event: any) => any) => void;
};

export default class BaseMarsLayers {
	name: string;
	layerType: string;
	map: MapProps;
	layer: LayerProp;
	layerConfigs?: object;

	constructor({ name, map, layerConfigs }, layerType) {
		this.name = name;
		this.layerType = layerType;
		this.map = map;
		this.layerConfigs = layerConfigs || {};
	}

	create() {
		this.layer = new mars3d.layer[this.layerType]({
			name: this.name,
			...this.layerConfigs,
		});
		this.map.addLayer(this.layer);
	}

	remove() {
		this.map.removeLayer(this.layer, true);
	}
}
