import styled from 'styled-components';
import background from '@renderer/images/background';
const LayoutContainer = styled.div`
	height: 100%;
	display: flex;
	flex-direction: row;
	position: relative;
	font-size: 18px;

	.left-container {
		width: 100%;
		display: flex;
		flex-direction: row;
		background-image: url(${background.background});
		background-repeat: no-repeat;
		background-size: 100%, 100%;
		background-position: center;
		align-content: center;
		justify-content: center;
		align-items: center;
		color: #ffffff;
		text-align: center;

		h3 {
			font-weight: 500;
			font-size: 16px;
			color: #d9fbff;
		}

		p {
			margin: 0;
			padding: 2px;

			span {
				font-size: 18px;
			}
		}

		.total {
			flex: 1;

			div {
				font-weight: 600;
				font-size: 26px;
			}

			span {
				font-size: 18px;
			}
		}
		.total-type {
			flex: 1;

			.number {
				font-size: 26px;
				font-weight: 600;
			}

			.percent {
				font-size: 18px;
			}
		}
	}
`;
export default LayoutContainer;
