/**
 * main
 * ① 自启动服务 redis
 * ② 与renderer创建双向通信
 * ③ 连接redis服务获取数据并通信至renderer
 *
 * ① 自启动服务 websocket
 * ② 获取数据并通信至renderer
 */

import {
	app,
	shell,
	BrowserWindow,
	ipcMain,
	IpcMainEvent,
	protocol,
} from 'electron';
import { join } from 'path';
import { electronApp, optimizer, is } from '@electron-toolkit/utils';
import icon from '../../resources/icon.png?asset';

import { DataHandler } from './data-handler';
import { createWsClient } from './websocketMenu';
import fetchDataSync from './fetchData';
const fs = require('fs');

function createModulesWsClient(ws, mainWindow) {
	setTimeout(() => {
		ws.close();
		ws = createWsClient(mainWindow);
		// let templateFilePath = join(process.cwd(), '/mockData')
		// if (process.env.NODE_ENV === 'development') {
		//   templateFilePath = join(process.cwd(), '/mockData')
		// }
		// mainWindow.webContents.send('url', templateFilePath)
	}, 2000);
}

async function createWindow({ isDev }): void {
	let jsonData = null,
		firstly = true,
		roadGeometry = null;
	try {
		// const res = await fetchDataSync('http://f.i2value.cn/web-json/zhongchai20230904.json')
		const res = await fetchDataSync(
			'http://f.hotgrid.cn/web-json/zhongchai20230904.json',
		);
		jsonData = res;
	} catch (error) {
		console.log(error);
	}

	// // Create the browser window.
	const mainWindow = new BrowserWindow({
		width: 3840,
		height: 1080,
		show: false, //  窗口是否在创建时显示。 默认值为 true
		title: '重柴大屏',
		autoHideMenuBar: false,
		frame: isDev ? true : false,
		fullscreen: isDev ? false : true,
		transparent: false,
		...(process.platform === 'linux' ? { icon } : {}),
		webPreferences: {
			preload: join(__dirname, '../preload/index.js'),
			sandbox: false,
			webSecurity: false,
		},
		icon: join(__dirname, '../../resources/icon.png'),
	});

	let ws;
	let dataHandler: any;

	mainWindow.on('ready-to-show', () => {
		mainWindow.show();
		ws = createWsClient(mainWindow);
	});

	mainWindow.webContents.setWindowOpenHandler((details) => {
		shell.openExternal(details.url);
		return { action: 'deny' };
	});

	mainWindow.webContents.on('did-finish-load', async (event: IpcMainEvent) => {
		if (dataHandler) {
			createModulesWsClient(ws, mainWindow);
		}
		if (firstly) {
			firstly = false;
			ipcMain.on('action:login', (_, data) => {
				createModulesWsClient(ws, mainWindow);
				dataHandler = new DataHandler({ jsonData, token: data });
				dataHandler.eventEmitter.on('data:handler', (data) => {
					event.sender.send('message', JSON.stringify(data));
				});
				dataHandler.eventEmitter.on('data:VINs', (data) => {
					event.sender.send('VINs', JSON.stringify(data));
				});
			});
			// redis相关数据通讯通道

			// 双向extent数据通讯
			ipcMain.on('extent', (_, data) => {
				dataHandler.eventEmitter.emit('change:extent', data);
			});
			// 监测图层数据通讯
			ipcMain.on('layer', (_, data) => {
				dataHandler.eventEmitter.emit('change:layer', data);
			});
			ipcMain.on('change:affiliation', (_, data) => {
				dataHandler.eventEmitter.emit('change:affiliation', data);
			});
			// fs.readFile(join(__dirname, '../../public/data/road_geometry.json'), 'utf-8', (err, data) => {
			//   if (err) {
			//     console.error('读取文件时发生错误:', err);
			//     return;
			//   }
			//   // event.sender.send('roadGeometry', data)
			//   roadGeometry = JSON.parse(data)
			// });

			// ipcMain.on('roadGeometry_send', (_, data) => {
			//   if (data) event.sender.send('roadGeometryData', roadGeometry)
			// })
		} else {
			setTimeout(() => {
				dataHandler.resetInstance();
			}, 3000);
		}
		event.sender.send('data:config-json', jsonData);

		// 监测页面是否刷新
		ipcMain.on('reload', (_, data) => {
			// dataHandler.eventEmitter.removeAllListeners()
			// dataHandler = null
			// dataHandler = new DataHandler({ jsonData })
			// dataHandler.eventEmitter.emit('change:reload', 'reload')
			//   ws.close();
			// ws.send('Connection')
		});
		// Sending data to websocket server
		// 若强制刷新后,重新从ws获取数据
		if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
			mainWindow.webContents.openDevTools();
		}
	});

	// 禁止用户在生产环境中刷新页面。
	// mainWindow.webContents.on('before-input-event', (event, input) => {
	//   if ((input.control || input.meta) && input.key.toLowerCase() === 'r') {
	//     event.preventDefault() // 阻止 Ctrl+R 或 Command+R 的默认行为
	//   }
	// })

	// 隐藏菜单栏
	// mainWindow.setMenu(null)

	// // 禁用开发者工具
	// mainWindow.webContents.on('devtools-opened', () => {
	//   mainWindow.webContents.closeDevTools()
	// })
	mainWindow.webContents.on('before-input-event', (event, input) => {
		if (input.control && input.key.toLowerCase() === 'k') {
			event.sender.send('Control_Shift_K', true);
			event.preventDefault();
		}
	});

	// HMR for renderer base on electron-vite cli.
	// Load the remote URL for development or the local html file for production.
	if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
		// mainWindow.webContents.openDevTools()
		mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL']);
	} else {
		mainWindow.loadFile(join(__dirname, '../renderer/index.html'));
	}
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
	// Set app user model id for windows
	electronApp.setAppUserModelId('com.electron');

	// Default open or close DevTools by F12 in development
	// and ignore CommandOrControl + R in production.
	// see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
	app.on('browser-window-created', (_, window) => {
		optimizer.watchWindowShortcuts(window);
	});

	// Open windows.
	createWindow({ isDev: is.dev });

	app.on('activate', function () {
		// On macOS it's common to re-create a window in the app when the
		// dock icon is clicked and there are no other windows open.
		if (BrowserWindow.getAllWindows().length === 0) createWindow();
	});
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
	if (process.platform !== 'darwin') {
		app.quit();

		// 断开redis链接 todo
	}
});

// In this file you can include the rest of your app"s specific main process
// code. You can also put them in separate files and require them here.

app.setAsDefaultProtocolClient('beijing-vehicle');

app.on('open-url', (event, url) => {
	event.preventDefault();
	handleDeepLink(url);
});

app.on('second-instance', (event, commandLine, workingDirectory) => {
	const url = commandLine.find((arg) => arg.startsWith('beijing-vehicle://'));
	if (url) handleDeepLink(url);
});

function handleDeepLink(url: string) {
	const parsedUrl = new URL(url);
	console.log('接收到协议请求:', parsedUrl.host, parsedUrl.searchParams);

	// 实际业务逻辑（如打开特定窗口）
	if (parsedUrl.host === 'open') {
		mainWindow.show(); // 显示主窗口
	}
}

// 确保单实例运行（Windows/Linux）
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) app.quit();
