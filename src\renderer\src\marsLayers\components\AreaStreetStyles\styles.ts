import { background } from '@renderer/images/background';
import styled from 'styled-components';
import iconSelect from '@renderer/images/icon/select.png';
import iconSelectUn from '@renderer/images/icon/selectUn.png';
import labelImg from '@renderer/images/icon/labelEntityIcon.png';

const AreaViolationWrapStyled = styled.div`
	width: 400px;
	position: absolute;
	z-index: 5;
	right: 120px;
	top: 90px;
`;

const AreaViolationStyled = styled.div`
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #fff;
	margin-top: 10px;
	.echarts-map {
		width: 30%;
	}

	.echarts-line {
		/* background: linear-gradient(133deg, #042b62 0%, #114585 0%, #042c62 100%); */
		background: linear-gradient(
			133deg,
			rgba(4, 43, 98, 0.5) 0%,
			rgba(17, 69, 133, 0.5) 45%,
			rgba(4, 44, 98, 0.5) 100%
		);
		border-radius: 4px;
		border: 1px solid;
		position: relative;
		border-image: linear-gradient(
				307deg,
				rgba(200, 200, 200, 0),
				rgba(1, 184, 255, 1),
				rgba(151, 151, 151, 0)
			)
			1 1;
		width: 100%;
		height: 100%;
		/* padding-top: 10px; */
		display: flex;
		flex-direction: row;
		align-items: center;
		img {
			width: 23px;
			height: 17px;
			margin: 10px;
			position: relative;
			left: 8%;
		}

		.echarts-title1 {
			display: flex;
			flex-direction: column;
			span {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 24px;
				color: #d9fbff;
				line-height: 33px;
				text-align: left;
				font-style: normal;
				position: relative;
				left: 26%;
			}
		}
		.echarts-title {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 20px;
			font-size: 14px;
			line-height: 20px;
			color: #fff;
			cursor: pointer;

			span {
				width: 70px;
				display: flex;
				justify-content: center;
				flex-shrink: 0;
				margin: 0 10px;
			}
		}
		.tabs {
			margin-top: 5px;
			width: 30px;
			display: flex;
			flex-direction: column;
			margin-right: 20px;
			cursor: pointer;
			align-items: center;
			height: 100%;
			justify-content: center;
			.tabs-item {
				line-height: 14px;
				padding: 4px 5px;
				color: #b6c1d2;
				font-size: 14px;
				margin-top: 40px;
			}
			img {
				width: 16px;
				padding: 0 2px 3px;
			}
			a {
				border-right: 2px solid rgba(255, 255, 255, 0.1);
			}
			.active {
				border-right: 2px solid #3ebdcb;
				color: #3ebdcb;
			}
		}
		.title-select {
			background: url(${iconSelect}) no-repeat center center;
			background-size: contain;
		}
		.title-un-select {
			background: url(${iconSelectUn}) no-repeat center center;
			background-size: contain;
		}
	}
`;

const AreaStyle = styled.div`
	width: 109px;
	height: 226px;
	position: fixed;
	left: 484px;
	bottom: -10px;
	z-index: 3;
	transform: scale(0.8);
	/* img {
    width: 100%;
    height: auto;
    overflow: hidden;
  } */

	.degree {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		.degreeContent {
			display: flex;
			align-items: center;
			.degreeItem {
				width: 40px;
				height: 18px;
				border-radius: 25px;
			}
			.itemName {
				font-size: 20px;
				color: #fff;
				font-weight: 600;
				margin-left: 10px;
			}
		}
	}
`;

const AreaStyleP = styled.div`
	width: 519px;
	height: 86px;
	position: fixed;
	left: 214px;
	bottom: -60px;
	z-index: 3;
	transform: scale(0.8);
	img {
		width: 100%;
		height: auto;
		overflow: hidden;
	}
`;

const StreetStyle = styled.div`
	width: 262px;
	height: 574px;
	/* display: inline-block; */
	position: fixed;
	left: 415px;
	bottom: -128px;
	z-index: 3;
	transform: scale(0.8);
	img {
		width: 100%;
		height: auto;
		overflow: hidden;
		transform: scale(0.7);
	}
`;

const UnitStyle = styled.div`
	width: 129px;
	height: 46px;
	position: fixed;
	left: 484px;
	top: 100px;
	z-index: 3;
	.unitArea {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		font-size: 20px;
		font-weight: 600;
	}
`;
const UnitIndustryStyle = styled.div`
	width: 129px;
	height: 46px;
	position: fixed;
	right: 300px;
	top: 100px;
	z-index: 3;
	.unitArea {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		font-size: 20px;
		font-weight: 600;
	}
`;

const ReferenceStyled = styled.div`
	position: relative;
	/* background: linear-gradient(133deg, #042b62 0%, #114585 45%, #042c62 100%); */
	background: linear-gradient(
		133deg,
		rgba(4, 43, 98, 0.5) 0%,
		rgba(17, 69, 133, 0.5) 45%,
		rgba(4, 44, 98, 0.5) 100%
	);
	border-radius: 4px;
	border: 1px solid;
	border-image: linear-gradient(
			307deg,
			rgba(200, 200, 200, 0),
			rgba(1, 184, 255, 1),
			rgba(151, 151, 151, 0)
		)
		1 1;
	/* margin-top: 25px; */
	.selectAssembly {
		width: 73%;
		margin-left: 50px;
		/* padding-right: 20px; */
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
		}
		.ant-radio-button-wrapper {
			width: 90px;
			border: 0;
			border-radius: 10px;
			font-size: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			span:hover {
				color: #3adaff;
			}
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
	}
`;

const LabelLayerStyled = styled.div`
	width: auto;
	height: 77px;
	display: inline-block;
	background-image: url(${labelImg});
	background-size: 61px 59px;
	background-repeat: no-repeat;
	background-position: center 25px;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 3;
	text-align: center;
	color: #fff;
	line-height: 40px;
	display: none;
	transform: translate(-50%, -50%);
	margin-top: -20px;
	span {
		width: auto;
		height: 32px;
		display: inline-block;
		padding: 0 5px;
		background: linear-gradient(180deg, rgba(204, 155, 5, 0) 0%, #a38020 100%);
		border: 1px solid;
		border-top: 0;
		border-image: linear-gradient(
				180deg,
				rgba(200, 200, 200, 0),
				rgba(255, 186, 1, 1),
				rgba(235, 235, 235, 1)
			)
			1 1;
	}
`;

const ViolationTypeStyled = styled.div`
	position: fixed;
	left: 510px;
	top: 150px;
	z-index: 3;
	font-size: 20px;
	color: #b1c5d1;
	background: #0f3c63;
	border-radius: 15px;
	.type {
		height: 35px;
		line-height: 35px;
		border: 1px solid #1e96ae;
		padding: 0px 10px;
	}
	.type:first-child {
		border-radius: 15px 15px 0px 0px;
	}
	.type:last-child {
		border-radius: 0px 0px 15px 15px;
	}
`;

const RoadTopFive = styled.div`
	> div {
		position: fixed;
		right: 530px;
		bottom: 10px;
		width: 350px;
		z-index: 3;
		font-size: 20px;
		color: #b1c5d1;
		background: #0f3c63;
		border-radius: 15px;
		padding: 15px 15px;
		.type {
			height: 35px;
			line-height: 35px;
			padding: 10px 0px;
		}
		.type:first-child {
			border-radius: 15px 15px 0px 0px;
		}
		.type:last-child {
			border-radius: 0px 0px 15px 15px;
		}
	}
`;

const IndustryViolationWrapStyled = styled.div`
	position: absolute;
	left: 500px;
	top: 60px;
	z-index: 101;
	width: 500px;
	/* height: 300px; */
	height: auto;
	padding: 15px 0 15px 15px;
	.unit {
		position: fixed;
		right: 100px;
		top: 60px;
	}
	.ant-radio-group {
		border: 0;
		border-radius: 0;
		.ant-radio-button-wrapper {
			border: 1px solid #2b8eff;
			/* margin-right: 10px; */
			border-radius: 0;
			span {
				color: #e2f0ff;
				font-size: 14px;
				font-family: 'TimesNewRoman';
				letter-spacing: 3px;
			}
			&.ant-radio-button-wrapper-checked {
				span {
					color: #e2f0ff;
				}
			}
		}
	}
`;
export {
	AreaViolationWrapStyled,
	AreaViolationStyled,
	AreaStyle,
	UnitStyle,
	UnitIndustryStyle,
	ReferenceStyled,
	LabelLayerStyled,
	StreetStyle,
	AreaStyleP,
	ViolationTypeStyled,
	RoadTopFive,
	IndustryViolationWrapStyled,
};
