import React, { useEffect, useState } from 'react';
import { Radio } from 'antd';
import Echarts from '@renderer/components/echarts';
import Box from '../../baseUI/Box';
import moment from 'moment';
import { MileageCharacteristicsOfSreetsAndTownsStyle } from './style';
import { getStreetTownShip } from '@renderer/api';
import TimeTypeRadio from '@renderer/baseUI/TimeTypeRadio';

const MileageCharacteristicsOfSreetsAndTowns = () => {
	const [option, setOption] = useState({});
	const [radioValue, setRadioValue] = useState('hour');
	const radioOption = [
		{
			label: '时',
			value: 'hour',
		},
		{
			label: '日',
			value: 'day',
		},
	];
	useEffect(() => {
		const json =
			radioValue === 'hour'
				? {
						start_time: moment()
							.subtract(1, 'hour')
							.format('YYYY-MM-DD HH:00:00'),
						end_time: moment()
							.subtract(1, 'hour')
							.format('YYYY-MM-DD HH:00:00'),
						time_type: 1,
				  }
				: {
						start_time: moment()
							.subtract(1, 'day')
							.format('YYYY-MM-DD 00:00:00'),
						end_time: moment().subtract(1, 'day').format('YYYY-MM-DD 23:59:59'),
						time_type: 2,
				  };

		getStreetTownShip(json).then((res) => {
			getOption(res);
		});
	}, [radioValue]);

	const getOption = (data) => {
		let series: any = [];
		data.yData.map((i) => {
			series.push({
				value: i.SUM,
				tooltip: {
					formatter: (params) => {
						let x = data.xData[params.dataIndex];
						let list1 = `${i.name[0]}${Math.floor(i.SUM[0])}`;
						let list2 = `${i.name[1]}${Math.floor(i.SUM[1])}`;
						let list3 = `${i.name[2]}${Math.floor(i.SUM[2])}`;
						return `${x}<br />前三名街乡镇 <br />1:${list1}<br/>2:${list2}<br/>3:${list3}`;
					},
				},
			});
		});
		let option = {
			xAxis: {
				data: data.xData,
				axisLabel: {
					color: '#E8F4FF',
				},
			},
			yAxis: {
				axisLabel: {
					color: '#E8F4FF',
				},
			},
			tooltip: {
				show: true,
			},
			grid: {
				top: 30,
				left: 80,
				right: 2,
				bottom: 30,
			},
			series: {
				type: 'candlestick',
				itemStyle: {
					color: '#268AFF', // 往上升
					borderColor: '#268AFF',
					color0: '#36F097', // 往下降
					borderColor0: '#36F097',
				},
				data: series,
			},
		};
		setOption(option);
	};
	return (
		<Box
			title="街乡镇里程特征"
			titlewidth="100%"
			height="100%"
			subTitle={
				<div style={{ display: 'flex' }}>
					<TimeTypeRadio
						type={radioValue}
						typeOptions={radioOption}
						timeType={(e) => setRadioValue(e)}
					/>
				</div>
			}
		>
			<MileageCharacteristicsOfSreetsAndTownsStyle>
				{/* <div className="time-radio">
          <Radio.Group
            defaultValue={radioValue}
            buttonStyle="solid"
            size="small"
            onChange={(e) => setRadioValue(e.target.value)}
          >
            {radioOption.map((item) => {
              return <Radio.Button value={item.value}>{item.key}</Radio.Button>
            })}
          </Radio.Group>
        </div> */}
				<Echarts option={option}></Echarts>
			</MileageCharacteristicsOfSreetsAndTownsStyle>
		</Box>
	);
};

export default MileageCharacteristicsOfSreetsAndTowns;
