/*
 * @Descripttion:
 * @version:
 * @Author: Gqq
 * @Date: 2024-07-23 16:32:20
 */
import styled from 'styled-components';

const Container = styled.div`
	width: 100%;
	height: 94%;
	padding-top: 1%;
	display: flex;
	.left {
		width: 32%;
		height: 100%;
		img {
			width: 100%;
			height: 100%;
		}
	}
	.right {
		width: 62%;
		height: 100%;
		border-radius: 4px 4px 0px 0px;
		border: 1px solid #368ec1;
		margin-left: 1%;
		.ant-table-wrapper {
			height: 96%;
			.ant-table {
				.ant-table-container {
					.ant-table-thead {
						height: 10%;
						.ant-table-cell {
							background: #081f38;
							color: #fff;
							font-size: 14px;
							.ant-table-cell {
								border-bottom: none;
							}
						}
					}
					.ant-table-tbody {
						max-height: 88%;
						overflow-y: auto;
						background: linear-gradient(
							180deg,
							rgba(13, 130, 234, 0.31) 0%,
							rgba(9, 30, 59, 0) 100%
						);
						tr {
							background: none;
							color: #fff;
						}
						.ant-table-cell {
							background: none;
						}
						.ant-table-cell-row-hover {
							background: none;
						}
					}
				}
			}
		}
		.ant-table-wrapper .ant-table-thead > tr > td {
			border: none;
		}
		.ant-table-wrapper .ant-table {
			background: none;
		}
		.ant-table-wrapper .ant-table-tbody > tr > td {
			border-bottom: 1px solid #3488b9;
		}
	}
`;

export default Container;
