import styled from 'styled-components';

const LayoutContainer = styled.div`
	display: flex;
	flex-direction: row;
	width: 100%;
	height: 100%;
	justify-content: space-around;
	.left-container {
		width: 45%;
		// .echarts {
		//   width: 100%;
		//   height: 213px;
		//   display: flex;
		//   flex-direction: column;
		//   justify-content: center;
		//   align-items: center;
		//   .legend-container {
		//     display: flex;
		//     .legend-item {
		//       display: flex;
		//       height: 20px;
		//       justify-content: center;
		//       align-items: center;
		//       margin-right: 5px;
		//       span:nth-child(1) {
		//         display: block;
		//         width: 12px;
		//         height: 8px;
		//         border-radius: 2px;
		//         margin-right: 16p;
		//       }
		//       span:nth-child(2) {
		//         padding-left: 5px;
		//         height: 20px;
		//         font-size: 14px;
		//         font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
		//         font-weight: 400;
		//         color: #d8f1ff;
		//         line-height: 20px;
		//         letter-spacing: 1px;
		//       }
		//     }
		//   }
		// }
	}
	.right-container {
		width: 50%;
		// margin-top: 10px;
		// margin-left: 20px;
		// .table {
		//   width: 420px;
		//   height: 95%;
		//   background: linear-gradient(180deg, rgba(13, 130, 234, 0.31) 0%, rgba(9, 30, 59, 0) 100%);
		//   border-radius: 4px 4px 0px 0px;
		//   border: 1px solid #368ec1;
		//   //overflow: hidden;
		//   .list-content .list-ul {
		//     height: 40px;
		//     line-height: 40px;
		//   }
		//   .list-content .list-ul .list-li {
		// flex: 1;
		// text-align: center;
		// cursor: pointer;
		// color: #d8f1ff;
		// font-size: 16px;
		// overflow: hidden;
		// text-overflow: ellipsis;
		.echarts {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			margin-top: 10px;
			.legend-container {
				display: flex;
				margin-left: 40px;
				.legend-item {
					display: flex;
					height: 20px;
					justify-content: center;
					align-items: center;
					margin-left: 20px;
					margin-bottom: 5px;
					margin-top: 5px;
					span:nth-child(1) {
						display: block;
						width: 12px;
						height: 8px;
						border-radius: 2px;
					}
					span:nth-child(2) {
						padding-left: 5px;
						height: 20px;
						font-size: 14px;
						font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #d8f1ff;
						line-height: 20px;
						letter-spacing: 1px;
					}
				}
			}
		}
	}

	// ul {
	//         padding: 0;
	//         margin: 0;
	//         list-style: none;
	//       }
	//       .column-ul {
	//         display: flex;
	//         flex-direction: row;
	//         justify-content: space-around;
	//         margin-left: -1px;
	//         border-right: 1px solid #3488b9;
	//         border-bottom: 1px solid #3488b9;
	//         background: #081F38;
	//         .column-li {
	//           flex: 1;
	//           color: #D8F1FF;;
	//           font-size: 16px;
	//           height: 40px;
	//           line-height: 40px;
	//           text-align: center;
	//           //border-top: 1px solid #3488b9;
	//           //border-left: 1px solid #3488b9;
	//           box-sizing: border-box;
	//         }
	//       }
	//     }
	//   }
`;
export default LayoutContainer;
