import styled from 'styled-components';

export const SearchBoxStyled = styled.div`
	height: auto;
	.searchBtn {
		width: 100px;
		height: 50px;
		position: absolute;
		right: 300px;
		top: 120px;
		background-color: red;
		cursor: pointer;
	}
`;

export const SearchDetailsListStyled = styled.div`
	position: absolute;
	left: 50%;
	top: 16%;
	transform: translateX(-50%);
	z-index: 199;
	min-width: 680px;
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	color: #fff;
	flex-direction: column;
	background: linear-gradient(
		to right,
		rgba(11, 41, 69, 0.8),
		rgba(15, 51, 74, 0.8)
	);
	padding: 10px 0 0 10px;
	border-radius: 5px;
	border: 2px solid rgba(9, 132, 182, 0.7);

	.middle-container {
		width: 100%;
		padding-right: 10px;
		box-sizing: border-box;
		padding-top: 10px;
		position: relative;
		.Violation {
			margin-bottom: 10px;
		}
		.close {
			position: absolute;
			right: 15px;
			top: 0;
			cursor: pointer;
			font-size: 20px;
			&:hover {
				scale: calc(1.1);
			}
		}
	}

	.table {
		/* background: linear-gradient(180deg, rgba(13,130,234,0.31) 0%, rgba(9,30,59,0) 100%); */
		/* background: linear-gradient(180deg, rgba(13,130,234, 1) 0%, rgba(9,30,59, 0.1) 100%); */
		border-radius: 7px 7px 0 0;
	}
	dl {
		border: 2px solid rgba(9, 132, 182, 0.7);
		border-radius: 7px 7px 0 0;
		border-bottom: 0;
		width: 100%;
		box-sizing: border-box;
		margin-top: 10px;
		margin-bottom: 0;
		dt {
			display: flex;
			background-color: rgba(10, 27, 65, 0.8);
			font-family: PingFangSC-Regular, PingFang SC;
			color: #d8f1ff;
			letter-spacing: 1px;
			font-size: 20px;
			padding: 4px 0;
			span {
				text-align: center;
				line-height: 26px;
				&.no1 {
					width: 30%;
				}
				&.no2 {
					width: 10%;
				}
				&.no3 {
					width: 25%;
				}
				&.no4 {
					width: 10%;
				}
				&.no5 {
					width: 25%;
				}
			}
		}
		dd {
			display: flex;
			border-bottom: 1px solid rgba(9, 132, 182, 0.7);
			background-color: linear-gradient(
				180deg,
				rgba(0, 184, 255, 0.2) 0%,
				rgba(0, 221, 255, 0) 100%
			);
			margin: 0;
			height: 29px;
			color: #d4d4d4;
			cursor: pointer;
			/* font-size: 20px; */
			align-self: center;
			/* align-items: center; */
			&:hover {
				background: rgba(58, 218, 255, 0.1);
			}
			span {
				text-align: center;
				line-height: 30px;
				&.no1 {
					width: 30%;
				}
				&.no2 {
					width: 10%;
				}
				&.no3 {
					width: 25%;
				}
				&.no4 {
					width: 10%;
				}
				&.no5 {
					width: 25%;
				}
			}
			&.active {
				background: linear-gradient(
					180deg,
					rgba(0, 184, 255, 0.2) 0%,
					rgba(0, 221, 255, 0.1) 100%
				);
			}
		}
	}
	.sewage-treatment-content {
		.sewage-treatment-settings {
			display: flex;
			padding: 0 10px;
			padding-top: 10px;
			.settings-option-size {
				display: flex;
				align-items: center;
				margin-right: 15px;
				.settings-label {
					font-size: 18px;
					min-width: 20px;
					white-space: nowrap;
				}
			}
			.settings-select {
				.ant-select-selector {
					background: rgba(64, 146, 171, 0.16);
					border-radius: 4px;
					border: 1px solid #1f7eb1bd;
					font-size: 18px !important;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					height: 32px;
					color: #ffffff !important;
					display: flex;
					align-items: center;
				}
				.ant-select-selection-overflow {
					text-overflow: ellipsis;
					white-space: nowrap;
					height: 49px;
					align-items: center;
					flex-wrap: nowrap;
					overflow: hidden;
				}
				.ant-select-selection-overflow-item {
					background-color: transparent;
					border: 0;
					.ant-select-selection-item {
						background-color: transparent;
						border: 0;
					}
					.ant-select-selection-item-remove {
						color: #fff;
						font-size: 20px;
					}
				}
			}
			.settings-select-popup {
				.ant-select-item {
					align-items: center;
					min-height: 40px;
					.ant-select-item-option-content {
						font-size: 22px;
					}
				}
			}
			.ant-select-arrow {
				color: rgba(255, 255, 255, 1);
				font-size: 18px;
			}
			.settings-slect-define {
				display: flex;
				margin-right: 70px;
			}
			.settings-option-time {
				margin-right: 70px;
				display: flex;
				white-space: nowrap;
				.settings-option-datePicker {
					background-color: transparent;
					border: 1px solid rgba(31, 126, 177, 0.7411764706);
					height: 48px;
					.ant-picker-input > input {
						color: #fff;
						font-size: 24px;
					}
					.anticon {
						color: #fff;
					}
				}
			}
		}
		.settings-button {
			flex: 1;
			display: flex;
			flex-direction: row-reverse;
			.ant-btn {
				width: 136px;
				height: 49px;
				box-sizing: border-box;
				border-radius: 4px;
				font-size: 26px;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #ffffff;
				line-height: 36px;
				color: #fff;
				border: 0;
				text-align: center;
				&.settings-button-query {
					background: rgba(71, 208, 226, 0.7);
					background-image: url('~@/assets/images/yongchuan/sewage/query.png');
					background-repeat: no-repeat;
					background-position: 12px center;
					background-size: 30px 30px;
					margin-right: 30px;
					&:hover {
						background: rgba(71, 208, 226, 0.8);
					}
				}
			}
		}
		.sewage-treatment-table {
			padding: 10px;
		}
	}
`;
