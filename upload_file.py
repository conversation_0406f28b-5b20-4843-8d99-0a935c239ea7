import requests
import json

def send_file():
    data = {'subsystem_code': 'web',
            'subsystem_api': 'web',
            'api_params': '重柴C端程序',
            'file_name': f"tripod-visualization-destop.exe"}

    files = {'files': (f"tripod-visualization-destop.exe", open('/opt/web/dist/win-unpacked/tripod-visualization-destop.exe', 'rb'),
                       "application/vnd.ms-excel")}

    r = requests.post(url="https://api-file-manage.airqualitychina.cn/api/hold-filename-upload",
                      data=data,
                      files=files)

    file_url = json.loads(r.content)['result'][0]['url']
    url = "https://f.hotgrid.cn/" + file_url

    print(url)

if __name__ == "__main__":

    send_file()
