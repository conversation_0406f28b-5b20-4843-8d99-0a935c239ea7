/**
 * 高密度站
 */
// import icons from '@/images/map/standard';
import pointImg from '@renderer/images/layer/std/high';
import { styleType } from './util';

const getStyle = (item: any): styleType => {
	const ele = item;
	return {
		icon: pointImg[`${ele.pic}`],
		text: `${ele.value}`,
		labels: {
			font_size: 20,
			font_weight: 400,
			pixelOffsetY: -10,
			visibleDepth: false,
		},
		scale: 1.2,
	};
};

export default {
	getStyle,
};
