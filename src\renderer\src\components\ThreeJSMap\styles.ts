import styled from 'styled-components';

interface MapContainerProps {
	width: number;
	height: number;
}

export const MapContainer = styled.div<MapContainerProps>`
	width: ${(props) => props.width}px;
	height: ${(props) => props.height}px;
	border: 1px solid #333;
	border-radius: 8px;
	overflow: hidden;
	position: relative;
	background: #000011;

	canvas {
		display: block;
		cursor: grab;

		&:active {
			cursor: grabbing;
		}
	}
`;

export const LoadingContainer = styled.div`
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: #fff;
	font-size: 16px;
	z-index: 10;
`;

export const ControlsContainer = styled.div`
	position: absolute;
	top: 10px;
	right: 10px;
	z-index: 10;
	display: flex;
	flex-direction: column;
	gap: 8px;
`;

export const ControlButton = styled.button`
	background: rgba(0, 0, 0, 0.7);
	border: 1px solid #333;
	color: #fff;
	padding: 8px 12px;
	border-radius: 4px;
	cursor: pointer;
	font-size: 12px;

	&:hover {
		background: rgba(0, 0, 0, 0.9);
		border-color: #555;
	}

	&:active {
		transform: translateY(1px);
	}
`;

export const LegendContainer = styled.div`
	position: absolute;
	bottom: 10px;
	left: 10px;
	background: rgba(0, 0, 0, 0.8);
	border: 1px solid #333;
	border-radius: 4px;
	padding: 12px;
	color: #fff;
	font-size: 12px;
	z-index: 10;
`;

export const LegendTitle = styled.div`
	font-weight: bold;
	margin-bottom: 8px;
	font-size: 14px;
`;

export const LegendItem = styled.div`
	display: flex;
	align-items: center;
	margin-bottom: 4px;

	&:last-child {
		margin-bottom: 0;
	}
`;

export const LegendColor = styled.div<{ color: string }>`
	width: 16px;
	height: 16px;
	background-color: ${(props) => props.color};
	margin-right: 8px;
	border: 1px solid #555;
`;

export const TooltipContainer = styled.div`
	position: absolute;
	background: rgba(0, 0, 0, 0.9);
	border: 1px solid #333;
	border-radius: 4px;
	padding: 8px 12px;
	color: #fff;
	font-size: 12px;
	pointer-events: none;
	z-index: 20;
	max-width: 200px;

	&::before {
		content: '';
		position: absolute;
		bottom: -6px;
		left: 50%;
		transform: translateX(-50%);
		width: 0;
		height: 0;
		border-left: 6px solid transparent;
		border-right: 6px solid transparent;
		border-top: 6px solid rgba(0, 0, 0, 0.9);
	}
`;

export const TooltipTitle = styled.div`
	font-weight: bold;
	margin-bottom: 4px;
`;

export const TooltipValue = styled.div`
	color: #ccc;
`;
