import styled from 'styled-components';

const SpatialDistributionStyle = styled.div`
	position: absolute;
	right: 190px;
	top: 50px;
	z-index: 102;
	width: 510px;
	height: auto;
	/* overflow: hidden; */
	/* background-color: #00153cc5; */
	padding: 10px 0 0 15px;
	/* border: 2px solid rgba(19, 20, 20, 0.7); */
	background: linear-gradient(
		133deg,
		#0b2945b5 0%,
		#0b2945ba 45%,
		#0f334abd 100%
	);
	border-radius: 4px;
	border: 2px solid;
	border-image: linear-gradient(
			307deg,
			rgba(200, 200, 200, 0),
			rgba(1, 184, 255, 1),
			rgba(151, 151, 151, 0)
		)
		2 2;
	.spatial-distribution-top {
		display: flex;
		flex-direction: row-reverse;
		padding-right: 10px;
	}
	.ant-radio-group {
		border: 0;
		border-radius: 0;
		.ant-radio-button-wrapper {
			border: 1px solid #2b8eff;
			/* margin-right: 10px; */
			border-radius: 0;
			span {
				color: #e2f0ff;
				font-size: 14px;
				font-family: 'TimesNewRoman';
				letter-spacing: 3px;
			}
			&.ant-radio-button-wrapper-checked {
				span {
					color: #e2f0ff;
				}
			}
		}
	}
	.tabs {
		display: flex;
		padding: 20px 0;
		.tabs-item {
			div {
				color: #fff;
				padding: 0 15px;
				position: relative;
				font-size: 16px;
				cursor: pointer;
				&::after {
					content: '';
					position: absolute;
					right: 0;
					top: 1px;
					z-index: 1;
					height: 16px;
					width: 1;
					border-right: 1px solid rgba(255, 255, 255, 0.3);
				}
				/* &:last-of-type {
          &::after {
            display: none;
          }
        } */
			}
			&.active {
				div {
					color: rgba(58, 218, 255, 1);
				}
			}
			&:last-child {
				div {
					&::after {
						display: none;
					}
				}
			}
		}
	}
	.down-container {
		padding-bottom: 10px;
		.botLine {
			width: 96%;
			height: 1px;
			background-color: rgba(58, 218, 255, 0.6);
			margin-bottom: 2px;
		}
		.echarts-container {
			.echarts {
				height: 200px;
			}
		}
		.line-eachart {
			margin: 5px 0;
			height: 220px;
		}
		.ranking-eachart {
			position: relative;
			width: 100%;
			height: 220px;
			.rankingTitle {
				position: absolute;
				top: 20%;
				p {
					font-size: 20px;
					color: #e2f0ff;
					font-weight: bold;
					writing-mode: vertical-rl; /* vertical right-to-left */
					text-orientation: mixed;
				}
			}
		}
	}
`;

export default SpatialDistributionStyle;
