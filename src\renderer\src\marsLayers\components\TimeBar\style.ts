import styled from 'styled-components';
import background from '../../../images/background';
import icon from '../../../images/icon';

export const TimeBarContent = styled.div`
	width: 60%;
	height: 5%;
	padding: 0 1%;
	position: absolute;
	left: 50%;
	bottom: 5%;
	z-index: 100;
	transform: translateX(-50%);
	.tiebarButton {
		background: url(${icon.bot}) no-repeat;
		background-size: 100% 100%;
		width: 1777px;
		height: 43px;
		position: absolute;
		left: -19%;
		bottom: -60px;
		cursor: pointer;
	}
	.layerMenuContent {
		width: 100%;
		height: 100%;
		position: absolute;
		left: 0;
		bottom: 0;
		padding: 0 1%;
		background: url(${background.timebar});
		background-size: 100% 100%;
	}
	.ant-slider {
		.ant-slider-track {
			background-color: #64ced3;
			height: 10px;
		}
		.ant-slider-rail {
			background-color: rgba(52, 157, 162, 0.52);
			height: 10px;
		}
		.ant-slider-dot {
			inset-block-start: 0;
			width: 9px;
			height: 9px;
			background-color: rgba(255, 255, 255, 0.6);
			border: 2px solid #5ce7ed;
		}
		.ant-slider-handle::after {
			width: 12px;
			height: 12px;
			background-color: #ffffff;
			box-shadow: 0 0 0 2px #5ce7ed;
		}
		.ant-slider-mark-text {
			color: #b3b3b3;
			margin-top: 1%;
		}
		.ant-slider-mark-text-active {
			color: #fff;
		}
	}
	// .ant-tooltip {
	//   .ant-tooltip-content {
	//     .ant-tooltip-inner {
	//       background: 'red';
	//     }
	//   }
	// }
`;
