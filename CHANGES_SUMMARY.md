# VehicleHistoricalTrajectory.jsx 改造总结

## 改造目标
将实时轨迹功能改造为每5秒获取一次当前数据，累积收集轨迹点，然后使用FixedRoute来实现连续轨迹播放。

## 主要修改

### 1. 数据收集策略
- **新策略**: 每5秒获取一次当前位置数据
- **累积机制**: 将每次获取的数据点累积到轨迹数组中
- **轨迹限制**: 最多保存120个点（约10分钟的轨迹数据）

### 2. 轨迹点存储
```javascript
// 新增全局变量
realTimeTrajectoryPoints = [], // 存储实时轨迹点
maxTrajectoryPoints = 120; // 最多保存120个点

// 轨迹点数据结构
const newPoint = {
  position: [gcj2wgs2[0], gcj2wgs2[1]], // 转换后的坐标
  speed: latestPoint.speed || 0,        // 速度信息
  time: latestPoint.gDate,              // 时间戳
  data: latestPoint                     // 原始数据
};
```

### 3. 动态轨迹更新
- **实时更新**: 每次获取新数据后，重新创建FixedRoute
- **连续轨迹**: 使用累积的所有点创建连续的轨迹线
- **自动播放**: 轨迹准备完成后自动开始播放

### 4. 定时器调整
- **获取频率**: 每5秒获取一次数据 (`5000ms`)
- **轨迹更新**: 每次获取数据后立即更新轨迹

### 5. 用户控制功能
- **清空轨迹**: 提供"清空实时轨迹"按钮
- **点数显示**: 实时显示当前收集的轨迹点数
- **状态管理**: 使用React状态管理轨迹点数量

## 技术细节

### 数据处理
1. 获取轨迹数据（用户保持原有API调用方式）
2. 将经纬度坐标转换为 mars3d 可用格式
3. 提取速度信息用于 FixedRoute 播放
4. 使用最新数据点的属性信息用于 popup 显示

### FixedRoute 配置
```javascript
graphicModel = new mars3d.graphic.FixedRoute({
  name: `${vid}_realtime`,
  frameRate: 1,
  speed: speeds,           // 速度数组
  positions,              // 位置数组
  clockLoop: false,       // 不循环播放
  camera: {
    type: 'gs',
    pitch: -30,
    radius: 20000,
  },
  model: {
    url: getCarTypeModel(type),
    size: 2,
    fill: true,
    color: getCarColor(type),
    minimumPixelSize: 50,
  },
  attr: res[res.length - 1], // 最新数据作为属性
});
```

### 自动播放控制
```javascript
// 移除自动播放
.then(function () {
  // 轨迹准备完成，但不自动播放
  console.log('实时轨迹准备完成，等待手动播放');
  setRealTimeTrajectoryReady(true);
  // 只有在历史轨迹播放时才自动开始实时轨迹
  if (isStart && !isPause) {
    graphicModel.start();
  }
});
```

### UI控制增强
1. **新增状态**: `realTimeTrajectoryReady` 用于跟踪实时轨迹是否准备好
2. **新增按钮**: "播放实时轨迹" 按钮，只在实时轨迹准备好且没有播放历史轨迹时显示
3. **控制函数增强**: `btnPause`、`btnProceed`、`btnStop` 现在可以同时控制历史轨迹和实时轨迹

### 事件处理
- 添加了轨迹播放开始和结束的事件监听
- 保持了原有的 popup 绑定功能
- 优化了错误处理和重试机制
- 增强了状态管理，支持历史轨迹和实时轨迹的独立控制

## 优势
1. **解决自动播放问题**: 实时轨迹不再自动播放，用户可以手动控制
2. **更流畅的轨迹显示**: 使用 FixedRoute 可以显示连续的轨迹线
3. **更合理的数据获取频率**: 2分钟间隔减少了服务器压力
4. **更好的用户体验**: 提供了独立的实时轨迹控制按钮
5. **兼容性**: 保持了原有的API调用方式

## 注意事项
1. 实时轨迹数据需要包含多个点才能形成有效的轨迹
2. 如果2分钟内没有轨迹数据，实时轨迹按钮不会显示
3. 历史轨迹和实时轨迹可以独立控制，但共享相同的控制按钮
