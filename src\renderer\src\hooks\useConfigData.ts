import { useEffect, useState } from 'react';

import { useSessionStorage } from './useSessionStorage';

export const useConfigData = <T>(property: string, defaultValue: T): T => {
	const [config, setConfig] = useState<T>(defaultValue);

	const [value, setValue] = useSessionStorage<T | null>('config-json', null);

	useEffect(() => {
		if (value && value[property]) {
			setConfig(value[property]);
		}
	}, [value, property]);

	return [config, setValue];
};
