import styled from 'styled-components';

const Container = styled.div`
	display: flex;
	width: 100%;
	height: 100%;
	flex-direction: row;
	margin-top: 10px;
	justify-content: center;
	.left {
		// .tip-container {
		//   margin-top: 14px;
		//   display: flex;
		//   flex-direction: row;
		// }
		// .frontPage {
		//   //padding-left: 5px;

		//   padding-right: 5px;
		//   margin-left: 10px;
		//   //width: 150px;
		//   height: 40px;
		//   line-height: 40px;
		//   background: rgba(54, 142, 193, 0.12);
		//   position: relative;
		//   img {
		//     width: 21px;
		//     height: 20px;
		//     margin: 0 5px 0 5px;
		//   }
		//   .name {
		//     font-weight: 400;
		//     color: #c9dce9;
		//     font-size: 14px;
		//   }
		//   .nums {
		//     color: #1cf1d1;
		//     font-size: 16px;
		//     margin-left: 8px;
		//   }
		// }

		// .echarts-container {
		//   display: flex;
		// }
		// .echarts {
		//   width: 150px;
		//   height: 150px;
		//   margin-left: 12px;
		// }
		// .legend {
		//   width: 100%;
		//   display: flex;
		//   flex-direction: row;
		//   flex-wrap: wrap;
		//   margin-top: 10px;
		//   .legend-item {
		//     display: flex;
		//     flex-direction: row;
		//     justify-content: center;
		//     align-items: center;
		//     padding: 0 40px 0 0;
		//     .legend-line {
		//       width: 8px;
		//       height: 8px;
		//       background-color: var(--color);
		//     }

		//     .legend-name {
		//       padding-left: 8px;
		//       display: flex;
		//       flex-direction: row;
		//       span:nth-child(1) {
		//         color: #c9dce9;
		//         font-size: 12px;
		//         padding-right: 8px;
		//       }
		//       span:nth-child(2) {
		//         color: #c9dce9;
		//         font-size: 12px;
		//       }
		//     }
		//   }
		// }
	}
	.right {
		// .user-car {
		//   margin-top: 14px;
		//   display: flex;
		//   flex-direction: column;
		//   height: 100%;
		//   justify-content: space-between;
		//   .user-car-item {
		//     padding-bottom: 20px;
		//     display: flex;
		//     .sort {
		//       color: #fe2c46;
		//       font-size: 14px;
		//     }
		//     span:nth-child(2) {
		//       display: block;
		//       color: #ffffff;
		//       font-size: 14px;
		//       margin-left: 10px;
		//       width: 185px; /* 设置容器宽度 */
		//     white-space: nowrap; /* 禁止文字换行 */
		//     overflow: hidden; /* 隐藏超出容器范围的内容 */
		//     text-overflow: ellipsis;
		//     }
		//     span:nth-child(3) {
		//       width: 63px;
		//       color: #ffffff;
		//       font-size: 14px;
		//       margin-left: 8px;
		//     }
		//   }
		//   span:nth-child(4) {
		//     color: #dfdfdf;
		//     font-size: 14px;
		//     opacity: 0.5;
		//   }
		// }
	}
`;

export default Container;
