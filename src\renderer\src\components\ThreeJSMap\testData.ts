// 测试用的北京区域GeoJSON数据（简化版）
export const beijingGeoJSON = {
	type: 'FeatureCollection' as const,
	features: [
		{
			type: 'Feature' as const,
			properties: { name: '朝阳区', code: '110105' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.443, 39.921],
						[116.514, 39.921],
						[116.514, 39.982],
						[116.443, 39.982],
						[116.443, 39.921],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '海淀区', code: '110108' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.298, 39.959],
						[116.443, 39.959],
						[116.443, 40.097],
						[116.298, 40.097],
						[116.298, 39.959],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '西城区', code: '110102' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.365, 39.893],
						[116.443, 39.893],
						[116.443, 39.959],
						[116.365, 39.959],
						[116.365, 39.893],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '东城区', code: '110101' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.443, 39.893],
						[116.434, 39.893],
						[116.434, 39.959],
						[116.443, 39.959],
						[116.443, 39.893],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '丰台区', code: '110106' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.286, 39.858],
						[116.443, 39.858],
						[116.443, 39.893],
						[116.286, 39.893],
						[116.286, 39.858],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '石景山区', code: '110107' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.195, 39.906],
						[116.286, 39.906],
						[116.286, 39.959],
						[116.195, 39.959],
						[116.195, 39.906],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '通州区', code: '110112' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.514, 39.858],
						[116.731, 39.858],
						[116.731, 39.982],
						[116.514, 39.982],
						[116.514, 39.858],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '大兴区', code: '110115' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.286, 39.728],
						[116.514, 39.728],
						[116.514, 39.858],
						[116.286, 39.858],
						[116.286, 39.728],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '房山区', code: '110111' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[115.993, 39.728],
						[116.286, 39.728],
						[116.286, 39.858],
						[115.993, 39.858],
						[115.993, 39.728],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '昌平区', code: '110114' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.298, 40.097],
						[116.514, 40.097],
						[116.514, 40.218],
						[116.298, 40.218],
						[116.298, 40.097],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '顺义区', code: '110113' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.514, 40.097],
						[116.731, 40.097],
						[116.731, 40.218],
						[116.514, 40.218],
						[116.514, 40.097],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '怀柔区', code: '110116' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.514, 40.218],
						[116.731, 40.218],
						[116.731, 40.414],
						[116.514, 40.414],
						[116.514, 40.218],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '密云区', code: '110118' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.731, 40.218],
						[117.323, 40.218],
						[117.323, 40.414],
						[116.731, 40.414],
						[116.731, 40.218],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '门头沟区', code: '110109' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[115.993, 39.906],
						[116.195, 39.906],
						[116.195, 40.097],
						[115.993, 40.097],
						[115.993, 39.906],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '平谷区', code: '110117' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[116.731, 39.982],
						[117.323, 39.982],
						[117.323, 40.218],
						[116.731, 40.218],
						[116.731, 39.982],
					],
				],
			},
		},
		{
			type: 'Feature' as const,
			properties: { name: '延庆区', code: '110119' },
			geometry: {
				type: 'Polygon' as const,
				coordinates: [
					[
						[115.993, 40.218],
						[116.514, 40.218],
						[116.514, 40.414],
						[115.993, 40.414],
						[115.993, 40.218],
					],
				],
			},
		},
	],
};

// 生成测试数据的函数
export const generateTestData = (dataType: string) => {
	const regions = [
		'朝阳区',
		'海淀区',
		'西城区',
		'东城区',
		'丰台区',
		'石景山区',
		'通州区',
		'大兴区',
		'房山区',
		'昌平区',
		'顺义区',
		'怀柔区',
		'密云区',
		'门头沟区',
		'平谷区',
		'延庆区',
	];

	switch (dataType) {
		case 'population':
			// 人口数据（万人）
			return regions.map((name) => ({
				name,
				value: Math.floor(Math.random() * 300) + 50, // 50-350万人口
				unit: '万人',
				description: `${name}常住人口`,
			}));

		case 'gdp':
			// GDP数据（亿元）
			return regions.map((name) => ({
				name,
				value: Math.floor(Math.random() * 4000) + 500, // 500-4500亿GDP
				unit: '亿元',
				description: `${name}地区生产总值`,
			}));

		case 'pollution':
			// 污染指数
			return regions.map((name) => ({
				name,
				value: Math.floor(Math.random() * 150) + 50, // 50-200污染指数
				unit: 'AQI',
				description: `${name}空气质量指数`,
			}));

		case 'temperature':
			// 温度数据（摄氏度）
			return regions.map((name) => ({
				name,
				value: Math.floor(Math.random() * 20) + 15, // 15-35度
				unit: '°C',
				description: `${name}平均气温`,
			}));

		case 'traffic':
			// 交通拥堵指数
			return regions.map((name) => ({
				name,
				value: Math.floor(Math.random() * 5) + 3, // 3-8拥堵指数
				unit: '级',
				description: `${name}交通拥堵等级`,
			}));

		case 'housing':
			// 房价数据（万元/平米）
			return regions.map((name) => ({
				name,
				value: Math.floor(Math.random() * 8) + 3, // 3-11万元/平米
				unit: '万元/㎡',
				description: `${name}平均房价`,
			}));

		default:
			return regions.map((name) => ({
				name,
				value: Math.floor(Math.random() * 100),
				unit: '',
				description: `${name}数值`,
			}));
	}
};

// 数据类型配置
export const dataTypeConfig = {
	population: {
		label: '人口数量',
		unit: '万人',
		colorScheme: 'blue',
		description: '各区县常住人口数量',
	},
	gdp: {
		label: 'GDP',
		unit: '亿元',
		colorScheme: 'green',
		description: '各区县地区生产总值',
	},
	pollution: {
		label: '空气质量',
		unit: 'AQI',
		colorScheme: 'red',
		description: '各区县空气质量指数',
	},
	temperature: {
		label: '气温',
		unit: '°C',
		colorScheme: 'orange',
		description: '各区县平均气温',
	},
	traffic: {
		label: '交通拥堵',
		unit: '级',
		colorScheme: 'purple',
		description: '各区县交通拥堵等级',
	},
	housing: {
		label: '房价',
		unit: '万元/㎡',
		colorScheme: 'viridis',
		description: '各区县平均房价',
	},
};

// 颜色方案配置
export const colorSchemes = {
	blue: [
		'#f7fbff',
		'#deebf7',
		'#c6dbef',
		'#9ecae1',
		'#6baed6',
		'#4292c6',
		'#2171b5',
		'#08519c',
		'#08306b',
	],
	red: [
		'#fff5f0',
		'#fee0d2',
		'#fcbba1',
		'#fc9272',
		'#fb6a4a',
		'#ef3b2c',
		'#cb181d',
		'#a50f15',
		'#67000d',
	],
	green: [
		'#f7fcf5',
		'#e5f5e0',
		'#c7e9c0',
		'#a1d99b',
		'#74c476',
		'#41ab5d',
		'#238b45',
		'#006d2c',
		'#00441b',
	],
	purple: [
		'#fcfbfd',
		'#efedf5',
		'#dadaeb',
		'#bcbddc',
		'#9e9ac8',
		'#807dba',
		'#6a51a3',
		'#54278f',
		'#3f007d',
	],
	orange: [
		'#fff5eb',
		'#fee6ce',
		'#fdd0a2',
		'#fdae6b',
		'#fd8d3c',
		'#f16913',
		'#d94801',
		'#a63603',
		'#7f2704',
	],
	viridis: [
		'#440154',
		'#482777',
		'#3f4a8a',
		'#31678e',
		'#26838f',
		'#1f9d8a',
		'#6cce5a',
		'#b6de2b',
		'#fee825',
	],
};
