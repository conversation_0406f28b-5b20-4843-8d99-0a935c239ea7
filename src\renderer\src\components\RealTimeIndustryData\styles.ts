import styled from 'styled-components';

const RealTimeIndustryDataStyled = styled.div`
	width: 100%;
	height: 100%;
	margin: 0 auto;
	margin-top: 10px;
	.RealTimeIndustryNav {
		width: auto;
		z-index: 9;
		background-color: rgba(0, 65, 94) !important;
		color: #fff !important;
		font-size: 15px !important;
		.ant-segmented-group {
			.ant-segmented-item {
				background: rgba(14, 47, 89, 0.66);
				border-radius: 4px;
			}
			.ant-segmented-item-selected {
				background: radial-gradient(
					37% 68% at 50% 50%,
					#3bc3f4 0%,
					rgba(52, 112, 190, 0) 100%
				);
				box-shadow: inset 0px 0px 5px 0px #45eeff;
				border-radius: 4px;
				border: 1px solid;
				border-image: radial-gradient(
						circle,
						rgba(85, 252, 255, 1),
						rgba(71, 211, 219, 1)
					)
					1 1;
			}
		}
	}
	/* .ant-segmented-item:hover {
    color: #111 !important;
  } */
	.ant-segmented-item-selected {
		background-color: rgba(0, 116, 145) !important;
	}
	.ant-segmented-item {
		width: 100%;
	}
`;

export default RealTimeIndustryDataStyled;
