import styled from 'styled-components';
import iconSelect from '@renderer/images/icon/select.png';
import iconSelectUn from '@renderer/images/icon/selectUn.png';
import background from '@renderer/images/background';

export const StreetDeregulationWrapStyled = styled.div`
	position: absolute;
	right: 200px;
	top: 40px;
	z-index: 199;
	width: 450px;
	height: auto;
	/* background-color: rgb(10, 39, 66); */
	background: linear-gradient(to right, #0b2945, #0f334a);
	padding: 10px 0 0 10px;
	border-radius: 5px;
	border: 2px solid rgba(9, 132, 182, 0.7);
	.buttonclicked {
		position: absolute;
		right: -5px;
		top: -5px;
		background: url(${background.arrowhead}) no-repeat;
		background-size: 50% 50%;
		background-position: center center;
		width: 64px;
		height: 64px;
		transform: rotate(0deg);
		cursor: pointer;
		&.rotated {
			transform: rotate(180deg);
			transition: transform 0.5s ease;
		}
	}

	.ant-radio-group {
		border: 0;
		border-radius: 0;
		.ant-radio-button-wrapper {
			border: 1px solid #2b8eff;
			margin-right: 10px;
			border-radius: 0;
			span {
				color: #e2f0ff;
			}
			&.ant-radio-button-wrapper-checked {
				span {
					color: #e2f0ff;
				}
			}
		}
	}
`;

export const StreetDeregulationListStyled = styled.div`
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	color: #fff;
	flex-direction: column;
	/* padding-top: 20px; */
	.echarts-map {
		width: 30%;
	}

	.echarts-line {
		width: 90%;
		padding-top: 10px;
		display: flex;
		.echarts-title {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 20px;
			font-size: 14px;
			line-height: 20px;
			color: #fff;
			cursor: pointer;

			span {
				width: 70px;
				display: flex;
				justify-content: center;
				flex-shrink: 0;
				margin: 0 10px;
			}
		}
		.tabs {
			margin-top: 5px;
			width: 30px;
			display: flex;
			flex-direction: column;
			margin-right: 20px;
			cursor: pointer;
			align-items: center;
			height: 100%;
			justify-content: center;
			.tabs-item {
				line-height: 14px;
				padding: 4px 5px;
				color: #b6c1d2;
				font-size: 14px;
				margin-top: 40px;
			}
			img {
				width: 16px;
				padding: 0 2px 3px;
			}
			a {
				border-right: 2px solid rgba(255, 255, 255, 0.1);
			}
			.active {
				border-right: 2px solid #3ebdcb;
				color: #3ebdcb;
			}
		}
		.title-select {
			background: url(${iconSelect}) no-repeat center center;
			background-size: contain;
		}
		.title-un-select {
			background: url(${iconSelectUn}) no-repeat center center;
			background-size: contain;
		}
	}
	.middle-container,
	.right-container {
		width: 100%;
		padding-right: 10px;
		box-sizing: border-box;
		padding-top: 10px;
		.Violation {
			font-size: 18px;
			font-weight: 400;
			/* font-family: 'TimesNewRoman',PingFangSC-Regular,PingFang SC; */
			color: #ffffff;
			padding-left: 10px;
			border-bottom: 2px solid;
			width: 65%;
			border-image: linear-gradient(
					340deg,
					rgba(255, 255, 255, 0),
					rgba(255, 255, 255, 0.1),
					rgba(255, 255, 255, 1),
					rgba(255, 255, 255, 0),
					rgba(151, 151, 151, 1)
				)
				1 1;
			margin-left: 0px;
			position: relative;
			height: 38px;
			&::before {
				content: '';
				position: absolute;
				top: -12px;
				left: -13px;
				background: url(${background.arrow}) no-repeat;
				width: 47px;
				height: 56px;
			}
			span {
				position: absolute;
				right: 34%;
				top: 2px;
				font-size: 22px;
				background: linear-gradient(
					180deg,
					#ffffff 0%,
					#c3dff4 46%,
					#78d9ff 100%
				);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				font-weight: 600;
			}
			.Violation1 {
				border-bottom: none;
				font-size: 20px;
				font-weight: 400;
				color: #c3dff4;
				line-height: 32px;
				padding-left: 21px;
				letter-spacing: 1px;
				white-space: nowrap;
			}
			.line {
				width: 95px;
				height: 2px;
				border: none;
				filter: blur(0.5px);
				position: relative;
				left: -10px;
				top: 2px;
			}
		}
	}
	.right-container {
		padding-bottom: 10px;
	}
	.table {
		/* background: linear-gradient(180deg, rgba(13,130,234,0.31) 0%, rgba(9,30,59,0) 100%); */
		/* background: linear-gradient(180deg, rgba(13,130,234, 1) 0%, rgba(9,30,59, 0.1) 100%); */
		border-radius: 7px 7px 0 0;
	}
	dl {
		border: 2px solid rgba(9, 132, 182, 0.7);
		border-radius: 7px 7px 0 0;
		border-bottom: 0;
		width: 100%;
		box-sizing: border-box;
		margin-top: 10px;
		margin-bottom: 0;
		dt {
			display: flex;
			background-color: #060026d4;
			font-family: PingFangSC-Regular, PingFang SC;
			color: #d8f1ff;
			letter-spacing: 1px;
			font-size: 20px;
			padding: 4px 0;
			span {
				text-align: center;
				line-height: 26px;
				&.no1 {
					width: 18%;
				}
				&.no2 {
					width: 29%;
				}
				&.no3 {
					width: 25%;
				}
				&.no4 {
					width: 28%;
				}
			}
		}
		dd {
			display: flex;
			border-bottom: 1px solid rgba(9, 132, 182, 0.7);
			background-color: #0f334a;
			margin: 0;
			height: 29px;
			color: #d4d4d4;
			cursor: pointer;
			/* font-size: 20px; */
			align-self: center;
			/* align-items: center; */
			&:hover {
				background: rgba(58, 218, 255, 0.2);
			}
			span {
				text-align: center;
				line-height: 30px;
				&.no1 {
					width: 18%;
					&.top0 {
						color: rgb(213, 46, 76);
					}
					&.top1 {
						color: rgb(229, 101, 16);
					}
					&.top2 {
						color: rgb(203, 146, 28);
					}
				}
				&.no2 {
					width: 29%;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				&.no3 {
					width: 25%;
				}
				&.no4 {
					width: 28%;
				}
				&:last-of-type {
					/* border-bottom: 0; */
				}
			}
		}
	}
`;
