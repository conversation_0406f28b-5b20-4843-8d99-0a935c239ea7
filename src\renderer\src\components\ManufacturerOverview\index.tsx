import React, { useEffect, useState, useMemo, useContext } from 'react';
import { XyzTransition, XyzTransitionGroup } from '@animxyz/react';
import Box from '../../baseUI/Box';
import Container from './style';
import imgs from '../../images/users';
// import Echarts from '@renderer/components/echarts'
// import imags from '../../images/networkedData/index'
// import LevelTwoTitle from '../../components/LevelTwoTitle'
import { ManufacturersComponent } from './components/ManufacturersComponent';
import ManufacturersEcharts from './components/ManufacturersEcharts';
import { getUserCarTop, getCompany, getVehicleBrand } from '@renderer/api';
import { PageContext } from '@renderer/context';
/**
 * 厂商概况
 */
type overviewProps = {
	img: string;
	name: string;
	color: string;
	key: string;
	num?: string;
};
type detailItemProps = {
	name: string;
	vehicle_type_nums: number;
	user_nums: number;
	rate: number;
};
type resProps = {
	overview: {
		total_vehicle_type_nums: number;
		total_user_nums: number;
		total_company_nums: number;
	};
	detail: detailItemProps[];
};
type userCarProps = {
	name: string;
	nums: number;
};
const ManufacturersAndUsers = () => {
	const { countyId } = useContext(PageContext);
	// const color = ['#FF6B45', '#01E2FF', '#03FF9F', '#F09E38', '#ffa800', '#EC6B9E', '#CEE2E9;']
	const [overview, setOverview] = useState<Array<overviewProps>>([]);
	const [userData, setUserData] = useState([]);

	// const [userCar, setUserCar] = useState<any>([
	//   { id: 1, name: '北京公交集团第分公司', number: '300' },
	//   { id: 2, name: '北京公交集团第分公司', number: '300' },
	//   { id: 3, name: '北京公交集团第分公司', number: '300' },
	//   { id: 4, name: '北京公交集团第分公司', number: '300' },
	//   { id: 5, name: '北京公交集团第分公司', number: '300' }
	// ])
	// const [titleData, setTilleData] = useState<Array<overviewProps>>([
	//   {
	//     img: imgs.group,
	//     name: '厂商数量',
	//     color: ' #01E2FF',
	//     key: 'total_company_nums'
	//   },
	//   {
	//     img: imgs.car,
	//     name: '车型数量',
	//     color: '#01E2FF',
	//     key: 'total_vehicle_type_nums'
	//   },
	//   {
	//     img: imgs.user,
	//     name: '用户数量',
	//     color: '#01E2FF',
	//     key: 'total_user_nums'
	//   }
	// ])

	// const [options, setOption] = useState({})

	useEffect(() => {
		getVehicleBrand({}).then((res) => {
			const data = {
				vehicle_num: res?.vehicle_num || 0,
				brand_num: res?.brand_num || 0,
			};
			setOverview(data);
			const userData = res.rate.map((item) => {
				return {
					name: item.name,
					value: Math.floor(item.value * 100),
				};
			});
			setUserData(userData);
		});

		// getCompany({
		//   countyId
		// }).then((res) => {
		//   const overview = (res as resProps).overview
		//   const data = titleData.map((item) => {
		//     return { ...item, num: overview[item.key] }
		//   })
		//   setOverview(data)
		//   const data2 = (res as resProps).detail.map((item) => {
		//     return {
		//       name: item.name,
		//       value: (item.rate * 100).toFixed(0)
		//     }
		//   })
		//   setUserData(data2)
		// })
		// let sortColor = ''
		// getUserCarTop({ top: 5, countyId }).then((res) => {
		//   if ((res as userCarProps[]).length >= 5) {
		//     const newItem = (res as userCarProps[]).slice(0, 5).map((item, index) => {
		//       if (index == 0) {
		//         sortColor = '#FE2C46'
		//       } else if (index == 1) {
		//         sortColor = '#FF6703'
		//       } else if (index == 2) {
		//         sortColor = '#FFAC0B'
		//       } else {
		//         sortColor = '#9195A3'
		//       }
		//       return { ...item, color: sortColor }
		//     })
		//     setUserCar(newItem)
		//   }
		// })
	}, [countyId]);

	// useEffect(() => {
	//   const option = {
	//     tooltip: {
	//       trigger: 'item'
	//     },
	//     // legend: {
	//     //   top: '5%',
	//     //   left: 'center'
	//     // },
	//     color: color,
	//     series: [
	//       {
	//         //name: 'Access From',
	//         type: 'pie',
	//         radius: ['50%', '70%'],
	//         avoidLabelOverlap: false,
	//         itemStyle: {
	//           borderWidth: 5,
	//           borderRadius: 0,
	//           shadowBlur: 3
	//           // borderColor: color[i],
	//           //shadowColor: color[i],
	//         },
	//         label: {
	//           show: false,
	//           position: 'center'
	//         },
	//         // emphasis: {
	//         //   label: {
	//         //     show: true,
	//         //     fontSize: 40,
	//         //     fontWeight: 'bold'
	//         //   }
	//         // },
	//         labelLine: {
	//           show: false
	//         },
	//         data: userData
	//       }
	//     ]
	//   }
	//   setOption(option)
	// }, [userData])

	return (
		<Box title="厂商概况" titlewidth="95%" height="30%">
			<Container>
				<div className="left">
					{/* <LevelTwoTitle title='客车' width='90%'  />
          <div className="tip-container">
            {overview.map((item, index) => {
              return (
                <div key={index} className="frontPage">
                  <img src={item.img}></img>
                  <span className="name">{item.name}</span>
                  <span className="nums" style={{ color: item.color }}>
                    {item?.num}
                  </span>
                  <div className="line" style={{ background: item.color }}></div>
                </div>
              )
            })}
          </div>
          <div className="echarts-container">
            <div className="echarts">
              <Echarts option={options}></Echarts>
            </div>
            <div className="legend">
              {userData?.map((item, index) => {
                return (
                  <div key={index} className="legend-item">
                    <div className="legend-line" style={{ '--color': color[index] }}></div>
                    <div className="legend-name">
                      <span>{item.name}</span>
                      <span>{item.value}%</span>
                    </div>
                  </div>
                )
              })}
            </div>
          </div> */}
					<ManufacturersComponent overview={overview} />
				</div>

				<div className="right">
					{/* <LevelTwoTitle title = '用车大户TOP 5' width='100%'  /> */}

					{/* <div className="user-car">
            {userCar.map((item, index) => {
              return (
                <div key={index + item.id} className="user-car-item">
                  <span className="sort" style={{ color: item.color }}>
                    {index+1}
                  </span>
                  <span title={item.name}>{item.name}</span>
                  <span>{item.nums}140 </span>
                  <span>辆 </span>
                </div>
              )
            })}
          </div> */}
					{userData.length ? (
						<ManufacturersEcharts userData={userData} />
					) : null}
				</div>
			</Container>
		</Box>
	);
};
export default ManufacturersAndUsers;
