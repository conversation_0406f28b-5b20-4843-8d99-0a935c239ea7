import React, { useRef, useEffect, useState, useContext } from 'react';
import GridsPolygon from '@renderer/marsLayers/base/gridsPolygon';
import GridsIcon from '@renderer/marsLayers/base/gridsIcon';
import { MapContext } from '@renderer/context';
import { getWarnGrid } from '@renderer/api';

type Props = {
	map: {
		flyHome: () => void;
		getLayers: () => any[];
		addLayer: (layer) => void;
	};
	layerId: string;
	visible: boolean;
	// url: string
};

type RefProps = {
	create: () => void;
	remove: () => void;
	renders: (data: any[]) => void;
};

export default (props: Props) => {
	const { visible, map, layerId } = props;
	const { navList, setCurrentLayerId } = useContext(MapContext);
	setCurrentLayerId(layerId);
	const [popupData, setPopupData] = useState<
		{ LayerId: string; [propName: string]: any } | false
	>(false);
	const layer1_Ref = useRef<RefProps>();
	const layer2_Ref = useRef<RefProps>();

	const getWarnGridData = (json) => {
		getWarnGrid(json)
			.then((res) => {
				if (res.length) {
					if (json.warn_grid_type_id === 1) {
						layer1_Ref.current.renders([res, setPopupData]);
					} else {
						layer2_Ref.current.renders([res, setPopupData]);
					}
				}
			})
			.catch((err) => {
				console.log('err', JSON.stringify(err));
			});
	};

	useEffect(() => {
		if (!visible) return;
		layer1_Ref.current = new GridsPolygon({
			name: layerId + 'Polygon',
			map,
		});
		layer1_Ref.current.create();
		layer2_Ref.current = new GridsIcon({
			name: layerId,
			map,
		});
		layer2_Ref.current.create();

		getWarnGridData({
			pollutant_id: navList[0].id,
			warn_grid_type_id: 1,
		});
		getWarnGridData({
			pollutant_id: navList[0].id,
			warn_grid_type_id: 2,
		});

		return () => {
			layer1_Ref.current.remove();
			layer1_Ref.current = null;
			layer2_Ref.current.remove();
			layer2_Ref.current = null;
			setCurrentLayerId('');
		};
	}, [visible, navList[0]]);

	return null;
};
