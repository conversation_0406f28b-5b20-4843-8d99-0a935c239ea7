import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { Map, ChinaCRS, Cesium } from 'mars3d';
import { SceneMode } from 'mars3d-cesium';

import { MapContext } from '@renderer/context';
import { MapContainer } from './styles';

import 'mars3d/dist/mars3d.css';
import 'mars3d-cesium/Build/Cesium/Widgets/widgets.css';

Cesium.Ion.defaultAccessToken =
	'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.1Bbkalt4CSbNBrGE6nH6vQqKJ_jmnnQ-p5T16JIIxWM';

export const MapProvider = ({ children }) => {
	const [mounted, setMounted] = useState<boolean>(false);
	const [map, setMap] = useState<Map>();
	const [sceneMode, setSceneMode] = useState<SceneMode>(SceneMode.SCENE3D);

	const mapRef = useRef<HTMLDivElement>(null);

	// 初始化地图
	useLayoutEffect(() => {
		if (mapRef.current === undefined) return;
		const map = new Map(mapRef.current, {
			method: {
				chinaCRS: ChinaCRS.GCJ02,
			},
			scene: {
				resolutionScale: 4 / 3,
				showSkyAtmosphere: false,
			},
			control: {
				baseLayerPicker: false,
			},
			basemaps: [],
			layers: [],
		});

		map.camera.setView({
			destination: Cesium.Cartesian3.fromDegrees(
				116.550727,
				40.268658,
				585245.302974,
			),
		});

		map.unbindContextMenu();

		setMap(map);
		setMounted(true);

		return () => {
			map.destroy();
		};
	}, []);

	useEffect(() => {
		if (!mounted) return;
		map.scene.mode = sceneMode;
	}, [mounted, map, sceneMode]);

	return (
		<MapContext.Provider
			value={{
				map,
				mounted,
				sceneMode,
				setSceneMode,
			}}
		>
			<MapContainer ref={mapRef}>{mounted && children}</MapContainer>
		</MapContext.Provider>
	);
};
