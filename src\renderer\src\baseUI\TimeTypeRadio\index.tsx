import React, { useEffect, useState } from 'react';
import { Radio } from 'antd';
import { TimeTypeRadioContent } from './style';

export default function IndustryEmissions(props) {
	const { type, typeOptions, timeType } = props;

	const defaultTypeOptions = [
		{
			label: '当日',
			value: 'day',
		},
		{
			label: '24小时',
			value: '24hours',
		},
		{
			label: '7天',
			value: '7day',
		},
	];
	const [typeOption, setTypeOptions] = useState(defaultTypeOptions);

	const [defaultType, setDefaultType] = useState(type ? type : 'day');
	useEffect(() => {
		typeOptions && setTypeOptions(typeOptions);
		timeType && timeType(type);
	}, [type]);

	const onTypeChange = (e) => {
		timeType(e.target.value);
		setDefaultType(e.target.value);
	};
	return (
		<TimeTypeRadioContent>
			<Radio.Group
				options={typeOption}
				onChange={onTypeChange}
				value={defaultType}
				optionType="button"
			/>
		</TimeTypeRadioContent>
	);
}
