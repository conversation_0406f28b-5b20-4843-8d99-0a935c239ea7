import { useEffect, useRef } from 'react';

import * as echarts from 'echarts';
import { geoJson } from './data';

import { MapEcharts } from './style';
const l = [
	{ name: '东城区', value: 5616 },
	{ name: '西城区', value: 1785 },
	{ name: '朝阳区', value: 5764 },
	{ name: '海淀区', value: 2670 },
	{ name: '丰台区', value: 5316 },
	{ name: '石景山区', value: 720 },
	{ name: '门头沟区', value: 978 },
	{ name: '房山区', value: 11750 },
	{ name: '通州区', value: 12825 },
	{ name: '顺义区', value: 11403 },
	{ name: '大兴区', value: 9298 },
	{ name: '昌平区', value: 5069 },
	{ name: '平谷区', value: 2981 },
	{ name: '怀柔区', value: 4459 },
	{ name: '密云区', value: 3815 },
	{ name: '延庆区', value: 2197 },
	{ name: '开发区', value: 586 },
];

const AreaMap = ({ mapData = l, max = 12000 }) => {
	const chartRef = useRef(null);

	useEffect(() => {
		if (!chartRef?.current || !mapData) return;
		echartStart();
	}, [chartRef, mapData]);

	const echartStart = () => {
		const option = {
			geo: {
				show: false,
				map: 'beijing',
				label: {
					normal: {
						show: false,
					},
					emphasis: {
						show: false,
					},
				},
				scaleLimit: {
					//缩放级别
					min: 0.5,
					max: 2,
				},
			},
			grid: {
				left: 0,
				right: 0,
				top: 0,
				bottom: 0,
			},
			visualMap: {
				show: false,
				min: 0,
				max,
				left: 'left',
				top: 'bottom',
				text: ['高', '低'], // 文本，默认为数值文本
				calculable: false,
				inRange: {
					color: [
						'#0a10af',
						'#FE619F',
						'#0aa179',
						'#b9e310',
						'#5d76c8',
						'#FF5E34',
						'#a94e04',
					],
				},
				textStyle: {
					color: '#ffffff',
				},
			},
			series: [
				{
					type: 'map',
					roam: false,
					label: {
						show: true,
						color: '#fff',
						fontSize: 10,
						width: 100,
						height: 30,
						lineHeight: 18,
					},
					itemStyle: {
						normal: {
							areaColor: '#003669',
							borderColor: '#3fdaff',
							borderWidth: 1,
							shadowColor: 'rgba(63, 218, 255,0.3)',
							shadowBlur: 20,
						},
					},
					emphasis: {
						label: {
							color: '#fff',
						},
						itemStyle: {
							areaColor: '#003669',
							borderColor: '#ebae51',
							borderWidth: 3,
						},
					},
					select: {
						selectedMode: 'single',
						smooth: true,
						label: {
							color: '#fff',
						},
						itemStyle: {
							areaColor: '#003669',
							borderColor: '#ebae51',
							borderWidth: 3,
						},
					},
					data: mapData,
					zoom: 0.8,
					map: 'beijing', //使用
				},
			],
		};
		const myChart = echarts.init(chartRef.current);
		myChart.showLoading();
		echarts.registerMap('beijing', geoJson);
		myChart.hideLoading();
		myChart.setOption(option, true);
	};

	return (
		<MapEcharts>
			<div
				ref={chartRef}
				style={{
					width: '300px',
					height: '300px',
					position: 'absolute',
					top: '50%',
					left: '50%',
					transform: 'translate(-50%, -50%)',
				}}
			></div>
		</MapEcharts>
	);
};
export default AreaMap;
