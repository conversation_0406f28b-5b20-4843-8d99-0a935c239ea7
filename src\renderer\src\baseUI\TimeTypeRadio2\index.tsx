import React, { useEffect, useState } from 'react';
import { Radio } from 'antd';
import moment from 'moment';
import { TimeTypeRadioContent } from './style';

const typeOption = [
	{
		label: '当日',
		value: '1',
	},
	{
		label: '24小时',
		value: '3',
	},
	{
		label: '7天',
		value: '2',
	},
];

const industryViolationsTypeOption = [
	{
		label: '7天',
		value: '2',
	},
];

export default function IndustryEmissions(props) {
	const { defaultValue, timeType, industryViolationsType } = props;
	const [type, setType] = useState(defaultValue);

	useEffect(() => {
		let time;
		switch (type) {
			case '1':
				time = {
					start_time: moment().format('YYYY-MM-DD 00:00:00'),
					end_time: moment().format('YYYY-MM-DD HH:00:00'),
				};
				break;
			case '2':
				time = {
					start_time: moment().add(-7, 'd').format('YYYY-MM-DD 00:00:00'),
					end_time: moment().format('YYYY-MM-DD HH:00:00'),
				};
				break;
			case '3':
				time = {
					start_time: moment().add(-24, 'h').format('YYYY-MM-DD HH:00:00'),
					end_time: moment().format('YYYY-MM-DD HH:00:00'),
				};
				break;
			default:
				break;
		}
		if (industryViolationsType === '2') {
			timeType &&
				timeType({
					start_time: moment().add(-7, 'd').format('YYYY-MM-DD 00:00:00'),
					end_time: moment().format('YYYY-MM-DD HH:00:00'),
					time_type: '2',
				});
		} else {
			timeType && timeType({ ...time, time_type: type === '3' ? '1' : type });
		}
	}, [type, industryViolationsType]);

	const onTypeChange = (e) => {
		setType(e.target.value);
	};

	return (
		<TimeTypeRadioContent>
			<Radio.Group
				options={
					industryViolationsType === '2'
						? industryViolationsTypeOption
						: typeOption
				}
				onChange={onTypeChange}
				value={industryViolationsType !== '2' ? type : industryViolationsType}
				optionType="button"
			/>
		</TimeTypeRadioContent>
	);
}
