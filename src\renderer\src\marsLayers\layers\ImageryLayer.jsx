// 气象-热点网格
// 气象-气象等温
// 气象-气象等湿
// 气象-气象等压
// 气象-气象反演
import React, { useEffect, useContext, useState, useRef } from 'react';
import { Cartesian3, ImageMaterialProperty, Math } from 'mars3d-cesium';
import moment from 'moment';
import { getMeteorAllPic } from '@renderer/api';
import { MapContext } from '@renderer/context';
import { useAuth } from '@renderer/hooks/useAuth';
import humidity from '@renderer/images/layer/humidity.png';
import styled from 'styled-components';
import inversion1 from '@renderer/images/layer/air_inversion1.png';
import inversion from '@renderer/images/layer/inversion.png';
// import inversion2 from '@renderer/images/layer/air_inversion2.png'

const orientation = {
	heading: Math.toRadians(0),
	pitch: Math.toRadians(-35),
	roll: 0.0,
};

export default (props) => {
	const { visible, map, layerId } = props;
	const { navList, sceneMode } = useContext(MapContext);
	const layerRef = useRef();
	const { token } = useAuth();
	const rectangleCoordinate = [
		117.5082261, 41.0592191, 0, 117.5082261, 39.442185, 0, 115.4168938,
		39.442185, 0, 115.4168938, 41.0592191, 0,
	];
	// , 117.5082261, 41.0592191, 0
	const getMeteorAllPicData = (parame) => {
		getMeteorAllPic(parame)
			.then((res) => {
				if (res.length) drawImageryLayer(res[0], parame.json.picType);
			})
			.catch((err) => {
				console.log('err', JSON.stringify(err));
			});
	};

	const legend = () => {
		switch (layerId) {
			case 'humidity':
				return (
					<LegendStyle>
						<img src={humidity} alt="" />
					</LegendStyle>
				);
			case 'air_inversion':
				return (
					<LegendStyle>
						<img src={inversion} alt="" />
					</LegendStyle>
				);
			default:
				return null;
		}
	};

	const drawImageryLayer = (data, type) => {
		layerRef.current = {
			id: `${layerId}imageryProvider`,
			name: 'imageryProvider',
			polygon: {
				hierarchy: Cartesian3.fromDegreesArrayHeights(rectangleCoordinate),
				// perPositionHeight: true,
				material: new ImageMaterialProperty({
					image: data[type],
					transparent: true,
				}),
				outline: false,
				outlineWidth: 2,
			},
		};
		map.entities.add(layerRef.current);
	};

	const drawInversionImageryLayer = () => {
		layerRef.current = {
			id: `${layerId}imageryProvider`,
			name: 'imageryProvider',
			polygon: {
				hierarchy: Cartesian3.fromDegreesArrayHeights(rectangleCoordinate),
				// perPositionHeight: true,
				material: new ImageMaterialProperty({
					image: inversion1,
					transparent: true,
				}),
				outline: false,
				outlineWidth: 2,
			},
		};
		map.entities.add(layerRef.current);
	};

	useEffect(() => {
		if (!visible) return;
		map.entities.remove(layerRef.current);
		map.camera.flyTo({
			destination:
				sceneMode === 3
					? Cartesian3.fromDegrees(116.551734, 38.357551, 130000)
					: Cartesian3.fromDegrees(116.701734, 40.257551, 500000),
			orientation: sceneMode === 3 ? orientation : {},
			duration: 4,
		});

		if (layerId === 'air_inversion') {
			drawInversionImageryLayer();
		} else {
			let picType = '';
			switch (layerId) {
				case 'temp': // 温度
					picType = 'CLIP_TMP_PRES';
					break;
				case 'humidity': // 湿度
					picType = 'CLIP_RH';
					break;
				case 'air_pressure': // 气压
					picType = 'CLIP_PRES';
					break;
				default:
					break;
			}
			getMeteorAllPicData({
				json: {
					token,
					provinceId: '1',
					cityId: '1',
					countyId: '0',
					dateTime: moment().subtract(1, 'hour').format('YYYY-MM-DD HH:00:00'),
					picType,
				},
			});
		}

		return () => {
			map.entities.remove(layerRef.current);
		};
	}, [visible, navList[0]]);

	return visible && legend();
};

const LegendStyle = styled.div`
	width: 519px;
	height: 86px;
	display: inline-block;
	position: absolute;
	left: 60%;
	bottom: -15px;
	z-index: 3;
	transform: scale(0.8);
	img {
		width: 100%;
		height: 100%;
		overflow: hidden;
	}
`;
