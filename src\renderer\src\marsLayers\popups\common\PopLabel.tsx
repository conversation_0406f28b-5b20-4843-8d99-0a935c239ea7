import React from 'react';
import { Tooltip } from 'antd';

import styled from 'styled-components';

type Props = {
	aloneRow?: Array<Item>;
	multiline?: Array<Item>;
	data?: Array<Item1>; //自定义每行的宽是整行还是半行 true 为整行
};

type Item = {
	label: string;
	value: string;
	color?: string;
	tip?: boolean;
	filter?: boolean; //是否保留两位小数
};
type Item1 = {
	label: string;
	value: string;
	color?: string;
	tip?: boolean;
	filter?: boolean;
	isRow?: boolean | undefined | null;
};
const PopLabelContainer = styled.div`
	width: 100%;
	/* height: 100%; */
	font-size: 22px;
	font-family: PingFangSC-Regular, PingFang SC;
	font-weight: 500;
	line-height: 25px;
	padding: 30px 35px 0;
	.label {
		color: #65c2ff;
		font-weight: 400;
	}
	.multiline {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		.item {
			width: 50%;
			margin-bottom: 16px; //大小待定
		}
	}
	p {
		margin-bottom: 16px;
		&:last-of-type {
			margin-bottom: 0;
		}
	}

	.oneLine {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		overflow: hidden;
		margin-bottom: 16px; //大小待定
	}
	.data {
		display: flex;
		flex-wrap: wrap;

		li {
			margin-bottom: 16px; //大小待定
			&:last-of-type {
				margin-bottom: 0;
			}
		}
	}
`;

export default function PopLabel(props: Props) {
	const { aloneRow, multiline, data } = props;

	const numFilter = (value) => {
		return Number(value).toFixed(2);
	};

	return (
		<PopLabelContainer>
			<div className="multiline">
				{multiline?.map((it, index) => (
					<div className="item" key={index}>
						<span className="label">{it.label}</span>：
						<span
							style={{
								color: it['color'] || '#fff',
							}}
						>
							{it.filter ? numFilter(it.value) : it.value}
						</span>
					</div>
				))}
			</div>
			{aloneRow?.map((it, index) => {
				return (
					<Tooltip
						key={index}
						title={it.tip ? it.value : null}
						color="#213340"
						overlayClassName="ant-tooltip-content"
					>
						<p className={it.tip ? 'oneLine' : ''}>
							<span className="label">{it.label}</span>：
							<span
								style={{
									color: it['color'] || '#fff',
								}}
							>
								{it.filter ? numFilter(it.value) : it.value}
							</span>
						</p>
					</Tooltip>
				);
			})}
			{data ? (
				<ul className="data">
					{data.map((it, index) => {
						return (
							<li style={{ width: it.isRow ? '100%' : '50%' }} key={it.label}>
								<span className="label">{it.label}</span>：
								<span
									style={{
										color: it['color'] || '#fff',
									}}
								>
									{it.filter ? numFilter(it.value) : it.value}
								</span>
							</li>
						);
					})}
				</ul>
			) : (
				''
			)}
		</PopLabelContainer>
	);
}
