// import { getCarColor } from '../../../../../main/data'

export const getStreetPop = (value) => {
	if (!value) return {};

	return {
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'none',
			},
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',
			padding: 8,
			textStyle: {
				color: '#fff',
			},
		},
		legend: {
			data: value?.text,
			textStyle: {
				fontSize: 16,
				color: '#E2F0FF',
			},
			pageTextStyle: {
				color: '#fff', // 文字样式
			},
		},
		grid: {
			top: 32,
			bottom: 0,
			left: 0,
			right: 50,
			containLabel: true,
		},
		xAxis: [
			{
				type: 'value',
				name: 'per',
				data: value?.per,
				min: 0,
				axisPointer: {
					type: 'shadow',
				},
				axisLabel: {
					textStyle: {
						color: '#C6C7C7',
						fontSize: 16,
					},
				},
			},
			{
				type: 'value',
				name: '环比',
				axisLabel: {
					textStyle: {
						color: '#E2F0FF',
						fontSize: 16,
					},
					formatter: '{value} %',
				},
				splitLine: {
					show: false,
				},
			},
		],
		yAxis: [
			{
				name: value?.unit,
				nameTextStyle: {
					color: '#D8DBDE',
					fontSize: 16,
					padding: [18, 0],
				},
				type: 'category',
				data: value.adm,
				axisLabel: {
					// interval: 0,
					textStyle: {
						color: '#E2F0FF',
						fontSize: 17,
					},
				},
				splitLine: {
					show: true,
					lineStyle: {
						color: 'rgba(222, 227, 239, 0.3)',
						type: [2, 4],
					},
				},
			},
		],
		series: [
			{
				name: value?.text[0],
				type: 'bar',
				itemStyle: {
					opacity: 1,
					color: '#61C6FF',
				},
				barWidth: 5,
				data: value?.count,
			},
			{
				name: value?.text[1],
				type: 'bar',
				itemStyle: {
					opacity: 1,
					color: '#B186FF',
				},
				barWidth: 5,
				data: value?.dod,
			},
			{
				name: value?.text[2],
				type: 'line',
				tooltip: {
					backgroundColor: 'rgba(13, 64, 71, 0.50)',
					borderColor: 'rgba(143, 225, 252, 0.60)',
					padding: 8,
					textStyle: {
						color: '#fff',
					},
				},
				xAxisIndex: 1,
				smooth: true,
				symbol: 'circle',
				symbolSize: 8,
				color: '#FFCC85',
				data: value?.per,
			},
		],
	};
};
