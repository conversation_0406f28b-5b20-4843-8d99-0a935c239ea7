import { useContext, useEffect, useState } from 'react';
import { Routes, Route } from 'react-router-dom';

import { AppProvider, AuthProvider } from './provider';
import { ProtectedRoute } from './router';

import HomePage from '@renderer/pages';
import LoginPage from '@renderer/pages/login';

const App = () => {
	return (
		<AppProvider>
			<AuthProvider>
				<Routes>
					<Route
						path="/"
						element={
							<ProtectedRoute>
								<HomePage />
							</ProtectedRoute>
						}
					/>
					<Route path="/login" element={<LoginPage />} />
				</Routes>
			</AuthProvider>
		</AppProvider>
	);
};

export default App;
