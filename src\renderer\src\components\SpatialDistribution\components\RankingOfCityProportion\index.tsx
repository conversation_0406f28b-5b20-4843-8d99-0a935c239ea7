import { useEffect } from 'react';
import { calculatePercentage } from '@renderer/hooks';

interface mapValue {
	name: string;
	value: number;
}

type Props = {
	setRankingOption: React.Dispatch<React.SetStateAction<object>>;
	mapData: mapValue[];
};

const RankingOfCityProportion = (props: Props) => {
	const { setRankingOption, mapData } = props;
	// 数字转化添加单位
	const tranNumber = (num, point) => {
		// 将数字转换为字符串,然后通过split方法用.分隔,取到第0个
		const numStr: any = num.toString().split('.')[0];
		if (numStr.length < 6) {
			// 判断数字有多长,如果小于6,,表示10万以内的数字,让其直接显示
			return numStr;
		} else if (numStr.length >= 6 && numStr.length <= 8) {
			// 如果数字大于6位,小于8位,让其数字后面加单位万
			const decimal: any = numStr.substring(
				numStr.length - 4,
				numStr.length - 4 + point,
			);
			// 由千位,百位组成的一个数字
			const a: any = num / 10000;
			return parseFloat(parseInt(a) + '.' + decimal).toFixed(1) + '万';
		} else if (numStr.length > 8) {
			// 如果数字大于8位,让其数字后面加单位亿
			const decimal: any = numStr.substring(
				numStr.length - 8,
				numStr.length - 8 + point,
			);
			const a: any = num / 100000000;
			return parseFloat(parseInt(a) + '.' + decimal).toFixed(1) + '亿';
		}
	};
	useEffect(() => {
		let echartsData: Array<object>;
		let total: number;
		if (!mapData?.length) {
			echartsData = [];
			total = 0;
		} else {
			echartsData = calculatePercentage(mapData);
			total = tranNumber(
				mapData.map((item) => item.value).reduce((acc, curr) => acc + curr, 0),
				2,
			);
		}

		// const rich = {
		//   total: {
		//     color: '#ffc72b',
		//     fontSize: 30,
		//     align: 'center'
		//   },
		//   white: {
		//     color: '#fff',
		//     align: 'center',
		//     fontSize: 14,
		//     padding: [10, 0]
		//   },
		//   blue: {
		//     color: '#49dff0',
		//     fontSize: 16,
		//     align: 'center'
		//   }
		// }
		const option = {
			animation: true,
			tooltip: {
				show: true,
				trigger: 'item',
				textStyle: {
					fontSize: 16,
				},
				formatter: (params) => {
					return `${params.name} ${params.value}%`;
				},
			},
			title: [
				{
					text: '{total|' + total + '}\n{name|' + '总量' + '}',
					left: 'center',
					top: 'center',
					textStyle: {
						rich: {
							name: {
								fontSize: 24,
								fontWeight: 'normal',
								color: '#fff',
								padding: [-20, 0],
							},
							total: {
								color: '#ffc72b',
								fontSize: 26,
								align: 'center',
								padding: [20, 20],
							},
						},
					},
				},
			],
			series: [
				{
					name: '',
					type: 'pie',
					radius: ['58%', '75%'],
					top: '5%',
					color: [
						'#c487ee',
						'#deb140',
						'#49dff0',
						'#034079',
						'#6f81da',
						'#00ffb4',
					],
					labelLine: {
						normal: {
							length: 25,
						},
					},
					label: {
						normal: {
							backgroundColor: 'rgba(255, 147, 38, 0)',
							borderColor: 'transparent',
							borderRadius: 4,
							textStyle: {
								color: '#fff',
								fontSize: 16,
							},
						},
					},
					data: echartsData,
				},
			],
		};
		setRankingOption(option);
	}, [mapData]);

	return null;
};

export default RankingOfCityProportion;
