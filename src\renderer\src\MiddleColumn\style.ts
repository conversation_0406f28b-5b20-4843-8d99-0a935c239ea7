import styled from 'styled-components';
import leftImg from '@renderer/images/layout/layout-left.png';
import rightImg from '@renderer/images/layout/layout-right.png';
import popupBg2 from '@renderer/images/layer/popupBg2.png';

export const MapContainer = styled.div`
	position: relative;
	width: 100%;
	height: 100%;
	&::before {
		content: '';
		position: absolute;
		left: -810px;
		top: 28px;
		background-image: url(${leftImg});
		background-repeat: no-repeat;
		background-size: cover;
		width: 1042px;
		height: 899px;
		display: inline-block;
		z-index: 3;
	}
	&::after {
		content: '';
		position: absolute;
		right: -810px;
		top: 28px;
		background-image: url(${rightImg});
		background-repeat: no-repeat;
		background-size: cover;
		width: 1042px;
		height: 899px;
		display: inline-block;
		z-index: 3;
	}
	/* .mars3d-divGraphic {
    pointer-events: inherit !important;
    transform: none !important;
    width: 100%;
  } */
	.mars3d-hideDiv {
		position: relative !important;
	}
	.cesium-widget canvas:focus {
		outline: none !important;
	}
	.mars3d-contextmenu {
		.mars3d-contextmenu-ul {
			.contextmenu-item {
				a {
					padding: 5px 10px;
				}
			}
		}
	}
	&.stdg-point,
	&.stds-point,
	&.hight-point,
	&.weather_station-point {
		.mars3d-divlayer {
			.mars3d-divGraphic {
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%) !important;
				transform-origin: initial !important;
				.closeButton {
					width: 10px;
					height: 10px;
					display: block;
					position: absolute;
					right: -520px;
					top: -265px;
					z-index: 2;
					color: #fff;
					font-size: 30px;
					font-family: sans-serif;
					cursor: pointer;
				}
			}
		}
	}
	&.weather_station-point {
		.closeButton {
			top: -210px !important;
			right: -380px !important;
		}
		#weather_stationPopup {
			& > div {
				background-image: url(${popupBg2});
				background-position: 0 0;
				background-repeat: no-repeat;
				width: 883px;
				height: 537px;
			}
		}
	}
	&.vehicleOnline-point {
		background: transparent;
		.mars3d-divGraphic {
			top: -20px !important;
		}
		.mars3d-popup-background {
			/* background: rgb(28 93 131 / 90%) !important; */
		}
		.mars3d-popup-content-wrapper {
			border-radius: 10px !important;
		}
		.mars3d-template-content > div {
			padding: 3px 0 !important;
		}
	}
	&.userEnterprise-point {
		.mars3d-divGraphic {
			top: -20px !important;
		}
		.mars3d-popup-background {
			background: rgb(28 93 131 / 90%) !important;
		}
		.mars3d-popup-content-wrapper {
			border-radius: 10px !important;
		}
		.mars3d-template-content > div {
			padding: 3px 0 !important;
		}
	}
`;
