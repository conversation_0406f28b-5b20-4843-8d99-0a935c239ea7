import React, { useEffect } from 'react';
import * as mars3d from 'mars3d';

export default ({ map }) => {
	if (!map) return null;

	useEffect(() => {
		map.addLayer(
			new mars3d.layer.XyzLayer({
				name: 'baseLayer',
				brightness: 2,
				url: 'https://thematic.geoq.cn/arcgis/rest/services/StreetThematicMaps/PurplishBlue_OnlySymbol/MapServer/tile/{z}/{y}/{x}',
			}),
		);
	}, []);

	return null;
};
