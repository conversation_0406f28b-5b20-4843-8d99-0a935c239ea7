import { ReactElement } from 'react';

export type ScrollListProps = {
	data: Data;
	columns: Columns;
	thheight?: string; // 表头的高
	OnRowSelection?: (data) => void; //data 为点击的行的信息对象
	autoscroll?: boolean; // 是否自动循环滚动
	scroll?: boolean; // 是否是可滚动的
	tablebgcolor?: string; // 表格背景
	rowClassName?: string; // 每一行表格内容的类名
	customwidth?: string | boolean; // 自定义每一列宽度，true:使用columns数据中每一项的宽，false：自动撑开，string：使用columns数据的宽，其他的设置为此固定宽
	rowbgcolor?: Array<string> | string; // 传入数组颜色时，循环展示颜色，只有一个颜色时，就是所有行都有相同的背景色。
};

export type Props = ScrollListProps & {
	width: string;
	height: string;
	trheight?: string;
	thbgcolor?: string; // 表头背景颜色
	thclassname?: string; // 表头的类名
	showheader?: boolean; // 是否展示表头
	stickyTop?: boolean; // 是否展示置顶信息
	stickyTopData?: Data; // 置顶信息内容
	stickyTopClassName?: string; // 置顶内容类名
	fontSize?: number;
	fontWeight?: number;
	color?: string;
};

export type Columns = {
	title: string; // 展示的标题内容
	dataIndex: string; // 字段名称
	width?: string; // customwidth为true 或string 时生效
	titleTips?: string; // 默认显示 dataIndex  可以传相应展示字段
	titleRender?: () => ReactElement; // 表头自定义渲染的内容
	render?: (title, item) => ReactElement; // 表的内容行中对应列的自定义渲染的内容
}[];

export type Data = {}[];
