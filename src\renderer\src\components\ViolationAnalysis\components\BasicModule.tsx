import { FC } from 'react';
import styled from 'styled-components';
import { Radio, CheckboxOptionType } from 'antd';

import { Columns, Data } from '@renderer/baseUI/ListWidthTitle/type';
import ListWidthTitle from '@renderer/baseUI/ListWidthTitle';

import { LayoutContainer } from '../RightSide/styles';

const optionTable = {
	width: '100%',
	height: '350px',
	fontSize: 18,
	thclassname: 'th_row',
	tablebgcolor: 'rgba(9,30,59,0)',
	trheight: '40px',
	thheight: '40px',
	customwidth: true,
	rowbgcolor: [
		'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
	],
};

interface IProps {
	title: string;
	columns: Columns;
	data: Data;
	options: Array<CheckboxOptionType>;
	onChange: (e: Event) => void;
}

const BasicModule: FC<IProps> = ({
	title,
	columns,
	data,
	options,
	onChange,
}) => {
	const handleChange = (e) => {
		onChange && onChange(e);
	};

	return (
		<>
			<div className="slidingLayer">
				<span className="slidtext">{title}</span>
				<ReferenceStyled>
					<div className="selectAssembly">
						<Radio.Group
							options={options}
							onChange={handleChange}
							defaultValue={options[0].value}
							optionType="button"
						/>
					</div>
				</ReferenceStyled>
			</div>
			<LayoutContainer>
				<div className="echarts-line">
					<ListWidthTitle columns={columns} data={data} {...optionTable} />
				</div>
			</LayoutContainer>
		</>
	);
};

export default BasicModule;

const ReferenceStyled = styled.div`
	.selectAssembly {
		width: 100%;
		margin-left: auto;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		box-shadow: inset 0px 0px 5px 0px #45eeff;
		border-radius: 6px 6px 6px 6px;
		border-image: radial-gradient(
				circle,
				rgba(85, 252, 255, 1),
				rgba(71, 211, 219, 1)
			)
			1 1;
		background: rgba(54, 121, 141, 0.23);
		border: 1px solid #3adaff;

		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
		}
		.ant-radio-group {
			display: flex;
		}
		.ant-radio-button-wrapper {
			width: 80px;
			border: 0;
			border-radius: 5px;
			font-size: 16px;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			span:hover {
				color: #3adaff;
			}
		}
		.ant-radio-button-wrapper-checked {
			background: radial-gradient(
				72% 90% at 50% 50%,
				#3bc3f4 0%,
				rgba(52, 112, 190, 0) 100%
			);
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
	}
`;
