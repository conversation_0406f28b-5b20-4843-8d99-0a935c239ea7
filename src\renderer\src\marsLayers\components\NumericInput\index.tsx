import React, { useEffect, useRef, useState } from 'react';
import { Input } from 'antd';
import _ from 'lodash';

interface NumericInputProps {
	style: React.CSSProperties;
	value: string;
	height: number;
	onChange: (value: string) => void;
}

const NumericInput = (props: NumericInputProps) => {
	const { onChange, value, height } = props;
	const inputRef = useRef<any>(null);
	const [valueInput, setValueInput] = useState(value);

	const debouncedOnInputChange = _.debounce((value) => {
		onChange(value);
	}, 300);

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { value: inputValue } = e.target;
		const reg = /^-?\d*(\.\d*)?$/;
		if (reg.test(inputValue) || inputValue === '' || inputValue === '-') {
			setValueInput(inputValue);
			debouncedOnInputChange(inputValue);
		}
	};

	useEffect(() => {
		if (inputRef.current) {
			inputRef.current.blur();
			setValueInput(value);
		}
	}, [height, value]);

	return (
		<Input
			{...props}
			ref={inputRef}
			onChange={handleChange}
			addonBefore={'当前最大值:'}
			value={valueInput}
			maxLength={16}
		/>
	);
};

export default NumericInput;
