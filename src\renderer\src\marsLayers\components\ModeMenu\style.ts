import styled from 'styled-components';
import background from '../../../images/background';
import icon from '../../../images/icon';

export const ModeMenuContent = styled.div`
	/* width: 46%; */
	width: 1022px;
	height: 5%;
	position: absolute;
	left: 50%;
	top: 150px;
	transform: translateX(-50%);
	z-index: 100;

	.layermenuButton {
		width: 317px;
		height: 40px;
		background: url(${icon.spread2});
		background-size: 317px 40px;
		background-repeat: no-repeat;
		/* position: absolute;
    left: 50%;
    transform: translateX(-50%); */
		margin: 0 auto 0;
		cursor: pointer;
	}

	.layerMenuContent {
		padding: 0.5%;
		min-height: 100px;
		background: url(${background.layerMenu});
		background-size: 100% 100%;
		padding-bottom: 15px;
		position: relative;
		.layersButton {
			width: 100%;
			font-size: 24px;
			font-weight: 500;
			color: #ffffff;
			display: flex;
			align-items: center;
			div {
				margin-left: 10%;
			}
		}

		.children {
			color: #38f3ff;
			display: flex;
			flex-wrap: wrap;
			.ant-spin-nested-loading {
				position: static !important;
			}
			.item {
				display: flex;
				align-items: center;
				margin: 0 15px 0 0;
				white-space: nowrap;
				font-size: 18px;
				> div:nth-child(1) {
					width: 12px;
					height: 12px;
					margin: 0 5px 0 0;
					border-radius: 50px;
				}
			}
		}

		.ant-tabs-nav::before {
			border-bottom: none;
		}

		.ant-tabs-tab-btn {
			width: 100%;
			height: 48px;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.ant-tabs {
			margin: 1% 2.5% 1.5%;
		}

		.ant-tabs-tab {
			border: 1px solid;
			border: 1px solid #3adaff;
			color: #ebf5ff;
			font-weight: 500;
			font-size: 24px;
			margin-right: 0.5%;
			width: 180px;
			height: 48px;
			padding: 0px 23px;
		}

		.ant-tabs-tab-active {
			background: url(${background.layerButton});
			background-size: 100% 100%;
		}
		:where(.css-dev-only-do-not-override-1m62vyb).ant-tabs-card.ant-tabs-top
			> .ant-tabs-nav
			.ant-tabs-tab {
			border-radius: 4px;
		}

		.five-point {
			position: absolute;
			bottom: 0;
			left: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			transform: translateX(-50%);
			display: none;
			li {
				width: 10px;
				height: 10px;
				background-color: #0298cd;
				overflow: hidden;
				margin: 0 5px;
				border-radius: 100%;
			}
		}
	}
`;
