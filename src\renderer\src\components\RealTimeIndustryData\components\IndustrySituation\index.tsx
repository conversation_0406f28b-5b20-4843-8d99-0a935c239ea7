import { useEffect, useState } from 'react';
import Echarts from '@renderer/components/echarts';
import type { TableProps } from 'antd';
import { tranNumber } from '@renderer/hooks';
import ScrollListWidthTitle from '@renderer/components/SlidingLayer/DailyActivityLevel/components/ScrollListWidthTitle';
import IndustrySituationStyled from './style';

type Props = {
	regionName: string;
	regionDetails: regionData[];
	setRegionName: any;
	industryDetails: Array<any>;
	unit: string;
	setShowIndustrySituation: React.Dispatch<React.SetStateAction<boolean>>;
};

interface regionData {
	name: string;
	value: undefined;
}

interface DataType {
	type: string;
	num: number;
	time: number;
	licheng: number;
	oi: number;
	paifang: number;
}

const IndustrySituation = (props: Props) => {
	const {
		regionName,
		setRegionName,
		unit,
		setShowIndustrySituation,
		regionDetails,
		industryDetails,
	} = props;
	const [data, setData] = useState([]);
	const [industrySituationOption, setIndustrySituationOption] = useState({});

	const columns: TableProps<DataType>['columns'] = [
		{
			title: '行业类型',
			dataIndex: 'type',
		},
		{
			title: '车辆数量',
			dataIndex: 'num',
		},
		{
			title: '里程',
			dataIndex: 'licheng',
		},
		{
			title: '排放量',
			dataIndex: 'paifang',
		},
		{
			title: '油耗',
			dataIndex: 'oi',
		},
		{
			title: '运行时长',
			dataIndex: 'time',
		},
	];

	const optionTable = {
		columns: columns,
		width: '100%',
		height: '206px',
		fontSize: 18,
		thclassname: 'th_row',
		tablebgcolor: 'rgba(9,30,59,0)',
		trheight: '40px',
		thheight: '40px',
		customwidth: true,
		rowbgcolor: [
			'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
		],
		thbgcolor: '#081F38',
	};

	const handelClose = () => {
		setRegionName('');
		setShowIndustrySituation(false);
	};

	useEffect(() => {
		if (!industryDetails?.length) return;
		const data = industryDetails.map((item) => {
			return {
				type: item.TYPE_OF_INDUSTRY,
				num: tranNumber(item.VID_COUNT, 2) + ' 辆',
				time: tranNumber(item.ONLINE_PERIOD, 2) + ' h',
				licheng: tranNumber(item.DISTANCE, 2) + ' km',
				oi: tranNumber(item.OIL_CONSUMPTION, 2) + ' L',
				paifang: tranNumber(item.NOX_EMISSIONS, 2) + ' g',
			};
		});
		setData(data);
	}, [industryDetails]);

	useEffect(() => {
		if (!regionName) return;
		if (!regionDetails?.length) return;
		const allValue = regionDetails.reduce((acc, curr) => {
			return acc + curr.value;
		}, 0);
		console.log(allValue);

		const option = {
			animation: true,
			legend: {
				icon: 'circle',
				orient: 'horizontal',
				type: 'scroll',
				width: '220px',
				left: 10,
				top: 10,
				pageTextStyle: {
					color: '#fff', // 文字样式
				},
				textStyle: {
					color: '#fff',
				},
			},
			title: {
				subtext: `总量\n\n${tranNumber(allValue, 2)}` + `${unit}`,
				x: '60%',
				y: '30%',
				subtextStyle: {
					color: '#fff',
					fontSize: 18,
					fontWeight: '1000',
					align: 'center',
				},
			},
			tooltip: {
				show: true,
				backgroundColor: '#fff',
				borderColor: '#ddd',
				borderWidth: 1,
				formatter: (p) => {
					const txtCon =
						"<div style='text-align:left'>" +
						// `<div style="background:${p.color};width:15px;height:15px;borderRadius:50%"></div>` +
						`<span style='color:#000'>` +
						p.name +
						`</span><br /><span style=color:#000> ` +
						p.percent +
						'%</span></div>';
					return txtCon;
				},
				textStyle: {
					rich: {
						a: {
							fontSize: 18,
							background: '#e4ff00',
							with: '10',
						},
						b: {
							fontSize: 10,
							color: '#e4ff00',
							padding: [0, 0, 14, 0],
						},
						c: {
							fontSize: 10,
							color: '#fff',
							padding: [5, 0],
						},
					},
				},
			},
			series: {
				type: 'pie',
				radius: ['40%', '65%'],
				center: ['32%', '50%'],
				height: '100%',
				top: '6%',
				right: '0',
				data: regionDetails,
				color: [
					'#3C97F2',
					'#44CC86',
					'#9BE2E8',
					'#6DC8EC',
					'#6E8CDE',
					'#EB5A49',
					'#FB9E02',
					'#F7DE2C',
					'#FF7081',
				],
				labelLine: {
					show: true,
				},
				label: {
					show: false,
					// position: 'inner',
					// formatter: '{d}%',
					textStyle: {
						color: '#fff',
					},
				},
			},
		};
		setIndustrySituationOption(option);
	}, [regionName, regionDetails]);

	return (
		industrySituationOption && (
			<IndustrySituationStyled>
				<div className="industrySituationOptionTitle">
					<div className="tit">
						<p>{regionName}行业情况</p>
						<div className="close" onClick={handelClose}></div>
					</div>
					<Echarts
						option={industrySituationOption}
						style={{
							width: '100%',
							height: '100%',
						}}
					/>
				</div>
				<div className="industrySituationOptionContent">
					{/* <Table
            columns={columns}
            dataSource={data}
            // pagination={{ pageSize: 10 }}
            scroll={{ y: '100%' }}
          /> */}
					<ScrollListWidthTitle
						columns={columns}
						data={data}
						{...optionTable}
					></ScrollListWidthTitle>
				</div>
			</IndustrySituationStyled>
		)
	);
};

export default IndustrySituation;
