import React from 'react';
import { useFont } from '@renderer/hooks';
import LayoutContainer from './style';

type DataItem = {
	total: number;
	guo5: number;
	guo6: number;
};

export const ConnectedVehiclesComponent: React.FC<DataItem> = ({
	total,
	guo5,
	guo6,
}) => {
	return (
		<>
			<LayoutContainer>
				<div className={'left-container' + ' ' + useFont()}>
					<div className="total-type">
						<h3>国Ⅴ</h3>
						<p className="number">
							{guo5 || '--'}
							<span>辆</span>
						</p>
						<p className="percent">
							{Number((guo5 / total) * 100).toFixed(0) + '%' || '--'}
						</p>
					</div>
					<div className="total">
						<div>{total || '--'}</div>
						<span>辆</span>
					</div>
					<div className="total-type">
						<h3>国Ⅵ</h3>
						<p className="number">
							{guo6 || '--'}
							<span>辆</span>
						</p>
						<p className="percent">
							{Number((guo6 / total) * 100).toFixed(0) + '%' || '--'}
						</p>
					</div>
				</div>
			</LayoutContainer>
		</>
	);
};

export default ConnectedVehiclesComponent;
