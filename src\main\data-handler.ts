import { Websocket } from './websocket';
import { EventEmitter } from 'events';
import * as fs from 'fs';
import { isInPolygon } from './utils';
import { LayerType } from '../renderer/src/marsLayers/components/LayerMenu/layerDatas';
import { cloneDeep } from 'lodash';

interface VINType {
	VIN: string;
	lon: number | string;
	lat: number | string;
	LON: number | string;
	LAT: number | string;
	TIME: string;
	type: string;
	speed: string | number;
	order: number;
	is_illegal: string;
	cell: string;
	id: number;
	is_online: boolean;
	affiliation: string;
}

interface Extent {
	xmin: number;
	xmax: number;
	ymin: number;
	ymax: number;
}

type longitue = number;
type latitude = number;
type Polygon = Array<[longitue, latitude]>;

export class DataHandler {
	private vins: Set<string> = new Set([]);
	private polygon: Polygon = [
		[73.500226, 18.1608963],
		[135.0858829, 18.1608963],
		[135.0858829, 53.55791967],
		[73.500226, 53.55791967],
		[73.500226, 18.1608963],
	];
	// realTimeData 用于存储从redis获取的最新的数据,当extent改变时直接使用
	private realTimeData: Array<VINType> = [];
	eventEmitter: EventEmitter;
	private socket: any = null;
	private values: any = null;
	jsonData: any;
	token: string;
	vehicleState: string;
	affiliation: string;
	constructor({ jsonData = null, token = '' }) {
		this.vehicleState = 'onLine';
		this.jsonData = jsonData;
		this.token = token;
		this.eventEmitter = new EventEmitter();
		this.socket = new Websocket({ jsonData, token });
		// this.onReload()
		this.connectRedis();
		this.onLayerChange();
		this.onExtentsChange();
		this.onAffiliationChange();
	}

	resetInstance() {
		this.socket.VINList = [];
	}

	onReload() {
		this.eventEmitter.on('change:reload', (value) => {
			this.socket = null;
			this.socket = new Websocket({
				jsonData: this.jsonData,
				token: this.token,
			});
		});
	}
	// 注册 change:extent 事件
	onExtentsChange() {
		this.eventEmitter.on('change:extent', (value) => {
			this.values = cloneDeep(value);

			const vinList1 = this.realTimeData.filter((item) => {
				const { lon, lat } = item;
				return isInPolygon([lon, lat], this.polygon);
			});
			// .filter((item) => {
			//   if(this.affiliation) {
			//     return item.affiliation === this.affiliation
			//   } else {
			//     return true
			//   }
			// });
			const vinList = vinList1
				.filter((item) => {
					if (value.type.key == 'all') {
						return true;
					}
					return value?.type?.child?.some((item2) => {
						if (value.is_illegal == '1') {
							return item2.label == item.type && item.is_illegal == '1';
						}
						return item2.label == item.type;
					});
				})
				.map((item) => `${item.VIN}|${item.type}|${item.lon}|${item.lat}`);
			this.vins = new Set(vinList);
			let list = this.handleRawData(this.vins, this.realTimeData);
			this.eventEmitter.emit('data:handler', list);
		});
	}

	onAffiliationChange() {
		this.eventEmitter.on('change:affiliation', (value) => {
			this.affiliation = value;
		});
	}

	onLayerChange() {
		this.eventEmitter.on('change:layer', (value) => {
			if (
				[LayerType.VEHICLE_ONLINE, LayerType.USER_ENTERPRISE].includes(value)
			) {
				this.socket.connectAgain();
			} else {
				this.socket.disconnect();
			}
		});
	}

	// 处理 main 返回的 extent
	handleExtents(extent: Extent) {
		const { xmin, ymin, xmax, ymax } = extent;
		const polygon = [
			[xmin, ymin],
			[xmax, ymin],
			[xmax, ymax],
			[xmin, ymax],
			[xmin, ymin],
		];
		return polygon as Polygon;
	}

	handleRawData(vins: Set<string>, VINDataList: Array<VINType>) {
		const lists = VINDataList.map((item, index) => {
			return {
				...item,
				order: item.id,
			};
		}).filter((o) => vins.has(`${o.VIN}|${o.type}|${o.lon}|${o.lat}`));
		return lists;
	}

	// 连接Redis并注册
	connectRedis() {
		this.socket.eventEmitter.on('data:VINList', (VINs) => {
			this.vins = new Set(VINs);
			this.eventEmitter.emit('data:VINs', VINs);
		});
		this.socket.eventEmitter.on('data:websocket', (data) => {
			this.realTimeData = data;
			this.eventEmitter.emit('change:extent', {
				extent: this.values?.extent,
				type: this.values?.type,
				is_illegal: this.values?.is_illegal,
				vehicleState: this.values?.vehicleState,
			});
		});
	}

	// 测试 获取实时数据
	test1(f, VINs) {
		const setVINs = new Set(VINs);
		const file = fs.readFileSync(f);
		const json = JSON.parse(file.toString());
		const data = json
			.map((item, index) => {
				return {
					...item,
					// lon: Number(item.LON),
					// lat: Number(item.LAT),
					order: index,
				};
			})
			.filter((o) => setVINs.has(o.VIN));
		return data;
	}
}
