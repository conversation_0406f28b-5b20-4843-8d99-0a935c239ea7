function formatNumber(num) {
	// 确保输入是数字类型
	if (typeof num !== 'number') {
		throw new Error('Input must be a number.');
	}

	// 处理负数
	if (num < 0) {
		if (num > -1) {
			// 负数且大于-1，保留两位小数
			return parseFloat(num.toFixed(2));
		} else {
			// 负数且小于等于-1，保留一位小数
			return parseFloat(num.toFixed(0));
		}
	}

	// 处理正数或零
	if (num > 0) {
		if (num < 0.001) {
			return parseFloat(num.toFixed(4));
		} else if (num < 0.01) {
			return parseFloat(num.toFixed(3));
		} else if (num < 1) {
			// 正数且小于1，保留两位小数
			return parseFloat(num.toFixed(3));
		} else {
			// 正数且大于等于1，不保留小数
			return parseFloat(num.toFixed(1));
		}
	} else {
		// 等于0，不保留小数
		return num;
	}
}

// 数字转化添加单位
const tranNumber = (num, point) => {
	if (typeof num !== 'number' && typeof num !== 'string') {
		return 0;
	}

	// 将数字转换为字符串,然后通过split方法用.分隔,取到第0个
	const numStr = num.toString().split('.')[0];
	if (Number(num) <= -1) {
		return Number(num);
	} else if (Number(num) < 0) {
		return Number(num).toFixed(4);
	} else if (Number(num) === 0) {
		return 0;
	} else if (Number(num) < 1) {
		return Number(num).toFixed(4);
	} else if (Number(num) < 10) {
		return Number(num).toFixed(1);
	} else if (numStr.length < 5) {
		// 判断数字有多长,如果小于5,,表示1万以内的数字,让其直接显示
		return numStr;
	} else if (numStr.length >= 5 && numStr.length <= 8) {
		// 如果数字大于6位,小于8位,让其数字后面加单位万
		const decimal = numStr.substring(
			numStr.length - 4,
			numStr.length - 4 + point,
		);
		// 由千位,百位组成的一个数字
		const a = num / 10000;
		return parseFloat(parseInt(a) + '.' + decimal).toFixed(1) + '万';
	} else if (numStr.length >= 8) {
		// 如果数字大于8位,让其数字后面加单位亿
		const decimal = numStr.substring(
			numStr.length - 8,
			numStr.length - 8 + point,
		);
		const a = num / 100000000;
		return parseFloat(parseInt(a) + '.' + decimal).toFixed(1) + '亿';
	}
};
const tranNumberS = (num, point) => {
	if (typeof num !== 'number' && typeof num !== 'string') {
		return 0;
	}

	// 将数字转换为字符串,然后通过split方法用.分隔,取到第0个
	const numStr = num.toString().split('.')[0];
	if (Number(num) <= -1) {
		return Number(num);
	} else if (Number(num) < 0) {
		return Number(num).toFixed(4);
	} else if (Number(num) === 0) {
		return 0;
	} else if (Number(num) < 1) {
		return Number(num).toFixed(4);
	} else if (Number(num) < 10) {
		return Number(num).toFixed(1);
	} else if (numStr.length < 5) {
		// 判断数字有多长,如果小于5,,表示10万以内的数字,让其直接显示
		return Number(num).toFixed(1);
	} else if (numStr.length === 5) {
		return Number(num);
	} else if (numStr.length > 5 && numStr.length <= 8) {
		// 如果数字大于4位,小于9位,让其数字后面加单位万
		const decimal = numStr.substring(
			numStr.length - 4,
			numStr.length - 4 + point,
		);
		// 由千位,百位组成的一个数字
		const a = num / 10000;
		return parseFloat(parseInt(a) + '.' + decimal).toFixed(1) + '万';
	} else if (numStr.length >= 8) {
		// 如果数字大于8位,让其数字后面加单位亿
		const decimal = numStr.substring(
			numStr.length - 8,
			numStr.length - 8 + point,
		);
		const a = num / 100000000;
		return parseFloat(parseInt(a) + '.' + decimal).toFixed(1) + '亿';
	}
};

// 数字转化添加单位
const tranNumbers = (num, point) => {
	if (typeof num !== 'number' && typeof num !== 'string') {
		return 0;
	}
	// 将数字转换为字符串,然后通过split方法用.分隔,取到第0个
	const numStr = num.toString().split('.')[0];
	if (Number(num) <= -1) {
		return Number(num);
	} else if (Number(num) < 0) {
		return Number(num).toFixed(4);
	} else if (Number(num) === 0) {
		return 0;
	} else if (Number(num) < 1) {
		return Number(num).toFixed(4);
	} else if (Number(num) < 10) {
		return Number(num).toFixed(1);
	} else if (numStr.length < 5) {
		// 判断数字有多长,如果小于6,,表示10万以内的数字,让其直接显示
		return numStr;
	} else if (numStr.length >= 6) {
		// 如果数字大于8位,让其数字后面加单位亿
		const decimal = numStr.substring(
			numStr.length - 8,
			numStr.length - 8 + point,
		);
		const a = num / 100000000;
		return parseFloat(parseInt(a) + '.' + decimal).toFixed(1) + '亿';
	}
};
export { formatNumber, tranNumber, tranNumbers, tranNumberS };
