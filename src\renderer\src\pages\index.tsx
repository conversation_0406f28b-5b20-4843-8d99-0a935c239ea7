import { useCallback, useEffect, useState } from 'react';

import { isEqual, cloneDeep } from 'lodash';
import { message } from 'antd';
import localForage from 'localforage';
import dayjs from 'dayjs';

import { PageContext } from '@renderer/context';
import { useFetchModuleData, useIpcRenderer } from '@renderer/hooks';
import MiddleColumn from '@renderer/MiddleColumn';
import Layout from '@renderer/baseUI/Layout';
import ModeMenu from '@renderer/marsLayers/components/ModeMenu/index.jsx';
import RegionalBanner from '@renderer/components/RegionalBanner';
import ViolationBanner from '@renderer/components/ViolationBanner';
import RoadBanner from '@renderer/components/RoadBanner';
import { LayerType } from '@renderer/marsLayers/components/LayerMenu/layerDatas';
import AreaLeftSide from '@renderer/components/AreaSide/AreaLeftSide/index.js';
import AreaRightSide from '@renderer/components/AreaSide/AreaRightSide/index.js';
import StreetLeftSide from '@renderer/components/StreetSide/StreetLeftSide/index.js';
import StreetRightSide from '@renderer/components/StreetSide/StreetRightSide/index.js';
import IndustryLeftSide from '@renderer/components/IndustrySide/IndustryLeftSide/index.js';
import IndustryRightSide from '@renderer/components/IndustrySide/IndustryRightSide/index.js';
import UserEnterpriseLeftSide from '@renderer/components/UserEnterprise/UserEnterpriseLeftSide/index.js';
import UserEnterpriseRightSide from '@renderer/components/UserEnterprise/UserEnterpriseRightSide/index.js';
import AreaLeftSideV from '@renderer/marsLayers/components/AreaSide/AreaLeftSideV';
import AreaRightSideV from '@renderer/marsLayers/components/AreaSide/AreaRightSideV';
import LeftSideExpansion from '@renderer/components/ViolationAnalysis/LeftSide';
import RightSideExpansion from '@renderer/components/ViolationAnalysis/RightSide';
import HeaderTime from './HeaderTime';
import Count from './Count';
import SlidingLayer from './SlidingLayer';
import layoutTop from '@renderer/images/Layout/layout-top.png';
import layoutBottom from '@renderer/images/layout/layout-bottom.png';
import type { ModuleList } from '@renderer/types';
import { getMaxTime } from '@renderer/api/vehicle';

import '@animxyz/core';
import 'antd/dist/reset.css';
import { HeaderStyled } from './styles';
import moment from 'moment';

const { Header, Main, Left, Middle, Right } = Layout;
let resCopy;
let isFirst = true;
let deletDBing = false;

const LayerDataDB = localForage.createInstance({
	name: 'VehicleLayerDataDB',
	storeName: 'VehicleLayerDataStore',
}); // 图层数据库

const RealTimeOfflineDataDB = localForage.createInstance({
	name: 'RealTimeOfflineDataDB',
	storeName: 'RealTimeOfflineStore',
}); // 实时离线数据库

const RoadGeometryDataDB = localForage.createInstance({
	name: 'RoadGeometryDataDB',
	storeName: 'RoadGeometryDataStore',
}); // 实时离线数据库

const BeiJingTownDataDB = localForage.createInstance({
	name: 'BeiJingTownDataDB',
	storeName: 'BeiJingTownDataStore',
}); // 北京街乡镇地理信息数据库

const MainPage = () => {
	const [modulesList, setModulesList] = useState<ModuleList>([]);
	// const moduleList = useFetchModuleData();
	const [slidingLayerInfo, setSlidingLayerInfo] = useState<any>(null);
	const [countyId, setCountyId] = useState<string>('');
	const [parsentFlash, setParsentFlash] = useState(false);
	const [selectMode, setSelectMode] = useState({});
	const [switchingPointsId, setSwitchingPointsId] = useState(1);
	const [hideModeMenu, setHideModeMenu] = useState(false);
	const [showModeMenu, setShowModeMenu] = useState(true);
	const [currentTopNavMenu, setCurrentTopNavMenu] = useState(''); // 点开区域之后顶部选择的模块按钮
	const [currentTopNavMenuList, setCurrentTopNavMenuList] = useState([]); // 点开区域之后顶部选择的模块按钮列表（违规分析 ViolationAnalysis）
	const [regionData, setRegionData] = useState([]); // 区域模块地图不同类型数据
	const [regionName, setRegionName] = useState(null); // 区域模块中当前选中的地区
	const [streetName, setStreetName] = useState(null); // 街乡镇模块中选中的街道
	const [selectRegionDate, setSelectRegionDate] = useState({
		// 横幅中选择的时间
		timeType: '7day',
		customDate: {
			start_time: '',
			end_time: '',
			time_type: '',
		},
	});
	const [regionalIndicators, setRegionalIndicators] = useState([]); // 区域模块右侧echarts图形数据
	const [currentSelectedId, setCurrentSelectedId] = useState(''); // 当前选择的图层id
	const [streetShipData, setStreetShipData] = useState({}); // 街乡镇数据
	const [openTrajectory, setOpenTrajectory] = useState(false); // 轨迹侧边栏开启
	const [currentTrajectoryInfo, setCurrentTrajectoryInfo] = useState({}); // 轨迹侧边栏实时数据
	// const [showBackgroundFallingAreaMap, setShowBackgroundFallingAreaMap] = useState<boolean>(false) // 落区图背景展示
	const [showBackgroundFallingArea, setShowBackgroundFallingArea] =
		useState<boolean>(false); // 落区图背景展示
	const [showSideExpansionField, setShowSideExpansionField] =
		useState<boolean>(false); // 违规分析两侧扩展栏
	const [showStreet, setShowStreet] = useState<boolean>(false); //行业两侧扩展栏
	const [showSideIndustry, setShowSideIndustry] = useState<boolean>(false); //行业两侧扩展栏
	const [showUserEnterprise, setShowUserEnterprise] = useState<boolean>(false); //用户企业两侧扩展栏
	const [currentIndustry, setCurrentIndustry] = useState('全部'); // 当前行业
	const [currentIndustryList, setCurrentIndustryList] = useState([]); // 当前行业列表
	const [currentIdList, setCurrentIdList] = useState([1]); // 多选框选中id列表
	const [currentVehicleInfo, setCurrentVehicleInfo] = useState([]); // 当前车辆信息
	const [currentViolationClueCode, setCurrentViolationClueCode] = useState(''); // 当前车辆违规线索codo
	const [currentHistoricalTrackDate, setCurrentHistoricalTrackDate] =
		useState(''); // 当前选择的历史轨迹日期
	const [historicalStartTime, setHistoricalStartTime] = useState(
		dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
	); // 单车当前选择的开始
	const [historicalEndTime, setHistoricalEndTime] = useState(
		dayjs().format('YYYY-MM-DD'),
	); // 单车当前选择的结束
	const [violationInfo, setViolationInfo] = useState({}); // 违规分析-参数信息
	const [affiliationId, setAffiliationId] = useState(''); // 用户企业-选中企业id
	const [maxTime, setMaxTime] = useState();
	window.onbeforeunload = function () {
		window.electron.ipcRenderer.send('reload');
	};
	window.electron.ipcRenderer.on('Control_Shift_K', (_event, data) => {
		if (data && deletDBing == false) {
			deletDBing = true;
			localForage
				.dropInstance({
					name: 'CommonDataDB',
				})
				.then(function () {
					message.success('缓存清除成功');
					deletDBing = false;
				})
				.catch(function (err) {
					message.error('缓存清除失败');
					deletDBing = false;
				});
		}
	});

	// useEffect(() => {
	// 	const list = [
	// 		{ id: 'ConnectedVehicles', order: 1, title: '联网数据', isChange: false },
	// 		{
	// 			id: 'RealTimeOnlineDetail',
	// 			order: 2,
	// 			title: '实时排放',
	// 			isChange: false,
	// 		},
	// 		{
	// 			id: 'HistoricalActivityLevel',
	// 			order: 3,
	// 			title: '厂商及用户',
	// 			isChange: false,
	// 		},
	// 		{
	// 			id: 'RealTimeOnlineDetail',
	// 			order: 4,
	// 			title: '车辆违规',
	// 			isChange: false,
	// 		},
	// 		{
	// 			id: 'HistoricalActivityLevel',
	// 			order: 5,
	// 			title: '车辆分布',
	// 			isChange: false,
	// 		},
	// 		{
	// 			id: 'RealTimeOnlineDetail',
	// 			order: 6,
	// 			title: '街巷镇违规',
	// 			isChange: false,
	// 		},
	// 	];
	// 	setModulesList(list);
	// }, []);

	useEffect(() => {
		getMaxTime().then((res) => {
			setMaxTime(res);
		});
	}, []);
	const listener = useCallback((_event, res) => {
		if (!res) return;
		let data = res.content || [];
		if (isEqual(data, resCopy)) {
			return;
		}
		data = cloneDeep(data).map((item, i) => {
			if (isFirst || isEqual(resCopy ? resCopy[i]?.id : undefined, item.id)) {
				return { ...item, isChange: false };
			} else {
				return { ...item, isChange: true };
			}
		});
		resCopy = cloneDeep(data)?.map((item) => {
			return { id: item.id, order: item.order, title: item.title };
		});
		isFirst = false;
		setModulesList(data);
		setTimeout(() => {
			setModulesList(resCopy);
		}, 3000);
	}, []);

	useIpcRenderer('modulesList', listener);

	return (
		<PageContext.Provider
			value={{
				slidingLayerInfo,
				parsentFlash,
				setParsentFlash,
				selectMode,
				setSelectMode,
				setSlidingLayerInfo,
				countyId,
				setCountyId,
				switchingPointsId,
				setSwitchingPointsId,
				hideModeMenu,
				setHideModeMenu,
				showModeMenu,
				setShowModeMenu,
				currentTopNavMenu,
				setCurrentTopNavMenu,
				regionName,
				setRegionName,
				streetName,
				setStreetName,
				regionData,
				setRegionData,
				selectRegionDate,
				setSelectRegionDate,
				regionalIndicators,
				setRegionalIndicators,
				currentSelectedId,
				setCurrentSelectedId,
				streetShipData,
				setStreetShipData,
				openTrajectory,
				setOpenTrajectory,
				currentTrajectoryInfo,
				setCurrentTrajectoryInfo,
				showBackgroundFallingArea,
				setShowBackgroundFallingArea,
				showSideIndustry,
				setShowSideIndustry,
				showStreet,
				setShowStreet,
				showUserEnterprise,
				setShowUserEnterprise,
				showSideExpansionField,
				setShowSideExpansionField,
				currentIndustry,
				setCurrentIndustry,
				currentIndustryList,
				setCurrentIndustryList,
				currentTopNavMenuList,
				setCurrentTopNavMenuList,
				LayerDataDB,
				RealTimeOfflineDataDB,
				RoadGeometryDataDB,
				BeiJingTownDataDB,
				currentVehicleInfo,
				setCurrentVehicleInfo,
				currentViolationClueCode,
				setCurrentViolationClueCode,
				currentHistoricalTrackDate,
				setCurrentHistoricalTrackDate,
				historicalStartTime,
				setHistoricalStartTime,
				historicalEndTime,
				setHistoricalEndTime,
				violationInfo,
				setViolationInfo,
				currentIdList,
				setCurrentIdList,
				affiliationId,
				setAffiliationId,
				maxTime,
			}}
		>
			{maxTime && (
				<Layout>
					<Header>
						<HeaderStyled>
							<img src={layoutTop} className="header-bg" />
							<div className="header-date">
								<HeaderTime />
							</div>
							{/* <h2 className="title">
								重点移动源排放远程监测和智能决策支撑驾驶舱
							</h2> */}
						</HeaderStyled>
					</Header>
					{/* 模块菜单 */}
					{showModeMenu ? (
						<ModeMenu
							parsentFlash={parsentFlash}
							openTrajectory={openTrajectory}
						/>
					) : null}

					{/* 违规分析模块菜单 */}
					{currentSelectedId === LayerType.VIOLATING_ANALYSIS ? (
						<ViolationBanner
							key={currentSelectedId}
							currentSelectedId={currentSelectedId}
						/>
					) : null}
					{/* 区县、街乡镇、路段模块菜单 */}
					{/* {currentSelectedId === LayerType.AREA ||
					currentSelectedId === LayerType.INDUSTRY ||
					currentSelectedId === LayerType.Street_Township ? (
						<RegionalBanner
							key={currentSelectedId}
							currentSelectedId={currentSelectedId}
						/>
					) : null}
					{currentSelectedId === LayerType.USER_ENTERPRISE ||
					currentSelectedId === LayerType.ROAD ? (
						<RoadBanner
							key={currentSelectedId}
							currentSelectedId={currentSelectedId}
						/>
					) : null} */}
					<Main>
						<Left>
							<Count
								modulesList={modulesList.filter((ele) => ele.order % 2 === 1)}
								direction="left"
							/>
						</Left>
						<Middle>
							<MiddleColumn />
						</Middle>
						<Right>
							<Count
								modulesList={modulesList.filter((ele) => ele.order % 2 === 0)}
								direction="right"
							/>
						</Right>
						<img src={layoutBottom} className="layout-bottom" />
						{/* {showBackgroundFallingArea ? (
							<>
								<AreaLeftSide
									currentSelectedId={currentSelectedId}
									streetShipData={streetShipData}
								/>
								<AreaRightSide
									currentSelectedId={currentSelectedId}
									streetShipData={streetShipData}
								/>
							</>
						) : null} */}
						{/* 街乡镇两侧模块 */}
						{/* {showStreet ? (
							<>
								<StreetLeftSide
									currentSelectedId={currentSelectedId}
									streetShipData={streetShipData}
								/>
								<StreetRightSide
									currentSelectedId={currentSelectedId}
									streetShipData={streetShipData}
								/>
							</>
						) : null} */}
						{/* 行业两侧模块 */}
						{/* {showSideIndustry ? (
							<>
								<IndustryLeftSide
									currentSelectedId={currentSelectedId}
									streetShipData={streetShipData}
								/>
								<IndustryRightSide
									currentSelectedId={currentSelectedId}
									streetShipData={streetShipData}
								/>
							</>
						) : null} */}
						{/* 用户企业两侧模块 */}
						{/* {showUserEnterprise ? (
							<>
								<UserEnterpriseLeftSide
									currentSelectedId={currentSelectedId}
									streetShipData={streetShipData}
								/>
								<UserEnterpriseRightSide
									currentSelectedId={currentSelectedId}
									streetShipData={streetShipData}
								/>
							</>
						) : null} */}
						{/* 违规分析两侧栏 */}
						{showSideExpansionField ? (
							<>
								<LeftSideExpansion
									currentSelectedId={currentSelectedId}
									streetShipData={streetShipData}
								/>
								<RightSideExpansion
									currentSelectedId={currentSelectedId}
									streetShipData={streetShipData}
								/>
							</>
						) : null}
						{openTrajectory ? (
							<>
								<AreaLeftSideV />
								<AreaRightSideV />
							</>
						) : null}
					</Main>
					{slidingLayerInfo != null ? (
						<SlidingLayer {...slidingLayerInfo} />
					) : null}
				</Layout>
			)}
		</PageContext.Provider>
	);
};

export default MainPage;
