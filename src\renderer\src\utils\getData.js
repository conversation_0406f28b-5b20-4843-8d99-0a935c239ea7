import moment from 'moment';
import { getPoiTrance, getPoiRoad } from '@renderer/api';
export class ManageLayerData {
	constructor({ layerId, jsonData = null, DB = null }) {
		this.portList = {
			trafficVolumeDistribution: getPoiRoad,
			trafficVolumeThermodynamics: getPoiTrance,
			vehicleEmissionsDistribution: getPoiRoad,
			vehicleEmissionsThermodynamics: getPoiRoad,
		};
		this.hourNumber = 24;
		this.layerId = layerId;
		this.jsonData = null;
		this.localforage = DB;
		if (jsonData) {
			this.jsonData = jsonData;
		} else {
			let jsonData = sessionStorage?.getItem('zcJsonData');
			if (jsonData) this.jsonData = JSON.parse(jsonData);
		}
	}
	run() {
		let self = this;
		this.getDateList().forEach((item) => {
			self.localforage
				.getItem(`${self.layerId}_${item.endTime}`)
				.then(function (value) {
					if (!value || !value.length) self.getData(item);
				})
				.catch(function (err) {
					self.getData(item);
				});
		});
	}
	async getData(dateObj, fn = () => {}) {
		let param = this.getParams(dateObj);
		try {
			const data = await this.portList[this.layerId](param);
			this.addDbData(data, dateObj);
			fn && fn(data);
		} catch (a) {
			// console.log(a)
		}
	}
	addDbData(data, dateObj) {
		this.localforage
			.setItem(`${this.layerId}_${dateObj.endTime}`, data)
			.then((value) => {})
			.catch((err) => {});
	}
	getParams(o) {
		let top = 80000;
		switch (this.layerId) {
			case 'trafficVolumeThermodynamics':
				if (this.jsonData?.traffic?.heatmap?.top)
					top = Number(this.jsonData.traffic.heatmap.top);
				return {
					target: 1,
					hour: o.endTime,
					vehicle_type: '',
					top,
					token: '',
				};
			case 'vehicleEmissionsThermodynamics':
				if (this.jsonData?.vehicleEmissions?.heatmap?.top)
					top = Number(this.jsonData.vehicleEmissions.heatmap.top);
				return {
					start_time: o.startTime,
					end_time: o.endTime,
					vehicle_type: '',
					time_type: '1',
					target: 2,
					extract: '1',
					top,
				};
			default:
				break;
		}
	}
	getDateList() {
		let dateList = [];
		for (let i = 0; i <= this.hourNumber - 1; i++) {
			let startTime = moment().subtract(this.hourNumber - i + 1, 'h');
			let endTime = moment()
				.subtract(this.hourNumber - i, 'h')
				.format('YYYY-MM-DD HH:00:00');
			dateList.push({
				startTime: startTime.format('YYYY-MM-DD HH:00:00'),
				endTime,
			});
		}
		return dateList;
	}
}
export const clearOldDBData = (localforage) => {
	// 遍历indexDB的key,删除过期数据
	localforage.iterate((value, key, iterationNumber) => {
		let isOld = moment(key.split('_')[1]).isSameOrBefore(
			moment().subtract(27, 'h').format('YYYY-MM-DD HH:00:00'),
		);
		if (isOld) {
			localforage.removeItem(key);
		}
	});
};
export const deleteDBExpiredData = () => {
	this.localforage
		.keys()
		.then(function (keys) {
			// 包含所有 key 名的数组
			if (keys?.length) {
				keys.forEach((item) => {
					const lastEight = Number(item.substring(item.length - 10));
					const expiration = Number(
						moment().subtract(26, 'h').format('YYYY-MMDDHH'),
					);
					if (lastEight < expiration) {
						this.localforage
							.removeItem(item)
							.then(function () {})
							.catch(function (err) {});
					}
				});
			}
		})
		.catch(function (err) {
			// 当出错时，此处代码运行
			// console.log(err)
		});
};

export const readDbData = ({ layerId, endTime, localforage }) => {
	return new Promise(function (resolve, reject) {
		localforage
			.getItem(`${layerId}_${endTime}`)
			.then(function (value) {
				if (value) {
					resolve(value);
				} else {
					resolve([]);
				}
			})
			.catch(function (err) {
				// 当出错时，此处代码运行
				// reject(err)
				resolve([]);
			});
	});
};

export const CountyId = {
	东城区: '7',
	丰台区: '17',
	大兴区: '4',
	密云区: '10',
	平谷区: '9',
	延庆区: '16',
	开发区: '18',
	怀柔区: '11',
	房山区: '5',
	昌平区: '15',
	朝阳区: '3',
	海淀区: '2',
	石景山区: '6',
	西城区: '8',
	通州区: '14',
	门头沟区: '13',
	顺义区: '12',
};
