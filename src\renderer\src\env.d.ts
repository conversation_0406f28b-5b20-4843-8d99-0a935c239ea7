/// <reference types="vite/client" />
interface ImportMetaEnv {
	readonly MAIN_VITE_API_DOMAIN: string;
	readonly RENDERER_VITE_API_DOMAIN: string;
	readonly RENDERER_VITE_API_BEIJING_VEHICLE_RBAC: sring;
	readonly RENDERER_VITE_API_WEATHER: sring;
	readonly RENDERER_VITE_API_NATIONAL_MIDSERVICE: sring;
	readonly RENDERER_VITE_API_NATIONAL_TRACEABILITY: sring;
	readonly RENDERER_VITE_GEOSERGER_AIR_URL: sring;
	readonly RENDERER_VITE_Authorization: sring;
	readonly RENDERER_VITE_TOKEN: sring;
	// more env variables...
}

interface ImportMeta {
	readonly env: ImportMetaEnv;
}
