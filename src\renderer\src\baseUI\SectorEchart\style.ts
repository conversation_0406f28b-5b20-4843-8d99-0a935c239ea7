import styled from 'styled-components';
const LayoutContainer = styled.div`
	display: flex;
	width: 100%;
	height: 100%;
	//position: relative;
	.legend {
		position: absolute;
		right: 12%;
		.legend-item {
			display: flex;
			flex-direction: row;
			margin-bottom: 3px;
			.circle {
				border-radius: 14px;
				display: block;
				width: 12px;
				height: 12px;
				margin-right: 5px;
			}
			.name {
				font-size: 16px;
				font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #e2f0ff;
			}
		}
	}
`;
export default LayoutContainer;
