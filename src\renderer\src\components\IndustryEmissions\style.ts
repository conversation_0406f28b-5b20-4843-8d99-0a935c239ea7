import styled from 'styled-components';
import boxTitleBg from '../../images/networkedData/boxTitleBg.png';
import titleBg from '../../images/networkedData/titleBg.png';
import imags from '../../images/networkedData/index';

const TimeTypeRadioStyle = styled.div`
	display: inline-block;
`;

const DropdownStyle = styled.div`
	display: inline-block;
	margin-left: 10px;
	position: relative;
	top: -3px;
`;

const Container = styled.div`
  height: 100%;
  display: flex;
  justify-content: space-between;

  > div {
    width: 50%;
  }
  .echarts-line {
    width: 90%;
    padding-top: 10px;
    display: flex;
    align-items: center;

    .echarts-title {
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 20px;
      font-size: 14px;
      line-height: 20px;
      color: #fff;
      cursor: pointer;
      padding-bottom: 30px;
      padding-left: 30px;

      span {
        width: 20px;
        height: 40px;
        color: #E8F4FF;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        margin: 8px -10px;
        border-right: 2px solid rgb(26, 62, 71);
        padding-right: 10px;
      }
    }
    .title-select {
      color: rgb(62, 189, 203) !important;
      border-right: 2px solid rgb(62, 189, 203) !important;
    }
    .title-un-select {
    }
    .rankList {
      box-sizing: border-box;
      margin: 20px 35px;
      width: 100%;
      .th_row {
        .title-li:first-child {
          width: 70px;
          flex: initial;
        }
        .title-li:nth-child(3) {
            width: 120px;
            flex: initial;
          }
        .title-li:nth-child(4),
          .title-li:nth-child(5) {
            width: 100px;
            flex: initial;
          }
        }
      }
      .list-content,
      .list-content1 {
        .list-ul {
          .list-li {
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            white-space: nowrap;
            display: flow-root;
            line-height: 38px;
          }
          .list-li:first-child {
            width: 70px;
            flex: initial;
          }
          .list-li:nth-child(3) {
            width: 120px;
            flex: initial;
          }
          .list-li:nth-child(4),
          .list-li:nth-child(5) {
            width: 100px;
            flex: initial;
          }
        }
      }
    }
  }
`;
export { TimeTypeRadioStyle, DropdownStyle, Container };
