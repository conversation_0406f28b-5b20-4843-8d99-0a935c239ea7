/**
 * 联网车辆(辆)
 */
import React, { useEffect, useState, useContext } from 'react';

import Echarts from '@renderer/components/echarts';
import ConnectedVehiclesComponent from './ConnectedVehiclesComponent';
import Box from '@renderer/baseUI/Box';
import DelayUI from '@renderer/baseUI/DelayUI';
import titleBg from '@renderer/images/networkedData/titleBg.png';
import imags from '@renderer/images/networkedData/index';
import { getNetworked, getWiredByMonth } from '@renderer/api';
import { INDUSTRY_COLOR, INDUSTRY_ORDER } from '@renderer/constants/color';
import { PageContext } from '@renderer/context';
import RealTimeOnlineEcharts from '../RealTimeOnlineEcharts';

import { Container } from './style';

type DataArr = {
	name?: string;
	value?: number;
	itemStyle?: object;
};

type CarEchartsData = {
	name: string;
	value: number;
};

const ConnectedVehicles = () => {
	const { countyId } = useContext(PageContext);
	const [netNum, setNetNum] = useState<any>();
	const [echartsData, setEchartsData] = useState<any>([]);

	useEffect(() => {
		const p = {
			vehicle_type: [1, 2],
			vehicle_level: [1, 2, 3],
			token: 2,
			countyId,
		};
		getNetworked(p).then((res) => {
			setNetNum(res);
			const carData: CarEchartsData[] = [];

			res.data
				.map((item) => {
					const industry = item.industry;
					return {
						...item,
						industry: industry === '其他用途车' ? '其他用途' : industry,
					};
				})
				.sort((a, b) => INDUSTRY_ORDER[a.industry] - INDUSTRY_ORDER[b.industry])
				.map((item) => {
					const carObj = {
						name: `${item.industry}`,
						num: item.nums,
						value: item.rate,
					};
					carData.push(carObj);
				});
			setEchartsData(carData);
		});
	}, [countyId]);

	return (
		<Box title="联网车辆" titlewidth="95%" height="100%">
			<Container>
				<div className="up-container">
					{netNum && <ConnectedVehiclesComponent {...netNum} />}
				</div>
				<div className="right">
					{echartsData?.length > 0 ? (
						<RealTimeOnlineEcharts data={echartsData} />
					) : (
						<div>暂无数据</div>
					)}
					<div className="background"></div>
				</div>
			</Container>
		</Box>
	);
};

export default ConnectedVehicles;
