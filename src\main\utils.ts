// 从redis中根据type类型获取数据
// async function getDataBytype(client, typeId: number) {
//   const json = []
//   for await (const key of client.scanIterator()) {
//     // use the key!
//     const value = await client.get(key)
//     const item = JSON.parse(value!)
//     // if (isInPolygon([item.LON, item.LAT], extent)) {
//     //   json.push(item)
//     // }
//   }
// }

// 这个函数将检查给定的经纬度是否在指定的区域内
// function isLatLngInRegion(latitude, longitude, regionBounds) {
//   const h3Index = h3.geoToH3(latitude, longitude, /* 选择适当的H3分辨率 */);

//   // 解构区域边界对象
//   const { xmin, xmax, ymin, ymax } = regionBounds;

//   // 判断经纬度是否在区域内
//   if (longitude >= xmin && longitude <= xmax && latitude >= ymin && latitude <= ymax) {
//     return true;
//   } else {
//     return false;
//   }
// }

function isInPolygon(checkPoint, polygonPoints) {
	let counter = 0;
	let p1, p2;
	const pointCount = polygonPoints.length;
	p1 = polygonPoints[0];

	for (let i = 1; i <= pointCount; i++) {
		p2 = polygonPoints[i % pointCount];
		if (
			checkPoint[0] > Math.min(p1[0], p2[0]) &&
			checkPoint[0] <= Math.max(p1[0], p2[0])
		) {
			if (checkPoint[1] <= Math.max(p1[1], p2[1])) {
				if (p1[0] != p2[0]) {
					const xinters =
						((checkPoint[0] - p1[0]) * (p2[1] - p1[1])) / (p2[0] - p1[0]) +
						p1[1];
					if (p1[1] == p2[1] || checkPoint[1] <= xinters) {
						counter++;
					}
				}
			}
		}
		p1 = p2;
	}
	if (counter % 2 == 0) {
		return false;
	} else {
		return true;
	}
}

// 创建一个函数来判断经纬度是否在给定的区域内
function isLatLngInRegion(latitude, longitude, regionBounds) {
	// 解构区域边界对象
	const { xmin, xmax, ymin, ymax } = regionBounds;

	// 判断经纬度是否在区域内
	if (
		longitude >= xmin &&
		longitude <= xmax &&
		latitude >= ymin &&
		latitude <= ymax
	) {
		return true;
	} else {
		return false;
	}
}

export { isInPolygon, isLatLngInRegion };
