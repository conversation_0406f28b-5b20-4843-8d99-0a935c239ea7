import moment from 'moment';
import { getCarColorTwo } from '../../../../../../main/data';
import {
	formatNumber,
	tranNumber,
	tranNumbers,
	tranNumberS,
} from '@renderer/hooks/index';
import { INDUSTRY_COLOR } from '@renderer/constants/color';

export const getIper = (data) => {
	const colorList: any = [];
	// const colorList = [
	// 	'#FD9546',
	// 	'#8A40FF',
	// 	'#E3A07C',
	// 	'#491DFF',
	// 	'#8EFAFF',
	// 	'#A3EE63',
	// 	'#63A4EE',
	// 	'#6863EE',
	// ];
	data.map((item, index) => {
		colorList.push(INDUSTRY_COLOR[item.name]);
	});
	const options = {
		// backgroundColor: '#061740',
		color: colorList,
		tooltip: {
			show: true,
			formatter: (params) => {
				return `<span style="font-size: larger;">${params.name} ${params.value}%</span>`;
			},
		},
		legend: [
			{
				type: 'scroll', // 设置图例翻页
				orient: 'vertical', // 图例排布
				right: '10%',
				top: '15%',
				itemGap: 10,
				// textStyle: {
				//   color: '#fff',
				//   fontSize: '14px'
				// },
				//图例标记的图形高度
				itemHeight: 10,
				//图例标记的图形宽度
				itemWidth: 10,
				formatter: function (name) {
					for (let i = 0; i < data?.length; i++) {
						if (!data?.length) return;
						if (name == data[i].name) {
							return '{name|' + name + '}';
						}
					}
				},
				textStyle: {
					rich: {
						name: {
							fontSize: 16,
							fontWeight: 400,
							width: 100,
							height: 20,
							padding: [0, 0, 0, 5],
							color: '#fff',
							// fontSize: '14px',
							fontFamily: 'Source Han Sans CN-Regular',
						},
						// num: {
						//   fontSize: 14,
						//   fontWeight: 500,
						//   height: 20,
						//   width: 50,
						//   align: 'left',
						//   color: 'rgba(0, 0, 0, 0.65)',
						//   fontFamily: 'Source Han Sans CN-Regular'
						// },
						value: {
							fontSize: 16,
							fontWeight: 500,
							height: 20,
							width: 50,
							align: 'left',
							color: '#fff',
							// fontSize: '14px',
							fontFamily: 'Source Han Sans CN-Regular',
						},
					},
				},
			},
		],
		series: [
			{
				name: '',
				type: 'pie',
				clockwise: false,
				radius: ['100%', '170%'],
				emphasis: {
					scale: false,
				},
				center: ['35%', '55%'],
				top: 'center',
				label: {
					show: false,
					position: 'inside',
					color: '#fff',
				},
				data,
			},
			{
				name: '',
				type: 'pie',
				clockwise: false,
				radius: ['180%', '175%'],
				center: ['35%', '55%'],
				top: 'center',
				avoidLabelOverlap: false,
				itemStyle: {
					borderRadius: 1000,
				},
				label: {
					show: false,
					position: 'outside',
					color: '#E6F7FF',
					lineHeight: 18,
					fontSize: 16,
					// formatter: (params) => {
					//   const { data } = params
					//   return data.name + ' ' + data.value + '%'
					// }
				},
				emphasis: {
					disabled: true,
				},
				// 占位样式
				emptyCircleStyle: {
					color: 'rgba(255,255,255,0)',
				},
				data: data?.length > 0 ? data : [],
			},
		],
	};
	return options;
};

export const getIndustryTop = (val) => {
	if (!val) return;
	const seriesList: any = [];
	const colorList = ['#FD9546', '#8A40FF'];
	val.map((item, index) => {
		seriesList.push({
			name: item.name,
			type: 'bar',
			stack: 'total',
			color: colorList[index],
			emphasis: {
				focus: 'series',
			},
			barWidth: 14,
			data: item.value?.map((i) => formatNumber(i)),
		});
	});

	const options = {
		tooltip: {
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',
			padding: 8,
			textStyle: {
				color: '#fff',
			},
		},
		legend: {
			width: '80%',
			right: '10%',
			top: '10%',
			textStyle: {
				fontSize: 16,
				color: '#E2F0FF',
			},
			type: 'scroll',
			pageTextStyle: {
				color: '#fff', // 文字样式
			},
		},
		grid: {
			top: 70,
			bottom: 22,
			left: '20%',
			right: '10%',
		},
		xAxis: {
			type: 'value',
			axisPointer: {
				type: 'shadow',
			},
			axisLabel: {
				textStyle: {
					color: 'rgba(212, 232, 254, 1)',
					fontSize: 16,
				},
				formatter: function (value, index) {
					return tranNumber(Number(value), 2);
				},
			},
		},
		yAxis: {
			name: `单位:辆`,
			nameTextStyle: {
				color: '#D8DBDE',
				fontSize: 14,
				padding: [-10, 0],
			},
			type: 'category',
			data: val[0]?.data,
			axisLabel: {
				textStyle: {
					color: '#E2F0FF',
					fontSize: 16,
				},
				interval: 0,
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: 'rgba(222, 227, 239, 0.3)',
					type: [2, 4],
				},
			},
		},
		dataZoom: [
			//Y轴滑动条
			{
				type: 'inside', //滑动条
				show: true, //开启
				yAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
				left: '93%', //滑动条位置
			},
			//y轴内置滑动
			{
				type: 'inside', //内置滑动，随鼠标滚轮展示
				yAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
			},
		],
		series: seriesList,
	};
	return options;
};
export const getIndustryMid = (val) => {
	if (!val) return;
	const seriesList: any = [];
	const colorList = [
		'#FD9546',
		'#8A40FF',
		'#E3A07C',
		'#491DFF',
		'#8EFAFF',
		'#A3EE63',
		'#63A4EE',
		'#6863EE',
	];
	val.map((item, index) => {
		seriesList.push({
			name: item.name,
			type: 'bar',
			stack: 'total',
			color: INDUSTRY_COLOR[item.name],
			emphasis: {
				focus: 'series',
			},
			barWidth: 14,
			data: item.value?.map((i) => formatNumber(i)),
		});
	});

	return {
		tooltip: {
			trigger: 'axis',
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',
			padding: 8,
			textStyle: {
				color: '#fff',
			},
			formatter: function (val) {
				let tooltipText = '';
				// 改变默认“，”
				val.forEach(function (val) {
					let value = val.value;
					if (typeof value === 'number') {
						value = value.toString().replace(/,/g, ''); // 去掉逗号
					}
					tooltipText += val.seriesName + ': ' + value + '<br/>';
				});
				return tooltipText;
			},
		},
		legend: {
			width: '80%',
			right: '10%',
			// top: '10%',
			textStyle: {
				fontSize: 16,
				color: '#E2F0FF',
			},
			type: 'scroll',
			pageTextStyle: {
				color: '#fff', // 文字样式
			},
		},
		grid: {
			top: 70,
			bottom: 22,
			left: '10%',
			right: '10%',
		},
		xAxis: {
			type: 'category',
			data: val[0]?.time,
			axisLabel: {
				textStyle: {
					color: '#E2F0FF',
					fontSize: 18,
				},
				formatter: (params) => {
					if (typeof params !== 'string') {
						return params;
					}
					// 检查 params 是否包含时间部分 (HH:mm 或 HH:mm:ss)
					const hasTimePart = /\s\d{2}:\d{2}(?::\d{2})?/.test(params);
					const momentDate = moment(
						params,
						hasTimePart ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD',
						true,
					);
					if (!momentDate.isValid()) {
						return params;
					}
					return hasTimePart
						? momentDate.format('HH:mm')
						: momentDate.format('MM/DD');
				},
			},
		},
		yAxis: {
			name: `单位: 辆`,
			nameTextStyle: {
				color: '#D8DBDE',
				fontSize: 14,
			},
			type: 'value',
			splitLine: {
				show: true,
				lineStyle: {
					color: 'rgba(222, 227, 239, 0.3)',
					type: [2, 4],
				},
			},
			axisLabel: {
				textStyle: {
					color: 'rgba(212, 232, 254, 1)',
					fontSize: 18,
				},
				formatter: function (value, index) {
					return tranNumber(Number(value), 2);
				},
			},
		},
		dataZoom: [
			//Y轴滑动条
			{
				type: 'inside', //滑动条
				show: true, //开启
				xAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
				left: '93%', //滑动条位置
			},
			//y轴内置滑动
			{
				type: 'inside', //内置滑动，随鼠标滚轮展示
				xAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
			},
		],
		series: seriesList,
	};
};

export const getIndustryBtm = (val) => {
	if (!val) return;
	const seriesList: any = [];
	const colorList = [
		'#FD9546',
		'#8A40FF',
		'#E3A07C',
		'#491DFF',
		'#8EFAFF',
		'#A3EE63',
		'#63A4EE',
		'#6863EE',
	];
	val.map((item, index) => {
		seriesList.push({
			name: item.name,
			type: 'line',
			stack: 'total',
			symbol: 'none',
			color: INDUSTRY_COLOR[item.name],
			areaStyle: {
				color: INDUSTRY_COLOR[item.name],
			},
			data: item.value?.map((i) => formatNumber(i)),
		});
	});

	return {
		tooltip: {
			trigger: 'axis',
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',
			padding: 8,
			textStyle: {
				color: '#fff',
			},
			formatter: function (params) {
				let relVal = '<strong>' + params[0].name + '</strong><br/>';
				for (let i = 0, l = params.length; i < l; i++) {
					const formattedValue = tranNumberS(params[i].value, 2);
					relVal += `${params[i].marker} ${params[i].seriesName}: ${formattedValue}<br/>`;
				}
				return relVal;
			},
		},
		legend: {
			width: '80%',
			right: '10%',
			// top: '10%',
			textStyle: {
				fontSize: 16,
				color: '#E2F0FF',
			},
			type: 'scroll',
			pageTextStyle: {
				color: '#fff', // 文字样式
			},
		},
		grid: {
			top: 70,
			bottom: 22,
			left: '10%',
			right: '10%',
		},
		xAxis: {
			type: 'category',
			data: val[0]?.time,
			axisLabel: {
				textStyle: {
					color: '#E2F0FF',
					fontSize: 18,
				},
				formatter: (params) => {
					if (typeof params !== 'string') {
						return params;
					}
					// 检查 params 是否包含时间部分 (HH:mm 或 HH:mm:ss)
					const hasTimePart = /\s\d{2}:\d{2}(?::\d{2})?/.test(params);
					const momentDate = moment(
						params,
						hasTimePart ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD',
						true,
					);
					if (!momentDate.isValid()) {
						return params;
					}
					return hasTimePart
						? momentDate.format('HH:mm')
						: momentDate.format('MM/DD');
				},
			},
		},
		yAxis: {
			name: `单位: km`,
			nameTextStyle: {
				color: '#D8DBDE',
				fontSize: 14,
			},
			type: 'value',
			splitLine: {
				show: true,
				lineStyle: {
					color: 'rgba(222, 227, 239, 0.3)',
					type: [2, 4],
				},
			},
			axisLabel: {
				textStyle: {
					color: 'rgba(212, 232, 254, 1)',
					fontSize: 18,
				},
				formatter: function (value, index) {
					return tranNumbers(Number(value), 2);
				},
			},
		},
		dataZoom: [
			//Y轴滑动条
			{
				type: 'inside', //滑动条
				show: true, //开启
				xAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
				left: '93%', //滑动条位置
			},
			//y轴内置滑动
			{
				type: 'inside', //内置滑动，随鼠标滚轮展示
				xAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
			},
		],
		series: seriesList,
	};
};

/*************  ✨ Codeium Command ⭐  *************/
/**
 *  Generate echarts configuration for online situation chart
 *  @param {Object} data - chart data
 *  @return {Object} echarts configuration
 */
/******  9c5cfe5a-8540-4f08-bf10-f317abf934d2  *******/
export const getOnlineSituationEcharts = (data) => {
	return {
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999',
				},
			},
		},
		grid: {
			top: 35,
			bottom: 45,
		},
		legend: {
			data: ['上线率', '总量'],
			top: 0, // 调整图例的位置，使其移动到图的上方
			right: 100, // 调整图例的位置，使其移动到图的右侧
			textStyle: {
				color: '#fff',
				fontSize: 16,
			},
			itemWidth: 10, // 设置图例项的宽度
			itemHeight: 10, // 设置图例项的高度
			itemShape: 'circle', // 设置图例项的形状为圆形
		},
		xAxis: [
			{
				type: 'category',
				data: data?.adm,
				axisPointer: {
					type: 'shadow',
				},
				axisLabel: {
					rotate: 40,
					textStyle: {
						color: '#E2F0FF',
						fontSize: 14,
					},
				},
			},
		],
		yAxis: [
			{
				name: `单位: 辆`,
				nameTextStyle: {
					color: 'rgba(212, 232, 254, 1)',
					fontSize: 12,
				},
				type: 'value',
				splitLine: {
					lineStyle: {
						type: 'dashed',
					},
				},
				axisLabel: {
					textStyle: {
						color: '#E2F0FF',
						fontSize: 16,
					},
					formatter: function (value, index) {
						return tranNumber(Number(value), 2);
					},
				},
			},
			{
				type: 'value',
				name: '上线率',
				nameTextStyle: {
					color: '#d8e2ef',
				},
				// interval: 1025,
				axisLabel: {
					formatter: function (value, index) {
						return tranNumber(Number(value), 2) + ' %';
					},
					textStyle: {
						fontSize: '12',
						color: '#d8e2ef',
					},
				},
				splitLine: {
					show: false,
				},
				axisLine: {
					show: false,
				},
			},
		],
		series: [
			{
				name: '总量',
				type: 'bar',
				tooltip: {},
				barWidth: 20,
				itemStyle: {
					// 设置柱状图的颜色为绿色
					color: 'rgb(102,204,204)',
				},
				data: data?.nums,
			},
			{
				name: '上线率',
				type: 'line',
				yAxisIndex: 1,
				itemStyle: {
					// 设置温度系列的样式
					color: 'rgb(42, 112, 252)', // 修改温度系列的颜色为橙色
				},
				data: data?.onlineData?.map((item, index) =>
					((item / data?.nums[index]) * 100).toFixed(0),
				),
			},
		],
	};
};
