import React from 'react';
import _ from 'lodash';
import { Tooltip } from 'antd';
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';

import { FiveviewsUI } from './style';

const Item = ({
	name,
	children,
	visible,
	position,
	setValue,
	optionStyle,
	menuStyle,
	isOpacity,
}) => {
	return (
		<div className="show-option">
			<div
				className="show-county-close"
				onClick={() => {
					setValue(!visible, position);
				}}
				style={{
					...menuStyle,
					opacity: isOpacity ? '0.6' : '1',
				}}
			>
				<Tooltip title={name?.length > 5 ? name : null} color="#213340">
					<div className="show-county-arrow">
						<div className="show-county-text">{name}</div>
						{_.isEmpty(children) ? null : visible ? (
							<CaretUpOutlined className="arrow" />
						) : (
							<CaretDownOutlined className="arrow" />
						)}
					</div>
				</Tooltip>
			</div>
			{visible && (
				<div
					className="show-county-none"
					style={{
						...menuStyle,
					}}
				>
					{children?.map((item, i) => {
						return (
							<div
								className={`show-county-style ${
									item.visible ? 'active-option' : ''
								}`}
								onClick={() => {
									setValue(!item.visible, position, i);
								}}
								key={item?.id}
								style={{ ...optionStyle }}
							>
								{item.name}
							</div>
						);
					})}
				</div>
			)}
		</div>
	);
};

export default function Fiveviews(props) {
	const { data, setData } = props;

	return (
		<FiveviewsUI>
			{data !== undefined &&
				data?.map((item, index) => {
					let isOpacity = false;
					// if (index === 0) {
					//   isOpacity = data[0]?.id !== 'county';
					// }
					return (
						<Item
							{...item}
							position={index}
							setValue={setData}
							key={item.name}
							isOpacity={isOpacity}
						/>
					);
				})}
		</FiveviewsUI>
	);
}
