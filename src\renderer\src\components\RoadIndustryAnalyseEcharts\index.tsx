import { useEffect, useRef, useState, useContext } from 'react';
import * as echarts from 'echarts';
import { type } from 'os';
import { formatNumber, tranNumber } from '@renderer/hooks';

const RoadIndustryAnalyseEcharts = (props) => {
	const { dataArr = null, columnList = '' } = props;
	const echartsContainerRef = useRef(null);
	const echartsChartRef = useRef<any>(null);

	useEffect(() => {
		if (!dataArr || !dataArr?.result?.length) return;
		echartsChartRef.current = echarts.init(echartsContainerRef.current);
		// mock 数据
		// const dataArr = {
		//   xdata: ['主干道', '次干道', '高速公路', '快速路', '支路'],
		//   result: [
		//     { name: '货车', data: [320, 435, 490, 270, 360] },
		//     { name: '渣土车', data: [150, 220, 210, 310, 288] },
		//     { name: '工程车', data: [250, 280, 240, 180, 288] },
		//     { name: '公交车', data: [180, 250, 210, 180, 288] },
		//     { name: '其他客车', data: [130, 210, 340, 180, 288] },
		//     { name: '环卫车', data: [230, 220, 260, 180, 288] },
		//     { name: '其他用途', data: [180, 420, 210, 180, 288] }
		//   ]
		// }
		dataArr.result.forEach((item) => {
			item.data = item.data.map((i) => formatNumber(i));
		});
		const diamondData = dataArr.result.reduce((pre, cur, index) => {
			pre[index] = cur.data.map(
				(el, id) => el + (pre[index - 1] ? pre[index - 1][id] : 0),
			);
			return pre;
		}, []);

		const color = [
			[
				{ offset: 0, color: '#61C6FF' },
				{ offset: 0.5, color: '#61C6FF' },
				{ offset: 0.5, color: '#3fb2f4' },
				{ offset: 1, color: '#3fb2f4' },
			],
			[
				{ offset: 0, color: '#0080FF' },
				{ offset: 0.5, color: '#0080FF' },
				{ offset: 0.5, color: '#006dd9' },
				{ offset: 1, color: '#006dd9' },
			],
			[
				{ offset: 0, color: '#00FF00' },
				{ offset: 0.5, color: '#00FF00' },
				{ offset: 0.5, color: '#00d000' },
				{ offset: 1, color: '#00d000' },
			],
			[
				{ offset: 0, color: '#00CED1' },
				{ offset: 0.5, color: '#00CED1' },
				{ offset: 0.5, color: '#00acaf' },
				{ offset: 1, color: '#00acaf' },
			],
			[
				{ offset: 0, color: '#FFD700' },
				{ offset: 0.5, color: '#FFD700' },
				{ offset: 0.5, color: '#d8b801' },
				{ offset: 1, color: '#d8b801' },
			],
			[
				{ offset: 0, color: '#E3A07C' },
				{ offset: 0.5, color: '#E3A07C' },
				{ offset: 0.5, color: '#e88a57' },
				{ offset: 1, color: '#e88a57' },
			],
			[
				{ offset: 0, color: '#85A9FF' },
				{ offset: 0.5, color: '#85A9FF' },
				{ offset: 0.5, color: '#5f8efb' },
				{ offset: 1, color: '#5f8efb' },
			],
		];

		let series = dataArr.result.reduce((p, c, i, array) => {
			p.push(
				{
					z: i + 1,
					stack: '总量',
					type: 'bar',
					name: c.name,
					barWidth: 30,
					data: c.data,
					itemStyle: {
						color: {
							type: 'linear',
							x: 0,
							x2: 1,
							y: 0,
							y2: 0,
							colorStops: color[i],
						},
					},
				},
				{
					z: i + 10,
					type: 'pictorialBar',
					symbolPosition: 'end',
					symbol: 'diamond',
					symbolOffset: [0, '-50%'],
					symbolSize: [30, 10],
					data: diamondData[i],
					itemStyle: {
						color: {
							type: 'linear',
							x: 0,
							x2: 1,
							y: 0,
							y2: 0,
							colorStops: color[i],
						},
					},
					tooltip: { show: false },
				},
			);

			// 是否最后一个了？
			if (p.length === array.length * 2) {
				p.push({
					z: array.length * 2,
					type: 'pictorialBar',
					symbolPosition: 'start',
					data: dataArr.result[0].data,
					symbol: 'diamond',
					symbolOffset: ['0%', '50%'],
					symbolSize: [30, 10],
					itemStyle: {
						color: {
							type: 'linear',
							x: 0,
							x2: 1,
							y: 0,
							y2: 0,
							colorStops: color[0],
						},
					},
					tooltip: { show: false },
				});
				return p;
			}

			return p;
		}, []);

		// tooltip
		const tooltip = {
			trigger: 'axis',
			formatter: (params) => {
				if (!params || params.length === 0) {
					return '';
				}
				let tooltipContent = '';
				params.forEach((param) => {
					if (
						param.seriesType === 'bar' ||
						param.seriesType === 'pictorialBar'
					) {
						const value = param.value;
						const formattedValue = tranNumber(value, 2);
						tooltipContent += `<div>${param.seriesName}: ${formattedValue} </div>`;
					}
				});

				return tooltipContent;
			},
		};

		// legend
		const legend = {
			data: dataArr.result.map((item) => item.name),
			textStyle: { fontSize: 14, color: '#fff' },
			itemWidth: 25,
			itemHeight: 15,
			itemGap: 15,
			bottom: '5%',
			selectedMode: false,
		};
		// grid
		const grid = { top: '20%', left: '15%', right: '5%', bottom: '35%' };

		// xAxis
		const xAxis = {
			axisTick: { show: true },
			axisLine: { lineStyle: { color: 'rgba(255,255,255, .2)' } },
			axisLabel: { textStyle: { fontSize: 14, color: '#fff' } },
			data: dataArr.xdata,
		};

		// yAxis
		const yAxis = [
			{
				name: `单位(${columnList.unit})`,
				nameTextStyle: {
					color: 'white',
					padding: [20, 20, 0, 20],
				},
				type: 'value',
				splitLine: {
					show: true,
					lineStyle: { color: 'rgba(102, 178, 245, 0.537)' },
				},
				axisLine: { show: false },
				axisLabel: {
					textStyle: { fontSize: 16, color: '#fff' },
					formatter: function (value, index) {
						return tranNumber(Number(value), 2);
					},
				},
			},
		];

		const title = {
			text: `道路级别行业${columnList.title}统计`,
			textStyle: {
				color: '#ffffff',
				fontSize: 20,
			},
			x: 'center',
			y: '5%',
		};

		// 渲染
		const option = {
			title,
			tooltip,
			xAxis,
			yAxis,
			series,
			grid,
			legend,
			backgroundColor: 'rgba(9, 30, 59, .7)',
		};
		echartsChartRef.current.setOption(option);
	}, [dataArr]);

	return (
		<div
			ref={echartsContainerRef}
			style={{
				width: '400px',
				height: '300px',
				position: 'absolute',
				top: '380px',
				right: '120px',
				zIndex: 6,
			}}
		/>
	);
};

export default RoadIndustryAnalyseEcharts;
