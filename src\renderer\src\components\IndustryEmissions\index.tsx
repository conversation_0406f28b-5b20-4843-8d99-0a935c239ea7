import { useEffect, useState } from 'react';
import { Space, Button, Dropdown } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import Echarts from '@renderer/components/echarts';
import { useAuth } from '@renderer/hooks/useAuth';
import moment from 'moment';
import Box from '../../baseUI/Box';
import TimeTypeRadio from '../../baseUI/TimeTypeRadio2';
import {
	getNox,
	// getIndustyListGroupByFourthType,
	getIndustryNoxUser,
	getEmissionIndustyListGroupByLevel,
	getEmissionIndustyListGroupByDatetime,
	getAffiliationInfoByID,
} from '@renderer/api';
import { getCarColorTwo } from '../../../../main/data';
import { TimeTypeRadioStyle, DropdownStyle, Container } from './style';
import ScrollListWidthTitle from '@renderer/components/SlidingLayer/DailyActivityLevel/components/ScrollListWidthTitle';
import industryClassification from '@renderer/components/AreaSide/components/IndustryClassification';
import type { MenuProps } from 'antd';

const typeList = [
	{ id: '1', name: '行业' },
	{ id: '2', name: '类型' },
	{ id: '3', name: '用户' },
];

const columns = [
	{
		title: '排行',
		dataIndex: 'rank',
	},
	{
		title: '用户名称',
		dataIndex: 'name',
	},
	{
		title: '所属区县',
		dataIndex: 'district_name',
	},
	{
		title: '排放量(kg)',
		dataIndex: 'nox_sum',
	},
	{
		title: '排放强度(g/km)',
		dataIndex: 'strength',
	},
];

const optionTable = {
	width: '100%',
	height: '200px',
	fontSize: 18,
	thclassname: 'th_row',
	tablebgcolor: 'rgba(9,30,59,0)',
	trheight: '40px',
	thheight: '40px',
	customwidth: true,
	rowbgcolor: [
		'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
	],
	thbgcolor: '#081F38',
};

export default function BJVehicleCountMonitoring() {
	const { token } = useAuth();
	const [timeData, setTimeData] = useState({
		start_time: '',
		end_time: '',
		time_type: '',
	});
	const [option1, setOption1] = useState({});
	const [option2, setOption2] = useState({});
	const [option3, setOption3] = useState({});
	const [type, setType] = useState('1');
	const [currentIndustryTop, setCurrentIndustryTop] = useState('全部');

	const items: MenuProps['items'] = Object.keys(industryClassification()).map(
		(item, idx) => {
			return {
				label: item,
				key: `${idx}`,
			};
		},
	);

	const [data, setData] = useState([]);

	const optionList = {
		option1,
		option2,
		option3,
	};

	useEffect(() => {
		if (!timeData.start_time || !timeData.end_time) return;
		const json = {
			token,
			data_type: '1',
			...timeData,
		};
		// getNoxData(json)
		// getIndustyListGroupByFourthTypeInfo(json)
		getIndustryNoxUserData();
		getEmissionIndustyListGroupByLevelData();
		getEmissionIndustyListGroupByDatetimeData();
		// getNox1(json)
	}, [timeData]);

	const getEmissionIndustyListGroupByDatetimeData = () => {
		const start_time = timeData.start_time;
		const end_time = timeData.end_time;
		const county_id = '';
		const time_type = 1;
		getEmissionIndustyListGroupByDatetime({
			start_time,
			end_time,
			county_id,
			time_type,
		})
			.then((res) => {
				const result = res as any;
				const xData = result[0]?.group_details?.datetime.map((item) =>
					moment(item).format('YYYY-MM-DD HH:00:00'),
				);

				const series =
					Array.isArray(result) &&
					result?.map((item) => {
						const color = getCarColorTwo(item.second_type);
						const colorNum = color.slice(4).slice(0, color.slice(4).length - 1);
						return {
							type: 'line',
							smooth: true, // 是否曲线
							name: item.second_type, // 图例对应类别
							color: getCarColorTwo(item.second_type),
							data: item.group_details.count, // 纵坐标数据
							areaStyle: {
								color: {
									type: 'linear',
									x: 0, //右
									y: 0, //下
									x2: 0, //左
									y2: 1, //上
									colorStops: [
										{
											offset: 0.1,
											color: `rgba(${colorNum}, 0.8)`, // 0% 处的颜色
										},
										{
											offset: 1,
											color: `rgba(${colorNum}, 0.1)`, // 100% 处的颜色
										},
									],
								},
							},
						};
					});

				const option2 = {
					grid: {
						left: '3%',
						right: '4%',
						top: '5%',
						bottom: '10%',
						height: '80%',
						containLabel: true,
					},
					legend: {
						show: true,
						left: '9%',
						bottom: '0',
						align: 'right',
						icon: 'circle',
						textStyle: {
							color: '#E2F0FF',
							fontSize: '12',
						},
					},
					tooltip: {
						trigger: 'axis',
					},
					animation: true,
					animationDuration: 1000,
					animationDelay: 1500,
					xAxis: {
						type: 'category',
						axisLine: {
							lineStyle: {
								color: 'rgba(198, 199, 199, 0.32)',
								fontSize: 12,
							},
						},
						axisLabel: {
							color: '#E8F4FF',
							fontSize: 12,
							formatter: (value, index) => {
								const time =
									timeData.time_type === '1'
										? moment(value).format('HH:00')
										: moment(value).format('MM-DD');
								return time;
							},
						},
						axisTick: {
							show: false,
						},
						data: xData,
					},
					yAxis: {
						type: 'value',
						axisLine: {
							lineStyle: {
								color: 'rgba(198, 199, 199, 0.32)',
								fontSize: 12,
							},
						},
						splitLine: {
							show: true,
							lineStyle: {
								color: 'rgba(198, 199, 199, 0.32)',
								type: 'dashed',
							},
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							fontSize: 16,
							color: '#E8F4FF',
						},
					},
					series,
				};
				setOption2(option2);
			})
			.catch((error) => {
				console.log('error', error);
			});
	};

	const getEmissionIndustyListGroupByLevelData = () => {
		const start_time = timeData.start_time;
		const end_time = timeData.end_time;
		const county_id = '';
		getEmissionIndustyListGroupByLevel({
			start_time,
			end_time,
			county_id,
		})
			.then((res) => {
				const result = res as any;
				const series: any = [];

				Array.isArray(result) &&
					result?.map((item) => {
						if (item.fourth_type !== '排放因子') {
							const color = getIndustyListGroupByFourthTypeColor(
								item.fourth_type,
							);
							series.push({
								name: item.fourth_type,
								type: 'bar',
								stack: 'total',
								label: {
									show: false,
								},
								emphasis: {
									focus: 'series',
								},
								data: item.group_details.count,
								itemStyle: {
									normal: {
										color: function (params) {
											return {
												type: 'linear',
												x: 1,
												y: 0,
												x2: 0,
												y2: 0,
												colorStops: [
													{
														offset: 0,
														color: `rgba(${color},0.4)`, // 0% 处的颜色
													},
													{
														offset: 0.5,
														color: `rgba(0,0,0,0)`, // 50% 处的颜色
													},
													{
														offset: 1,
														color: `rgba(${color},0.4)`, // 100% 处的颜色
													},
												],
											};
										},
										borderWidth: 2,
										borderColor: `rgba(${color},0.9)`,
									},
								},
							});
						} else {
							series.push({
								name: item.fourth_type,
								type: 'line',
								stack: 'Total',
								symbol: 'circle',
								symbolSize: 6,
								itemStyle: {
									color: '#FFFFFF',
								},
								data: item.group_details.count,
							});
						}
					});
				const option1 = {
					legend: {
						show: true,
						left: '15%',
						bottom: '0',
						align: 'right',
						textStyle: {
							color: '#E2F0FF',
							fontSize: '14',
						},
						data: [
							{ name: '中型', itemStyle: { color: 'rgb(255,204,133)' } },
							{ name: '大型', itemStyle: { color: 'rgb(93,190,244)' } },
							{ name: '小型', itemStyle: { color: 'rgb(133,169,255)' } },
							{ name: '轻型', itemStyle: { color: 'rgb(26, 87, 255)' } },
							{ name: '重型', itemStyle: { color: 'rgb(12, 251, 255)' } },
							{ name: '排放因子' },
						],
						itemWidth: 16, // 设置宽度
						itemHeight: 4, // 设置高度
					},
					tooltip: {
						trigger: 'axis',
						axisPointer: {
							//type: "shadow",
							textStyle: {
								color: '#fff',
							},
						},
					},
					title: {
						text: '单位: 千克',
						left: 10, // 标题
						bottom: 0,
						textStyle: {
							color: '#D8DBDE',
							fontSize: 16,
						},
					},
					grid: {
						top: '1%',
						left: '4%',
						right: '4%',
						bottom: '15%',
						containLabel: true,
					},
					animation: true,
					animationDuration: 1000,
					animationDelay: 1500,
					xAxis: {
						type: 'value',
						axisLine: {
							lineStyle: {
								color: 'rgba(198, 199, 199, 0.32)',
							},
						},
						splitLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: true,
							interval: 'auto',
							textStyle: {
								color: '#E2F0FF',
								fontSize: 16,
							},
						},
					},
					yAxis: {
						type: 'category',
						data: result[0]?.group_details?.second_type,
						axisTick: {
							show: false,
						},
						axisLine: {
							lineStyle: {
								color: 'rgba(198, 199, 199, 0.32)',
							},
						},
						axisLabel: {
							color: '#E8F4FF',
							fontSize: '16',
						},
					},
					series,
				};
				setOption1(option1);
			})
			.catch((error) => {
				console.log('error', error);
			});
	};

	const getIndustryNoxUserData = () => {
		const start_time = timeData.start_time;
		const end_time = timeData.end_time;
		const county_id = '';
		getIndustryNoxUser({ start_time, end_time, county_id }).then((res) => {
			if (!res.length) {
				setData([]);
				return;
			}
			const ids = res.map((item) => item.id);
			getAffiliationInfoByID({
				affiliation_ids: ids.toString(),
			})
				.then((_res) => {
					if (_res.length) {
						//违规企业
						const data = res.map((item, idx) => {
							return {
								district_name: item.district_name,
								nox_sum: Math.floor(item.nox_sum),
								strength: item.strength.toFixed(4),
								rank: idx + 1,
								name: _res[idx].AFFILIATION,
							};
						});
						setData(data);
					} else {
						setData([]);
					}
				})
				.catch((error) => {
					console.log('error', error);
					setData([]);
				});
		});
	};

	// 行业类型--这里暂时用行业违规的接口，等后续修改
	const getIndustyListGroupByFourthTypeInfo = (json) => {
		getIndustyListGroupByFourthType(json).then((res) => {
			const result = res as any;
			const series: any = [];

			Array.isArray(result) &&
				result?.map((item) => {
					if (item.fourth_type !== '排放因子') {
						const color = getIndustyListGroupByFourthTypeColor(
							item.fourth_type,
						);
						series.push({
							name: item.fourth_type,
							type: 'bar',
							stack: 'total',
							label: {
								show: false,
							},
							emphasis: {
								focus: 'series',
							},
							data: item.group_details.count,
							itemStyle: {
								normal: {
									color: function (params) {
										return {
											type: 'linear',
											x: 1,
											y: 0,
											x2: 0,
											y2: 0,
											colorStops: [
												{
													offset: 0,
													color: `rgba(${color},0.4)`, // 0% 处的颜色
												},
												{
													offset: 0.5,
													color: `rgba(0,0,0,0)`, // 50% 处的颜色
												},
												{
													offset: 1,
													color: `rgba(${color},0.4)`, // 100% 处的颜色
												},
											],
										};
									},
									borderWidth: 2,
									borderColor: `rgba(${color},0.9)`,
								},
							},
						});
					} else {
						series.push({
							name: item.fourth_type,
							type: 'line',
							stack: 'Total',
							symbol: 'circle',
							symbolSize: 6,
							itemStyle: {
								color: '#FFFFFF',
							},
							data: item.group_details.count,
						});
					}
				});
			const option1 = {
				legend: {
					show: true,
					left: '15%',
					bottom: '0',
					align: 'right',
					textStyle: {
						color: '#E2F0FF',
						fontSize: '14',
					},
					data: [
						{ name: '中型', itemStyle: { color: 'rgb(255,204,133)' } },
						{ name: '大型', itemStyle: { color: 'rgb(93,190,244)' } },
						{ name: '小型', itemStyle: { color: 'rgb(133,169,255)' } },
						{ name: '轻型', itemStyle: { color: 'rgb(26, 87, 255)' } },
						{ name: '重型', itemStyle: { color: 'rgb(12, 251, 255)' } },
						{ name: '排放因子' },
					],
					itemWidth: 16, // 设置宽度
					itemHeight: 4, // 设置高度
				},
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						//type: "shadow",
						textStyle: {
							color: '#fff',
						},
					},
				},
				title: {
					text: '单位: 千克',
					left: 10, // 标题
					bottom: 0,
					textStyle: {
						color: '#D8DBDE',
						fontSize: 16,
					},
				},
				grid: {
					top: '1%',
					left: '4%',
					right: '4%',
					bottom: '15%',
					containLabel: true,
				},
				animation: true,
				animationDuration: 1000,
				animationDelay: 1500,
				xAxis: {
					type: 'value',
					axisLine: {
						lineStyle: {
							color: 'rgba(198, 199, 199, 0.32)',
						},
					},
					splitLine: {
						show: false,
					},
					axisTick: {
						show: false,
					},
					axisLabel: {
						show: true,
						interval: 'auto',
						textStyle: {
							color: '#E2F0FF',
							fontSize: 16,
						},
					},
				},
				yAxis: {
					type: 'category',
					data: result[0]?.group_details?.second_type,
					axisTick: {
						show: false,
					},
					axisLine: {
						lineStyle: {
							color: 'rgba(198, 199, 199, 0.32)',
						},
					},
					axisLabel: {
						color: '#E8F4FF',
						fontSize: '16',
					},
				},
				series,
			};
			setOption1(option1);
		});
	};
	// const getNox1 = (json) => {
	//   getNox({ ...json, accumulated: '1' }).then((res) => {
	//     let result = res as any
	//     console.log('result', result)
	//     let yData: any
	//     let type = { 重型: [], 大型: [], 中型: [], 小型: [], 轻型: [], 其它: [], 排放因子: [] }
	//     result?.map((item) => {
	//       yData.push(item.second_type)
	//     })
	//     for (let key in type) {
	//     }
	//     let series: any = []
	//     Array.isArray(result) &&
	//       result?.map((item) => {
	//         if (item.fourth_type !== '排放因子') {
	//           let color = getIndustyListGroupByFourthTypeColor(item.fourth_type)
	//           series.push({
	//             name: item.fourth_type,
	//             type: 'bar',
	//             stack: 'total',
	//             label: {
	//               show: false
	//             },
	//             emphasis: {
	//               focus: 'series'
	//             },
	//             data: item.group_details.count,
	//             itemStyle: {
	//               normal: {
	//                 color: function (params) {
	//                   return {
	//                     type: 'linear',
	//                     x: 1,
	//                     y: 0,
	//                     x2: 0,
	//                     y2: 0,
	//                     colorStops: [
	//                       {
	//                         offset: 0,
	//                         color: `rgba(${color}, 0.8)` // 0% 处的颜色
	//                       },
	//                       {
	//                         offset: 1,
	//                         color: `rgba(${color}, 0.1)` // 100% 处的颜色
	//                       }
	//                     ]
	//                   }
	//                 }
	//               }
	//             }
	//           })
	//         } else {
	//           series.push({
	//             name: item.fourth_type,
	//             type: 'line',
	//             stack: 'Total',
	//             itemStyle: {
	//               color: '#8EFAFF'
	//             },
	//             data: item.group_details.count
	//           })
	//         }
	//       })
	//     const option1 = {
	//       legend: {
	//         show: true,
	//         left: '5%',
	//         bottom: '10%',
	//         align: 'right',
	//         textStyle: {
	//           color: '#E2F0FF',
	//           fontSize: '14'
	//         },
	//         itemWidth: 16, // 设置宽度
	//         itemHeight: 4 // 设置高度
	//       },
	//       tooltip: {
	//         trigger: 'axis',
	//         axisPointer: {
	//           type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
	//         }
	//       },
	//       grid: {
	//         top: '6%',
	//         left: '5%',
	//         right: '4%',
	//         bottom: '25%',
	//         containLabel: true
	//       },
	//       animation: true,
	//       animationDuration: 1000,
	//       animationDelay: 1500,
	//       xAxis: {
	//         type: 'value',
	//         axisLine: {
	//           lineStyle: {
	//             color: 'rgba(198, 199, 199, 0.32)'
	//           }
	//         },
	//         splitLine: {
	//           show: false
	//         },
	//         axisTick: {
	//           show: false
	//         },
	//         axisLabel: {
	//           show: false
	//         }
	//       },
	//       yAxis: {
	//         type: 'category',
	//         data: yData,
	//         axisTick: {
	//           show: false
	//         },
	//         axisLine: {
	//           lineStyle: {
	//             color: 'rgba(198, 199, 199, 0.32)'
	//           }
	//         },
	//         axisLabel: {
	//           color: '#E2F0FF',
	//           fontSize: '16'
	//         }
	//       },
	//       series
	//     }
	//     setOption1(option1)
	//   })
	// }

	// 车辆类型
	const getNoxData = (json) => {
		getNox({ ...json, accumulated: '0' }).then((res) => {
			const result = res as any;
			const xData = result[0]?.group_details?.datetime.map((item) =>
				moment(item).format('YYYY-MM-DD HH:00:00'),
			);

			const series =
				Array.isArray(result) &&
				result?.map((item) => {
					const color = getCarColorTwo(item.second_type);
					const colorNum = color.slice(4).slice(0, color.slice(4).length - 1);
					return {
						type: 'line',
						smooth: true, // 是否曲线
						name: item.second_type, // 图例对应类别
						color: getCarColorTwo(item.second_type),
						data: item.group_details.count, // 纵坐标数据
						areaStyle: {
							color: {
								type: 'linear',
								x: 0, //右
								y: 0, //下
								x2: 0, //左
								y2: 1, //上
								colorStops: [
									{
										offset: 0.1,
										color: `rgba(${colorNum}, 0.8)`, // 0% 处的颜色
									},
									{
										offset: 1,
										color: `rgba(${colorNum}, 0.1)`, // 100% 处的颜色
									},
								],
							},
						},
					};
				});

			const option2 = {
				grid: {
					left: '3%',
					right: '4%',
					top: '5%',
					bottom: '10%',
					height: '80%',
					containLabel: true,
				},
				legend: {
					show: true,
					left: '9%',
					bottom: '0',
					align: 'right',
					icon: 'circle',
					textStyle: {
						color: '#E2F0FF',
						fontSize: '12',
					},
				},
				tooltip: {
					trigger: 'axis',
				},
				animation: true,
				animationDuration: 1000,
				animationDelay: 1500,
				xAxis: {
					type: 'category',
					axisLine: {
						lineStyle: {
							color: 'rgba(198, 199, 199, 0.32)',
							fontSize: 12,
						},
					},
					axisLabel: {
						color: '#E8F4FF',
						fontSize: 12,
						formatter: (value, index) => {
							const time =
								timeData.time_type === '1'
									? moment(value).format('HH:00')
									: moment(value).format('MM-DD');
							return time;
						},
					},
					axisTick: {
						show: false,
					},
					data: xData,
				},
				yAxis: {
					type: 'value',
					axisLine: {
						lineStyle: {
							color: 'rgba(198, 199, 199, 0.32)',
							fontSize: 12,
						},
					},
					splitLine: {
						show: true,
						lineStyle: {
							color: 'rgba(198, 199, 199, 0.32)',
							type: 'dashed',
						},
					},
					axisTick: {
						show: false,
					},
					axisLabel: {
						fontSize: 16,
						color: '#E8F4FF',
					},
				},
				series,
			};
			setOption2(option2);
		});
	};

	// 行业类型颜色
	const getIndustyListGroupByFourthTypeColor = (type) => {
		let color;
		switch (type) {
			case '大型':
				color = '93,190,244';
				break;
			case '中型':
				color = '255,204,133';
				break;
			case '小型':
				color = '133,169,255';
				break;
			case '轻型':
				color = '26,87,255';
				break;
			case '重型':
				color = '12,251,255';
				break;
		}
		return color;
	};

	const handleMenuClickTop: MenuProps['onClick'] = (e) => {
		setCurrentIndustryTop(Object.keys(industryClassification())[e.key]);
	};

	const menuPropsTop = {
		items,
		onClick: handleMenuClickTop,
	};

	return (
		<Box
			title="行业排放"
			titlewidth="95%"
			height="100%"
			subTitle={
				<>
					<TimeTypeRadioStyle>
						<TimeTypeRadio defaultValue="1" timeType={(e) => setTimeData(e)} />
					</TimeTypeRadioStyle>
					{type === '3' ? (
						<DropdownStyle>
							<Dropdown menu={menuPropsTop} className={`dropDown1`} key={1}>
								<Button>
									<Space>
										{currentIndustryTop}
										<DownOutlined />
									</Space>
								</Button>
							</Dropdown>
						</DropdownStyle>
					) : null}
				</>
			}
		>
			<Container>
				<div className="echarts-line">
					<div className="echarts-title">
						{typeList.map((item) => (
							<span
								key={item.id}
								className={`${
									type == item.id ? 'title-select' : 'title-un-select'
								}`}
								onClick={() => setType(item.id)}
							>
								{item.name}
							</span>
						))}
					</div>
					{type === '3' ? (
						<div className="rankList">
							<ScrollListWidthTitle
								columns={columns}
								data={data}
								{...optionTable}
							/>
						</div>
					) : (
						<div className={`echarts${type}`}>
							<Echarts
								option={type === '1' ? option1 : option2}
								style={{ height: 220, width: 700 }}
								notMerge={true}
							></Echarts>
						</div>
					)}
				</div>
				{/* <div className="echarts1">
          <Echarts option={option1} />
        </div>
        <div className="echarts2">
          <Echarts option={option2} />
        </div> */}
			</Container>
		</Box>
	);
}
