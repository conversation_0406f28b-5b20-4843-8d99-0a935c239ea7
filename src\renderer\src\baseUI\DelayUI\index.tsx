import { useState, useEffect } from 'react';
import NumberAnimation from '../NumberAnimation';

const DelayUI = (props) => {
	const { targetNumber, delayTime, style, showEcharts } = props;
	let timer;
	const [isShow, setIsShow] = useState(false);

	useEffect(() => {
		if (timer) {
			clearTimeout(timer);
		}
		timer = setTimeout(() => {
			setIsShow(true);
		}, delayTime);
	}, []);

	useEffect(() => {
		showEcharts && showEcharts(isShow);
	}, [isShow]);

	return (
		<div>
			{!isShow ? (
				0
			) : (
				<NumberAnimation targetNumber={targetNumber} style={style} />
			)}
		</div>
	);
};

export default DelayUI;
