// 派发弹窗
import React, { memo } from 'react';

import Std from './Std';
import Weather from './Weather';
// import Sensor from './Sensor'
// import PollutionSource from './PollutionSource'
// import Distribution from './Distribution'
import { LayerType } from '@renderer/marsLayers/components/LayerMenu/layerDatas';

export default memo((props: { LayerId: string; [propName: string]: any }) => {
	switch (props.LayerId) {
		case LayerType.STDG: // 国控站
		case LayerType.STDS: // 市控站
		case LayerType.HIGHT: // 高密度站
			return <Std {...props} />;
		case LayerType.WEATHERSTATION: // 气象站
			return <Weather {...props} />;
	}
});
