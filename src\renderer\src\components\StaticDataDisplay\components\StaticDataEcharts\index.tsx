import { tranNumber } from '@renderer/hooks';
import { getCarColor } from '../../../../../../main/data';

const getBarChart = (data, unit) => {
	return {
		grid: {
			containLabel: true,
			bottom: '0%',
			top: '14%',
			left: '2%',
			right: '5%',
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow',
			},
		},
		legend: {
			orient: 'horizontal',
			top: '0',
			right: '4%',
			data: data?.tab,
			itemWidth: 12,
			itemHeight: 12,
			itemGap: 10,
			icon: 'rect',
			textStyle: {
				fontSize: 16,
				color: '#fff',
				padding: [5, 0, 0, 2],
				rich: {
					a: {
						verticalAlign: 'middle',
					},
				},
			},
		},
		xAxis: {
			triggerEvent: true,
			data: data?.date,
			axisLabel: {
				show: true,
				fontSize: 14,
				color: '#fff',
				align: 'center',
				verticalAlign: 'top',
			},
			axisTick: {
				show: true,
				lineStyle: {
					show: true,
					color: '#0B3561',
					width: 2,
				},
			},
		},
		yAxis: [
			{
				name: `单位: ${unit}`,
				nameTextStyle: {
					color: 'rgba(212, 232, 254, 1)',
					fontSize: 12,
				},
				type: 'value',
				splitLine: {
					lineStyle: {
						color: 'rgba(108, 166, 219, 0.5)',
						type: 'dashed',
					},
				}, //设置横线样式
				axisLine: {
					show: false,
					lineStyle: {
						color: '#233653',
					},
				},
				axisLabel: {
					textStyle: {
						fontSize: '12',
						color: '#fff',
					},
					formatter: function (value, index) {
						return tranNumber(Number(value), 2);
					},
				},
			},
		],
		series: [
			{
				name: data?.tab[1],
				type: 'bar',
				barWidth: 30,
				silent: true,
				itemStyle: {
					normal: {
						color: '#286FFC',
					},
				},
				data: data?.new,
			},
			{
				name: data?.tab[0],
				type: 'bar',
				barWidth: 30,
				silent: true,
				itemStyle: {
					normal: {
						color: '#3BEC99',
					},
				},
				data: data?.total,
			},
		],
	};
};

export const colors = [
	'#1E90FF',
	'#40E0D0',
	'#6495ED',
	'#4682B4',
	'#00BFFF',
	'#87CEEB',
	'#00FFFF',
	'#70DDFF',
	'#FF9F6A',
	'#20D469',
	'#D083FF',
	'#FF8282',
	'#769CFF',
	'#FFCF55',
];

const getLineChart = (data, unit) => {
	const series: any = [];
	let nameData = [];
	if (Object.keys(data)?.length > 0) {
		nameData = data?.resultArray?.map((item) => item.name);
		data?.resultArray?.map((item, idx) => {
			series.push({
				name: item?.name,
				type: 'line',
				symbol: 'circle',
				showAllSymbol: true,
				symbolSize: 0,
				smooth: false,
				lineStyle: {
					normal: {
						width: 3,
						color: colors[idx], // 线条颜色
					},
				},
				itemStyle: {
					color: colors[idx],
				},
				data: item?.value,
			});
		});
	}

	return {
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				lineStyle: {
					color: {
						type: 'linear',
						x: 0,
						y: 0,
						x2: 0,
						y2: 1,
						colorStops: [
							{
								offset: 0,
								color: 'rgba(126,199,255,0)', // 0% 处的颜色
							},
							{
								offset: 0.5,
								color: 'rgba(126,199,255,1)', // 100% 处的颜色
							},
							{
								offset: 1,
								color: 'rgba(126,199,255,0)', // 100% 处的颜色
							},
						],
						global: false, // 缺省为 false
					},
				},
			},
		},
		legend: {
			orient: 'horizontal',
			top: '0',
			right: '4%',
			type: 'scroll',
			itemWidth: 12,
			itemHeight: 12,
			itemGap: 10,
			textStyle: {
				fontSize: 16,
				color: '#fff',
				padding: [5, 0, 0, 2],
				rich: {
					a: {
						verticalAlign: 'middle',
					},
				},
			},
			data: nameData,
		},
		grid: {
			containLabel: true,
			bottom: '0%',
			top: '14%',
			left: '2%',
			right: '5%',
		},
		xAxis: [
			{
				triggerEvent: true,
				type: 'category',
				boundaryGap: false,
				axisLabel: {
					show: true,
					fontSize: 14,
					color: '#fff',
					align: 'center',
					verticalAlign: 'top',
				},
				axisTick: {
					show: true,
					lineStyle: {
						show: true,
						color: '#0B3561',
						width: 2,
					},
				},
				data: data?.xData,
			},
		],
		yAxis: [
			{
				name: `单位: ${unit}`,
				nameTextStyle: {
					color: 'rgba(212, 232, 254, 1)',
					fontSize: 12,
				},
				type: 'value',
				splitLine: {
					lineStyle: {
						color: 'rgba(108, 166, 219, 0.5)',
						type: 'dashed',
					},
				}, //设置横线样式
				axisLine: {
					show: false,
					lineStyle: {
						color: '#233653',
					},
				},
				axisLabel: {
					textStyle: {
						fontSize: '12',
						color: '#fff',
					},
					formatter: function (value, index) {
						return tranNumber(Number(value), 2);
					},
				},
			},
		],
		series: series,
	};
};

export { getBarChart, getLineChart };
