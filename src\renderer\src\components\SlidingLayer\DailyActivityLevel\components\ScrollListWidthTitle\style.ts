import styled from 'styled-components';
import { Props, ScrollListProps } from './type';

export const ScrollListUI = styled.div<ScrollListProps>`
	overflow: ${(props) => (props.scroll ? 'scroll' : 'hidden')};
	&::-webkit-scrollbar {
		width: 0;
		height: 0;
	}
	background: ${(props) => (props.tablebgcolor ? props.tablebgcolor : 'unset')};
	height: 100%;
	.list-content,
	.list-content1 {
		width: 100%;
	}
	.list-ul {
		display: flex;
		align-items: center;
		border-bottom: 1.6px solid #368ec1;
		.list-li {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			flex: 1;
			text-align: center;
			cursor: pointer;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.list-li:not(:last-child) {
			border-right: 1.6px solid #368ec1;
		}
		${(props) => {
			let res = '';
			props.columns.forEach((item, index) => {
				res += item.width
					? `
         .list-li:nth-of-type(${index + 1}) {
           width: ${item.width};
         }`
					: '';
			});
			return res;
		}}
	}
	${(props) => {
		let res = '';
		props.rowbgcolor &&
			Array.isArray(props.rowbgcolor) &&
			props.rowbgcolor.forEach((item, index) => {
				res += `
          .list-ul:nth-of-type( ${props.rowbgcolor?.length}n+${index + 1}) {
           background: ${item};
         }`;
			});
		return res;
	}}
	${(props) => {
		if (props.rowbgcolor && !Array.isArray(props.rowbgcolor)) {
			return `
          .list-ul {
           background: ${props.rowbgcolor};
         }`;
		}
	}}
`;

export const StyledContainer = styled.div<Props>`
	width: ${(props) => props.width};
	height: ${(props) => props.height};
	text-align: center;
	${(props) => (props.tablebgcolor ? ' background:' + props.tablebgcolor : '')};
	${(props) => (props.fontSize ? ' font-size: ' + props.fontSize + 'px;' : '')};
	${(props) => (props.fontWeight ? 'font-weight: ' + props.fontWeight : '')};
	${(props) => (props.color ? 'color:' + props.color : null)};
	ul.list-ul-title {
		/* border-top: 1.6px solid #368ec1; */
		/* border-left: 1.6px solid #368ec1;
		border-right: 1.6px solid #368ec1; */
		border-top-left-radius: 5px;
		border-top-right-radius: 5px;
		height: ${(props) => props.thheight};
		width: 100%;
		display: flex;
		align-items: center;
		background: ${(props) => (props.thbgcolor ? props.thbgcolor : 'unset')};

		.title-li {
			flex: 1;
			text-align: center;
			cursor: pointer;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			background: rgba(48, 122, 184, 0.8);
		}
		.title-li:not(:last-child) {
			border-right: 1.6px solid #368ec1;
		}
	}
	.list-content,
	.list-content1 {
		ul {
			height: ${(props) => props.trheight};
		}
	}
	.eventTable {
		width: 100%;
		height: ${(props) =>
			props.showheader
				? parseInt(props.height) -
				  parseInt(props?.thheight || '0') -
				  parseInt(props?.trheight || '0') *
						(props?.stickyTopData?.length || 0) +
				  'px'
				: props.height};
		border: 1.6px solid #368ec1;
		border-bottom-left-radius: 2px;
		border-bottom-right-radius: 2px;
	}
	.stickyTop-ul {
		height: ${(props) => props.trheight};
		width: 100%;
		display: flex;
		align-items: center;
		.stickyTop-li {
			flex: 1;
			text-align: center;
			cursor: pointer;
		}
	}
	ul.list-ul-title,
	ul.list-ul,
	ul.stickyTop-ul {
		.title-li,
		.list-li,
		.stickyTop-li {
			${(props) =>
				props.customwidth ? ' width:' + props.customwidth : 'flex:1;'}
		}

		${(props) => {
			let res = '';
			props.columns.forEach((item, index) => {
				res += item.width
					? `
         .title-li:nth-of-type(${index + 1}) {
            width: ${item.width};
          }
         .list-li:nth-of-type(${index + 1}) {
           width: ${item.width};
         }
         .stickyTop-li:nth-of-type(${index + 1}) {
           width: ${item.width};
         }

         `
					: '';
			});
			return res;
		}}
	}
	ul,
	li {
		list-style: none;
		padding: 0;
		margin: 0;
	}
`;
