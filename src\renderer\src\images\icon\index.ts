import layerMenu from './layerMenu.png';
import timebar from './timebar.png';
import point from './point.png';
import spread from './spread.png';
import spread1 from './spread1.png';
import spread2 from './spread2.png';
import bot from './bot.png';
import marshalling from './leftmodal.png';
import marshalling1 from './rightmodal.png';
import right from './right1.png';
import close1 from './close1.png';
import dataBg from './dataBg.png';
import searchBg from './searchBg.png';
import close from './close.png';
import returnIcon from './return.png';
import searchRegionBg from './searchRegionBg.png';
import topNavBg from './topNavBg.png';
import topNavFrame from './topNavFrame.png';
import selectRegionBg from './selectRegionBg.png';
import heatGrid from './heatGrid.png';
import carIcon from './carIcon.png';
import moreInfo from './moreInfo.png';
import realTimeInfo from './realTimeInfo.png';
import violationInfo from './violationInfo.png';
import accumulated from './accumulated.png';
import historicalTrajectory from './historicalTrajectory.png';
import historicalEmissions from './historicalEmissions.png';
import space from './space.png';
import cumulative from './cumulative.png';
export default {
	layerMenu,
	timebar,
	point,
	spread,
	spread1,
	spread2,
	bot,
	marshalling,
	marshalling1,
	right,
	close1,
	close,
	dataBg,
	searchBg,
	returnIcon,
	searchRegionBg,
	topNavBg,
	topNavFrame,
	selectRegionBg,
	heatGrid,
	accumulated,
	violationInfo,
	realTimeInfo,
	carIcon,
	moreInfo,
	historicalTrajectory,
	historicalEmissions,
	space,
	cumulative,
};
