import Request from '../request';
import { ViolationGISListData, ViolationPieListData } from './interfaces';

// 北京重柴api（WebSocket版本）
const baseURL = import.meta.env.RENDERER_VITE_API_DOMAIN;

const request = new Request({ baseURL });

// 图层--车辆情况--违规车辆
export const getViolationRealtimeData = (config: Record<string, any> = {}) => {
	return request.get<ViolationGISListData>(
		`/v2/violation/realtime-data`,
		config,
	);
};

// 图层--车辆情况--违规车辆--饼图
export const getViolationRealtimeDataRegion = (
	config: Record<string, any> = {},
) => {
	return request.get<ViolationPieListData>(
		`/v2/violation/realtime-data-region`,
		config,
	);
};
