import { useEffect, useState } from 'react';
import { Popover } from 'antd';

import Box from '../../baseUI/Box';
import { VechicleAnalysisContent } from './style';
// import LevelTwoTitle from '../../baseUI/LevelTwoTitle'
import LevelTwoTitleUpdate from '../../baseUI/LevelTwoTitleUpdate';
import IndustryAanalysisEcharts from '../../baseUI/IndustryAanalysisEcharts';
import RegistrationAreaEcharts from '../../baseUI/RegistrationAreaEcharts';
import RegistryAreaMap from './components/RegistryAreaMap';
import {
	getRegisterCounty,
	getIndustryDistribution,
	getDistributionRegistrationAreas,
} from '@renderer/api';
/**
 * 车辆分析
 */
interface list {
	name: string;
	nums: number;
	rate: number;
}
interface mapData {
	name?: string;
	value?: number;
}

interface props {
	name: string;
	num: number;
	rate: number;
}

const VechicleAnalysis = (props) => {
	const [list, setList] = useState<list[]>();
	const [mapData, setMapData] = useState<mapData[]>();
	const [echartData, setEchartData] = useState<Array<props>>([]);
	const [show, setShow] = useState(false);
	const [areaData, setAreaData] = useState<any>();
	// const [carName, setCarName] = useState([])
	useEffect(() => {
		getIndustryDistribution().then((res: any) => {
			if (!res) return;
			const data = (res as any).map((item) => {
				return {
					name: item.industry,
					num: item.nums,
					value: (item.rate * 100).toFixed(0),
				};
			});
			setEchartData(data);
		});

		// getIndustryDistribution({ county: 1 }).then((res: any) => {
		//   if (!res) return
		//   setAreaData(res)
		//   setCarName(res.map((item) => item.name))
		// })

		// getRegisterCounty().then((res) => {
		//   if (!res) return
		//   setList(res as list[])
		//   const mapData: mapData[] = []
		//   for (const item of res as list[]) {
		//     const temp: mapData = {}
		//     temp.name = item.name
		//     temp.value = item.nums
		//     mapData.push(temp)
		//   }
		//   setMapData(mapData)
		// })
		getDistributionRegistrationAreas().then((res: any) => {
			setMapData(res);
			setAreaData(res[0]?.group_details.adm);
		});
	}, []);

	useEffect(() => {}, []);

	// const content = (name) => {
	//   const data = areaData.filter((item) => (item.county = name))

	//   return data.map((item, index) => {
	//     return (
	//       <div className="area-item" key={index}>
	//         <span>{item.name}</span>
	//         <span>{item.nums}辆</span>
	//         <span>{item.rate * 100}%</span>
	//       </div>
	//     )
	//   })
	// }

	return (
		<VechicleAnalysisContent>
			<Box title="车辆分析" titlewidth="90%">
				<div className="container">
					<div className="container-left">
						<LevelTwoTitleUpdate title="行业分布" />
						<div className="echart-left">
							{echartData?.length > 0 ? (
								<IndustryAanalysisEcharts right={'10'} data={echartData} />
							) : (
								<div>暂无数据</div>
							)}
						</div>
					</div>
					<div className="container-right">
						<LevelTwoTitleUpdate title="注册区域分布" width="90%" />
						<div className="register-area">
							<div className="list">
								{mapData?.length !== undefined ? (
									<RegistrationAreaEcharts
										data={mapData}
										// carname={carName}
										areadata={areaData}
									></RegistrationAreaEcharts>
								) : (
									<div>暂无数据</div>
								)}
							</div>
							{/* <div className="map">
                <RegistryAreaMap mapData={mapData} />
              </div>
              <div className="list">
                {list?.map((item, index) => (
                  <Popover
                    placement="topLeft"
                    overlayClassName="troublesPop"
                    key={index}
                    content={() => content(item.name)}
                    trigger="click"
                  >
                    <div className="list-item">
                      <div className="name">{item.name}</div>
                      <div className="value">{item.nums}</div>
                    </div>
                  </Popover>
                ))}
              </div> */}
						</div>
					</div>
				</div>
			</Box>
		</VechicleAnalysisContent>
	);
};

export default VechicleAnalysis;
