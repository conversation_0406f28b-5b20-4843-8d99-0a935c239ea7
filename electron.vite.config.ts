import { resolve } from 'path';
import { defineConfig, externalizeDepsPlugin } from 'electron-vite';
import react from '@vitejs/plugin-react';
import cesium from 'vite-plugin-cesium';
import { mars3dPlugin } from 'vite-plugin-mars3d';
// import { vitePluginMars3d } from 'vite-plugin-mars3d'

export default defineConfig({
	main: {
		plugins: [externalizeDepsPlugin()],
	},
	preload: {
		plugins: [externalizeDepsPlugin()],
	},
	renderer: {
		resolve: {
			alias: {
				'@renderer': resolve('src/renderer/src'),
				'@types': resolve('src/types'),
				'@images': resolve('src/renderer/src/images'),
			},
		},
		plugins: [react(), mars3dPlugin(), cesium({ rebuildCesium: true })],
	},
});
