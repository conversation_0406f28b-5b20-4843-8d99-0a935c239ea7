import { useContext, useEffect, useState } from 'react';
import { Input, Space, Button, Dropdown, message } from 'antd';
import type { MenuProps } from 'antd';
import moment from 'moment';
import _debounce from 'lodash/debounce';
import { XyzTransitionGroup } from '@animxyz/react';
import { SearchOutlined, DownOutlined } from '@ant-design/icons';
import { PageContext } from '@renderer/context';
import {
	useAuth,
	formatNumber,
	useSpaceTimeParams,
} from '@renderer/hooks/index';
// import { COUNTY_ID_NAME_MAP } from '@renderer/marsLayers/components/AreaStreetStyles'
import industryClassification from '@renderer/components/AreaSide/components/IndustryClassification';
import TimeTypeRadio from '@renderer/baseUI/TimeTypeRadioCustom';
import icon from '@renderer/images/icon';
import RegionalBannerStyled from './styles';
import { getTargetList, ViolationNavList } from '@renderer/utils/util';
import LayerChoose from '@renderer/components/LayerChoose';

interface CustomDate {
	start_time: string;
	end_time: string;
	time_type: string;
}

let bannerTimeout;

const RegionalBanner = ({ currentSelectedId }) => {
	const {
		// violationInfo,
		// setViolationInfo,
		// hideModeMenu,
		// setCurrentTopNavMenu,
		// regionName,
		// setRegionName,
		// setRegionData,
		selectRegionDate,
		setSelectRegionDate,
		// setRegionalIndicators,
		// setStreetName,
		// streetShipData,
		currentIndustry,
		setCurrentIndustry,
		setCurrentIndustryList,
		setCurrentTopNavMenuList, // 多选框选择名称列表
		// currentIdList,
		setCurrentIdList,
	} = useContext(PageContext);

	// const [currentIdList, setCurrentIdList] = useState([1]) // 多选框选中id列表
	// const [currentId, setCurrentId] = useState(5)
	// const [currentRegion, setCurrentRegion] = useState('全市')
	// const [currentIndustry, setCurrentIndustry] = useState('全部')
	// const [navList, setNavList] = useState(getTargetList(currentSelectedId))
	const [timeType, setTimeType] = useState('24hours');
	const [customDate, setCustomDate] = useState<CustomDate>({
		start_time: moment().subtract(8, 'day').format('YYYY-MM-DD 00:00:00'),
		end_time: moment().subtract(1, 'day').format('YYYY-MM-DD 23:59:59'),
		time_type: '3',
	});
	// const [hideBanner, setHideBanner] = useState(false)
	// const [searchText, setSearchText] = useState('')
	const [messageApi, contextHolder] = message.useMessage();
	// const [hasSearchRegion, setHasSearchRegion] = useState(false)
	// const [hasDropdown, setHasDropdown] = useState(false)
	// const [hasDropdownIndustry, setHasDropdownIndustry] = useState(false)
	const [violationTypeOptions, setViolationTypeOptions] =
		useState(ViolationNavList);
	// const [navChooseId, setNavChooseId] = useState<any>()

	const typeOptions = [
		// {
		//   label: '当日',
		//   value: 'day'
		// },
		{
			label: '昨日',
			value: '24hours',
		},
		// {
		// 	label: '7天',
		// 	value: '7day',
		// },
		// {
		// 	label: '30天',
		// 	value: '30day',
		// },
		// {
		// 	label: '自定义',
		// 	value: 'custom',
		// },
	];

	const onChange = (e) => {
		setViolationTypeOptions(e);
	};

	useEffect(() => {
		const visibleIds = violationTypeOptions
			.filter((item) => item.visible)
			.map((item) => item.id);
		setCurrentIdList(visibleIds);
		const visibleValues = violationTypeOptions
			.filter((item) => item.visible)
			.map((item) => item.value);
		setCurrentTopNavMenuList(visibleValues);
	}, [violationTypeOptions]);

	// const items: MenuProps['items'] = Object.values(COUNTY_ID_NAME_MAP).map((item, idx) => {
	//   // if (item === '北京市') return null
	//   return {
	//     label: item,
	//     key: `${idx + 1}`
	//   }
	// })

	// const handleMenuClick: MenuProps['onClick'] = (e) => {
	//   setCurrentRegion(COUNTY_ID_NAME_MAP[e.key])
	//   setRegionName(COUNTY_ID_NAME_MAP[e.key])
	// }

	// const menuProps = {
	//   items,
	//   onClick: handleMenuClick
	// }

	const industryItems: MenuProps['items'] = Object.keys(
		industryClassification(),
	).map((item, idx) => {
		return {
			label: item,
			key: `${idx}`,
		};
	});

	const industryHandleMenuClick: MenuProps['onClick'] = (e) => {
		console.log('================industryHandleMenuClick====================');
		console.log(e, industryClassification());
		console.log('================industryHandleMenuClick====================');
		const label = Object.keys(industryClassification())[e.key];
		setCurrentIndustry(label);
		setCurrentIndustryList(industryClassification()[label]);
	};

	const menuPropsIndustry = {
		items: industryItems,
		onClick: industryHandleMenuClick,
	};

	useEffect(() => {
		setSelectRegionDate({
			timeType,
			customDate: useSpaceTimeParams(timeType, customDate),
		});
	}, [timeType, customDate]);

	return (
		<RegionalBannerStyled>
			<XyzTransitionGroup xyz="fade up-100%">
				{contextHolder}
				<div className={`top`}>
					<LayerChoose
						className="violation-nav"
						data={violationTypeOptions}
						onChange={onChange}
					/>
					{/* <Dropdown menu={menuPropsIndustry}>
            <Button>
              <Space>
                {currentIndustry}
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown> */}
					<div className="selectTime">
						<TimeTypeRadio
							right={'5%'}
							top={'80%'}
							type={timeType}
							typeOptions={typeOptions}
							timeType={(data) => setTimeType(data)}
							setTimeType={setTimeType}
							setCustomDate={setCustomDate}
							customStartTime={selectRegionDate.customDate}
						/>
					</div>
				</div>
			</XyzTransitionGroup>
		</RegionalBannerStyled>
	);
};

export default RegionalBanner;
