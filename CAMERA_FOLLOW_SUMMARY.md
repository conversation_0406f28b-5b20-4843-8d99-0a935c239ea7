# 实时轨迹改用 FixedRoute 实现丝滑镜头跟随

## 修改目标
将实时轨迹从 ModelPrimitive 改为 FixedRoute 实现，获得与历史轨迹相同的丝滑镜头跟随效果。

## 问题与解决
**问题**: ModelPrimitive 无法实现丝滑的镜头跟随效果，手动控制镜头会导致跳跃和不自然的移动。
**解决方案**: 改用 FixedRoute 来实现实时轨迹，利用其原生的镜头跟随功能。

## 主要修改

### 1. 历史轨迹镜头优化
**位置**: FixedRoute 配置中的 camera 参数

**修改前**:
```javascript
camera: {
  type: 'gs',
  pitch: -30,
  radius: 20000,
},
```

**修改后**:
```javascript
camera: {
  type: 'gs',
  pitch: -35,
  radius: 300,
  followedX: 80,
  followedZ: 50,
  heading: 0,
  roll: 0,
},
```

### 2. 实时轨迹改用 FixedRoute
**新实现方式**: 累积收集轨迹点，使用 FixedRoute 创建完整轨迹

**核心逻辑**:
```javascript
// 累积轨迹点（不限制数量，保持完整轨迹）
realTimePositions.push([transformedCoords[0], transformedCoords[1]]);

// 首次创建 FixedRoute（当有2个点时）
if (realTimePositions.length === 2 && !realTimeFixedRoute) {
  realTimeFixedRoute = new mars3d.graphic.FixedRoute({
    name: `${vid}_realtime`,
    frameRate: 1,
    positions: realTimePositions,
    clockLoop: false,
    camera: {
      type: 'gs',
      pitch: -35,
      radius: 300,
      followedX: 80,
      followedZ: 50,
    },
    model: {
      url: getCarTypeModel(res[0].type_of_industry),
      size: 3,
      fill: true,
      color: getCarColor(res[0].type_of_industry),
      minimumPixelSize: 80,
      maximumScale: 100,
    },
    attr: res[0],
  });

  // 自动开始播放
  realTimeFixedRoute.start();
} else if (realTimeFixedRoute && realTimePositions.length > 2) {
  // 动态添加新点到现有轨迹
  const newPoint = new mars3d.LngLatPoint(transformedCoords[0], transformedCoords[1]);
  realTimeFixedRoute.addDynamicPosition(newPoint, res[0].gDate);

  // 更新属性
  realTimeFixedRoute.attr = res[0];
}
```

### 3. 车辆模型优化

**历史轨迹车辆模型**:
```javascript
model: {
  url: getCarTypeModel(type),
  size: 3,                 // 从 2 增加到 3
  fill: true,
  color: getCarColor(type),
  minimumPixelSize: 80,    // 从 50 增加到 80
  maximumScale: 100,       // 新增最大缩放限制
},
```

**实时轨迹车辆模型**:
```javascript
style: {
  url: getCarTypeModel(res[0].type_of_industry),
  size: 3,                 // 从 2 增加到 3
  fill: true,
  color: getCarColor(res[0].type_of_industry),
  minimumPixelSize: 80,    // 从 50 增加到 80
  maximumScale: 100,       // 新增最大缩放限制
},
// 移除了无效的camera配置，改用手动控制
```

## 镜头参数说明

### 核心参数
- **type: 'gs'**: 跟随模式，镜头会跟随车辆移动
- **pitch: -35**: 俯视角度35度，提供良好的观察视角
- **radius: 300**: 镜头距离车辆300米，近距离跟随
- **followedX: 80**: X轴跟随距离，镜头在车辆前方80米
- **followedZ: 50**: Z轴跟随高度，镜头高度50米
- **heading: 0**: 方向角，保持默认方向
- **roll: 0**: 翻滚角，保持水平

### 视觉效果
- **贴近跟随**: 镜头距离从20000米缩小到300米
- **动态视角**: 镜头会自动跟随车辆的移动方向
- **平滑过渡**: 镜头移动平滑自然
- **清晰模型**: 车辆模型在近距离下更加清晰

## 使用效果

### 历史轨迹播放
- 点击"开始"按钮后，镜头会自动跟随车辆
- 提供类似无人机跟拍的视觉效果
- 可以清楚看到车辆的行驶路径和周围环境

### 实时轨迹显示
- 实时位置更新时，车辆模型稳定显示
- 镜头每3秒平滑跟随到车辆位置
- 使用flyTo方法，2秒平滑过渡动画
- 车辆模型经过优化，显示更清晰

## 最终效果
1. **持续跟随**: 镜头一直跟随车辆，不会中断或重置
2. **完整轨迹**: 保留所有历史轨迹点，轨迹不断延长
3. **丝滑体验**: 使用 FixedRoute 原生镜头跟随，无跳跃
4. **动态更新**: 使用 addDynamicPosition 动态添加新点
5. **连续播放**: 轨迹播放不会重置，保持连续性

## 优势
1. **沉浸式体验**: 贴近车辆的跟随视角更加真实
2. **清晰观察**: 近距离观看车辆模型和轨迹细节
3. **自动跟随**: 无需手动调整镜头位置
4. **平滑动画**: 镜头移动自然流畅
5. **完整记录**: 保留完整的移动历史，不丢失任何轨迹
6. **性能优化**: 使用 addDynamicPosition 而不是重建整个轨迹
7. **统一体验**: 历史轨迹和实时轨迹都有相同的跟随效果

## 注意事项
1. 近距离镜头在复杂地形下可能需要调整高度
2. 跟随参数可以根据实际需要进一步调整
3. 车辆模型放大后在某些情况下可能需要优化显示
4. 镜头跟随功能会自动启用，无需额外操作
