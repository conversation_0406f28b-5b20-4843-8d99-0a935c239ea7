// 北京缩略地图

import { useState, useEffect, useRef } from 'react';
import { init, Echarts } from 'echarts';

import { ThumbnailMapContianer } from './styles';

const ThumbnailMap = () => {
	const [echartInstance, setEchartInstance] = useState<Echarts>();

	const chartRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const instance = init(chartRef.current);
		const clickListener = (e) => {
			console.log(e);
		};

		instance.on('click', clickListener);

		setEchartInstance(instance);

		return () => {
			instance.off('click', clickListener);
			instance.dispose();
		};
	}, []);

	return (
		<ThumbnailMapContianer>
			<div className="thumbnail-map-charts" ref={chartRef}></div>
		</ThumbnailMapContianer>
	);
};

export default ThumbnailMap;
