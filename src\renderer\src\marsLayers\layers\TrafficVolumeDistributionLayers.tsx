import { useEffect, memo, useRef, useContext } from 'react';
// import localforage from 'localforage'
import * as mars3d from 'mars3d';
import _ from 'lodash';
import styled from 'styled-components';
import { wgs84togcj02 } from '@renderer/utils';
import { MapContext } from '@renderer/context';
import useRoadData from '@renderer/marsLayers/hooks/useRoadData';

// localforage.config({
//   name: 'zcLayerDataDB',
//   storeName: 'zcLayerDataStore'
// })
const COLORS = {
	0: 'rgba(0, 255, 0)',
	1: 'rgba(50, 255, 0)',
	2: 'rgba(100, 255, 0)',
	3: 'rgba(150, 255, 0)',
	4: 'rgba(200, 255, 0)',
	5: 'rgba(255, 255, 0)',
	6: 'rgba(255, 180, 0)',
	7: 'rgba(255, 120, 0)',
	8: 'rgba(255, 60, 0)',
	9: 'rgba(255, 0, 0)',
	'-': 'rgb(102,102,102)',
};

const LEVELNAME = {
	0: '一级',
	1: '二级',
	2: '三级',
	3: '四级',
	4: '五级',
	5: '六级',
	6: '七级',
	7: '八级',
	8: '九级',
	9: '十级',
};

const getLevelList = (arr) => {
	let num_max = Math.max(...arr);
	let num_min = Math.min(...arr);
	let diff = num_max - num_min;
	return [
		{ max: num_min + diff * 0.1, min: num_min, level: 0 },
		{ max: num_min + diff * 0.2, min: num_min + diff * 0.1, level: 1 },
		{ max: num_min + diff * 0.3, min: num_min + diff * 0.2, level: 2 },
		{ max: num_min + diff * 0.4, min: num_min + diff * 0.3, level: 3 },
		{ max: num_min + diff * 0.5, min: num_min + diff * 0.4, level: 4 },
		{ max: num_min + diff * 0.6, min: num_min + diff * 0.5, level: 5 },
		{ max: num_min + diff * 0.7, min: num_min + diff * 0.6, level: 6 },
		{ max: num_min + diff * 0.8, min: num_min + diff * 0.7, level: 7 },
		{ max: num_min + diff * 0.9, min: num_min + diff * 0.8, level: 8 },
		{ max: num_max, min: num_min + diff * 0.9, level: 9 },
	];
};

const getLevel = (value, arr) => {
	const newArr = arr.filter((item) => {
		return item.min <= value && item.max >= value;
	});
	if (newArr.length) {
		return newArr[0].level;
	}
	return 0;
};

type Props = {
	map: any;
	visible: boolean;
	layerId: string;
	foreignData?: null | [[], any][];
};

export default memo((props: Props) => {
	const { map, visible, foreignData = null, layerId } = props;
	const roadNetwork = useRef(null);
	const heightScale = layerId === 'trafficVolumeDistribution' ? 0.15 : 10000;
	// const [renderData, setRenderData] = useState<null | any[]>(null)
	const polylines = useRef<mars3d.Cesium.PolylineCollection | null>(null);
	const { sceneMode, timeBarEndTime } = useContext(MapContext) as any;
	const [roadData] = useRoadData(layerId); // 数据处理及更新hook
	const primitiveCollection = useRef<mars3d.Cesium.PrimitiveCollection | null>(
		null,
	);
	const handler = new mars3d.Cesium.ScreenSpaceEventHandler(map.scene.canvas);
	const clearLayerData = () => {
		// polylines.current!.removeAll()
		// primitiveCollection.current!.removeAll()
		if (roadNetwork.current) {
			roadNetwork.current.clearDrawing();
			roadNetwork.current.clear();
			roadNetwork.current.enabledEvent = false;
			map.removeLayer(roadNetwork.current);
		}
	};

	// useEffect(() => {
	//   polylines.current = map.scene.primitives.add(new mars3d.Cesium.PolylineCollection())
	//   map.scene.postProcessStages.fxaa.enabled = true
	//   primitiveCollection.current = map.scene.primitives.add(new mars3d.Cesium.PrimitiveCollection())
	// }, [])

	useEffect(() => {
		return () => {
			clearLayerData();
		};
	}, [timeBarEndTime]);

	useEffect(() => {
		if (visible && Array.isArray(roadData)) {
			roadNetwork.current = new mars3d.layer.GraphicLayer();
			map.addLayer(roadNetwork.current);
			// const values = roadData.map((item) => item[1]);
			// const list = [...getLevelList(values)];

			// 按数据数量等分成10份
			const data = roadData.sort((a, b) => a[1] - b[1]);
			const length = data.length;
			const chunkSize = Math.ceil(length / 10);

			for (let i = 0; i < length; i++) {
				const level = Math.floor(i / chunkSize);
				data[i][2] = level;
			}

			const PolylineData = data.map((item) => {
				const level = item[2];
				const array = item[0];
				let positions = [];
				for (let index = 0; index < array.length; index = index + 2) {
					const newArray = wgs84togcj02(array[index], array[index + 1]);
					if (newArray[0] && newArray[1]) {
						positions.push([newArray[0], newArray[1]]);
					}
				}

				return {
					positions,
					style: {
						width: 3,
						color: COLORS[level],
					},
					attr: {
						roadId: item[0],
						level,
					},
				};
			});
			const graphic = new mars3d.graphic.PolylineCombine({
				instances: PolylineData,
				// 高亮时的样式
				highlight: {
					type: mars3d.EventType.click,
					color: mars3d.Cesium.Color.WHITE,
				},
			});
			roadNetwork.current.addGraphic(graphic);

			// if (sceneMode === mars3d.Cesium.SceneMode.SCENE2D) {
			//   // 无法设置宽度，渲染速度3秒
			//   const geometryInstances = roadData.map((item, index) => {
			//     const array = item[0]
			//     let positions = []
			//     for (let index = 0; index < array.length; index = index + 2) {
			//       const newArray = wgs84togcj02(array[index], array[index + 1])
			//       if (newArray[0] && newArray[1]) {
			//         positions.push(newArray[0])
			//         positions.push(newArray[1])
			//       }
			//     }
			//     const level = getLevel(item[1], list)
			//     return new mars3d.Cesium.GeometryInstance({
			//       geometry: new mars3d.Cesium.SimplePolylineGeometry({
			//         positions: mars3d.Cesium.Cartesian3.fromDegreesArray(positions)
			//         // positions
			//       }),
			//       id: `${layerId}_road_${index}`,
			//       attributes: {
			//         color: mars3d.Cesium.ColorGeometryInstanceAttribute.fromColor(mars3d.Cesium.Color.AQUA)
			//       }
			//     })
			//   })
			//   primitiveCollection.current!.add(
			//     new mars3d.Cesium.Primitive({
			//       geometryInstances: geometryInstances,
			//       appearance: new mars3d.Cesium.PerInstanceColorAppearance({
			//         flat: true
			//       })
			//     })
			//   )

			//   /**
			//    * 可以设置宽度，渲染速度8秒多
			//         const dd = roadData?.map(item => {
			//           //@ts-ignore
			//           return (item[0].map(([lng,lat]) => [Number(lng), Number(lat)]))
			//         })
			//         const ccc = dd.map(item => {
			//           const colors = []
			//           const pos = []
			//           // @ts-ignore
			//           item.map(([lng, lat],index) => {
			//               // @ts-ignore
			//             pos.push(Cartesian3.fromDegrees(lng, lat))
			//               // @ts-ignore
			//             colors.push(getBgColor(renderData[index][1], layerId))
			//           })

			//           return new GeometryInstance({
			//             geometry: new PolylineGeometry({
			//               positions: pos,
			//               vertexFormat : PolylineColorAppearance.VERTEX_FORMAT,
			//               colors : colors,
			//               // colorsPerVertex : true,
			//               width: 8
			//             })
			//           })
			//         })

			//         primitiveCollection.current!.add(new Cesium.Primitive({
			//           geometryInstances: ccc,
			//           appearance: new PolylineColorAppearance()
			//         }))
			//           */

			//   // renderData?.forEach((line) => {
			//   //   addPolyline(line[0], line[1])
			//   // })
			// } else {
			//   // 渲染速度6.5秒

			//   const geometryInstances = roadData.map((item, index) => {
			//     const array = item[0]
			//     let positions = []
			//     for (let index = 0; index < array.length; index = index + 2) {
			//       const newArray = wgs84togcj02(array[index], array[index + 1])
			//       if (newArray[0] && newArray[1]) {
			//         positions.push(newArray[0])
			//         positions.push(newArray[1])
			//       }
			//     }
			//     const level = getLevel(item[1], list)
			//     return new mars3d.Cesium.GeometryInstance({
			//       geometry: new mars3d.Cesium.WallGeometry({
			//         positions: mars3d.Cesium.Cartesian3.fromDegreesArray(positions),
			//         // positions,
			//         maximumHeights: new Array(item[0].length / 2).fill(1000 + item[1] * heightScale),
			//         minimumHeights: new Array(item[0].length / 2).fill(0)
			//       }),
			//       id: `${layerId}_road_${index}`,
			//       attributes: {
			//         color: mars3d.Cesium.ColorGeometryInstanceAttribute.fromColor(
			//           // getBgColor(item[1], layerId)
			//           mars3d.Cesium.Color.fromCssColorString(COLORS[level])
			//         )
			//       }
			//     })
			//   })
			//   primitiveCollection.current!.add(
			//     new mars3d.Cesium.Primitive({
			//       geometryInstances: geometryInstances,
			//       appearance: new mars3d.Cesium.PerInstanceColorAppearance({
			//         flat: true
			//       })
			//     })
			//   )

			//   // // 渲染7.47s
			//   // renderData?.forEach((line, index) => {
			//   //   addWall(line[0], line[1], index)
			//   // })
			// }

			// handler.setInputAction(function (event) {
			//   console.log('路网点击事件', event)
			//   const pickedObject = map.scene.pick(event.position)
			//   // if (pickedObject?.id && pickedObject.id.indexOf(`${layerId}_road_`) >= 0) {
			//   //   console.log('路网点击事件1111', pickedObject)

			//   // }
			//   console.log('路网点击事件1111', pickedObject)
			// }, mars3d.Cesium.ScreenSpaceEventType.LEFT_CLICK)
		}
		return () => {
			// handler.removeInputAction(mars3d.Cesium.ScreenSpaceEventType.LEFT_CLICK)
			clearLayerData();
		};
	}, [roadData, visible]);

	// 贴地折线
	function addPolyline(positions, value) {
		const arr = positions.flat().map((i) => parseFloat(i));
		if (polylines.current === null) return;
		const line = {
			positions: mars3d.Cesium.Cartesian3.fromDegreesArray(arr),
			width: 8,
			material: mars3d.Cesium.Material.fromType(
				mars3d.Cesium.Material.FadeType,
				{
					repeat: false,
					fadeInColor: getBgColor(value, layerId),
					fadeOutColor: getBgColor(value, layerId),
					time: new mars3d.Cesium.Cartesian2(0.0, 0.0),
					fadeDirection: {
						x: true,
						y: false,
					},
				},
			),
		};
		polylines.current.add(line);
	}

	// 墙
	function addWall(positions, height, index) {
		// 绘制墙体
		const arr = positions.flat().map((i) => parseFloat(i));
		map.entities.add({
			name: '路网3D墙',
			id: index,
			wall: {
				positions: mars3d.Cesium.Cartesian3.fromDegreesArray(arr),
				// 设置高度
				maximumHeights: new Array(arr.length / 2).fill(
					1000 + height * heightScale,
				),
				minimumHeights: new Array(arr.length / 2).fill(0),
				material: getBgColor(height, layerId).withAlpha(0.2),
			},
		});
	}

	return (
		<RoadLayerStyled>
			<ul className="road-network-legend">
				{Object.values(LEVELNAME).map((item, i) => {
					return (
						<li>
							<i style={{ backgroundColor: COLORS[i] }} />
							{item}
						</li>
					);
				})}
			</ul>
		</RoadLayerStyled>
	);
});

const getBgColor = (value, layerId) => {
	if (layerId === 'trafficVolumeDistribution') {
		if (value <= 1000) return mars3d.Cesium.Color.fromCssColorString('#7CFC00');
		if (value <= 3500) return mars3d.Cesium.Color.fromCssColorString('#FFFF00');
		if (value <= 4500) return mars3d.Cesium.Color.fromCssColorString('#FF8000');
		return mars3d.Cesium.Color.fromCssColorString('#FF0000');
	} else {
		if (value <= 0.01) return mars3d.Cesium.Color.fromCssColorString('#7CFC00');
		if (value <= 0.035)
			return mars3d.Cesium.Color.fromCssColorString('#FFFF00');
		if (value <= 0.045)
			return mars3d.Cesium.Color.fromCssColorString('#FF8000');
		return mars3d.Cesium.Color.fromCssColorString('#FF0000');
	}
};

const RoadLayerStyled = styled.div`
	.road-network-legend {
		display: flex;
		flex-direction: column;
		position: absolute;
		bottom: 5px;
		left: 500px;
		z-index: 3;
		margin-bottom: 100px;
		padding: 10px 15px;
		background-color: rgba(0, 0, 0, 0.4);
		li {
			width: auto;
			align-items: center;
			display: flex;
			margin-top: 5px;
			color: #fff;
			i {
				width: 20px;
				height: 20px;
				display: inline-block;
				background-color: #fff;
				margin-right: 5px;
			}
		}
	}
`;
