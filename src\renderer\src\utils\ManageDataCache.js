import moment from 'moment';

import { ManageLayerData, clearOldDBData } from '@renderer/utils/getData';
import { getPoiRoad, getRealTimeOffline } from '@renderer/api';

// 路网、热力图数据缓存
export const LayerDataCache = (e) => {
	const { layerId, jsonData, bd } = e;
	const localforage = bd;
	// 清除indexDB过期数据
	clearOldDBData(localforage);
	if (
		layerId == 'trafficVolumeThermodynamics' ||
		layerId == 'vehicleEmissionsThermodynamics'
	) {
		const manageLayerData = new ManageLayerData({
			layerId: layerId,
			jsonData: jsonData,
			DB: localforage,
		});
		manageLayerData.run();
	} else {
		let max = 80000;
		if (jsonData) {
			max = Number(jsonData?.distribution.top);
		}
		const target = layerId === 'trafficVolumeDistribution' ? '2' : '1'; // 2:车流量路网 1:车排放路网
		const setDBData = (key, data) => {
			localforage
				.setItem(key, data)
				.then((value) => {})
				.catch((err) => {});
		};

		const requestNewData = async (time_current, reload = false) => {
			const end_time = moment(time_current).format('YYYY-MM-DD HH:00:00');
			let res = await getPoiRoad({
				start_time: moment(end_time)
					.subtract(1, 'h')
					.format('YYYY-MM-DD HH:00:00'),
				end_time: end_time,
				vehicle_type: '',
				time_type: '1',
				target,
				token: '',
				top: max,
			});
			if (Array.isArray(res)) {
				setDBData(`${layerId}_${end_time}`, res);
				return res;
			} else {
				setDBData(`${layerId}_${end_time}`, null);
				return null;
			}
		};

		const time_current = moment().format('YYYY-MM-DD HH:00:00');
		for (let i = 24; i > 0; i--) {
			localforage
				.getItem(
					layerId +
						'_' +
						moment(time_current).subtract(i, 'h').format('YYYY-MM-DD HH:00:00'),
				)
				.then((value) => {
					if (!value) {
						requestNewData(
							moment(time_current)
								.subtract(i, 'h')
								.format('YYYY-MM-DD HH:00:00'),
						);
					}
				})
				.catch(() => {
					requestNewData(
						moment(time_current).subtract(i, 'h').format('YYYY-MM-DD HH:00:00'),
					);
				});
		}
	}
};

// 实时离线数据缓存
export const RealTimeOfflineDataCache = (e) => {
	const { jsonData, bd } = e;
	const localforage = bd;

	const setDBData = (key, data) => {
		localforage
			.setItem(key, data)
			.then((value) => {})
			.catch((err) => {});
	};

	const requestNewData = async (jsonData) => {
		let nums = 190009;
		let times = 2;
		if (jsonData?.carNumber) nums = jsonData.carNumber;
		if (jsonData?.offlineTime) times = jsonData.offlineTime;

		let res = await getRealTimeOffline({
			nums,
			times,
		});

		if (res && typeof res === 'string') {
			try {
				res = JSON.parse(res);
				if (res?.result?.length) {
					setDBData('RealTimeOfflineData', res.result);
				} else {
					setDBData('RealTimeOfflineData', []);
				}
			} catch (error) {
				setDBData('RealTimeOfflineData', []);
			}
		} else if (res && typeof res === 'object') {
			if (res?.length) {
				setDBData('RealTimeOfflineData', res);
			} else {
				setDBData('RealTimeOfflineData', []);
			}
		} else {
			setDBData('RealTimeOfflineData', []);
		}
	};

	requestNewData(jsonData);
};

export const RoadGeometryDataCache = (e) => {
	const { bd } = e;
	const localforage = bd;

	const setDBData = (key, data) => {
		localforage
			.setItem(key, data)
			.then((value) => {})
			.catch((err) => {});
	};

	const getRoadGeometryData = () => {
		let jsonPath = `file:///public/data/road_geometry.json`;
		fetch(jsonPath)
			.then((res) => res.json())
			.then((res) => {
				setDBData('roadGeometry', res);
			});
	};

	localforage
		.getItem('roadGeometry')
		.then((value) => {
			if (!value) {
				getRoadGeometryData();
			}
		})
		.catch(() => {
			getRoadGeometryData();
		});
};

export const BeiJingTownDataCache = (e) => {
	const { bd } = e;
	const localforage = bd;

	const setDBData = (key, data) => {
		localforage
			.setItem(key, data)
			.then((value) => {})
			.catch((err) => {});
	};

	const getBeiJingTownData = () => {
		let jsonPath = `file:///public/data/townJson.json`;
		fetch(jsonPath)
			.then((res) => res.json())
			.then((res) => {
				setDBData('BeiJingTownData', res);
			});
	};

	localforage
		.getItem('BeiJingTownData')
		.then((value) => {
			if (!value) {
				getBeiJingTownData();
			}
		})
		.catch(() => {
			getBeiJingTownData();
		});
};
