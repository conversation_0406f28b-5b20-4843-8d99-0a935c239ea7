export type Dict<T = any> = Record<string, T>;

export type Merge<T, P> = P & Omit<T, keyof P>;

export type Omit<T, K> = Pick<T, Exclude<keyof T, K>>;

export type AnyFunction<T = any> = (...args: T[]) => any;

export type FunctionArguments<T = any> = T extends (...args: infer R) => any
	? R
	: never;

export type Booleanish = boolean | 'true' | 'false';
export type StringOrNumber = string | number;

export type MessageOptions = {
	condition: boolean;
	message: string;
};
