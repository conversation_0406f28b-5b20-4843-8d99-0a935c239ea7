import styled from 'styled-components';
import imgs from '../../../images/layersMenu';
import background from '../../../images/background';
export const DashBoardStyle = styled.div`
	position: absolute;
	bottom: -40px;
	left: 50%;
	transform: translateX(-50%);
	width: 900px;
	height: 280px;
	z-index: 9;
	display: flex;
	justify-content: space-between;
	/* padding: 10px 20px; */
	font-size: 18px;
	/* padding: 20px 0; */
	.bike-center {
		width: 100%;
		display: flex;
		> div {
			width: 33.3%;
			height: 100%;
			position: relative;
			margin: 0 5px;
			box-sizing: border-box;
		}
	}
`;

export const EchartsStyle = styled.div`
	width: 100%;
	height: 100%;
	color: #fff;

	.title {
		width: 100%;
		height: 12%;
		display: flex;
		justify-content: center;
		align-items: center;
		background: url(${background.title});
		background-size: 100% 100%;
	}
	.echarts {
		width: 100%;
		height: 100%;
		margin-top: -2%;
	}
	.count {
		width: 21%;
		height: 36%;
		position: absolute;
		top: 49%;
		left: 39%;
		background: url(${background.echarts});
		z-index: 100;
		background-size: 100% 100%;
		text-align: center;
		div:nth-child(1) {
			padding-top: 20%;
			font-size: 20px;
			font-weight: 600;
		}
		div:nth-child(2) {
			padding-top: 4%;
			font-size: 12px;
		}
	}
`;
