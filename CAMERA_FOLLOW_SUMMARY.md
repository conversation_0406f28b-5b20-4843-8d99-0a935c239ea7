# 镜头跟随车辆功能修改总结

## 修改目标
基于当前版本添加镜头跟随车辆的功能，让观看轨迹播放时有更逼真的视觉体验。

## 问题与解决
**遇到的问题**: 实时轨迹使用ModelPrimitive，添加镜头跟随后出现车辆闪烁和消失的问题。
**解决方案**: 保留历史轨迹的镜头跟随功能，实时轨迹恢复到稳定状态。

## 主要修改

### 1. 历史轨迹镜头优化
**位置**: FixedRoute 配置中的 camera 参数

**修改前**:
```javascript
camera: {
  type: 'gs',
  pitch: -30,
  radius: 20000,
},
```

**修改后**:
```javascript
camera: {
  type: 'gs',
  pitch: -35,
  radius: 300,
  followedX: 80,
  followedZ: 50,
  heading: 0,
  roll: 0,
},
```

### 2. 实时轨迹镜头跟随
**问题**: ModelPrimitive 不支持自动镜头跟随，之前的方案导致车辆闪烁消失

**解决方案**: 使用地图的 flyTo 方法实现平滑镜头跟随，并添加节流控制

**实现方式**:
```javascript
// 在车辆位置更新时
if (graphicModel) {
  graphicModel.addDynamicPosition(point, 6);

  // 平滑跟随车辆位置（节流控制）
  const now = Date.now();
  if (now - lastCameraUpdate > 3000) { // 每3秒最多更新一次镜头
    lastCameraUpdate = now;
    map.flyTo({
      destination: mars3d.Cesium.Cartesian3.fromDegrees(
        gcj2wgs2[0],
        gcj2wgs2[1],
        500
      ),
      orientation: {
        heading: mars3d.Cesium.Math.toRadians(0),
        pitch: mars3d.Cesium.Math.toRadians(-35),
        roll: 0
      },
      duration: 2
    });
  }
}
```

### 3. 车辆模型优化

**历史轨迹车辆模型**:
```javascript
model: {
  url: getCarTypeModel(type),
  size: 3,                 // 从 2 增加到 3
  fill: true,
  color: getCarColor(type),
  minimumPixelSize: 80,    // 从 50 增加到 80
  maximumScale: 100,       // 新增最大缩放限制
},
```

**实时轨迹车辆模型**:
```javascript
style: {
  url: getCarTypeModel(res[0].type_of_industry),
  size: 3,                 // 从 2 增加到 3
  fill: true,
  color: getCarColor(res[0].type_of_industry),
  minimumPixelSize: 80,    // 从 50 增加到 80
  maximumScale: 100,       // 新增最大缩放限制
},
// 移除了无效的camera配置，改用手动控制
```

## 镜头参数说明

### 核心参数
- **type: 'gs'**: 跟随模式，镜头会跟随车辆移动
- **pitch: -35**: 俯视角度35度，提供良好的观察视角
- **radius: 300**: 镜头距离车辆300米，近距离跟随
- **followedX: 80**: X轴跟随距离，镜头在车辆前方80米
- **followedZ: 50**: Z轴跟随高度，镜头高度50米
- **heading: 0**: 方向角，保持默认方向
- **roll: 0**: 翻滚角，保持水平

### 视觉效果
- **贴近跟随**: 镜头距离从20000米缩小到300米
- **动态视角**: 镜头会自动跟随车辆的移动方向
- **平滑过渡**: 镜头移动平滑自然
- **清晰模型**: 车辆模型在近距离下更加清晰

## 使用效果

### 历史轨迹播放
- 点击"开始"按钮后，镜头会自动跟随车辆
- 提供类似无人机跟拍的视觉效果
- 可以清楚看到车辆的行驶路径和周围环境

### 实时轨迹显示
- 实时位置更新时，车辆模型稳定显示
- 镜头每3秒平滑跟随到车辆位置
- 使用flyTo方法，2秒平滑过渡动画
- 车辆模型经过优化，显示更清晰

## 优势
1. **沉浸式体验**: 贴近车辆的跟随视角更加真实
2. **清晰观察**: 近距离观看车辆模型和轨迹细节
3. **自动跟随**: 无需手动调整镜头位置
4. **平滑动画**: 镜头移动自然流畅
5. **稳定性**: 实时轨迹不再闪烁，车辆正常显示
6. **节流控制**: 避免过于频繁的镜头移动
7. **统一体验**: 历史轨迹和实时轨迹都有镜头跟随效果

## 注意事项
1. 近距离镜头在复杂地形下可能需要调整高度
2. 跟随参数可以根据实际需要进一步调整
3. 车辆模型放大后在某些情况下可能需要优化显示
4. 镜头跟随功能会自动启用，无需额外操作
