import React from 'react';
// import DelayUI from '@renderer/baseUI/DelayUI'
import { useFont } from '@renderer/hooks';
import LayoutContainer from './style';

interface Props {
	overview: any;
}

export const ManufacturersComponent: React.FC<Props> = (props) => {
	const { overview } = props;

	return (
		<>
			<LayoutContainer>
				<div className={'left-container' + ' ' + useFont()}>
					<div className="describe">
						<dl className="no2">
							<dt>
								<div className="circle"></div>
								厂商数量
							</dt>
							<dd>
								{overview?.brand_num || '--'}
								<span>家</span>
							</dd>
						</dl>
						<dl className="no2">
							<dt>
								<div className="circle"></div>
								型号数量
							</dt>
							<dd>
								{overview?.vehicle_num || '--'}
								<span>种</span>
							</dd>
						</dl>
					</div>
				</div>
			</LayoutContainer>
		</>
	);
};
