import styled from 'styled-components';
import menuBg from '@renderer/images/layout/menu-bg.png';
import icon from '@renderer/images/icon';

const HeaderStyled = styled.div`
	width: 100%;
	height: 100%;
	position: relative;
	z-index: 9;
	.header-bg {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 181px;
	}

	.title {
		position: absolute;
		left: 50%;
		top: 26%;
		transform: translateX(-50%);
		color: #c9ebff;
		font-size: 48px;
		letter-spacing: 3px;
		margin-bottom: 0;
	}

	.header-date {
		position: absolute;
		right: 35px;
		top: 32px;
		color: #fff;
		display: flex;
	}

	.layout {
		padding-top: 15px;
		width: 100%;
		display: flex;
		justify-content: space-between;
		z-index: 10;
	}

	.layout-demo {
		display: flex;
		justify-content: space-evenly;
		width: 35%;
	}

	.menu-item {
		width: 203px;
		height: 70px;
		font-size: 30px;
		line-height: 54px;
		cursor: pointer;
		background: url(${menuBg}) no-repeat center bottom;
		background-size: 100% 27px;
		text-align: center;
		color: #fff;
		z-index: 99;
	}
`;

const FlashStyled = styled.div`
	.flash {
		margin: 0 auto;
		width: 100%;
		height: 100%;
		z-index: 8;
	}
`;

const ModuleStyled = styled.div`
	z-index: 999;
	position: absolute;
	/* background-color: #326182; */
	&.order1 {
		width: 96%;
		height: 236px;
		top: -50px;
		left: 40px;
		transition: transform 1.5s ease;
		.bottom {
			transform: rotateX(-90deg) translateZ(118px);
		}
	}
	&.order3 {
		width: 90%;
		height: 264px;
		top: 250px;
		left: 40px;
		transition: transform 2s ease;
		.bottom {
			transform: rotateX(-90deg) translateZ(132px);
		}
	}
	&.order5 {
		width: 88%;
		height: 264px;
		top: 570px;
		left: 40px;
		transition: transform 2.5s ease;
		.bottom {
			transform: rotateX(-90deg) translateZ(132px);
		}
	}
	&.order2 {
		width: 96%;
		height: 236px;
		top: -50px;
		right: 30px;
		transition: transform 1.5s ease;
		.bottom {
			transform: rotateX(-90deg) translateZ(118px);
		}
	}
	&.order4 {
		width: 90%;
		height: 264px;
		top: 250px;
		right: 30px;
		transition: transform 2s ease;
		.bottom {
			transform: rotateX(-90deg) translateZ(132px);
		}
	}
	&.order6 {
		width: 88%;
		height: 264px;
		top: 570px;
		right: 30px;
		transition: transform 2.5s ease;
		.bottom {
			transform: rotateX(-90deg) translateZ(132px);
		}
	}

	.cube {
		width: 100%;
		height: 100%;
		position: absolute;
		transform-style: preserve-3d;
		transform-origin: 50% 50%;
		animation: slowspin 3s forwards;
	}
	.bottom,
	.front {
		position: absolute;
		width: 100%;
		height: 100%;
	}

	.front {
		transform: translateZ(150px);
	}

	@keyframes slowspin {
		0% {
			transform: rotateX(0);
		}
		40%,
		to {
			transform: rotateX(90deg);
		}
	}

	&.orderLeft {
		transform: translateX(-840px);
		&.leftAnimation {
			transform: translateX(0px);
		}
	}
	&.orderRight {
		transform: translateX(840px);
		&.rightAnimation {
			transform: translateX(0px);
		}
	}
`;

const Animation = styled.div`
	width: 100%;
	height: 100%;
	transition: transform 3s ease;
	z-index: 3;
	position: relative;
`;

const SlidingLayerStyled = styled.div`
	height: 100%;
	transition: transform 3s ease;
	z-index: 99;
	position: absolute;
	.slidingLayer {
		display: flex;
		flex-direction: column;
		background: url(${icon.right}) no-repeat;
		background-size: 100%;
		width: 829px;
		height: 58px;
		margin: 0 10px 0 0;
	}
	.slidtext {
		width: 96px;
		height: 33px;
		overflow-wrap: break-word;
		color: rgba(255, 255, 255, 1);
		font-size: 24px;
		font-family: PingFangSC-Regular;
		text-align: left;
		white-space: nowrap;
		line-height: 38px;
		margin: 12px 0 0 70px;
	}

	.closeLayer {
		position: absolute;
		right: 20px;
		top: 20px;
		color: #fff;
		font-size: 20px;
		font-family: Verdana, Geneva, Tahoma, sans-serif;
		cursor: pointer;
		z-index: 2;
		background: url(${icon.close1}) no-repeat;
		width: 20px;
		height: 19px;
	}
	&.leftSlid {
		left: 0.59%;
		top: 11.59%;
		right: initial;
		animation: leftSlid 0.5s linear forwards;
		transform: translateX(-880px);
		background: url(${icon.marshalling}) 0px 0px no-repeat;
		background-size: 100% 100%;
		height: 928px;
		&.closeIng {
			animation: leftClose 0.5s linear forwards;
		}
	}
	&.rightSlid {
		right: 0.59%;
		top: 11.59%;
		left: initial;
		animation: rightSlid 0.5s linear forwards;
		background: url(${icon.marshalling1}) 0 0 no-repeat;
		background-size: 100% 85.9%;
		transform: translateX(880px);
		background-size: 100% 100%;
		height: 928px;
		&.closeIng {
			animation: rightClose 0.5s linear forwards;
		}
	}
	@keyframes leftSlid {
		0% {
			transform: translateX(-880px);
		}
		100% {
			transform: translateX(0);
		}
	}
	@keyframes rightSlid {
		0% {
			transform: translateX(880px);
		}
		100% {
			transform: translateX(0);
		}
	}
	@keyframes leftClose {
		0% {
			transform: translateX(0);
		}
		100% {
			transform: translateX(-880px);
		}
	}
	@keyframes rightClose {
		0% {
			transform: translateX(0);
		}
		100% {
			transform: translateX(880px);
		}
	}
	.slidingLayer-content {
		padding: 20px 30px 20px 30px;
		flex: 1;
	}
`;

export {
	HeaderStyled,
	FlashStyled,
	ModuleStyled,
	Animation,
	SlidingLayerStyled,
};
