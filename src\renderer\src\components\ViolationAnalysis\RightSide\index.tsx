import { useEffect, useState, useContext } from 'react';
import { RightSideStyled, LayoutContainer } from './styles';
import Echarts from '@renderer/components/echarts';
import {
	getEnterpriseUsersOnlineSituation,
	getNetworkUserIntervalStatistics,
	getUserViolations,
	getUserViolationsRanking,
	getUserViolationsAnalyse,
} from '@renderer/api';
import { getAffiliationInfoByID } from '@renderer/api';
import { PageContext } from '@renderer/context';
import {
	COUNTY_ID_NAME_MAP,
	unitList,
} from '@renderer/marsLayers/components/AreaStreetStyles';
import ListWidthTitle from '@renderer/baseUI/ListWidthTitle';
import BasicModule from '../components/BasicModule';
import StatisticsOfEntrances from '../components/StatisticsOfEntrances';
import StatisticsOfGatheringPlaces from '../components/StatisticsOfGatheringPlaces';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const optionTable = {
	width: '100%',
	height: '350px',
	fontSize: 18,
	thclassname: 'th_row',
	tablebgcolor: 'rgba(9,30,59,0)',
	trheight: '40px',
	thheight: '40px',
	customwidth: true,
	rowbgcolor: [
		'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
	],
};

const columns = [
	{
		title: '排行',
		dataIndex: 'RANK',
	},
	{
		title: '用户名称',
		dataIndex: 'NAME',
	},
	{
		title: '所属区域',
		dataIndex: 'district',
	},
	{
		title: '违规次数',
		dataIndex: 'nums_vio',
	},
	{
		title: '车辆数量',
		dataIndex: 'nums_all',
	},
];

const RightSide = (props: Props) => {
	const { currentSelectedId, streetShipData } = props;
	const {
		selectRegionDate,
		regionName,
		streetName,
		currentIndustry,
		currentIndustryList,
		currentIdList,
	} = useContext(PageContext);
	//图表
	const [data, setData] = useState([]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		const params: any = {
			top: 5,
			start_time,
			end_time,
			// time_type
			topic: currentIdList.toString(),
		};
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		if (currentIndustry && currentIndustry !== '全部') {
			params.industry_name = currentIndustryList.join(',');
		}
		getUserViolationsRanking(params)
			.then((res) => {
				const ids = res.map((item) => item.id);
				getAffiliationInfoByID({
					affiliation_ids: ids.toString(),
				})
					.then((o) => {
						if (o.length) {
							const _data = o.map((item, i) => {
								return {
									RANK: i + 1,
									NAME: item.AFFILIATION,
									district: res[i].district,
									nums_vio: res[i].nums_vio,
									nums_all: res[i].nums_all,
								};
							});
							setData(_data);
						}
					})
					.catch((error) => {
						console.log('error', error);
					});
			})
			.catch((err) => {
				setData([]);
			});
	}, [
		selectRegionDate,
		currentIndustry,
		currentSelectedId,
		regionName,
		currentIdList,
	]);

	return (
		<RightSideStyled>
			<div className="violation-analysis-content">
				<div className="slidingLayer">
					<span className="slidtext">用户违规情况</span>
				</div>
				<LayoutContainer>
					<div className="echarts-line">
						<ListWidthTitle columns={columns} data={data} {...optionTable} />
					</div>
				</LayoutContainer>
				<StatisticsOfGatheringPlaces />
				<StatisticsOfEntrances />
			</div>
		</RightSideStyled>
	);
};

export default RightSide;
