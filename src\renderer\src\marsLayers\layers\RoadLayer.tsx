import React, { useRef, useEffect, useState, useContext } from 'react';
import * as mars3d from 'mars3d';
import { Cartesian3, Math } from 'mars3d-cesium';
import styled from 'styled-components';
import { isEmpty } from 'lodash';

import { PageContext, MapContext } from '@renderer/context';
import LayerChoose from '@renderer/components/LayerChoose';
import RoadType from '@renderer/marsLayers/data/roadType';
import {
	getRoadRanking,
	getRoadRankingV2,
	getRoadAnalyse,
	getRoadInfo,
	getRoadStatisticsIndustryEmissions,
} from '@renderer/api';
import { wgs84togcj02 } from '@renderer/utils';
import RoadIndustryAnalyseEcharts from '@renderer/components/RoadIndustryAnalyseEcharts';
import ScrollListWidthTitle from '@renderer/components/SlidingLayer/DailyActivityLevel/components/ScrollListWidthTitle';
import { COUNTY_ID_NAME_MAP } from '@renderer/marsLayers/components/AreaStreetStyles';
import popupBg from '@renderer/images/layer/popupBg2.png';

const orientation = {
	heading: Math.toRadians(0),
	pitch: Math.toRadians(-35),
	roll: 0.0,
};

const COLORS = {
	0: 'rgba(0, 255, 0)',
	1: 'rgba(50, 255, 0)',
	2: 'rgba(100, 255, 0)',
	3: 'rgba(150, 255, 0)',
	4: 'rgba(200, 255, 0)',
	5: 'rgba(255, 255, 0)',
	6: 'rgba(255, 180, 0)',
	7: 'rgba(255, 120, 0)',
	8: 'rgba(255, 60, 0)',
	9: 'rgba(255, 0, 0)',
	'-': 'rgb(102,102,102)',
};

const LEVELNAME = {
	0: '一级',
	1: '二级',
	2: '三级',
	3: '四级',
	4: '五级',
	5: '六级',
	6: '七级',
	7: '八级',
	8: '九级',
	9: '十级',
};

const columns_base = [
	{
		title: '排名',
		dataIndex: 'RANK',
	},
	{
		title: '道路名称',
		dataIndex: 'name',
	},
	{
		title: '道路等级',
		dataIndex: 'level',
	},
];

const columns_index = [
	{
		title: '在线车辆',
		unit: '辆',
		dataIndex: 'value',
	},
	{
		title: '排放量',
		unit: 'T',
		dataIndex: 'value',
	},
	{
		title: '里程',
		unit: 'KM',
		dataIndex: 'value',
	},
	{
		title: '油耗',
		unit: 'L',
		dataIndex: 'value',
	},
];

let roadGeometryData = {},
	loading = false;

export default (props: Props) => {
	const {
		currentTopNavMenu,
		selectRegionDate,
		setCurrentSelectedId,
		currentSelectedId,
		countyId,
		RoadGeometryDataDB,
	} = useContext(PageContext);
	// console.log('路段图层》》》', currentTopNavMenu, selectRegionDate, currentSelectedId)
	const { sceneMode } = useContext(MapContext);
	const { layerId, visible, map } = props;
	const layerRef = useRef(null);
	const PopupLayerRef = useRef(null);
	const [data, setData] = useState([]);
	const [columns, setColumns] = useState([]);
	const [roadList, setRoadList] = useState([]);
	const roadLayer = useRef(null);
	const [typeOptions, setTypeOptions] = useState(RoadType);
	const [list, setList] = useState([]);
	const [showPopupDom, setShowPopupDom] = useState(false);
	const [levelId, setLevelId] = useState('1,3,4');
	const [popupData, setPopupData] = useState(null);
	const [roadIndustryData, setRoadIndustryData] = useState(null);
	const [columnList, setColumnList] = useState('');
	const onChange = (e) => {
		const _levelId = e
			.filter((item) => item.visible == true)
			.map((item) => {
				return item.id;
			})
			.toString(',');
		setLevelId(_levelId);
		setTypeOptions(e);
	};

	const getTopicIndex = {
		在线车辆: 1,
		排放量: 2,
		里程: 3,
		油耗: 4,
	};

	const getLevelList = (arr) => {
		let num_max = arr.length;
		let num_min = 0;
		let diff = num_max - num_min;
		return [
			{ max: num_min + diff * 0.2, min: num_min, level: 0 },
			{ max: num_min + diff * 0.4, min: num_min + diff * 0.2, level: 1 },
			{ max: num_min + diff * 0.6, min: num_min + diff * 0.4, level: 2 },
			{ max: num_min + diff * 0.7, min: num_min + diff * 0.6, level: 3 },
			{ max: num_min + diff * 0.8, min: num_min + diff * 0.7, level: 4 },
			{ max: num_min + diff * 0.9, min: num_min + diff * 0.8, level: 5 },
			{ max: num_min + diff * 0.94, min: num_min + diff * 0.9, level: 6 },
			{ max: num_min + diff * 0.97, min: num_min + diff * 0.94, level: 7 },
			{ max: num_min + diff * 0.99, min: num_min + diff * 0.97, level: 8 },
			{ max: num_max, min: num_min + diff * 0.99, level: 9 },
		];
	};

	const getLevel = (value, arr) => {
		return arr.filter((item) => {
			return item.min <= value && item.max >= value;
		})[0].level;
	};

	const getRoadAnalyseData = () => {
		if (loading) return;
		loading = true;
		getRoadAnalyse({
			start_time: selectRegionDate.customDate.start_time,
			end_time: selectRegionDate.customDate.end_time,
			level: levelId,
			county_name: COUNTY_ID_NAME_MAP[countyId] || '',
			topic: getTopicIndex[currentTopNavMenu],
		})
			.then((res) => {
				if (!isEmpty(res)) {
					if (roadLayer.current) {
						roadLayer.current.clearDrawing();
						roadLayer.current.clear();
						roadLayer.current.enabledEvent = false;
					}
					// const values = Object.values(res);
					// // console.log('????????', getLevelList(values), values);

					// const list = [...getLevelList(values)];

					const entries = Object.entries(res);
					const arr = entries.sort((a, b) => a[1] - b[1]);
					const length = arr.length;
					const chunkSize = window.Math.ceil(length / 10);

					for (let i = 0; i < length; i++) {
						const level = window.Math.floor(i / chunkSize);
						arr[i][2] = level;
					}

					const PolylineData = arr.map((item, idx) => {
						const level = item[2];
						const positions = roadGeometryData[item[0]]['geo'].map((item2) =>
							wgs84togcj02(item2[0], item2[1]),
						);
						return {
							positions,
							style: {
								width: 3,
								color: COLORS[level],
							},
							attr: {
								roadId: item[0],
								level,
							},
						};
					});
					const graphic = new mars3d.graphic.PolylineCombine({
						instances: PolylineData,
						// 高亮时的样式
						highlight: {
							type: mars3d.EventType.click,
							color: mars3d.Cesium.Color.WHITE,
						},
					});
					roadLayer.current.addGraphic(graphic);
					roadLayer.current.enabledEvent = true; // 恢复事件
					roadLayer.current.on(mars3d.EventType.click, function (event) {
						getRoadInfoData(
							event.graphic.attr.roadId,
							event.graphic.attr.level,
						);
					});
				}
				loading = false;
			})
			.catch((error) => {
				loading = false;
			});
	};

	const setColumnsList = () => {
		return [
			...columns_base,
			...columns_index.filter((item) => item.title === currentTopNavMenu),
		];
	};

	const closePopup = () => {
		setPopupData(null);
	};

	const getRoadRankingV2Data = () => {
		getRoadRankingV2({
			start_time: selectRegionDate.customDate.start_time,
			end_time: selectRegionDate.customDate.end_time,
			level: levelId,
			county_name: COUNTY_ID_NAME_MAP[countyId] || '',
			topic: getTopicIndex[currentTopNavMenu],
		})
			.then((res) => {
				setData(
					res.map((item, i) => {
						const RANK = i + 1;
						return {
							RANK,
							name: item.name || '--',
							level: item.level,
							value: item.value,
							roadId: item.id,
						};
					}),
				);
			})
			.catch((error) => {});
	};

	const getRoadInfoData = (roadId, level) => {
		getRoadInfo({
			start_time: selectRegionDate.customDate.start_time,
			end_time: selectRegionDate.customDate.end_time,
			road_id: roadId || '',
		})
			.then((res) => {
				const attr = {};
				attr['道路名称:'] = res?.name || '--';
				attr['道路等级:'] = RoadType.find(
					(item) => item.layerId == res.highway,
				).value;
				attr['所属区县:'] = res?.district_name || '--';
				attr['所属街乡镇:'] = res?.town_name || '--';
				attr['道路级别'] = LEVELNAME[level];
				attr['在线车辆:'] = res?.track_count || '--';
				attr['排放量:'] = res?.nox ? window.Math.floor(res.nox) : '--';
				attr['里程:'] = res?.distance ? window.Math.floor(res.distance) : '--';
				attr['油耗:'] = res?.oil ? window.Math.floor(res.oil) : '--';
				setPopupData(attr);
			})
			.catch((error) => {});
	};

	const getRoadStatisticsIndustryEmissionsData = () => {
		getRoadStatisticsIndustryEmissions({
			start_time: selectRegionDate.customDate.start_time,
			end_time: selectRegionDate.customDate.end_time,
			level: levelId,
			county_name: COUNTY_ID_NAME_MAP[countyId] || '',
			topic: getTopicIndex[currentTopNavMenu],
		})
			.then((res) => {
				setRoadIndustryData(res);
			})
			.catch((error) => {});
	};

	const getMapRoadInfo = (roadId) => {
		if (!roadLayer.current) return;
		roadLayer.current.eachGraphic((graphic) => {
			const level = graphic.instances.find((item) => item.attr.roadId == roadId)
				.attr.level;
			getRoadInfoData(roadId, level);
		});
	};

	const OnRowSelection = (data) => {
		getMapRoadInfo(data.roadId);
	};

	useEffect(() => {
		if (!visible) return;
		// 使用 forEach 方法遍历数组获取单位和其他信息
		columns_index.forEach((column) => {
			if (column.title === currentTopNavMenu) {
				setColumnList(column);
			}
		});

		setCurrentSelectedId(layerId);
		if (selectRegionDate.customDate.start_time == '' || currentTopNavMenu == '')
			return;
		roadLayer.current = new mars3d.layer.GraphicLayer();
		map.addLayer(roadLayer.current);
		// if (currentSelectedId == layerId) {
		RoadGeometryDataDB.getItem('roadGeometry')
			.then((value) => {
				if (value) {
					roadGeometryData = value;
					getRoadAnalyseData();
				}
			})
			.catch(() => {
				console.log('查询路段坐标数据异常');
			});
		getRoadStatisticsIndustryEmissionsData();
		getRoadRankingV2Data();
		setColumns(setColumnsList());
		// } else {
		//   setCurrentSelectedId(layerId)
		// }
		map.camera.flyTo({
			destination:
				sceneMode === 3
					? Cartesian3.fromDegrees(116.331734, 38.277551, 130000)
					: Cartesian3.fromDegrees(116.481934, 40.205551, 500000),
			orientation: sceneMode === 3 ? orientation : {},
			duration: 4,
		});

		return () => {
			if (roadLayer.current) {
				roadLayer.current.clearDrawing();
				roadLayer.current.clear();
				roadLayer.current.enabledEvent = false;
				map.removeLayer(roadLayer.current);
			}
			setCurrentSelectedId('');
		};
	}, [
		visible,
		currentTopNavMenu,
		levelId,
		countyId,
		selectRegionDate.customDate.end_time,
		selectRegionDate.customDate.start_time,
	]);

	return (
		<RoadLayerStyled>
			<LayerChoose data={typeOptions} onChange={onChange} />
			<RoadIndustryAnalyseEcharts
				dataArr={roadIndustryData}
				columnList={columnList}
			/>
			<div className="rankList">
				<h3>路段信息排行</h3>
				<ScrollListWidthTitle
					columns={columns}
					data={data}
					OnRowSelection={OnRowSelection}
					{...optionTable}
				/>
			</div>
			<ul className="road-network-legend">
				{Object.values(LEVELNAME).map((item, i) => {
					return (
						<li>
							<i style={{ backgroundColor: COLORS[i] }} />
							{item}
						</li>
					);
				})}
			</ul>
			{popupData ? (
				<PopupStyle ref={PopupLayerRef}>
					<div className="pop-warp">
						<div className="closeButton" onClick={closePopup}>
							x
						</div>
						<div className="title">
							<span className="text">{popupData['道路名称:']}</span>
						</div>
						<div className="content">
							<div className="multiline">
								{Object.entries(popupData).map((item, index) => (
									<div className="item" key={index}>
										<span className="label">{item[0]}</span>：
										<span
											style={{
												color: '#fff',
											}}
										>
											{item[1]}
										</span>
									</div>
								))}
							</div>
						</div>
					</div>
				</PopupStyle>
			) : null}
		</RoadLayerStyled>
	);
};

const PopupStyle = styled.div`
	width: 883px;
	height: 537px;
	z-index: 5;
	color: #fff;
	font-size: 28px;
	background-image: url(${popupBg});
	background-position: 0 0;
	background-repeat: no-repeat;
	background-size: cover;
	position: absolute;
	top: 50%;
	left: 50%;
	margin: auto;
	transform: translate(-50%, -50%);
	.pop-warp {
		position: relative;
		.closeButton {
			width: 10px;
			height: 10px;
			display: block;
			position: absolute;
			right: 50px;
			top: 50px;
			z-index: 2;
			color: #fff;
			font-size: 30px;
			font-family: sans-serif;
			cursor: pointer;
		}
		.title {
			height: 100px;
			width: 100%;
			display: flex;
			justify-content: space-between;
			padding: 10px 5px 0px 30px;
			margin-bottom: 10px;
			box-sizing: border-box;
			background-size: 100% 100%;
			background-repeat: no-repeat;
			font-size: 30px;
			font-family: PingFang SC;
			font-weight: 400;
			color: #22f3e2;
			align-items: center;
		}
		span.text {
			display: inline-block;
			padding: 10px 15px;
			font-size: 28px;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: 600;
			z-index: 0;
			color: #4efad7;
			width: 60%;
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
		}
		.back {
			cursor: pointer;
			color: #56d1ed;
			background: none;
			border: none;
			font-size: 40px;
		}
		.main {
			padding: 20px 34px 0;
			flex: 1;
			width: 100%;
			color: rgba(226, 240, 255, 0.7);
		}
		.content {
			width: 100%;
			/* height: 100%; */
			font-size: 22px;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 500;
			line-height: 25px;
			padding: 30px 35px 0;
			.label {
				color: #65c2ff;
				font-weight: 400;
			}
			.multiline {
				width: 100%;
				display: flex;
				flex-wrap: wrap;
				padding-top: 30px;
				.item {
					width: 50%;
					margin-bottom: 35px; //大小待定
				}
			}
			p {
				margin-bottom: 16px;
				&:last-of-type {
					margin-bottom: 0;
				}
			}

			.oneLine {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
				margin-bottom: 16px; //大小待定
			}
			.data {
				display: flex;
				flex-wrap: wrap;

				li {
					margin-bottom: 16px; //大小待定
					&:last-of-type {
						margin-bottom: 0;
					}
				}
			}
		}
	}
`;
const RoadLayerStyled = styled.div`
	h3 {
		color: #fff;
		font-size: 22px;
		/* background-color: #0042b18a; */
		text-align: center;
		margin-bottom: 0;
		border: 1.6px solid #368ec1;
		border-bottom: 0;
		/* background: linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%); */
		line-height: 40px;
		background: #368ec1bf;
	}
	.rankList {
		width: 400px;
		position: absolute;
		z-index: 5;
		right: 120px;
		top: 90px;
		.th_row {
			.title-li:first-child {
				width: 70px;
				flex: initial;
			}
			.title-li:nth-child(3) {
				width: 120px;
				flex: initial;
			}
			.title-li:nth-child(4),
			.title-li:nth-child(5) {
				width: 100px;
				flex: initial;
			}
		}
	}
	.list-content,
	.list-content1 {
		.list-ul {
			.list-li {
				text-overflow: ellipsis;
				overflow: hidden;
				word-break: break-all;
				white-space: nowrap;
				display: flow-root;
				line-height: 38px;
			}
			.list-li:first-child {
				width: 70px;
				flex: initial;
			}
			.list-li:nth-child(3) {
				width: 120px;
				flex: initial;
			}
			.list-li:nth-child(4),
			.list-li:nth-child(5) {
				width: 100px;
				flex: initial;
			}
		}
	}
	.road-network-legend {
		display: flex;
		flex-direction: column;
		position: absolute;
		bottom: 5px;
		left: 500px;
		z-index: 3;
		margin-bottom: 0;
		padding: 10px 15px;
		background-color: rgba(0, 0, 0, 0.4);
		li {
			width: auto;
			align-items: center;
			display: flex;
			margin-top: 5px;
			color: #fff;
			i {
				width: 20px;
				height: 20px;
				display: inline-block;
				background-color: #fff;
				margin-right: 5px;
			}
		}
	}
`;
type Props = {
	map: any;
	layerId: string;
	visible: boolean;
};

const optionTable = {
	width: '100%',
	height: '240px',
	fontSize: 18,
	thclassname: 'th_row',
	tablebgcolor: 'rgba(9,30,59,0.7)',
	trheight: '40px',
	thheight: '40px',
	customwidth: true,
	rowbgcolor: [
		'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
	],
	thbgcolor: '#081F38',
};
