import * as React from 'react';
import styled from 'styled-components';

const MainContainer = styled.main`
	width: 100%;
	height: calc(100% - 181px);
	/* display: flex; */
	position: relative;
	.layout-bottom {
		width: 1757px;
		height: 62px;
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		z-index: 1;
	}
`;

type Props = {
	children?: React.ReactNode;
};

const Main: React.FC<Props> = (props) => {
	return <MainContainer>{props.children}</MainContainer>;
};

export default Main;
