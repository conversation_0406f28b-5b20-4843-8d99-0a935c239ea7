import * as React from 'react';
import { useState, useRef, useEffect, useContext, useMemo } from 'react';
import 'mars3d-cesium';
import * as mars3d from 'mars3d';
import moment from 'moment';
import _, { forEach } from 'lodash';

import TimeBar from '@renderer/marsLayers/components/TimeBar';
import BeiJingMap from '@renderer/marsLayers/components/BeiJingMap';
import ModeMenu from '@renderer/marsLayers/components/ModeMenu';
import LayerMenu from '@renderer/marsLayers/components/LayerMenu';
import TrajectoryLayer from '@renderer/marsLayers/layers/VehicleHistoricalTrajectory'; // 历史轨迹回放
import {
	layerDatas,
	LayerType,
} from '@renderer/marsLayers/components/LayerMenu/layerDatas';
import RightNav from '@renderer/marsLayers/components/RightNav';
import Dimensional from '@renderer/marsLayers/components/Dimensional';
import Layers from '@renderer/marsLayers/layers';
import { MaskLayer, BaseLayer } from '@renderer/layers'; // update
import RoadNetwork from '@renderer/marsLayers/layers/RoadNetwork';
import BackgroundFallingAreaMap from '@renderer/marsLayers/components/BackgroundFallingAreaMap';
import Flash from '@renderer/assets/start.mp4';
import Fiveviews from '@renderer/components/SingleChoice';
import VehicleSearchPanel from '@renderer/marsLayers/components/VehicleSearchPanel';
import {
	geoCenter3D,
	geoCenter2D,
} from '@renderer/marsLayers/components/BeiJingMap/data';
import { getVehicleTrack } from '@renderer/api';
import { PageContext, MapContext } from '@renderer/context';
import { CountyId } from '@renderer/utils/getData';
import {
	LayerDataCache,
	RealTimeOfflineDataCache,
	RoadGeometryDataCache,
	BeiJingTownDataCache,
} from '@renderer/utils/ManageDataCache';
import BikeDateNew from '@renderer/marsLayers/components/BikeDateNew';

import 'mars3d/dist/mars3d.css';
import 'mars3d-cesium/Build/Cesium/Widgets/widgets.css';

import { MapContainer } from './style';
import './styles.css';

const { Cesium } = mars3d;
Cesium.Ion.defaultAccessToken =
	'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI5MzA2MGIyZC1kYTI3LTRlNjMtOTI1NS1jYWI3ZjZjMmM5NDEiLCJpZCI6NTA5MjMsImlhdCI6MTYxNzg2NjAxNn0.1Bbkalt4CSbNBrGE6nH6vQqKJ_jmnnQ-p5T16JIIxWM';

export const center2D = {
	name: '北京市',
	value: [116.41392, 39.97004],
};
export const height2D = 170824.7;
export const center3D = {
	name: '北京市',
	value: [116.395657, 39.340559],
};

export const height3D = 46466.3;
export const orientation = {
	heading: Cesium.Math.toRadians(0),
	pitch: Cesium.Math.toRadians(-35),
	roll: 0.0,
};

let stag = center3D.name;

const commonList = [
	{
		children: [
			{ name: 'PM₂.₅', id: '134004', visible: true },
			{ name: 'PM₁₀', id: '134002', visible: false },
			{ name: 'SO₂', id: '121026', visible: false },
			{ name: 'NO₂', id: '121004', visible: false },
			{ name: 'O₃', id: '105024', visible: false },
			{ name: 'CO', id: '121005', visible: false },
			{ name: 'AQI', id: '209002', visible: false },
		],
		menuStyle: { marginLeft: '-80px' },
		visible: false,
		id: '134004',
		name: 'PM₂.₅',
	},
];

const vehicleCondition = [
	{
		children: [
			{ name: '在线车辆', id: '1', visible: true },
			{ name: '离线车辆', id: '2', visible: false },
			{ name: '总车辆', id: '3', visible: false },
		],
		menuStyle: { marginLeft: '-80px' },
		visible: false,
		id: '1',
		name: '在线车辆',
	},
];

export default () => {
	const {
		setCountyId,
		setParsentFlash,
		setHideModeMenu,
		showBackgroundFallingArea,
		setShowBackgroundFallingArea,
		showSideIndustry,
		setShowSideIndustry,
		showStreet,
		setShowStreet,
		showUserEnterprise,
		setShowUserEnterprise,
		showSideExpansionField,
		setShowSideExpansionField,
		LayerDataDB,
		RealTimeOfflineDataDB,
		RoadGeometryDataDB,
		BeiJingTownDataDB,
		currentVehicleInfo,
		setCurrentVehicleInfo,
		regionName,
		setRegionName,
	} = useContext(PageContext);
	const videoRef = useRef<any>(null);
	const mapRef = useRef<any>(null);
	const divRef = useRef<any>(null);

	const [showTimebar, setShowTimebar] = useState<boolean>(false); // 时间轴显示隐藏
	const [showFiveviews, setShowFiveviews] = useState<boolean>(false); // 污染物选择框组件显示隐藏
	const [showVehicleSearchPanel, setShowVehicleSearchPanel] = useState(true); // 车辆搜索面板组件显示隐藏
	const [trafficPathData, setTrafficPathData] = useState([]); // 路网数据
	const [selectRegion, setSelectRegion] = useState(center2D); //选中的区
	// const [selectMode, setSelectMode] = useState({}) //选中的图层
	const [currentInfo, setCurrentInfo] = useState(null); // 存储当前选中的车辆信息
	const [carDetails, setCarDetails] = useState([]); // 存储当前车辆实时信息
	const [layers, setLayers] = useState(layerDatas); //图层菜单
	const [showFlash, setShowFlash] = useState<boolean>(false);
	const [sceneMode, setSceneMode] = useState(Cesium.SceneMode.SCENE3D); // 当前地图模式
	const [modalVisible, setModalVisible] = useState<boolean>(false);
	const [timeBarEndTime, setTimeBarEndTime] = useState(
		moment().subtract(24, 'h').format('YYYY-MM-DD HH:00:00'),
	); // 时间轴对应数据的开始时间
	const [trafficAllData, settrafficAllData] = useState([]); // 车流量热力图数据
	const [vehicleEmissionsAllData, setVehicleEmissionsAllData] = useState([]); // 车辆排放热力图数据
	const [navList, setNavList] = useState(commonList); // 污染物列表
	const [vehicleSearchData, setVehicleSearchData] = useState([]); // 车辆搜索数据列表
	const [showRoadNetwork, setShowRoadNetwork] = useState<boolean>(true); // 道路路网展示
	const [showBackgroundFallingAreaMap, setShowBackgroundFallingAreaMap] =
		useState<boolean>(false); // 落区图地图
	const [hideEchartsMap, setHideEchartsMap] = useState(false); // 隐藏右下角echarts地图
	const [currentLayerId, setCurrentLayerId] = useState('vehicleOnline'); // 当前所选中的图层id
	const [bickDate, setBickDate] = useState(false); // 单车日期
	const [trajectoryPathData, setTrajectoryPathData] = useState([]); // 小地图轨迹路径数据
	let tem = {},
		jsonData: any = null;
	// COUNTYS.map((item) => {
	//   tem[item] = useRef()
	// })

	useEffect(() => {
		setCurrentVehicleInfo(currentInfo);
	}, [currentInfo]);

	useEffect(() => {
		mapRef.current = new mars3d.Map(divRef.current, {
			method: {
				chinaCRS: mars3d.ChinaCRS.GCJ02, // 标识坐标系
			},
			scene: {
				resolutionScale: 4 / 3,
				showSkyAtmosphere: false, // 关闭球周边的白色轮廓 map.scene.skyAtmosphere = false
				// requestRenderMode: true //开启显式渲染
			},
			control: {
				baseLayerPicker: false,
			},
			basemaps: [],
			layers: [],
		});

		mapRef.current.camera.setView({
			destination: Cesium.Cartesian3.fromDegrees(
				116.550727,
				40.268658,
				585245.302974,
			),
		});

		mapRef.current.unbindContextMenu();
		/*
      const aud: any = document.getElementById('video');
      // video视频播放完成的事件
      aud.onended = () => {
        setShowFlash(false);
        setParsentFlash(false);
        if (import.meta.env.MODE != 'test') {
          // // 图层路网、热力图数据缓存
          // LayerDataCache({
          // 	layerId: 'trafficVolumeThermodynamics',
          // 	jsonData,
          // 	bd: LayerDataDB,
          // });
          // LayerDataCache({
          // 	layerId: 'vehicleEmissionsThermodynamics',
          // 	jsonData,
          // 	bd: LayerDataDB,
          // });
          // LayerDataCache({
          // 	layerId: 'trafficVolumeDistribution',
          // 	jsonData,
          // 	bd: LayerDataDB,
          // });
          // LayerDataCache({
          // 	layerId: 'vehicleEmissionsDistribution',
          // 	jsonData,
          // 	bd: LayerDataDB,
          // });
          // deleteDBExpiredData()

          // 图层实时离线数据缓存
        }
      };
    */
		RealTimeOfflineDataCache({ jsonData, bd: RealTimeOfflineDataDB });
		RoadGeometryDataCache({ bd: RoadGeometryDataDB });
		BeiJingTownDataCache({ bd: BeiJingTownDataDB });
		return () => {
			mapRef.current.destroy();
		};
	}, []);

	useEffect(() => {
		// 切换视图中心
		if (selectRegion && mapRef.current) {
			const or = sceneMode === Cesium.SceneMode.SCENE3D ? orientation : {};
			if (selectRegion.name === stag) {
				const cen =
					sceneMode === Cesium.SceneMode.SCENE3D ? center3D : center2D;
				const hei =
					sceneMode === Cesium.SceneMode.SCENE3D ? height3D : height2D;
				mapRef.current.camera.flyTo({
					destination: Cesium.Cartesian3.fromDegrees(...cen.value, hei),
					orientation: or,
					duration: 4,
				});
				setModalVisible(false);
				stag = cen.name;
				setCountyId('');
			} else {
				const centerHeight =
					sceneMode === Cesium.SceneMode.SCENE3D
						? geoCenter3D[selectRegion.name]
						: geoCenter2D[selectRegion.name];
				// 获取当前缩放比例
				mapRef.current.camera.flyTo({
					destination: Cesium.Cartesian3.fromDegrees(...centerHeight),
					orientation: or,
					duration: 4,
				});
				setModalVisible(true);
				stag = selectRegion.name;
				setCountyId(CountyId[stag]);
			}
		}
		// 取消车辆跟踪
		setCurrentInfo(null);
	}, [selectRegion]);

	const changeMode = (val) => {
		if (val === Cesium.SceneMode.SCENE3D) {
			const cen = geoCenter3D[stag]
				? geoCenter3D[stag]
				: [...center3D.value, height3D];
			mapRef.current.camera.setView({
				destination: Cesium.Cartesian3.fromDegrees(...cen),
				orientation,
				duration: 3,
			});
		} else {
			const cen = geoCenter2D[stag]
				? geoCenter2D[stag]
				: [...center2D.value, height2D];
			mapRef.current.camera.setView({
				destination: Cesium.Cartesian3.fromDegrees(...cen),
				duration: 3,
			});
		}
	};

	const layerDOM = useMemo(() => {
		if (!mapRef.current) return null;
		return layers.map((item) =>
			item.child?.length ? (
				item.child?.map((item) => (
					<Layers
						{...item}
						layerId={item.key}
						map={mapRef.current}
						key={item.key}
					/>
				))
			) : (
				<Layers
					{...item}
					layerId={item.key}
					map={mapRef.current}
					key={item.key}
				/>
			),
		);
	}, [mapRef.current, layers]);

	const changeData = (visible, position, childPosition) => {
		const newList = _.cloneDeep(navList);
		if (childPosition !== undefined) {
			// 选中子级
			newList[position].children.forEach((item, index) => {
				if (index === childPosition) {
					item.visible = visible;
					const id = item.id;
					const name = item.name;
					if (!visible) {
						// if (position === 1) {
						//   id = 'county';
						//   name = '区县';
						// } else if (position === 2) {
						//   // 污染物必选参数
						//   item.visible = true;
						// }
					}
					newList[position].id = id;
					newList[position].name = name;
				} else {
					item.visible = false;
				}
			});
			newList[position].visible = false;
			setNavList(newList);
		} else {
			// 展开关闭父级
			newList[position].visible = visible;
			setNavList(newList);
			return;
		}
		// setLayerContextData({
		//   ...layerContextData,
		//   homeControl: newList
		// })
	};

	useEffect(() => {
		if (!mapRef.current) return;
		let show = false;
		const list = [
			LayerType.TRAFFIC_VOLUME_DISTRIBUTION,
			LayerType.VEHICLE_EMISSIONS_DISTRIBUTION,
			LayerType.VEHICLE_EMISSIONS_THERMODYNAMICS,
			LayerType.TRAFFIC_VOLUME_THERMODYNAMICS,
		];
		let showRoadNetwork = false;
		let showBackgroundFallingAreaMap = false;
		let showBackgroundFallingArea = false;
		let hideModeMenu = false;
		let showFiveviews = false;
		let hideEchartsMap = false;
		let showSideExpansionField = false;
		let showSideIndustry = false;
		let showStreet = false;
		let showUserEnterprise = false;
		const listFiveviews = [
			LayerType.STDG,
			LayerType.STDS,
			LayerType.HIGHT,
			LayerType.GRIDS,
		];
		let showVehicleSearchPanel = false;
		const listVehicleSearchPanel = [
			LayerType.VEHICLE_ONLINE,
			LayerType.VIOLATING_VEHICLES,
		];
		layers.forEach((item) => {
			if (item.child?.length) {
				item.child.forEach((item) => {
					if (list.includes(item.key)) {
						item.visible && (show = true);
					}
					if (listFiveviews.includes(item.key)) {
						item.visible && (showFiveviews = true);
					}
					if (item.roadNetwork) {
						item.visible && (showRoadNetwork = true);
					}

					if (item.showBackground) {
						if (!item.visible) return;
						showBackgroundFallingAreaMap = true;
						if (item.key == 'ViolationAnalysis') {
							showSideExpansionField = true;
						} else {
							showBackgroundFallingArea = true;
						}
					}
					if (item.hideModeMenu) {
						item.visible && (hideModeMenu = true);
					}
					if (item.vehicleSearchPanel && item.visible) {
						showVehicleSearchPanel = true;
					}
				});
			} else {
				if (item.roadNetwork) {
					item.visible && (showRoadNetwork = true);
					if (item.key == 'userEnterprise') {
						item.visible && (showUserEnterprise = true);
					}
				}
				if (item.key == 'ViolationAnalysis') {
					item.visible && (showSideExpansionField = true);
				}
				if (item.showBackground) {
					if (!item.visible) return;
					showBackgroundFallingAreaMap = true;
					if (item.key == 'ViolationAnalysis') {
						showSideExpansionField = true;
					} else if (item.key == 'industry') {
						showSideIndustry = true;
					} else if (item.key == 'streetTownship') {
						showStreet = true;
					} else {
						showBackgroundFallingArea = true;
					}
				}
			}
			if (item.hideModeMenu) {
				item.visible && (hideModeMenu = true);
			}
			if (item.hideEchartsMap) {
				item.visible && (hideEchartsMap = true);
			}
		});
		setShowTimebar(show); // 时间轴显示隐藏
		setShowFiveviews(showFiveviews); // 污染物选择框组件显示隐藏
		setShowVehicleSearchPanel(showVehicleSearchPanel); // 车辆搜索面板组件显示隐藏
		setShowRoadNetwork(showRoadNetwork); // 路网显示隐藏
		setShowBackgroundFallingAreaMap(showBackgroundFallingAreaMap); // 落区地图
		setShowBackgroundFallingArea(showBackgroundFallingArea); // 区域、街乡镇落区图背景显示隐藏
		setShowSideExpansionField(showSideExpansionField); // 违规车辆两侧扩展栏显示隐藏
		setShowSideIndustry(showSideIndustry);
		setShowStreet(showStreet);
		setShowUserEnterprise(showUserEnterprise);
		setHideModeMenu(hideModeMenu);
		setHideEchartsMap(hideEchartsMap);
	}, [layers]);

	return (
		<MapContext.Provider
			value={{
				map: mapRef.current,
				selectRegion,
				setSelectRegion,
				currentInfo,
				setCurrentInfo,
				layers,
				setLayers,
				sceneMode,
				setSceneMode,
				trafficPathData,
				setTrafficPathData,
				timeBarEndTime,
				setTimeBarEndTime,
				trafficAllData,
				settrafficAllData,
				vehicleEmissionsAllData,
				setVehicleEmissionsAllData,
				showFlash,
				navList,
				setNavList,
				vehicleSearchData,
				setVehicleSearchData,
				carDetails,
				setCarDetails,
				currentLayerId,
				setCurrentLayerId,
				setBickDate,
				trajectoryPathData,
				setTrajectoryPathData,
			}}
		>
			{/* {showFlash && (
				<video
					id="video"
					className="flash"
					src={Flash}
					autoPlay={true}
					muted={true}
					objectfit="cover"
				></video>
			)} */}
			<MapContainer ref={divRef} className={`${currentLayerId}-point`}>
				{!showFlash && showVehicleSearchPanel && (
					<VehicleSearchPanel data={vehicleSearchData} map={mapRef.current} />
				)}
				<MaskLayer visible={modalVisible} />
				{/* 图层 */}
				{layerDOM}
				{/* 底图 */}
				<BaseLayer />
				{/* 右侧导航栏 */}
				{/* {!showFlash && <RightNav map={viewer} changeMode={changeMode} showFlash={showFlash} />} */}
				{/* 2D 3D平铺组件 */}
				{!showFlash && (
					<Dimensional
						map={mapRef.current}
						changeMode={changeMode}
						showFlash={showFlash}
					/>
				)}
				{/* 北京地图 */}
				<BeiJingMap
					showFlash={showFlash}
					hideEchartsMap={hideEchartsMap}
					showBeiJingMap={showBackgroundFallingAreaMap}
					trajectoryPathData={trajectoryPathData}
				/>
				{/* 图层菜单 */}
				<LayerMenu showFlash={showFlash} />
				{/* 仪表盘 */}
				{/* {currentInfo && <DashBoard info={currentInfo} />} */}
				{/* 单车日期选择 */}
				{/* {bickDate && <BikeDate />} */}
				{bickDate && <BikeDateNew map={mapRef.current} />}
				{showTimebar && <TimeBar showFlash={showFlash} />}
				{showFiveviews && (
					<div className="country">
						<Fiveviews data={navList} setData={changeData} />
					</div>
				)}
				<RoadNetwork map={mapRef.current} showRoadNetwork={showRoadNetwork} />
				<BackgroundFallingAreaMap
					map={mapRef.current}
					showBackgroundFallingAreaMap={showBackgroundFallingAreaMap}
				/>
				<TrajectoryLayer />
			</MapContainer>
		</MapContext.Provider>
	);
};
