import { useState, useEffect, useContext, useRef } from 'react';
import '@animxyz/core';
import { XyzTransitionGroup } from '@animxyz/react';
import * as echarts from 'echarts';
import { geoJson, geoCenter3D, geoCenter2D } from './data';
import { MapContext, PageContext } from '../../../context';
import background from '../../../images/background';
import { BeiJingMapEcharts } from './style';

let myChart = null;
function BeiJingMap(props) {
	const {
		showFlash,
		showBeiJingMap,
		hideEchartsMap,
		trajectoryPathData = [],
	} = props;
	const { setSelectRegion } = useContext(MapContext);
	const { setRegionName } = useContext(PageContext);
	const [immigration, setImmigration] = useState(false);
	const chartRef = useRef(null);
	useEffect(() => {
		if (!showFlash && !showBeiJingMap) {
			echartStart();
		}
		return () => {
			if (myChart) {
				myChart.dispose();
				myChart = null;
			}
		};
	}, [showFlash, showBeiJingMap, trajectoryPathData]);

	useEffect(() => {
		if (!myChart) return;
		if (myChart) {
			myChart.setOption({ series: [{ label: { show: immigration } }] }, false);
		}
		myChart.resize({
			width: 'auto',
			height: 'auto',
		});
	}, [immigration]);

	const echartStart = () => {
		const series = [
			{
				type: 'map',
				roam: false,
				label: {
					show: immigration,
					color: '#fff',
					fontSize: 10,
					width: 70,
					height: 30,
					backgroundColor: {
						image: background.beijingEchartsLable,
					},
					formatter: function (params) {
						if (params.name !== '西城区' && params.name !== '东城区') {
							return params.name;
						} else {
							return ''; // 不显示标签
						}
					},
					lineHeight: 18,
					rich: {
						a: {
							lineHeight: 30,
						},
					},
				},
				itemStyle: {
					normal: {
						areaColor: '#003669',
						borderColor: '#3fdaff',
						borderWidth: 1,
						shadowColor: 'rgba(63, 218, 255,0.3)',
						shadowBlur: 20,
					},
				},
				emphasis: {
					label: {
						color: '#fff',
						backgroundColor: {
							image: background.beijingEchartsLable_hover,
						},
					},
					itemStyle: {
						areaColor: '#003669',
						borderColor: '#ebae51',
						borderWidth: 3,
					},
				},
				select: {
					selectedMode: 'single',
					smooth: true,
					label: {
						color: '#fff',
						backgroundColor: {
							image: background.beijingEchartsLable_hover,
						},
					},
					itemStyle: {
						areaColor: '#003669',
						borderColor: '#ebae51',
						borderWidth: 3,
					},
				},
				zoom: 1.2,
				map: 'beijing', //使用
			},
		];
		if (trajectoryPathData.length) {
			series.push({
				type: 'lines',
				name: 'PLANT_CAINIAO_PATH',
				zlevel: 2,
				polyline: true,
				// effect: {
				//   show: true,
				//   period: 4, //箭头指向速度，值越小速度越快
				//   trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
				//   symbol: 'arrow', //箭头图标
				//   symbolSize: 5, //图标大小
				//   color: '#ffffff'
				// },
				lineStyle: {
					normal: {
						width: 2, //尾迹线条宽度
						opacity: 1, //尾迹线条透明度
						curveness: 0.05, //尾迹线条曲直度
						color: '#ffff00',
					},
				},
				data: [
					{
						coords: trajectoryPathData,
					},
				],
			});
		}
		let option = {
			geo: {
				show: false,
				map: 'beijing',
				label: {
					normal: {
						show: false,
					},
					emphasis: {
						show: false,
					},
				},
				scaleLimit: {
					//缩放级别
					min: 0.5,
					max: 2,
				},
			},
			series,
		};
		myChart = echarts.init(chartRef.current);
		myChart.showLoading();
		echarts.registerMap('beijing', geoJson);
		myChart.hideLoading();
		myChart.setOption(option, true);
		// 默认选中怀柔
		// myChart.dispatchAction({
		//   type: 'highlight',
		//   //seriesIndex：number | array系列 index，可以是一个数组指定多个系列
		//   seriesIndex: 0,
		//   //dataIndex：number 数据的 index
		//   dataIndex: 12
		// })

		//拿到点击对应的经纬度
		myChart.on('click', function (e) {
			// 取消默认选中
			// myChart.dispatchAction({
			//   type: 'downplay',
			//   seriesIndex: 0,
			//   dataIndex: 12
			// })
			setRegionName(e.name);
			setSelectRegion({
				name: e.name,
				value: [e.event.offsetX, e.event.offsetY],
			});
		});
	};

	const mouseEnter = (e) => {
		setImmigration(true);
	};

	const mouseLeave = (e) => {
		setImmigration(false);
	};

	return (
		<BeiJingMapEcharts
			hidden={hideEchartsMap ? true : false}
			onMouseEnter={mouseEnter}
			onMouseLeave={mouseLeave}
			className={immigration ? 'blowUp' : ''}
		>
			<div id="chart" ref={chartRef}></div>
		</BeiJingMapEcharts>
	);
}
export default BeiJingMap;
