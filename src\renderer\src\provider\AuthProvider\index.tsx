import { useContext, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

import { useSessionStorage } from '@renderer/hooks';
import { AuthContext } from '@renderer/context';

export const AuthProvider = ({ children }) => {
	const [token, setToken] = useSessionStorage('token', null);
	const navigate = useNavigate();

	const login = async (token: string) => {
		setToken(token);
		navigate('/');
	};

	const logout = () => {
		setToken(null);
		navigate('/login', { replace: true });
	};

	const value = useMemo(
		() => ({
			token,
			login,
			logout,
		}),
		[token],
	);

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
