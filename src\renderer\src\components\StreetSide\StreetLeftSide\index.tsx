import { useEffect, useState, useContext, useMemo } from 'react';
import Echarts from '@renderer/components/echarts';
import { Space, Button, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import {
	getRegionalIndustryProportion,
	getRegionalAnalysis,
	getIndustryOnlineSituation,
	getStreetTownshipIndustriesProportion,
	getSTIStats,
	getThemeTownDetailByTypeTime,
} from '@renderer/api';
import {
	getIper,
	getIndustry,
	getOnlineSituationEcharts,
} from '../components/IndustryProportion';
import { mergeObjects } from '../components/PublicMethods';
import industryClassification from '../components/IndustryClassification';
import { PageContext } from '@renderer/context';
import {
	COUNTY_ID_NAME_MAP,
	unitList,
} from '@renderer/marsLayers/components/AreaStreetStyles';
import {
	formatNumber,
	calculatePercentage,
	tranNumber,
	tranNumberS,
} from '@renderer/hooks/index';
import { AreaLeftSideStyled, LayoutContainer } from './styles';
import moment from 'moment';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const StreetLeftSide = (props: Props) => {
	const { currentSelectedId, streetShipData } = props;
	const { currentTopNavMenu, selectRegionDate, regionName, streetName } =
		useContext(PageContext);
	const [dataSum, setDataSum] = useState(0);
	const [optionsIP, setOptionsIP] = useState({}); //行业占比echarts
	const [optionsRA, setOptionsRA] = useState({}); //区域分析echarts
	const [currentIndustry, setCurrentIndustry] = useState('全部');
	const [title1, setTitle1] = useState(null);
	const [onlineOption, setOnlineOption] = useState({});
	const [distanceOption, setDistanceOption] = useState({});
	const streetId = useMemo(() => {
		if (streetName) {
			return streetShipData?.features.find(
				(i) => i.properties.name === streetName,
			).properties.region;
		}
		return '110101011';
	}, [streetName]);

	const items: MenuProps['items'] = Object.keys(industryClassification()).map(
		(item, idx) => {
			return {
				label: item,
				key: `${idx}`,
			};
		},
	);

	const handleMenuClick: MenuProps['onClick'] = (e) => {
		setCurrentIndustry(Object.keys(industryClassification())[e.key]);
	};

	const menuProps = {
		items,
		onClick: handleMenuClick,
	};

	function separateNumberAndChinese(str) {
		if (str === '0.00' || str === 0) {
			return { number: parseFloat(str), chinese: '' };
		}
		//const matches = str?.match(/([\d.]+)(\D+)/);
		const matches = `${str}`?.match(/([\d.]+)(.*)/);
		if (matches) {
			const number = parseFloat(matches[1]);
			const chinese = matches[2];
			return { number, chinese };
		} else {
			return { number: parseFloat(str), chinese: '' };
		}
	}

	const setUpOption2 = (data) => {
		const unit = unitList?.find((i) => i.name === currentTopNavMenu)?.unit;
		let sumCountsByAdm: any = [];
		let name;
		if (currentSelectedId === 'area') {
			sumCountsByAdm = data?.reduce((acc, curr) => {
				curr.data.forEach((count, index) => {
					acc[index] = (acc[index] || 0) + count;
				});
				return acc;
			}, []);
			name = data[0]?.county?.map((id) => COUNTY_ID_NAME_MAP[id]);
		} else {
			sumCountsByAdm = data?.reduce((acc, curr) => {
				curr.data.slice(0, 10).forEach((count, index) => {
					acc[index] = (acc[index] || 0) + count;
				});
				return acc;
			}, []);
			name = data[0]?.town_name;
		}
		const admCounts = sumCountsByAdm?.map((total, index) => {
			return {
				name: name[index],
				value: total >= 1 ? Number(total.toFixed(1)) : Number(total.toFixed(2)),
			};
		});
		const sortedAdmCounts = admCounts.sort((a, b) => a.value - b.value);
		const tradeAdm = sortedAdmCounts.map((item) => item.name);
		const tradeCount = sortedAdmCounts.map((item) => item.value);

		const option = getIndustry(
			{
				...data,
				name: tradeAdm,
				originalName: name,
				total: tradeCount,
				unit,
			},
			currentSelectedId,
		);
		setOptionsRA(option);
	};

	const getIndustryDataResult = (res) => {
		if (res?.data?.length > 0) {
			console.log('res', res);
			const carData = res?.data?.map((item) => {
				return {
					name: Object.keys(item)[0],
					value: formatNumber(Object.values(item)[0]),
				};
			});
			setOptionsIP(getIper(calculatePercentage(carData)));
			setDataSum(res?.sum);
		} else {
			setDataSum(0);
			setOptionsIP(getIper([]));
		}
	};

	// 区域-街乡镇行业占比
	const getIndustryData = (id, params) => {
		switch (id) {
			case 'area':
				getRegionalIndustryProportion(params)
					.then((res) => {
						getIndustryDataResult(res);
					})
					.catch((err) => {
						setDataSum(0);
						setOptionsIP(getIper([]));
					});
				break;
			case 'streetTownship':
				getStreetTownshipIndustriesProportion(params)
					.then((res) => {
						getIndustryDataResult(res);
					})
					.catch((err) => {
						setDataSum(0);
						setOptionsIP(getIper([]));
					});
				break;
			default:
				break;
		}
	};

	const getIndustryStatisticsResult = (res) => {
		if (res?.length > 0) {
			const carType = industryClassification();
			const arr: any = [];
			res?.map((item) => {
				Object.values(carType).forEach((item2, idx) => {
					if (item2.includes(item.name)) {
						arr.push({
							...item,
							name: Object.keys(carType)[idx],
						});
					}
				});
			});
			setUpOption2(mergeObjects(arr));
		} else {
			setUpOption2(mergeObjects([]));
		}
	};

	// 区域-街乡镇 行业数据统计
	const getIndustryStatistics = (id, params) => {
		switch (id) {
			case 'area':
				getRegionalAnalysis(params)
					.then((res) => {
						getIndustryStatisticsResult(res);
					})
					.catch((err) => {
						setUpOption2(mergeObjects([]));
					});
				break;
			case 'streetTownship':
				getSTIStats(params)
					.then((res) => {
						getIndustryStatisticsResult(res);
					})
					.catch((err) => {
						setUpOption2(mergeObjects([]));
					});
				break;
			default:
				break;
		}
	};

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time || !end_time) return;
		const params: any = {
			start_time,
			end_time,
			time_type,
			topic: unitList.find((i) => i.name === currentTopNavMenu)?.id,
		};
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		getIndustryStatistics(currentSelectedId, params);
	}, [selectRegionDate, currentTopNavMenu, currentSelectedId, regionName]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time || !end_time) return;
		setTitle1(streetName ? streetName : regionName);
		const params: any = {
			start_time,
			end_time,
			time_type,
			topic: unitList.find((i) => i.name === currentTopNavMenu)?.id,
		};
		if (streetName) {
			params.town_id = streetShipData?.features.find(
				(i) => i.properties.name === streetName,
			).properties.region;
		}
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		// if ('county_id' in params && 'town_id' in params) return
		getIndustryData(currentSelectedId, params);
	}, [
		selectRegionDate,
		currentTopNavMenu,
		regionName,
		streetName,
		currentSelectedId,
	]);

	const getEchartData = (type, setData) => {
		const units = {
			distance: '单位: km',
			online: '单位: 辆',
		};
		const { start_time, end_time } = selectRegionDate.customDate;
		getThemeTownDetailByTypeTime({
			start_time,
			end_time,
			date_type: 'hour',
			town_id: streetId,
			analysis: type,
		}).then((res: any) => {
			const option = {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow',
					},
				},
				legend: {
					top: 0, // 调整图例的位置，使其移动到图的上方
					right: 100, // 调整图例的位置，使其移动到图的右侧
					textStyle: {
						color: '#fff',
						fontSize: 16,
					},
					itemWidth: 10, // 设置图例项的宽度
					itemHeight: 10, // 设置图例项的高度
					itemShape: 'circle', // 设置图例项的形状为圆形
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true,
				},
				xAxis: [
					{
						type: 'category',
						axisPointer: {
							type: 'shadow',
						},
						axisLabel: {
							textStyle: {
								color: '#E2F0FF',
								fontSize: 14,
							},
						},
						data: res?.time_list.map((time) => {
							return moment(time).format('YYYY-MM-DD HH时');
						}),
					},
				],
				yAxis: [
					{
						name: units[type],
						nameTextStyle: {
							color: 'rgba(212, 232, 254, 1)',
							fontSize: 12,
						},
						type: 'value',
						splitLine: {
							lineStyle: {
								type: 'dashed',
							},
						},
						axisLabel: {
							textStyle: {
								color: '#E2F0FF',
								fontSize: 16,
							},
							formatter: function (value, index) {
								return tranNumber(Number(value), 2);
							},
						},
					},
				],
				series:
					res &&
					res['industry_second']?.map((item) => {
						let data = item.slice(1, item.length);
						return {
							name: item[0],
							type: 'bar',
							stack: 'total',
							data,
							barMaxWidth: 100,
						};
					}),
				animation: false,
			};
			setData(option);
		});
	};

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time) return;
		getEchartData('online', setOnlineOption);
		getEchartData('distance', setDistanceOption);
	}, [streetId, selectRegionDate]);

	return (
		<AreaLeftSideStyled>
			<div className="Areacontent">
				<div className="slidingLayer">
					<span className="slidtext">{title1 ? title1 : '全市'}行业情况</span>
				</div>
				<LayoutContainer style={{ height: '20%' }}>
					<div className={'container'}>
						<div className="describe">
							<dl className="no2">
								<dt>
									总量
									<span>
										{unitList?.find((i) => i.name === currentTopNavMenu)?.unit}
									</span>
								</dt>
								<br />
								<dd>
									{separateNumberAndChinese(tranNumberS(dataSum, 2)).number}
									<span style={{ fontSize: '36px', color: '#f5803e' }}>
										{separateNumberAndChinese(tranNumberS(dataSum, 2)).chinese}
										&nbsp;
									</span>
								</dd>
							</dl>
						</div>
						<div className="echarts-container">
							<div className="echarts">
								<Echarts option={optionsIP}></Echarts>
							</div>
						</div>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">在线车辆分析</span>
				</div>
				<LayoutContainer style={{ height: '35%' }}>
					<div className="echarts-line">
						<Echarts
							option={onlineOption}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">车辆里程分析</span>
				</div>
				<LayoutContainer style={{ height: '25%' }}>
					<div className="echarts-line">
						<Echarts
							option={distanceOption}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
			</div>
		</AreaLeftSideStyled>
	);
};

export default StreetLeftSide;
