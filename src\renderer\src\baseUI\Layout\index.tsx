import { useEffect, useState } from 'react';

import Header from './Header';
import Left from './Left';
import Main from './Main';
import Middle from './Middle';
import Right from './Right';
import LayoutContainer from './styles';

type BasicProps = {
	children?: React.ReactNode;
};

interface LayoutType extends React.FC<BasicProps> {
	Header: typeof Header;
	Main: typeof Main;
	Left: typeof Left;
	Middle: typeof Middle;
	Right: typeof Right;
}

const Layout: LayoutType = ({ children }) => {
	const [scale, setScale] = useState(1);
	const [translateY, setTranslateY] = useState(0);
	const getBrowserSizeAndRatio = () => {
		const width = window.innerWidth;
		const scale = width / 3840;
		const translateY = 0 - 1080 / 2;
		setScale(scale);
		setTranslateY(translateY);
	};

	useEffect(() => {
		window.addEventListener('resize', getBrowserSizeAndRatio);
		getBrowserSizeAndRatio();
		return () => {
			window.removeEventListener('resize', getBrowserSizeAndRatio);
		};
	}, []);
	return (
		<LayoutContainer scale={scale} translateY={translateY}>
			{children}
		</LayoutContainer>
	);
};

Layout.Header = Header;
Layout.Main = Main;
Layout.Left = Left;
Layout.Middle = Middle;
Layout.Right = Right;

export default Layout;
