/**
 * 违规车辆图层
 */
import React, { useState, useEffect, useContext } from 'react';
import {
	Cesium,
	layer as Layer,
	EventType,
	graphic,
	PointTrans,
	LngLatPoint,
} from 'mars3d';

import { COLORLIST } from '@renderer/utils/util';
import { MapContext, PageContext } from '@renderer/context';
import {
	getViolationRealtimeData,
	getViolationRealtimeDataRegion,
} from '@renderer/api';
import VehicleIndustryNumber from '@renderer/marsLayers/components/VehicleIndustryNumber';
import icon from '@renderer/images/icon/point.png';
import { INDUSTRY_COLOR } from '@renderer/constants/color';
import { getCarColor } from '@renderer/marsLayers/components/ModeMenu/data';

import { ILayerProps } from './interfaces';
import { useMap } from '@renderer/hooks';
import {
	ViolationGISListData,
	ViolationPieListData,
} from '@renderer/api/violations/interfaces';

const { GraphicLayer } = Layer;
const { dblClick } = EventType;
const { PointPrimitive } = graphic;
const { wgs2gcj } = PointTrans;

let layer;

const ViolationLayer = (props: ILayerProps) => {
	const map = useMap();
	const { visible, layerId } = props;
	const { currentInfo, setCurrentInfo, setBickDate } = useContext(MapContext);
	const { setOpenTrajectory, openTrajectory } = useContext(PageContext);
	const [data, setData] = useState();

	useEffect(() => {
		if (!layer || !visible) return;
		layer.clear();
		const fetchData = async () => {
			const result = await getViolationRealtimeData();
			setData(result);
			layer.enabledEvent = false; // 关闭事件，大数据addGraphic时影响加载时间
			updatePointByZoom(result);
			layer.enabledEvent = true; // 恢复事件
		};
		fetchData();
	}, [layer, visible]);

	function selectRandomPoints(data, count) {
		if (count >= data.length) return data;

		const result = [];
		const indices = new Set();

		while (indices.size < count) {
			const index = Math.floor(Math.random() * data.length);
			if (!indices.has(index)) {
				indices.add(index);
				result.push(data[index]);
			}
		}

		return result;
	}

	const updatePointByZoom = (result) => {
		// 根据缩放级别决定显示多少点位
		let displayCount = 0;
		const zoomLevel = map.level;
		if (zoomLevel < 8) {
			// 小比例尺下只显示1%的点位
			displayCount = Math.max(1, Math.floor(result.length * 0.01));
		} else if (zoomLevel < 12) {
			// 中等比例尺下显示10%的点位
			displayCount = Math.max(1, Math.floor(result.length * 0.1));
		} else {
			// 大比例尺下显示全部点位
			displayCount = result.length;
		}
		const displayData = selectRandomPoints(result, displayCount);

		// TODO 根据车辆是否在线添加不同样式图标
		displayData.forEach((item) => {
			const gcj2wgs = new wgs2gcj([item.lng, item.lat]);
			const position = new LngLatPoint(gcj2wgs[0], gcj2wgs[1]);
			const graphicPoint = new PointPrimitive({
				position,
				style:
					item.status === 1
						? {
								color: getCarColor(item.type),
								pixelSize: 8,
								distanceDisplayCondition: true,
								distanceDisplayCondition_far: Number.MAX_VALUE,
						  }
						: {
								color: 'transparent',
								pixelSize: 6,
								outlineColor: getCarColor(item.type),
								outlineWidth: 2,
								distanceDisplayCondition: true,
								distanceDisplayCondition_far: Number.MAX_VALUE,
						  },
				attr: item,
			});
			layer.addGraphic(graphicPoint);
		});
	};

	useEffect(() => {
		if (data) {
			const viewer = map.viewer;

			// 监听相机变化事件（包含缩放、平移等）
			viewer.camera.changed.addEventListener(() => {
				updatePointByZoom(data);
			});
		}
	}, [data]);

	useEffect(() => {
		if (!map) return;
		layer = new GraphicLayer({ id: layerId });
		map.addLayer(layer);
		// setLayer(layer);
		layer.on(dblClick, function (event) {
			const cartesianPosition = event.graphic.position;
			const cartographic = Cesium.Cartographic.fromCartesian(cartesianPosition);
			setCurrentInfo({
				VIN: event.graphic.attr.vid,
				type: event.graphic.id.split('|')[1],
				lon: Cesium.Math.toDegrees(cartographic.longitude),
				lat: Cesium.Math.toDegrees(cartographic.latitude),
			});
			setOpenTrajectory(true);
			setBickDate(true);
		});

		return () => {
			layer.clearDrawing();
			layer.clear(true);
			layer.enabledEvent = false;
			map.removeLayer(layer);
		};
	}, [map, layerId]);

	useEffect(() => {
		if (!currentInfo) {
			if (layer) layer.show = true;
			window.electron.ipcRenderer.send('layer', layerId);
		} else {
			if (layer) layer.show = false;
		}
	}, [currentInfo]);

	return null;
};

export default ViolationLayer;
