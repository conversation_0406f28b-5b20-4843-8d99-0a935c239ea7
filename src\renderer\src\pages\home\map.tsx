import * as React from 'react';
import { useState, useRef, useEffect, useContext, useMemo } from 'react';
import 'mars3d-cesium';
import * as mars3d from 'mars3d';
import moment from 'moment';
import _, { forEach } from 'lodash';

import TimeBar from '@renderer/marsLayers/components/TimeBar';
import BeiJingMap from '@renderer/marsLayers/components/BeiJingMap';
import ModeMenu from '@renderer/marsLayers/components/ModeMenu';
import LayerMenu from '@renderer/marsLayers/components/LayerMenu';
import VehicleHistoricalTrajectory from '@renderer/marsLayers/layers/VehicleHistoricalTrajectory'; // 历史轨迹回放
import {
	layerDatas,
	LayerType,
} from '@renderer/marsLayers/components/LayerMenu/layerDatas';
import RightNav from '@renderer/marsLayers/components/RightNav';
import Dimensional from '@renderer/marsLayers/components/Dimensional';
import Layers from '@renderer/marsLayers/layers';
import { MaskLayer, BaseLayer } from '@renderer/layers'; // update
import RoadNetwork from '@renderer/marsLayers/layers/RoadNetwork';
import BackgroundFallingAreaMap from '@renderer/marsLayers/components/BackgroundFallingAreaMap';
import Flash from '@renderer/assets/start.mp4';
import Fiveviews from '@renderer/components/SingleChoice';
import VehicleSearchPanel from '@renderer/marsLayers/components/VehicleSearchPanel';
import {
	geoCenter3D,
	geoCenter2D,
} from '@renderer/marsLayers/components/BeiJingMap/data';
import { getVehicleTrack } from '@renderer/api';
import { PageContext, MapContext } from '@renderer/context';
import { CountyId } from '@renderer/utils/getData';
import {
	LayerDataCache,
	RealTimeOfflineDataCache,
	RoadGeometryDataCache,
	BeiJingTownDataCache,
} from '@renderer/utils/ManageDataCache';
import BikeDateNew from '@renderer/marsLayers/components/BikeDateNew';

import './styles.css';
import { MapProvider } from '@renderer/provider';

export const center2D = {
	name: '北京市',
	value: [116.41392, 39.97004],
};
export const height2D = 170824.7;
export const center3D = {
	name: '北京市',
	value: [116.395657, 39.340559],
};
export const height3D = 46466.3;
export const orientation = {
	heading: Cesium.Math.toRadians(0),
	pitch: Cesium.Math.toRadians(-35),
	roll: 0.0,
};

let stag = center3D.name;

const commonList = [
	{
		children: [
			{ name: 'PM₂.₅', id: '134004', visible: true },
			{ name: 'PM₁₀', id: '134002', visible: false },
			{ name: 'SO₂', id: '121026', visible: false },
			{ name: 'NO₂', id: '121004', visible: false },
			{ name: 'O₃', id: '105024', visible: false },
			{ name: 'CO', id: '121005', visible: false },
			{ name: 'AQI', id: '209002', visible: false },
		],
		menuStyle: { marginLeft: '-80px' },
		visible: false,
		id: '134004',
		name: 'PM₂.₅',
	},
];

const vehicleCondition = [
	{
		children: [
			{ name: '在线车辆', id: '1', visible: true },
			{ name: '离线车辆', id: '2', visible: false },
			{ name: '总车辆', id: '3', visible: false },
		],
		menuStyle: { marginLeft: '-80px' },
		visible: false,
		id: '1',
		name: '在线车辆',
	},
];

export default () => {
	return (
		<MapProvider>
			{showFlash && (
				<video
					id="video"
					className="flash"
					src={Flash}
					autoPlay={true}
					muted={true}
					objectfit="cover"
				></video>
			)}
			<MaskLayer visible={modalVisible} />

			<BaseLayer />
		</MapProvider>
	);
};
