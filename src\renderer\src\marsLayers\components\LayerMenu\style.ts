import styled from 'styled-components';
import layersMenu from '../../../images/layersMenu';

type Style = {
	isVisible?: boolean;
};

export const LayerMenuBotton = styled.div<Style>`
	position: absolute;
	top: 65px;
	left: 125px;
	z-index: 101;
	.layerMenuPhoto {
		background: url(${layersMenu.radio}) no-repeat;
		width: 80px;
		height: 80px;
		cursor: pointer;
		transform: rotate(0deg);
		&.rotated {
			animation: spin 0.5s ease-in-out 1;
			transform: rotate(0deg);
			@keyframes spin {
				from {
					transform: rotate(0deg);
				}
				to {
					transform: rotate(360deg);
				}
			}
		}
		&.rev-rotated {
			animation: revSpin 0.5s ease-in-out 1;
			transform: rotate(0deg);
			@keyframes revSpin {
				from {
					transform: rotate(0deg);
				}
				to {
					transform: rotate(-360deg);
				}
			}
		}
	}

	.radiocenter {
		position: absolute;
		top: 1px;
		left: -4px;
		background: url(${layersMenu.radiocenter}) no-repeat;
		width: 88px;
		height: 88px;
		cursor: pointer;
	}
`;

export const LayerMenuContent = styled.div<Style>`
	width: 290px;
	/* height: 33%; */
	position: absolute;
	left: 7.19%;
	top: 11.35%;
	z-index: 100;
	display: flex;
	flex-direction: column;
	/* background: linear-gradient(180deg, rgba(0, 184, 255, 0.2) 0%, rgba(0, 221, 255, 0) 100%);
   */
	background-color: #092642d9;
	border: ${({ isVisible }) => (isVisible ? '1px solid' : '0')};
	border-image: linear-gradient(
			180deg,
			rgba(0, 184, 255, 1),
			rgba(53, 142, 193, 0.16)
		)
		1 1;
	backdrop-filter: blur(10px);
	/* border-left: 1px solid transparent;
  border-right: 1px solid transparent;
  border-image: linear-gradient(to bottom, hsl(195, 100%, 60%), rgba(0, 108, 155, 0)) 1 100%; */
	padding: ${({ isVisible }) => (isVisible ? '25px 0 35px 0' : '0')};
	/* .line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: #007CB0;
    display: ${({ isVisible }) => (isVisible ? 'block' : 'none')};
    opacity: ${({ isVisible }) => (isVisible ? 1 : 0)};
  } */

	&::before,
	&::after {
		content: '';
		position: absolute;
		border: 2px solid rgb(11, 185, 246, 9);
		width: 51px;
		height: 52px;
		display: ${({ isVisible }) => (isVisible ? 'block' : 'none')};
		opacity: ${({ isVisible }) => (isVisible ? 1 : 0)};
	}

	&::before {
		left: 0;
		bottom: 0;
		border-top: 0;
		border-right: 0;
	}

	&::after {
		right: 0;
		bottom: 0;
		border-top: 0;
		border-left: 0;
	}

	.transition {
		transition: opacity 1.5s ease-in-out;
	}

	.transition.in {
		animation: in 1s ease-in-out 1;
		opacity: 1;
	}

	.transition.out {
		animation: out 5s ease-in-out 1;
		opacity: 0;
	}

	@keyframes out {
		0% {
			opacity: 1;
		}
		100% {
			opacity: 0;
		}
	}

	@keyframes in {
		0% {
			opacity: 0;
		}
		100% {
			opacity: 1;
		}
	}

	.layerMenuPhoto {
		background: url(${layersMenu.radio}) no-repeat;
		width: 80px;
		height: 80px;
		position: absolute;
		top: -39px;
		left: -42px;
		cursor: pointer;
		transform: rotate(0deg);
		&.rotated {
			animation: spin 0.5s ease-in-out 1;
			transform: rotate(0deg);
			@keyframes spin {
				from {
					transform: rotate(0deg);
				}
				to {
					transform: rotate(360deg);
				}
			}
		}
		&.rev-rotated {
			animation: revSpin 0.5s ease-in-out 1;
			transform: rotate(0deg);
			@keyframes revSpin {
				from {
					transform: rotate(0deg);
				}
				to {
					transform: rotate(-360deg);
				}
			}
		}
	}

	.radiocenter {
		position: absolute;
		top: -39px;
		left: -46px;
		background: url(${layersMenu.radiocenter}) no-repeat;
		width: 88px;
		height: 88px;
		cursor: pointer;
	}

	.item {
		flex: 1;
		/* height: 33%; */
		line-height: 65px;
		position: relative;
		margin-bottom: 5px !important;
		cursor: pointer;
		/* transform: translateX(15px); */
		color: #fff;
		/* margin: -11px 0; */
		&:nth-child(1) .one:hover {
			background: url(${layersMenu.vehicleStatus_active}) 18px 9px / 60px 60px
				no-repeat !important;
		}
		&:nth-child(2) .one:hover {
			background: url(${layersMenu.ViolationAnalysis_active}) 18px 9px / 60px
				60px no-repeat !important;
		}
		&:nth-child(3) .one:hover {
			background: url(${layersMenu.industry_active}) 18px 9px / 60px 60px
				no-repeat !important;
		}
		&:nth-child(4) .one:hover {
			background: url(${layersMenu.area_active}) 18px 9px / 60px 60px no-repeat !important;
		}
		&:nth-child(5) .one:hover {
			background: url(${layersMenu.streetTownship_active}) 18px 9px / 60px 60px
				no-repeat !important;
		}
		&:nth-child(6) .one:hover {
			background: url(${layersMenu.road_active}) 18px 9px / 60px 60px no-repeat !important;
		}
		&:nth-child(7) .one:hover {
			background: url(${layersMenu.userEnterprise_active}) 18px 9px / 60px 60px
				no-repeat !important;
		}
		/*
		&:nth-child(1) .one:hover {
			background: url(${layersMenu.vehicleStatus_active}) 18px 9px / 60px 60px
				no-repeat !important;
		}
		&:nth-child(2) .one:hover {
			background: url(${layersMenu.trafficVolume_active}) 18px 9px / 60px 60px
				no-repeat !important;
		}
		&:nth-child(3) .one:hover {
			background: url(${layersMenu.vehicleEmissions_active}) 18px 9px / 60px
				60px no-repeat !important;
		}
		&:nth-child(4) .one:hover {
			background: url(${layersMenu.ViolationAnalysis_active}) 18px 9px / 60px
				60px no-repeat !important;
		}
		&:nth-child(5) .one:hover {
			background: url(${layersMenu.userEnterprise_active}) 18px 9px / 60px 60px
				no-repeat !important;
		}
		&:nth-child(6) .one:hover {
			background: url(${layersMenu.industry_active}) 18px 9px / 60px 60px
				no-repeat !important;
		}
		&:nth-child(7) .one:hover {
			background: url(${layersMenu.area_active}) 18px 9px / 60px 60px no-repeat !important;
		}
		&:nth-child(8) .one:hover {
			background: url(${layersMenu.streetTownship_active}) 18px 9px / 60px 60px
				no-repeat !important;
		}
		&:nth-child(9) .one:hover {
			background: url(${layersMenu.road_active}) 18px 9px / 60px 60px no-repeat !important;
		}
		&:nth-child(10) .one:hover {
			background: url(${layersMenu.site_type_active}) 18px 9px / 60px 60px
				no-repeat !important;
		}
		&:nth-child(11) .one:hover {
			background: url(${layersMenu.air_active}) 18px 9px / 60px 60px no-repeat !important;
		}
    */
		.one {
			/* width: 77%; */
			/* width: 80%; */
			height: 100%;
			padding: 0 0 3% 82px;
			display: flex;
			align-items: center;
			font-size: 20px;
			transform: translateX(6px);

			.center {
				/* transform: translateY(6px);
        padding-left: 8px; */
				user-select: none;
			}
		}
		.two {
			/* width: 50%; */
			margin: 0 0 0 84px;
			.hoverlevel:hover {
				background: url(${layersMenu.level_active}) 0% center no-repeat !important;
			}
			.lastchild:last-child {
				margin: 0 0 0 0 !important;
			}
			.twoChildren {
				height: 30px;
				padding: 0% 0 0 0%;
				margin-bottom: 10px;
				/* transform: translate(3px,7px); */
				font-size: 20px;
				&:last-child {
					margin: 0 0 0 0;
				}
				> div {
					padding: 0% 0 0 28px;
					line-height: 30px;
					/* transform: translateY(-26px); */
				}
			}
		}
	}
	.open {
		animation: open 0.3s linear forwards;
	}
	@keyframes open {
		0% {
			margin-bottom: 0;
		}
		100% {
			margin-bottom: 5%;
		}
	}
	.close {
		animation: close 0.3s linear forwards;
	}
	@keyframes close {
		0% {
			margin-bottom: 5%;
		}
		100% {
			margin-bottom: 0%;
		}
	}
`;

export const PassingValuesDynamically = styled.div<Style>`
	max-height: 630px;
	overflow: scroll;
	overflow-x: hidden;
	/* padding: ${({ isVisible }) => (isVisible ? '20px 0' : '0')}; */
	margin-right: 5px;
	/* 自定义滚动条 */
	&::-webkit-scrollbar {
		width: 6px;
		height: 10px;
	}

	&::-webkit-scrollbar-thumb {
		background-color: rgba(144, 147, 153, 0.5);
		background-clip: padding-box;
		min-height: 28px;
		-webkit-border-radius: 2em;
		-moz-border-radius: 2em;
		border-radius: 2em;
		transition: background-color 0.3s;
		cursor: pointer;
	}

	&::-webkit-scrollbar-track {
		width: 6px;
		background: rgba(#101f1c, 0.1);
		-webkit-border-radius: 2em;
		-moz-border-radius: 2em;
		border-radius: 2em;
	}

	/* 鼠标悬停时自定义滚动条 */
	&:hover {
		&::-webkit-scrollbar-thumb {
			background-color: #005a84; /* 鼠标悬停时滚动条滑块的颜色 */
		}
	}
`;
