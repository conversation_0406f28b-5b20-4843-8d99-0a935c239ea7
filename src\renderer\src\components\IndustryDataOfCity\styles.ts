import styled from 'styled-components';

const IndustryDataOfCityStyled = styled.div`
	width: 100%;
	height: 100%;
	margin: 0 auto;
	margin-top: 10px;
	.RealTimeIndustryNav {
		width: auto;
		z-index: 9;
		background-color: rgba(0, 65, 94) !important;
		color: #fff !important;
		font-size: 15px !important;
		.ant-segmented-group {
			.ant-segmented-item {
				background: rgba(14, 47, 89, 0.66);
				border-radius: 4px;
			}
			.ant-segmented-item-selected {
				background: radial-gradient(
					37% 68% at 50% 50%,
					#3bc3f4 0%,
					rgba(52, 112, 190, 0) 100%
				);
				box-shadow: inset 0px 0px 5px 0px #45eeff;
				border-radius: 4px;
				border: 1px solid;
				border-image: radial-gradient(
						circle,
						rgba(85, 252, 255, 1),
						rgba(71, 211, 219, 1)
					)
					1 1;
			}
		}
	}
	/* .ant-segmented-item:hover {
    color: #111 !important;
  } */
	.ant-segmented-item-selected {
		background-color: rgba(0, 116, 145) !important;
	}
	.ant-segmented-item {
		width: 100%;
	}
`;

export const ReferenceStyled = styled.div`
	.selectAssembly {
		width: 100%;
		margin-left: auto;
		/* padding-right: 20px; */
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		box-shadow: inset 0px 0px 5px 0px #45eeff;
		border-radius: 6px 6px 6px 6px;
		border-image: radial-gradient(
				circle,
				rgba(85, 252, 255, 1),
				rgba(71, 211, 219, 1)
			)
			1 1;
		background: rgba(54, 121, 141, 0.23);
		//border-radius: 6px;
		border: 1px solid #3adaff;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
		}
		.ant-radio-button-wrapper {
			width: 80px;
			border: 0;
			border-radius: 5px;
			font-size: 16px;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			span:hover {
				color: #3adaff;
			}
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
	}
`;

export default IndustryDataOfCityStyled;
