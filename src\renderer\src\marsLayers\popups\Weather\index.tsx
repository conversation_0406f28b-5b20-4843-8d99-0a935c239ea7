import React, { useState, useEffect, useRef, useContext } from 'react';
import ReactDOM from 'react-dom';
import moment from 'moment';
import PopContainer from '@renderer/marsLayers/popups/common/PopContainer';

const WeatherPopup = (info: any) => {
	let { openCount } = info;
	const [text, setText] = useState('');
	const [multiline, setMultiline] = useState([
		{ label: '经度', value: '' },
		{ label: '纬度', value: '' },
		{ label: '海拔', value: '' },
		{ label: '温度', value: '' },
		{ label: '湿度', value: '' },
		{ label: '风向', value: '' },
		{ label: '总降水量', value: '' },
		{ label: '风力等级', value: '' },
	]);
	const [aloneRow, setAloneRow] = useState([{ label: '更新时间', value: '' }]);
	useEffect(() => {
		setText(info?.station_name);
		setMultiline([
			{ label: '经度', value: info?.lng, filter: true },
			{ label: '纬度', value: info?.lat, filter: true },
			{ label: '海拔', value: info?.altitude },
			{ label: '温度', value: `${info?.mete_val[2]['209033']}℃` },
			{ label: '湿度', value: `${info?.mete_val[3]['209034']}%` },
			{ label: '风向', value: info?.mete_val[0]['209036'] },
			{ label: '总降水量', value: info?.mete_val[4]['209046'] },
			{ label: '风力等级', value: info?.mete_val[1]['209063'] },
		]);
		setAloneRow([
			{
				label: '更新时间',
				value: moment(info?.datetime).format('YYYY-MM-DD HH时'),
			},
		]);
	}, [openCount]);

	const WeatherP = (
		<PopContainer text={text} multiline={multiline} aloneRow={aloneRow} />
	);

	return ReactDOM.createPortal(
		WeatherP,
		document.querySelector(`#${info.LayerId}Popup`),
	);
};

export default WeatherPopup;
