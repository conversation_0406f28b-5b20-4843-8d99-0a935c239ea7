import React, { useState, useEffect } from 'react';
import { NumberBox } from './style';

type Style = {
	fontSize?: string;
	color?: string;
};
type Props = {
	style?: Style;
	targetNumber: number;
};

const NumberAnimation: React.FC<Props> = ({ targetNumber, style }) => {
	const [number, setNumber] = useState(0);

	useEffect(() => {
		const duration = 2; // 持续时间，单位为秒
		const step = (targetNumber - number) / (duration * 60); // 每帧增加的值，假设每秒钟有 60 帧

		const interval = setInterval(() => {
			setNumber((prevNumber) => {
				if (prevNumber >= targetNumber) {
					clearInterval(interval);
					return targetNumber;
				}
				return prevNumber + step;
			});
		}, 16.7); // 每帧持续时间，假设 60 帧每秒

		return () => clearInterval(interval);
	}, [targetNumber]);

	const isDecimal = (str) => {
		return /^\d+\.\d+$/.test(str);
	};
	return (
		<NumberBox>
			<div style={style} className="number-animation">
				{isDecimal(number)
					? isNaN(Number(number.toFixed(1)))
						? '--'
						: number.toFixed(1)
					: isNaN(Number(number))
					? '--'
					: number}
			</div>
		</NumberBox>
	);
};

export default NumberAnimation;
