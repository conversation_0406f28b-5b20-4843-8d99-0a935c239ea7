import styled from 'styled-components';

const LayoutContainer = styled.div`
	display: flex;
	height: 100%;
	.tabs {
		margin-top: 5px;
		width: 30px;
		display: flex;
		flex-direction: column;
		cursor: pointer;
		align-items: center;
		height: 100%;
		justify-content: center;
		.tabs-item {
			line-height: 14px;
			padding: 4px 5px;
			color: #b6c1d2;
			font-size: 14px;
		}
		img {
			width: 16px;
			padding: 0 2px 3px;
		}
		a {
			border-right: 2px solid rgba(255, 255, 255, 0.1);
		}
		.active {
			border-right: 2px solid #3ebdcb;
			color: #3ebdcb;
		}
	}
	.down-container {
		display: flex;
		flex: 1;
		height: 100%;
		padding-top: 8px;
	}
`;
export default LayoutContainer;
