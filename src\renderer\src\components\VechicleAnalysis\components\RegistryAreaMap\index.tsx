import { useEffect, useState } from 'react';

import * as echarts from 'echarts';
import { geoJson } from './data';

import { MapEcharts } from './style';

const RegistryAreaMap = ({ mapData }) => {
	useEffect(() => {
		echartStart();
	}, [mapData]);

	const echartStart = () => {
		let option = {
			geo: {
				show: false,
				map: 'beijing',
				label: {
					normal: {
						show: false,
					},
					emphasis: {
						show: false,
					},
				},
				scaleLimit: {
					//缩放级别
					min: 0.5,
					max: 2,
				},
			},
			grid: {
				left: '0%',
				right: '0%',
				top: '0%',
				bottom: '0%',
				height: '80%',
			},
			visualMap: {
				show: false,
				min: 0,
				max: 12000,
				left: 'left',
				top: 'bottom',
				text: ['高', '低'], // 文本，默认为数值文本
				calculable: false,
				inRange: {
					color: [
						'#0a10af',
						'#FE619F',
						'#0aa179',
						'#b9e310',
						'#5d76c8',
						'#FF5E34',
						'#a94e04',
					],
				},
				textStyle: {
					color: '#ffffff',
				},
			},
			series: [
				{
					type: 'map',
					roam: false,
					label: {
						show: true,
						color: '#fff',
						fontSize: 10,
						width: 100,
						height: 30,
						lineHeight: 18,
					},
					itemStyle: {
						normal: {
							areaColor: '#003669',
							borderColor: '#3fdaff',
							borderWidth: 1,
							shadowColor: 'rgba(63, 218, 255,0.3)',
							shadowBlur: 20,
						},
					},
					emphasis: {
						label: {
							color: '#fff',
						},
						itemStyle: {
							areaColor: '#003669',
							borderColor: '#ebae51',
							borderWidth: 3,
						},
					},
					select: {
						selectedMode: 'single',
						smooth: true,
						label: {
							color: '#fff',
						},
						itemStyle: {
							areaColor: '#003669',
							borderColor: '#ebae51',
							borderWidth: 3,
						},
					},
					data: mapData,
					zoom: 0.8,
					map: 'beijing', //使用
				},
			],
		};
		let myChart = echarts.init(document.getElementById('registration-map'));
		myChart.showLoading();
		// @ts-ignore
		echarts.registerMap('beijing', geoJson);
		myChart.hideLoading();
		myChart.setOption(option, true);
	};

	return (
		<MapEcharts>
			<div id="registration-map"></div>
		</MapEcharts>
	);
};
export default RegistryAreaMap;
