import { getCarColor } from '../../../../../../main/data';
import { formatNumber } from '@renderer/hooks/index';

export const getIper = (data) => {
	const colorList = [
		'#FD9546',
		'#8A40FF',
		'#E3A07C',
		'#491DFF',
		'#8EFAFF',
		'#A3EE63',
		'#63A4EE',
		'#6863EE',
	];
	return {
		// backgroundColor: '#061740',
		color: colorList,
		tooltip: {
			show: true,
			formatter: (params) => {
				return `<span style="font-size: larger;">${params.name} ${params.value}%</span>`;
			},
		},
		legend: [
			{
				type: 'scroll', // 设置图例翻页
				orient: 'horizontal', // 图例横向排布
				right: '5%',
				bottom: '0%',
				align: 'left',
				itemGap: 14,
				// textStyle: {
				//   color: '#fff',
				//   fontSize: '14px'
				// },
				//图例标记的图形高度
				itemHeight: 10,
				//图例标记的图形宽度
				itemWidth: 10,
				formatter: function (name) {
					for (let i = 0; i < data?.length; i++) {
						if (!data?.length) return;
						if (name == data[i].name) {
							return '{name|' + name + '}';
						}
					}
				},
				textStyle: {
					rich: {
						name: {
							fontSize: 16,
							fontWeight: 400,
							width: 100,
							height: 20,
							padding: [0, 0, 0, 5],
							color: '#fff',
							// fontSize: '14px',
							fontFamily: 'Source Han Sans CN-Regular',
						},
						// num: {
						//   fontSize: 14,
						//   fontWeight: 500,
						//   height: 20,
						//   width: 50,
						//   align: 'left',
						//   color: 'rgba(0, 0, 0, 0.65)',
						//   fontFamily: 'Source Han Sans CN-Regular'
						// },
						value: {
							fontSize: 16,
							fontWeight: 500,
							height: 20,
							width: 50,
							align: 'left',
							color: '#fff',
							// fontSize: '14px',
							fontFamily: 'Source Han Sans CN-Regular',
						},
					},
				},
			},
		],
		series: [
			{
				name: '',
				type: 'pie',
				clockwise: false,
				radius: ['100%', '170%'],
				width: '65%',
				height: '35%',
				emphasis: {
					scale: false,
				},
				center: ['56%', '25%'],
				top: 'center',
				label: {
					show: false,
					position: 'inside',
					color: '#fff',
				},
				data,
			},
			{
				name: '',
				type: 'pie',
				clockwise: false,
				radius: ['180%', '175%'],
				center: ['56%', '25%'],
				width: '65%',
				height: '35%',
				top: 'center',
				avoidLabelOverlap: false,
				itemStyle: {
					borderRadius: 1000,
				},
				label: {
					position: 'outside',
					color: '#E6F7FF',
					lineHeight: 18,
					fontSize: 16,
					// formatter: (params) => {
					//   const { data } = params
					//   return data.name + ' ' + data.value + '%'
					// }
				},
				emphasis: {
					disabled: true,
				},
				// 占位样式
				emptyCircleStyle: {
					color: 'rgba(255,255,255,0)',
				},
				data: data?.length > 0 ? data : [],
			},
		],
	};
};

export const getIndustry = (val) => {
	if (!val) return;
	const seriesList: any = [];
	const originalName = val.originalName;
	Object.keys(val).forEach((key) => {
		if (
			key !== 'name' &&
			key !== 'total' &&
			key !== 'originalName' &&
			key !== 'unit'
		) {
			const item = val[key];
			const count = item.data;
			const countSort = val.name.map((element) => {
				const index = originalName.indexOf(element);
				return formatNumber(count[index]);
			});

			seriesList.push({
				name: item.name,
				type: 'bar',
				stack: 'total',
				color: getCarColor(item.name),
				emphasis: {
					focus: 'series',
				},
				barWidth: 14,
				data: countSort,
			});
		}
	});

	const startValue = val?.name[val?.name?.length - 1];
	const endValue =
		val?.name?.length >= 10 ? val?.name[val?.name?.length - 10] : val?.name[0];

	return {
		tooltip: {
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',
			padding: 8,
			textStyle: {
				color: '#fff',
			},
		},
		legend: {
			width: '80%',
			right: '10%',
			textStyle: {
				fontSize: 16,
				color: '#E2F0FF',
			},
			type: 'scroll',
			pageTextStyle: {
				color: '#fff', // 文字样式
			},
		},
		grid: {
			top: 45,
			bottom: 0,
			left: '12.8%',
			right: '10%',
		},
		xAxis: {
			type: 'value',
			axisPointer: {
				type: 'shadow',
			},
			axisLabel: {
				textStyle: {
					color: '#C6C7C7',
					fontSize: 16,
				},
			},
		},
		yAxis: {
			name: val?.unit,
			nameTextStyle: {
				color: '#D8DBDE',
				fontSize: 14,
				padding: [-10, 0],
			},
			type: 'category',
			data: val?.name,
			axisLabel: {
				textStyle: {
					color: '#E2F0FF',
					fontSize: 18,
				},
				formatter: function (value, index) {
					if (value.length > 4) {
						return value.slice(0, 4) + '...';
					}
					return value;
				},
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: 'rgba(222, 227, 239, 0.3)',
					type: [2, 4],
				},
			},
		},
		dataZoom: [
			//Y轴滑动条
			{
				type: 'inside', //滑动条
				show: true, //开启
				yAxisIndex: 0,
				startValue,
				endValue,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
				left: '93%', //滑动条位置
			},
			//y轴内置滑动
			{
				type: 'inside', //内置滑动，随鼠标滚轮展示
				yAxisIndex: 0,
				startValue,
				endValue,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
			},
		],
		series: seriesList,
	};
};

export const getOnlineSituationEcharts = (data) => {
	return {
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999',
				},
			},
		},
		grid: {
			top: 35,
			bottom: 45,
		},
		legend: {
			data: ['上线率', '总量'],
			top: 0, // 调整图例的位置，使其移动到图的上方
			right: 40, // 调整图例的位置，使其移动到图的右侧
			textStyle: {
				color: '#fff',
				fontSize: 16,
			},
			itemWidth: 10, // 设置图例项的宽度
			itemHeight: 10, // 设置图例项的高度
			itemShape: 'circle', // 设置图例项的形状为圆形
		},
		xAxis: [
			{
				type: 'category',
				data: data?.adm,
				axisPointer: {
					type: 'shadow',
				},
				axisLabel: {
					textStyle: {
						color: '#E2F0FF',
						fontSize: 16,
					},
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				splitLine: {
					lineStyle: {
						type: 'dashed',
					},
				},
				axisLabel: {
					textStyle: {
						color: '#E2F0FF',
						fontSize: 16,
					},
					formatter: function (value, index) {
						if (value >= 10000) {
							return (value / 10000).toFixed(2) + '万';
						} else {
							return value;
						}
					},
				},
			},
		],
		series: [
			{
				name: '总量',
				type: 'bar',
				tooltip: {},
				barWidth: 20,
				itemStyle: {
					// 设置柱状图的颜色为绿色
					color: 'rgb(102,204,204)',
				},
				data: data?.nums,
			},
			{
				name: '上线率',
				type: 'line',
				itemStyle: {
					// 设置温度系列的样式
					color: 'rgb(42, 112, 252)', // 修改温度系列的颜色为橙色
				},
				data: data?.onlineData,
			},
		],
	};
};
