const ee = 0.00669342162296594323;
const a = 6378245.0;

export function handleExtents(extent) {
	const { xmin, ymin, xmax, ymax } = extent;
	const polygon = [
		[xmin, ymin],
		[xmax, ymin],
		[xmax, ymax],
		[xmin, ymax],
		[xmin, ymin],
	];
	return polygon;
}

export const isInPolygon = (checkPoint, polygonPoints) => {
	let counter = 0;
	let p1, p2;
	const pointCount = polygonPoints.length;
	p1 = polygonPoints[0];
	for (let i = 1; i <= pointCount; i++) {
		p2 = polygonPoints[i % pointCount];
		if (
			checkPoint[0] > window.Math.min(p1[0], p2[0]) &&
			checkPoint[0] <= window.Math.max(p1[0], p2[0])
		) {
			if (checkPoint[1] <= window.Math.max(p1[1], p2[1])) {
				if (p1[0] != p2[0]) {
					const xinters =
						((checkPoint[0] - p1[0]) * (p2[1] - p1[1])) / (p2[0] - p1[0]) +
						p1[1];
					if (p1[1] == p2[1] || checkPoint[1] <= xinters) {
						counter++;
					}
				}
			}
		}
		p1 = p2;
	}
	if (counter % 2 == 0) {
		return false;
	} else {
		return true;
	}
};

const out_of_china = (lng, lat) => {
	return (
		lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271 || false
	);
};

const transformlat = (lng, lat) => {
	var ret =
		-100.0 +
		2.0 * lng +
		3.0 * lat +
		0.2 * lat * lat +
		0.1 * lng * lat +
		0.2 * Math.sqrt(Math.abs(lng));
	ret +=
		((20.0 * Math.sin(6.0 * lng * Math.PI) +
			20.0 * Math.sin(2.0 * lng * Math.PI)) *
			2.0) /
		3.0;
	ret +=
		((20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin((lat / 3.0) * Math.PI)) *
			2.0) /
		3.0;
	ret +=
		((160.0 * Math.sin((lat / 12.0) * Math.PI) +
			320 * Math.sin((lat * Math.PI) / 30.0)) *
			2.0) /
		3.0;
	return ret;
};

const transformlng = (lng, lat) => {
	var ret =
		300.0 +
		lng +
		2.0 * lat +
		0.1 * lng * lng +
		0.1 * lng * lat +
		0.1 * Math.sqrt(Math.abs(lng));
	ret +=
		((20.0 * Math.sin(6.0 * lng * Math.PI) +
			20.0 * Math.sin(2.0 * lng * Math.PI)) *
			2.0) /
		3.0;
	ret +=
		((20.0 * Math.sin(lng * Math.PI) + 40.0 * Math.sin((lng / 3.0) * Math.PI)) *
			2.0) /
		3.0;
	ret +=
		((150.0 * Math.sin((lng / 12.0) * Math.PI) +
			300.0 * Math.sin((lng / 30.0) * Math.PI)) *
			2.0) /
		3.0;
	return ret;
};

export const wgs84togcj02 = (lng, lat) => {
	var lat = +lat;
	var lng = +lng;
	if (out_of_china(lng, lat)) {
		return [lng, lat];
	} else {
		var dlat = transformlat(lng - 105.0, lat - 35.0);
		var dlng = transformlng(lng - 105.0, lat - 35.0);
		var radlat = (lat / 180.0) * Math.PI;
		var magic = Math.sin(radlat);
		magic = 1 - ee * magic * magic;
		var sqrtmagic = Math.sqrt(magic);
		dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * Math.PI);
		dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * Math.PI);
		var mglat = lat + dlat;
		var mglng = lng + dlng;
		return [mglng, mglat];
	}
};

export let gcj02towgs84 = (lng, lat) => {
	if (out_of_china(lng, lat)) {
		return [lng, lat];
	} else {
		var dlat = transformlat(lng - 105.0, lat - 35.0);
		var dlng = transformlng(lng - 105.0, lat - 35.0);
		var radlat = (lat / 180.0) * Math.PI;
		var magic = Math.sin(radlat);
		magic = 1 - ee * magic * magic;
		var sqrtmagic = Math.sqrt(magic);
		dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * Math.PI);
		dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * Math.PI);
		var mglat = lat + dlat;
		var mglng = lng + dlng;
		return [lng * 2 - mglng, lat * 2 - mglat];
	}
};
