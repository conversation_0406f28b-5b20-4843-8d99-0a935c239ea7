import React, { useState, useEffect, useRef, useContext } from 'react';
import ReactDOM from 'react-dom';
import moment from 'moment';
import { MapContext } from '@renderer/context';
import PopContainer from '@renderer/marsLayers/popups/common/PopContainer';
import { getStdStationData, getHightStationData } from '@renderer/api';
import PollutantListCon from '@renderer/marsLayers/popups/common/PollutantList';
import BarEchart from '@renderer/marsLayers/popups/common/BarEchart';
import dayjs from 'dayjs';
const StdPopup = (info: any) => {
	let { openCount } = info;
	const { navList } = useContext(MapContext);
	const [text, setText] = useState('');
	const [selectedPollutantId, setSelectedPollutantId] = useState(134004);
	const [echartData, setEchartData] = useState<any>({
		time: [],
		data: [],
	});
	const echarsRef = useRef();
	let req = {
		station_id: info?.station_id,
		pollutant_id: selectedPollutantId,
	};
	const [listData, setListData] = useState([]);

	const [multiline, setMultiline] = useState([
		{ label: '城市', value: '' },
		{ label: '区县', value: '' },
		{ label: '经度', value: '' },
		{ label: '纬度', value: '' },
	]);
	const [aloneRow, setAloneRow] = useState([{ label: '更新时间', value: '' }]);

	useEffect(() => {
		setSelectedPollutantId(navList[0].id);
	}, [navList[0]]);

	const getStdStation = () => {
		req = {
			token:
				'qiYrxXEv5mJPW6-dU5_wQRI-PR9kst_qrORUjRNuf9omXjG98bE4NHg2o1NSyIIa1yXqWDSDVvwhUnPAt2gMWpOGxnty576cXBCZwI7OlUpd0HlEtkE0gczq2P-pFKLOfjnSVy7TNQocKSIU5m_x4aqytYzvAV7OWBcSrQB_WOWBMh7-bbMEq5ycWk8uJpfmTFCc4pyCu9o83oGvOKBOsk71dHNmoU1XJU0fDxef9BQ=',
			station_id: info?.station_id,
			is_bj: 0,
			start_time: dayjs(info?.end_time)
				.subtract(49, 'hour')
				.format('YYYY-MM-DD HH:00:00'),
			end_time: dayjs(info?.end_time)
				.subtract(1, 'hour')
				.format('YYYY-MM-DD HH:00:00'),
			pollutant_id: selectedPollutantId,
			time_type: 1,
		};
		getStdStationData(req)
			.then((res) => {
				setText(info?.name);
				setEchartData({
					time: res?.echarts_data?.value?.datetime || [],
					data: res?.echarts_data?.value?.value || [],
				});
				setListData(res?.real_time_data?.value || []);
				setMultiline([
					{ label: '城市', value: res?.real_time_data?.city },
					{ label: '区县', value: res?.real_time_data?.county },
					{
						label: '经度',
						value: res?.real_time_data?.longitude,
						filter: true,
					},
					{ label: '纬度', value: res?.real_time_data?.latitude, filter: true },
				]);
				setAloneRow([
					{
						label: '更新时间',
						value: moment(res?.real_time_data?.datetime).format(
							'YYYY-MM-DD HH时',
						),
					},
				]);
			})
			.catch();
	};

	const getHightStation = () => {
		req = {
			token:
				'qiYrxXEv5mJPW6-dU5_wQRI-PR9kst_qrORUjRNuf9omXjG98bE4NHg2o1NSyIIa1yXqWDSDVvwhUnPAt2gMWpOGxnty576cXBCZwI7OlUpd0HlEtkE0gczq2P-pFKLOfjnSVy7TNQocKSIU5m_x4aqytYzvAV7OWBcSrQB_WOWBMh7-bbMEq5ycWk8uJpfmTFCc4pyCu9o83oGvOKBOsk71dHNmoU1XJU0fDxef9BQ=',
			devsite_code: info?.devsite_code,
			start_time: dayjs(info?.end_time)
				.subtract(49, 'hour')
				.format('YYYY-MM-DD HH:00:00'),
			end_time: dayjs(info?.end_time)
				.subtract(1, 'hour')
				.format('YYYY-MM-DD HH:00:00'),
			pollutant_id: selectedPollutantId,
			time_type: 1,
		};
		getHightStationData(req)
			.then((res) => {
				setText(res?.real_time_data?.name);
				setEchartData({
					time: res?.echarts_data?.value?.datetime || [],
					data: res?.echarts_data?.value?.value || [],
				});
				let listData = res?.real_time_data?.value || [];
				const uniqueArray = listData.reduce((accumulator, current) => {
					if (
						!accumulator.some(
							(item) => JSON.stringify(item) === JSON.stringify(current),
						)
					) {
						accumulator.push(current);
					}
					return accumulator;
				}, []);
				setListData(uniqueArray);
				setMultiline([
					{ label: '城市', value: res?.real_time_data?.city_name },
					{ label: '区县', value: res?.real_time_data?.county_name },
					{ label: '经度', value: info?.lng, filter: true },
					{ label: '纬度', value: info?.lat, filter: true },
				]);
				setAloneRow([
					{
						label: '更新时间',
						value: moment(res?.real_time_data?.datetime).format(
							'YYYY-MM-DD HH时',
						),
					},
				]);
			})
			.catch();
	};

	useEffect(() => {
		setEchartData({
			time: [],
			data: [],
		});
		switch (info?.LayerId) {
			case 'stdg':
			case 'stds':
				getStdStation();
				break;
			case 'hight':
				getHightStation();
				break;
			default:
				break;
		}
	}, [openCount, selectedPollutantId]);

	const stdP = (
		<PopContainer text={text} multiline={multiline} aloneRow={aloneRow}>
			<PollutantListCon
				pollutantId={selectedPollutantId}
				data={listData || []}
				changePollutantId={setSelectedPollutantId}
			/>
			{echartData ? (
				<BarEchart
					inputRef={echarsRef}
					xAxisData={echartData.time}
					seriesData={echartData.data}
					yAxisName={echartData ? String(selectedPollutantId) : null}
				></BarEchart>
			) : (
				<div style={{ height: '230px' }}></div>
			)}
		</PopContainer>
	);

	return ReactDOM.createPortal(
		stdP,
		document.querySelector(`#${info.LayerId}Popup`),
	);
};

export default StdPopup;
