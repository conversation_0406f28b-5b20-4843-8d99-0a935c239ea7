import styled from 'styled-components';
import Echarts from '@renderer/components/echarts';

type Props = {
	width?: string;
	height?: string;
	echartsData: Array<any>;
};

export const Container = styled.div<Props>`
	width: ${(props) => props.width || '100%'};
	height: ${(props) => props.height || '365px'};
`;

const DailyActivityLevelEcharts = (props: Props) => {
	const { echartsData } = props;

	const option = {
		tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
		grid: {
			left: '4%',
			right: '4%',
			top: '15%',
			bottom: '10%',
			containLabel: true,
		},
		yAxis: {
			nameTextStyle: { fontSize: 20, color: '#D8DBDE' },
			type: 'value',
			splitLine: {
				show: true,
				lineStyle: {
					color: ['#C6C7C7'],
					width: 1,
					type: 'dashed',
				},
			},
			axisLine: { show: true, textStyle: { color: '#C6C7C7' } },
			axisLabel: {
				fontSize: 16,
				color: '#C6C7C7',
			},
		},
		xAxis: {
			type: 'category',
			data: echartsData.map((item) => item.name),
			axisLine: {
				show: true,
				textStyle: {
					color: '#1ebfda',
				},
			},
			axisTick: {
				show: false,
			},
			axisLabel: {
				interval: 0,
				show: true,
				textStyle: {
					color: '#D8DBDE',
				},
				formatter: function (params) {
					let newParamsName = '';
					const paramsNameNumber = params.length;
					const provideNumber = 4; //一行显示几个字
					const rowNumber = Math.ceil(paramsNameNumber / provideNumber);
					if (paramsNameNumber > provideNumber) {
						for (let p = 0; p < rowNumber; p++) {
							let tempStr = '';
							const start = p * provideNumber;
							const end = start + provideNumber;
							if (p == rowNumber - 1) {
								tempStr = params.substring(start, paramsNameNumber);
							} else {
								tempStr = params.substring(start, end) + '\n';
							}
							newParamsName += tempStr;
						}
					} else {
						newParamsName = params;
					}
					return newParamsName;
				},
			},
		},
		series: {
			type: 'bar',
			barWidth: 12,
			barGap: '40%',
			data: echartsData.map((item) => item.value),
			itemStyle: {
				barBorderRadius: [4, 4, 0, 0],
			},
			color: '#85A9FF',
		},
	};

	return (
		<Container {...props}>
			<Echarts option={option}></Echarts>
		</Container>
	);
};

export default DailyActivityLevelEcharts;
