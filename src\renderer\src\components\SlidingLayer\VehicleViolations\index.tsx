import React, { useState, useContext, useEffect } from 'react';
import LevelTwoTitle from '@renderer/components/LevelTwoTitle';
import ScrollList from '@renderer/components/ScrollList';
import icon from '@renderer/images/icon/point.png';
import LayoutContainer from './style';
type listProps = {
	violation_type_id: string;
	violation_type: string;
	vin: string;
};
type columnProps = {
	plate_number: string;
	violation_project: string;
	violation_time: string;
	violation_place: string;
};
type enterpriseProps = {
	name: string;
	color: string;
	violation_nums: string;
	rate: number | string | any;
};
const VehicleViolations = (props) => {
	const { enterprise, violatioData } = props;
	const [show, setShow] = useState(false);
	const [details, setDetails] = useState<columnProps>();
	const column = [
		{
			align: 'center',
			dataIndex: 'vin',
			key: 'vin',
			title: 'VIN',
			with: '100px',
		},
		{
			align: 'center',
			dataIndex: 'violation_type',
			key: 'violation_type',
			title: '违规项目',
		},
		{
			align: 'center',
			dataIndex: 'time',
			key: 'time',
			title: '违规时间',
		},
		// {
		// 	align: 'center',
		// 	dataIndex: 'address',
		// 	key: 'address',
		// 	title: '违规地址',
		// },
	];
	const onliClick = (e) => {
		setShow(true);
		setDetails(e);
	};
	// const [violatioData, setViolatioData] = useState<Array<listProps>>([])
	return (
		<LayoutContainer>
			<div className="middle-container">
				<LevelTwoTitle title="单车违规推送"></LevelTwoTitle>
				<div className="table">
					<ul className="column-ul">
						{column.map((item, index) => {
							return (
								<li className="column-li" key={index}>
									{item.title}{' '}
								</li>
							);
						})}
					</ul>
					<ScrollList
						onliClick={onliClick}
						data={violatioData}
						column={column}
					></ScrollList>
				</div>
			</div>
			<div className="right-container">
				<LevelTwoTitle title="违规企业TOP"></LevelTwoTitle>
				<div className="enterprise">
					{enterprise?.map((item, index) => {
						return (
							<div key={index} className="enterprise-item">
								<div className="up">
									<span style={{ color: item.color }}>{index + 1}</span>
									<span>{item.name}</span>
								</div>

								<div className="down">
									<img src={icon}></img>
									<span>违规数量:</span>
									<span>{item.vio_num}</span>
									<span>违规率:</span>
									<span>{item.vio_rate}</span>
								</div>
							</div>
						);
					})}
				</div>
			</div>
		</LayoutContainer>
	);
};

export default VehicleViolations;
