import { useEffect, useState, useContext } from 'react';
import { IndustryRightSideStyled, LayoutContainer } from './styles';
import Echarts from '@renderer/components/echarts';
import { Space, Button, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import {
	getIndustryOilConsumption,
	getIndustryDistanceAnalyse,
	getIndustryNoxAnalyse,
	getIndustryViolationAnalyse,
} from '@renderer/api';
import { PageContext } from '@renderer/context';
import {
	COUNTY_ID_NAME_MAP,
	unitList,
} from '@renderer/marsLayers/components/AreaStreetStyles';
import industryClassification, {
	VehicleLevelData,
	EmissionLevelData,
} from '../components/IndustryClassification';
import {
	IndustryRightSideTop,
	IndustryRightSideMid,
	IndustryRightSideBottom,
} from '../components/EnterpriseAnalysis';
import { count } from 'console';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const IndustryRightSide = (props: Props) => {
	const { currentSelectedId, streetShipData } = props;
	const { selectRegionDate, regionName, streetName } = useContext(PageContext);
	console.log('regionName', regionName);
	//图表
	const [option1, setOption1] = useState({});
	const [option2, setOption2] = useState({});
	const [option3, setOption3] = useState({});
	const [currentIndustryTop, setCurrentIndustryTop] = useState('全部');
	const [emissionLevel, setEmissionLevel] = useState('全部');
	const [title1, setTitle1] = useState(null);
	const items: MenuProps['items'] = VehicleLevelData.map((item, idx) => {
		return {
			label: item,
			key: item,
		};
	});

	const itemsEmission: MenuProps['items'] = EmissionLevelData.map(
		(item, idx) => {
			return {
				label: item,
				key: item,
			};
		},
	);

	const handleMenuClick: MenuProps['onClick'] = (e) => {
		setEmissionLevel(e.key);
	};

	const handleMenuClickTop: MenuProps['onClick'] = (e) => {
		setCurrentIndustryTop(e.key);
	};

	const menuProps = {
		items: itemsEmission,
		onClick: handleMenuClick,
	};

	const menuPropsTop = {
		items,
		onClick: handleMenuClickTop,
	};

	const getDropDownMenu = (params) => {
		const { type } = params;
		return (
			<Dropdown menu={menuProps} className={`dropDown${type}`} key={type}>
				<Button>
					<Space>
						{emissionLevel}
						<DownOutlined />
					</Space>
				</Button>
			</Dropdown>
		);
	};

	// 油耗分析
	const getChartOneData = (params) => {
		getIndustryOilConsumption(params)
			.then((res) => {
				if (res?.length > 0) {
					setOption1(IndustryRightSideTop(res));
				} else {
					setOption1({});
				}
			})
			.catch((err) => {
				setOption1({});
			});
	};

	// 排放分析
	const getChartTwoData = (params) => {
		getIndustryNoxAnalyse(params)
			.then((res) => {
				if (res?.length > 0) {
					setOption2(IndustryRightSideMid(res));
				} else {
					setOption2({});
				}
			})
			.catch((err) => {
				setOption2({});
			});
	};

	// 违规车辆分析
	const getChartThreeData = (params) => {
		getIndustryViolationAnalyse(params)
			.then((res) => {
				if (res?.length > 0) {
					setOption3(IndustryRightSideBottom(res));
				} else {
					setOption3({});
				}
			})
			.catch((err) => {
				setOption3({});
			});
	};
	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time) return;
		const params: any = {
			start_time,
			end_time,
			time_type,
		};
		params.county_id =
			Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) + 1;
		if (currentIndustryTop && currentIndustryTop !== '全部') {
			params.vehicle_level = currentIndustryTop;
		}
		getChartOneData(params);
	}, [currentIndustryTop, currentSelectedId, selectRegionDate, regionName]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time) return;
		const params: any = {
			start_time,
			end_time,
			time_type,
		};
		params.county_id =
			Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) + 1;
		if (emissionLevel && emissionLevel !== '全部') {
			params.emission_level = emissionLevel;
		}
		getChartTwoData(params);
	}, [emissionLevel, currentSelectedId, selectRegionDate, regionName]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time) return;
		const params: any = {
			start_time,
			end_time,
			time_type,
		};
		params.county_id =
			Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) + 1;
		getChartThreeData(params);
	}, [currentSelectedId, selectRegionDate, regionName]);
	return (
		<IndustryRightSideStyled>
			<div className="Streetcontent">
				<div className="slidingLayer">
					<span className="slidtext">油耗分析</span>
					<Dropdown menu={menuPropsTop} className={`dropDown1`} key={1}>
						<Button>
							<Space>
								{currentIndustryTop}
								<DownOutlined />
							</Space>
						</Button>
					</Dropdown>
				</div>
				<LayoutContainer style={{ height: '20%' }}>
					<div className="echarts-line">
						<Echarts
							option={option1}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">排放情况分析</span>
					{getDropDownMenu({ type: 2 })}
				</div>
				<LayoutContainer style={{ height: '35%' }}>
					<div className="echarts-line" style={{ width: '90%' }}>
						<Echarts
							option={option2}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">违规情况分析</span>
				</div>
				<LayoutContainer style={{ height: '25%' }}>
					<div className="echarts-line">
						<Echarts
							option={option3}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
			</div>
		</IndustryRightSideStyled>
	);
};

export default IndustryRightSide;
