import React, { useState, useContext, useEffect } from 'react';

import { SlidingLayerStyled } from './styles';
import { PageContext } from '../context';
import VehicleViolations from '../components/SlidingLayer/VehicleViolations';
import UserProfile from '../components/SlidingLayer/UserProfile';
import DailyActivityLevel from '../components/SlidingLayer/DailyActivityLevel';

const SlidingLayer = (props) => {
	const { direction, id, order, title, data } = props;

	const { setSlidingLayerInfo } = useContext(PageContext);
	const [com, setCom] = useState<any>(null);
	const [closeIng, setCloseIng] = useState<boolean>(false);
	const closeLayer = () => {
		setCloseIng(true);
		setTimeout(() => {
			setSlidingLayerInfo(null);
		}, 500);
	};

	useEffect(() => {
		switch (id) {
			case 'VehicleViolations':
				setCom(<VehicleViolations {...data} />);
				break;
			case 'UserProfile':
				setCom(<UserProfile {...data} />);
				break;
			case 'DailyActivityLevel':
				setCom(<DailyActivityLevel {...data} />);
				break;
			default:
				setCom(null);
				break;
		}
		return () => setCom(null);
	}, [id]);

	return (
		<SlidingLayerStyled
			className={`${direction}Slid ${closeIng ? 'closeIng' : ''}`}
		>
			<div className="slidingLayer">
				<span className="slidtext">详细信息</span>
				<span
					onClick={() => {
						closeLayer();
					}}
					className="closeLayer"
				></span>
				<div className="slidingLayer-content">{com}</div>
			</div>
		</SlidingLayerStyled>
	);
};

export default SlidingLayer;
