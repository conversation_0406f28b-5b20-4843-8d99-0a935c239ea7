import styled from 'styled-components';
import background from '@renderer/images/background';
import searchBg from '@renderer/images/icon/searchBg.png';

const BikeDateStyled = styled.div`
	position: absolute;
	top: 0;
	left: 50%;
	z-index: 101;
	transform: translateX(-50%);
	width: 34%;
	height: 60px;
	background: url(${background.regionalBanner}) no-repeat;
	background-size: 100% 100%;
	margin: 0 auto;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fff;
	border-radius: 15px;
	font-size: 20px;
	font-weight: 600;
	.selectAssembly {
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		padding-bottom: 10px;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
		}
		.ant-radio-button-wrapper {
			width: 80px;
			border: 0;
			border-radius: 10px;
			font-size: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			span:hover {
				color: #3adaff;
			}
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
	}
	.content {
		width: 100%;
		padding: 10px 30px;
		display: flex;
		flex-direction: row;
		justify-content: space-evenly;
		.item {
			display: flex;
			align-items: center;
			p {
				margin-right: 10px;
			}
		}
	}
	.search {
		width: 100px;
		height: 45px;
		background: url(${searchBg}) no-repeat;
		background-size: 100% 100%;
		line-height: 45px;
		text-align: center;
		cursor: pointer;
	}
	.btn-return {
		width: 70px;
		height: 45px;
		background: url(${searchBg}) no-repeat;
		background-size: 100% 100%;
		line-height: 45px;
		text-align: center;
		cursor: pointer;
	}
	.settings-select {
		.ant-select-selector {
			background: rgba(64, 146, 171, 0.16);
			border-radius: 4px;
			border: 1px solid #3fdaff;
			font-size: 18px !important;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			height: 32px;
			color: #ffffff !important;
			display: flex;
			align-items: center;
		}
		.ant-select-selection-overflow {
			text-overflow: ellipsis;
			white-space: nowrap;
			height: 49px;
			align-items: center;
			flex-wrap: nowrap;
			overflow: hidden;
		}
		.ant-select-selection-overflow-item {
			background-color: transparent;
			border: 0;
			.ant-select-selection-item {
				background-color: transparent;
				border: 0;
			}
			.ant-select-selection-item-remove {
				color: #fff;
				font-size: 20px;
			}
		}
	}
	.ant-select-arrow {
		color: rgba(255, 255, 255, 1);
		font-size: 18px;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-select-single.ant-select-open
		.ant-select-selection-item {
		color: #ffffff !important;
	}
	.ant-picker {
		background: rgba(64, 146, 171, 0.16);
		border-radius: 4px;
		border: 1px solid #3fdaff;
		font-size: 18px !important;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		height: 32px;
		color: #ffffff !important;
		display: flex;
		align-items: center;
	}
	.ant-picker-input {
		font-size: 20px;
		color: #e2f0ff !important;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-picker .ant-picker-suffix {
		display: none !important;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-picker .ant-picker-clear {
		display: none !important;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-picker
		.ant-picker-input
		> input {
		text-align: center !important;
		line-height: 0 !important;
		font-size: 18px !important;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-picker
		.ant-picker-input-placeholder
		> input {
		color: #fff !important;
	}
	:where(.css-dev-only-do-not-override-mu9r37).ant-picker {
		padding: 0 !important;
	}
`;

export { BikeDateStyled };
