import styled from 'styled-components';

type Style = {
	scale?: string | number;
	translateY?: string | number;
};

const LayoutContainer = styled.div<Style>`
	width: 3840px;
	height: 1080px;
	overflow: hidden;
	font-size: 18px;
	background-color: #040e18;
	position: relative;
	transform-origin: 0 0;
	transform: scale(${(props) => props.scale || 1})
		translateY(${(props) => props.translateY + 'px' || 0});
	top: 50%;
	.item-group {
		height: 30%;
		width: 100%;
	}
`;

export default LayoutContainer;
