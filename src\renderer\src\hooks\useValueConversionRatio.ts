import _ from 'lodash';

function calculatePercentage(objectsArray) {
	const newObjectsArray = _.cloneDeep(objectsArray);
	const totalValue = newObjectsArray.reduce((sum, obj) => sum + obj.value, 0);
	newObjectsArray.forEach((obj) => {
		obj.percentage = (obj.value / totalValue) * 100;
		// obj.percentage = Math.round((obj.value / totalValue) * 10000) / 100;
		obj.value = obj.percentage.toFixed(2);
		delete obj.percentage;
	});

	return newObjectsArray;
}

export { calculatePercentage };
