import styled from 'styled-components';
// import btnBg from '@/images/singleChoice/btnBg.png';
// import triangle from '@/images/singleChoice/triangle.png';

export const FiveviewsUI = styled.div`
	position: absolute;
	z-index: 2;
	font-size: 24px;
	font-weight: 400;
	color: #ffffff;
	text-align: center;
	/* top: 60px;
  left: 124.75rem; */
	top: 100px;
	right: 300px;
	display: flex;
	.show-option {
		margin-right: 64px;
	}
	.show-county-close {
		width: 112px;
		line-height: 33px;
		background-size: 100% 100%;
		margin-top: 10px;
		cursor: pointer;

		.show-county-arrow {
			position: relative;
			width: 100%;
			height: 40px;
			line-height: 40px;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(0, 38, 64, 0.9);
			border: 1px solid rgb(101, 183, 223, 0.6);
			background-image: linear-gradient(
					to right,
					rgba(14, 47, 66, 0.7),
					rgba(14, 47, 66, 0.7)
				),
				linear-gradient(180deg, rgba(11, 185, 246, 0), rgba(11, 185, 246, 1));

			.show-county-text {
				/* width: 85px; */
				padding-right: 4px;
				text-align: center;
				/* display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1; */
				overflow: hidden;
				color: #ffffff;
				font-size: 24px;
				display: inline-block;
			}

			.arrow {
				display: inline-block;
				/* position: absolute;
        top: 50%;
        right: 6px;
        transform: translateY(-50%); */
			}
		}
	}

	.show-county-none {
		/* max-height: 200px; */
		/* overflow-y: auto; */
		margin: 0 auto;
		width: 112px;
		margin-top: -1px;
		border: 1px solid transparent;
		background-clip: padding-box, border-box;
		background-origin: padding-box, border-box;
		background-image: linear-gradient(
				to right,
				rgba(14, 47, 66, 0.7),
				rgba(14, 47, 66, 0.7)
			),
			linear-gradient(180deg, rgba(11, 185, 246, 0), rgba(11, 185, 246, 1));

		.active-option {
			/* background-color: #2e79a8 !important; */
		}
		::-webkit-scrollbar {
			/*滚动条整体样式*/
			width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
			height: 1px;
		}
		::-webkit-scrollbar-thumb {
			/*滚动条里面小方块*/
			border-radius: 10px;
			box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
			background: #3e7998;
		}
		::-webkit-scrollbar-track {
			/*滚动条里面轨道*/
			box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
			border-radius: 10px;
			background: #182d4b;
		}
		.show-county-style {
			min-height: 43px;
			line-height: 43px;
			/* border: 0.5px solid #308ec2;
      background-color: #293f5c; */
			border-bottom-style: none;
			cursor: pointer;
			font-size: 21px;
			&:last-child {
				/* border: 0.5px solid #308ec2; */
			}
			&:hover {
				background: rgba(0, 38, 64, 0.9);
			}
		}
	}
`;
