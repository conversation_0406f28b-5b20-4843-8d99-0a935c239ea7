import { useState, useEffect } from 'react';
import { ActivityLevelContent } from './style';
import Box from '../../baseUI/Box';
import TimeTypeRadio from '@renderer/components/OnlineTypeRadio';
import OnlineCountEcharts from './components/OnlineCountEcharts';
import { RealTimeCumulativeOnline } from '../RealTimeCumulativeOnline';
import { getActivityLevel } from '@renderer/api';
import moment from 'moment';
import { type } from 'os';
/**
 * 活动水平
 */
type dataProps = {
	vehicle_type: string;
	in_beijing: boolean;
	nums: number;
};
type props = {
	total_online: number;
	beijing_in: number;
	beijing_out: number;
	total_online_rate: number;
	beijing_in_rate: number;
	beijing_out_rate: number;
	data: Array<dataProps>;
};
type echartProps = {
	length: number;
	value: string;
	name: string;
	rate: number;
};
const ActivityLevel = () => {
	const [data, setData] = useState<props>();
	const [echartData, setEchartData] = useState<echartProps>();
	// const [accumulate, setAccumulate] = useState<any>([])
	const [onlineType, setOnlineType] = useState('realTime');
	const [typeOptions, setTypeOptions] = useState([
		{
			label: '实时在线',
			value: 'realTime',
		},
		{
			label: '累计上线',
			value: 'Accumulate',
		},
	]);
	useEffect(() => {
		// if (onlineType == 'Accumulate') {
		//   start_time = moment().format('YYYY-MM-DD 00:00:00')
		//   end_time = moment().format('YYYY-MM-DD 23:59:59')
		// }
		getActivityLevel().then((res) => {
			const result = res as any;
			setData(result);
			const data = (res as any).data.map((item) => {
				return {
					name: `${item.industry}`,
					num: item.nums,
					value: item.rate,
				};
			});
			setEchartData(data);
			// const data2 = [
			//   {
			//     name: '京内',
			//     value: result.accumulate_beijing_in
			//   },
			//   {
			//     name: '京外',
			//     value: result.accumulate_beijing_out
			//   }
			// ]
			// setAccumulate(data2)
		});
	}, [onlineType]);
	return (
		<ActivityLevelContent>
			<Box
				title="活动水平"
				width="100%"
				titlewidth="95%"
				// subTitle={
				//   <TimeTypeRadio
				//     type={'realTime'}
				//     typeOptions={typeOptions}
				//     onlineType={(e) => setOnlineType(e)}
				//   />
				// }
			>
				<div className="container">
					<RealTimeCumulativeOnline
						name={onlineType}
						onlineType={onlineType}
						data={data}
						echartData={echartData}
					/>
				</div>
			</Box>
		</ActivityLevelContent>
	);
};

export default ActivityLevel;
