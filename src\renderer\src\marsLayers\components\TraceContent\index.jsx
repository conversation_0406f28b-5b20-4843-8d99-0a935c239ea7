import { useState, useEffect, useContext, useRef } from 'react';
import { Radio } from 'antd';
import moment from 'moment';
import { TraceContentStyle } from './style';

function TraceContent({ setVehicleState }) {
	const [selectedButton, setSelectedButton] = useState('onLine');

	// 柱状图默认自定义切换
	const handleClickReference = (value) => {
		console.log('handleClickReference>>', value);
		setSelectedButton(value);
		setVehicleState(value);
		// if (value === 'offLine') {
		// setShowPop(true)
		// } else {
		// setShowPop(false)
		// }
	};

	return (
		<TraceContentStyle>
			<div className="selectAssembly">
				<p>车辆情况：</p>
				<Radio.Button
					value="default"
					onClick={() => handleClickReference('onLine')}
					style={{ color: selectedButton === 'onLine' ? '#3ADAFF' : '#e2e5e5' }}
				>
					在线
				</Radio.Button>
				<Radio.Button
					value="custom"
					onClick={() => handleClickReference('offLine')}
					style={{
						color: selectedButton === 'offLine' ? '#3ADAFF' : '#e2e5e5',
					}}
				>
					离线
				</Radio.Button>
			</div>
		</TraceContentStyle>
	);
}
export default TraceContent;
