import { useEffect, useState, useContext } from 'react';
import Echarts from '@renderer/components/echarts';
import { Space, Button, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import {
	getRegionalIndustryProportion,
	getRegionalAnalysis,
	getIndustryOnlineSituation,
	getStreetTownshipIndustriesProportion,
	getSTIStats,
} from '@renderer/api';
import {
	getIper,
	getIndustry,
	getOnlineSituationEcharts,
} from '../components/IndustryProportion';
import { mergeObjects } from '../components/PublicMethods';
import industryClassification from '../components/IndustryClassification';
import { PageContext } from '@renderer/context';
import {
	COUNTY_ID_NAME_MAP,
	unitList,
} from '@renderer/marsLayers/components/AreaStreetStyles';
import {
	formatNumber,
	calculatePercentage,
	tranNumber,
} from '@renderer/hooks/index';
import { AreaLeftSideStyled, LayoutContainer } from './styles';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const AreaLeftSide = (props: Props) => {
	const { currentSelectedId, streetShipData } = props;
	const { currentTopNavMenu, selectRegionDate, regionName, streetName } =
		useContext(PageContext);
	const [dataSum, setDataSum] = useState(0);
	const [optionsIP, setOptionsIP] = useState({}); //行业占比echarts
	const [optionsRA, setOptionsRA] = useState({}); //区域分析echarts
	const [optionsOS, setOptionsOS] = useState({}); //上线情况echarts
	const [currentIndustry, setCurrentIndustry] = useState('全部');
	const [title1, setTitle1] = useState(null);

	const items: MenuProps['items'] = Object.keys(industryClassification()).map(
		(item, idx) => {
			return {
				label: item,
				key: `${idx}`,
			};
		},
	);

	const handleMenuClick: MenuProps['onClick'] = (e) => {
		setCurrentIndustry(Object.keys(industryClassification())[e.key]);
	};

	const menuProps = {
		items,
		onClick: handleMenuClick,
	};

	function separateNumberAndChinese(str) {
		if (str === '0.00' || str === 0) {
			return { number: parseFloat(str), chinese: '' };
		}
		const matches = str?.match(/([\d.]+)(\D+)/);
		if (matches) {
			const number = parseFloat(matches[1]);
			const chinese = matches[2];
			return { number, chinese };
		} else {
			return { number: parseFloat(str), chinese: '' };
		}
	}

	const setUpOption2 = (data) => {
		const unit = unitList?.find((i) => i.name === currentTopNavMenu)?.unit;
		let sumCountsByAdm: any = [];
		let name;
		if (currentSelectedId === 'area') {
			sumCountsByAdm = data?.reduce((acc, curr) => {
				curr.data.forEach((count, index) => {
					acc[index] = (acc[index] || 0) + count;
				});
				return acc;
			}, []);
			name = data[0]?.county?.map((id) => COUNTY_ID_NAME_MAP[id]);
		} else {
			sumCountsByAdm = data?.reduce((acc, curr) => {
				curr.data.slice(0, 10).forEach((count, index) => {
					acc[index] = (acc[index] || 0) + count;
				});
				return acc;
			}, []);
			name = data[0]?.town_name;
		}
		const admCounts = sumCountsByAdm?.map((total, index) => {
			return {
				name: name[index],
				value: total >= 1 ? Number(total.toFixed(1)) : Number(total.toFixed(2)),
			};
		});
		const sortedAdmCounts = admCounts.sort((a, b) => a.value - b.value);
		const tradeAdm = sortedAdmCounts.map((item) => item.name);
		const tradeCount = sortedAdmCounts.map((item) => item.value);

		const option = getIndustry(
			{
				...data,
				name: tradeAdm,
				originalName: name,
				total: tradeCount,
				unit,
			},
			currentSelectedId,
		);
		setOptionsRA(option);
	};

	const getIndustryDataResult = (res) => {
		if (res?.data?.length > 0) {
			const carData = res?.data?.map((item) => {
				return {
					name: Object.keys(item)[0],
					value: formatNumber(Object.values(item)[0]),
				};
			});
			setOptionsIP(getIper(calculatePercentage(carData)));
			setDataSum(res?.sum);
		} else {
			setDataSum(0);
			setOptionsIP(getIper([]));
		}
	};

	// 区域-街乡镇行业占比
	const getIndustryData = (id, params) => {
		switch (id) {
			case 'area':
				getRegionalIndustryProportion(params)
					.then((res) => {
						getIndustryDataResult(res);
					})
					.catch((err) => {
						setDataSum(0);
						setOptionsIP(getIper([]));
					});
				break;
			case 'streetTownship':
				getStreetTownshipIndustriesProportion(params)
					.then((res) => {
						getIndustryDataResult(res);
					})
					.catch((err) => {
						setDataSum(0);
						setOptionsIP(getIper([]));
					});
				break;
			default:
				break;
		}
	};

	const getIndustryStatisticsResult = (res) => {
		if (res?.length > 0) {
			const carType = industryClassification();
			const arr: any = [];
			res?.map((item) => {
				Object.values(carType).forEach((item2, idx) => {
					if (item2.includes(item.name)) {
						arr.push({
							...item,
							name: Object.keys(carType)[idx],
						});
					}
				});
			});
			setUpOption2(mergeObjects(arr));
		} else {
			setUpOption2(mergeObjects([]));
		}
	};

	// 区域-街乡镇 行业数据统计
	const getIndustryStatistics = (id, params) => {
		switch (id) {
			case 'area':
				getRegionalAnalysis(params)
					.then((res) => {
						getIndustryStatisticsResult(res);
					})
					.catch((err) => {
						setUpOption2(mergeObjects([]));
					});
				break;
			case 'streetTownship':
				getSTIStats(params)
					.then((res) => {
						getIndustryStatisticsResult(res);
					})
					.catch((err) => {
						setUpOption2(mergeObjects([]));
					});
				break;
			default:
				break;
		}
	};

	const getOnlineSituationResult = (res) => {
		if (res?.data?.length > 0) {
			const adm = res?.data?.map((item) => {
				return item[1];
			});
			const nums = res?.data?.map((item) => {
				return item[0];
			});
			const onlineData = adm?.map((county) => {
				return res?.online?.find((item) => item.county == county)?.nums;
			});

			setOptionsOS(getOnlineSituationEcharts({ adm, nums, onlineData }));
		} else {
			setOptionsOS(getOnlineSituationEcharts({}));
		}
	};

	// 区域-街乡镇 上线情况
	const getOnlineSituation = (id, params) => {
		switch (id) {
			case 'area':
				getIndustryOnlineSituation(params)
					.then((res) => {
						getOnlineSituationResult(res);
					})
					.catch((err) => {
						setOptionsOS(getOnlineSituationEcharts({}));
					});
				break;
			case 'streetTownship':
				getIndustryOnlineSituation(params)
					.then((res) => {
						getOnlineSituationResult(res);
					})
					.catch((err) => {
						setOptionsOS(getOnlineSituationEcharts({}));
					});
				break;
			default:
				break;
		}
	};

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time || !end_time) return;
		const params: any = {
			start_time,
			end_time,
			time_type,
			topic: unitList.find((i) => i.name === currentTopNavMenu)?.id,
		};
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		getIndustryStatistics(currentSelectedId, params);
	}, [selectRegionDate, currentTopNavMenu, currentSelectedId, regionName]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time || !end_time) return;
		setTitle1(streetName ? streetName : regionName);
		const params: any = {
			start_time,
			end_time,
			time_type,
			topic: unitList.find((i) => i.name === currentTopNavMenu)?.id,
		};
		if (streetName) {
			params.town_id = streetShipData?.features.find(
				(i) => i.properties.name === streetName,
			).properties.region;
		}
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		// if ('county_id' in params && 'town_id' in params) return
		getIndustryData(currentSelectedId, params);
	}, [
		selectRegionDate,
		currentTopNavMenu,
		regionName,
		streetName,
		currentSelectedId,
	]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time || !end_time) return;
		const params: any = {
			start_time,
			end_time,
			time_type,
		};
		if (currentIndustry && currentIndustry !== '全部') {
			const idx = Object.keys(industryClassification()).findIndex(
				(i) => i === currentIndustry,
			);
			params.industry_name = Object.values(industryClassification())[idx].join(
				',',
			);
		}
		getOnlineSituation(currentSelectedId, params);
	}, [currentIndustry, selectRegionDate, currentSelectedId]);

	return (
		<AreaLeftSideStyled>
			<div className="Areacontent">
				<div className="slidingLayer">
					<span className="slidtext">{title1 ? title1 : '全市'}行业情况</span>
				</div>
				<LayoutContainer style={{ height: '20%' }}>
					<div className={'container'}>
						<div className="describe">
							<dl className="no2">
								<dt>总量</dt>
								<br />
								<dd>
									{separateNumberAndChinese(tranNumber(dataSum, 2)).number}
									<span style={{ fontSize: '36px', color: '#f5803e' }}>
										{separateNumberAndChinese(tranNumber(dataSum, 2)).chinese}
										&nbsp;
									</span>
									<span>
										{unitList?.find((i) => i.name === currentTopNavMenu)?.unit}
									</span>
								</dd>
							</dl>
						</div>

						<div className="echarts-container">
							<div className="echarts">
								<Echarts option={optionsIP}></Echarts>
							</div>
						</div>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">
						{currentSelectedId === 'area'
							? '区域行业分析'
							: `${regionName ? regionName : ''}街乡镇Top10`}
					</span>
				</div>
				<LayoutContainer style={{ height: '35%' }}>
					<div className="echarts-line">
						<Echarts
							option={optionsRA}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">行业上线情况</span>
					<Dropdown menu={menuProps} className="dropDown">
						<Button>
							<Space>
								{currentIndustry}
								<DownOutlined />
							</Space>
						</Button>
					</Dropdown>
				</div>
				<LayoutContainer style={{ height: '25%' }}>
					<div className="echarts-line">
						<Echarts
							option={optionsOS}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
			</div>
		</AreaLeftSideStyled>
	);
};

export default AreaLeftSide;
