import React, { useState, useEffect, useContext } from 'react';
import * as mars3d from 'mars3d';
import { handleRawData, isInPolygon } from '@renderer/utils';
import { MapContext, PageContext } from '@renderer/context';
import { getRealTimeOffline, getVehicleNumber } from '@renderer/api';
import VehicleIndustryNumber from '../components/VehicleIndustryNumber';
import { INDUSTRY_COLOR } from '@renderer/constants/color';

let Cesium = mars3d.Cesium,
	graphicLayerOffLinePoint,
	realTimeData = [],
	selectModeChild;

const polygon = [
	[73.500226, 18.1608963],
	[135.0858829, 18.1608963],
	[135.0858829, 53.55791967],
	[73.500226, 53.55791967],
	[73.500226, 18.1608963],
];

const OffLine = (props) => {
	const { map, visible, layerId } = props;
	const [pieData, setPieData] = useState([]);
	const { setCurrentInfo, setBickDate, selectRegion } = useContext(MapContext);
	const {
		selectMode,
		setOpenTrajectory,
		openTrajectory,
		RealTimeOfflineDataDB,
		countyId,
	} = useContext(PageContext);
	const [vinsData, setVinsData] = useState([]);
	const vinsHandleRawData = (data) => {
		const _VINList = data
			.filter((item) => {
				const { lon, lat } = item;
				return isInPolygon([lon, lat], polygon);
			})
			.filter((item) =>
				selectModeChild?.some((item2) => {
					return item2.label == item.type;
				}),
			)
			.map((item) => `${item.VIN}|${item.type}`);
		const _vins = new Set(_VINList);
		const _data = handleRawData(_vins, data);
		setVinsData(_data);
	};
	const createPoints = () => {
		if (!graphicLayerOffLinePoint) return;
		graphicLayerOffLinePoint.clear();
		graphicLayerOffLinePoint.enabledEvent = false; // 关闭事件，大数据addGraphic时影响加载时间
		for (let j = 0; j < vinsData.length; ++j) {
			const obj = vinsData[j];
			const gcj2wgs = new mars3d.PointTrans.wgs2gcj([obj.lon, obj.lat]);
			const position = new mars3d.LngLatPoint(gcj2wgs[0], gcj2wgs[1]);

			const graphicPoint = new mars3d.graphic.PointPrimitive({
				position,
				style: {
					color: 'transparent',
					pixelSize: 6,
					outlineColor: INDUSTRY_COLOR[obj.type],
					outlineWidth: 2,
					distanceDisplayCondition: true,
					distanceDisplayCondition_far: Number.MAX_VALUE,
				},
				attr: obj,
			});
			graphicLayerOffLinePoint.addGraphic(graphicPoint);
		}
		graphicLayerOffLinePoint.enabledEvent = true; // 恢复事件
		// bindPopup(graphicLayerOffLinePoint)
	};
	// const bindPopup = (graphic) => {
	//   graphic.bindPopup(
	//     function (event) {
	//       const attr = {}
	//       attr['VIN'] = event.graphic.attr.VIN
	//       attr['类型'] = event.graphic.attr.type
	//       attr['时间'] = event.graphic.attr.TIME
	//       attr['速度'] = event.graphic.attr.speed
	//       attr['NOx'] = event.graphic.attr.noxKg

	//       return mars3d.Util.getTemplateHtml({ title: '', template: 'all', attr })
	//     }
	//     // { timeRender: true, closeButton: false }
	//   )
	// }
	const getRealTimeOfflineData = () => {
		let nums = 190009;
		let times = 2;
		let jsonData = sessionStorage.getItem('zcJsonData');
		if (jsonData) jsonData = JSON.parse(jsonData);
		nums = jsonData?.carNumber || nums;
		times = jsonData?.offlineTime || times;
		getRealTimeOffline({
			nums,
			times,
		})
			.then((res) => {
				console.log('getRealTimeOfflineData????', res);
				if (res?.length) {
					realTimeData = res;
					vinsHandleRawData(realTimeData);
				}
			})
			.catch((error) => {
				console.log('error', error);
				vinsHandleRawData([]);
			});
	};

	useEffect(() => {
		createPoints();
	}, [vinsData]);

	useEffect(() => {
		selectModeChild = selectMode.child;
		vinsHandleRawData(realTimeData);
	}, [selectMode.key]);

	// useEffect(() => {
	// 	if (!visible) return;
	// 	let nums = 190009;
	// 	let times = 2;
	// 	let jsonData = sessionStorage.getItem('zcJsonData');
	// 	if (jsonData) jsonData = JSON.parse(jsonData);
	// 	nums = jsonData?.carNumber || nums;
	// 	times = jsonData?.offlineTime || times;
	// 	getVehicleNumber({
	// 		nums,
	// 		times,
	// 		target: 2,
	// 		county_name: COUNTY_ID_NAME_MAP[countyId],
	// 	})
	// 		.then((res) => {
	// 			if (res.length) {
	// 				const data = res.map((item) => {
	// 					return {
	// 						value: item.value,
	// 						name: item.type,
	// 						color: getCarColor(item.type),
	// 					};
	// 				});
	// 				setPieData(data);
	// 			}
	// 		})
	// 		.catch((error) => {
	// 			console.log('error', error);
	// 		});
	// }, [countyId]);

	useEffect(() => {
		if (!visible) return;
		RealTimeOfflineDataDB.getItem('RealTimeOfflineData')
			.then((value) => {
				if (!value) {
					getRealTimeOfflineData();
				} else {
					if (value?.length) {
						realTimeData = value;
						vinsHandleRawData(realTimeData);
					}
				}
			})
			.catch(() => {
				getRealTimeOfflineData();
			});

		graphicLayerOffLinePoint = new mars3d.layer.GraphicLayer({
			cluster: {
				enabled: true,
			},
		});
		map.addLayer(graphicLayerOffLinePoint);

		graphicLayerOffLinePoint.on(mars3d.EventType.dblClick, function (event) {
			console.log('监听layer， 双击了矢量对象', event);
			const cartesianPosition = event.graphic.position;
			const cartographic = Cesium.Cartographic.fromCartesian(cartesianPosition);
			setCurrentInfo({
				VIN: event.graphic.attr.VIN,
				type: event.graphic.id.split('|')[1],
				lon: Cesium.Math.toDegrees(cartographic.longitude),
				lat: Cesium.Math.toDegrees(cartographic.latitude),
			});
			setOpenTrajectory(true);
			setBickDate(true);
		});

		// const ContextMenu = [
		//   {
		//     text: '查看车辆信息',
		//     callback: (e) => {
		//       const graphic = e.graphic
		//       if (graphic?.position) {
		//         setOpenTrajectory(true)
		//         setBickDate(true)
		//         const cartesianPosition = graphic.position
		//         const cartographic = Cesium.Cartographic.fromCartesian(cartesianPosition)
		//         setCurrentInfo({
		//           VIN: graphic.attr.vin,
		//           type: graphic.id.split('|')[1],
		//           lon: Cesium.Math.toDegrees(cartographic.longitude),
		//           lat: Cesium.Math.toDegrees(cartographic.latitude),
		//           layerId
		//         })
		//       }
		//     }
		//   }
		// ]
		// graphicLayerOffLinePoint.bindContextMenu(ContextMenu)

		return () => {
			graphicLayerOffLinePoint.clearDrawing();
			graphicLayerOffLinePoint.clear(true);
			graphicLayerOffLinePoint.enabledEvent = false;
			if (graphicLayerOffLinePoint) map.removeLayer(graphicLayerOffLinePoint);
			realTimeData = [];
		};
	}, [visible]);

	return (
		<>
			{!openTrajectory && pieData.length > 0 ? (
				<VehicleIndustryNumber data={pieData} title={'离线车辆'} />
			) : null}
		</>
	);
};

export default OffLine;
