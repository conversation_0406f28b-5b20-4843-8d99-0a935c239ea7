import { useState, useEffect, useContext } from 'react';
import { Cartesian3, SceneMode } from 'mars3d-cesium';
import * as mars3d from 'mars3d';
import _ from 'lodash';
import '@animxyz/core';
import { XyzTransitionGroup } from '@animxyz/react';
import { MapContext } from '../../../context';
import {
	center2D,
	height2D,
	center3D,
	height3D,
	orientation,
} from './../../index';
import RightNavStyle from './style';

const list = [
	{ id: SceneMode.SCENE2D, name: '2D' },
	{ id: SceneMode.SCENE3D, name: '3D' },
];
let info = {};
export default function RightNav({ map, changeMode, showFlash }) {
	const { active, setActive, sceneMode, setSceneMode } = useContext(MapContext);
	const [isShow, setIsShow] = useState(!showFlash);
	const [toggled, setToggled] = useState(false);
	let timeout;
	useEffect(() => {
		if (!map) return;
		// 获取现在的中心点坐标
		const camera = map.scene.camera;
		// const center = camera.positionCartographic
		// // 获取现在的高度
		// const height = center.height
		// // 获取现在的方向
		// const heading = camera.heading
		// // 获取现在的俯仰角
		// const pitch = camera.pitch
		// // 获取现在的视角角度
		// const roll = camera.roll
		// // 获取现在的中心坐标
		// const centerPoint = Cesium.Cartesian3.fromDegrees(
		//   Cesium.Math.toDegrees(center.longitude),
		//   Cesium.Math.toDegrees(center.latitude),
		//   height
		// )

		info = {
			camera,
		};

		switch (sceneMode) {
			case mars3d.Cesium.SceneMode.SCENE2D:
				map.scene.morphTo2D(1);
				break;
			case mars3d.Cesium.SceneMode.SCENE3D:
				map.scene.morphTo3D(1);
				break;
			default:
				break;
		}
	}, [sceneMode]);

	useEffect(() => {
		if (!map) return;
		map.scene.morphComplete.addEventListener(() => {
			if (map.scene.mode === Cesium.SceneMode.SCENE2D) {
				console.log('2d');
			} else {
				console.log('3d');
			}
			changeMode(map.scene.mode);
		});
	}, [map]);

	useEffect(() => {
		setIsShow(!showFlash);
		setToggled(false);
		if (!showFlash) {
			setTimeout(() => {
				setIsShow(false);
				setToggled(true);
			}, 5000);
		}
	}, []);

	// 修改中心点
	const getCenter = (val) => {
		const { camera } = info;
		if (val === mars3d.Cesium.SceneMode.SCENE3D) {
			camera.setView({
				destination: Cartesian3.fromDegrees(...center3D.value, height3D),
				orientation,
				duration: 2,
			});
		} else {
			camera.setView({
				destination: Cartesian3.fromDegrees(...center2D.value, height2D),
				duration: 2,
			});
		}
	};

	const handleMouseMove = () => {
		if (timeout) {
			clearTimeout(timeout);
		}
		setIsShow(true);
		setToggled(false);
	};

	const handleMouseLeave = () => {
		if (timeout) {
			clearTimeout(timeout);
		}
		timeout = setTimeout(() => {
			setIsShow(false);
			setToggled(true);
		}, 3000);
	};

	const handleClick = () => {
		if (timeout) {
			clearTimeout(timeout);
		}
		setIsShow(!isShow);
		setToggled(!toggled);
	};

	return (
		<RightNavStyle onMouseLeave={handleMouseLeave}>
			<XyzTransitionGroup
				className="item-group"
				xyz="fade down-100% back-5"
				onMouseMove={handleMouseMove}
			>
				{isShow &&
					list.map((item) => (
						<div
							className={
								sceneMode === item.id
									? 'right-nav-item active'
									: 'right-nav-item'
							}
							key={item.id}
							onClick={() => {
								setSceneMode(item.id);
							}}
						>
							{item.name}
						</div>
					))}
			</XyzTransitionGroup>
			<div className="right-nav-setting">
				<div
					className={
						toggled ? 'right-nav-spin rev-rotated' : 'right-nav-spin rotated'
					}
					onClick={handleClick}
				></div>
			</div>
		</RightNavStyle>
	);
}
