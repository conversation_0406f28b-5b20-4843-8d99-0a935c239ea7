.flash {
	/* margin: 0 auto; */
	width: 106%;
	height: 100%;
	z-index: 2;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

/* 定义滚动条的宽度和颜色 */
.ant-picker-time-panel-column::-webkit-scrollbar {
	width: 2px;
	background-color: none;
}

/* 定义滚动条滑块的样式 */
.ant-picker-time-panel-column::-webkit-scrollbar-thumb {
	background-color: rgba(211, 212, 213, 0.8);
	border-radius: 5px;
}

/* 定义滚动条轨道的样式 */
.ant-picker-time-panel-column::-webkit-scrollbar-track {
	background-color: none;
}

/* 定义滚动条轨道上的按钮样式 */
.ant-picker-time-panel-column::-webkit-scrollbar-button {
	display: none;
}

.custom-tooltip-bk-box {
	box-shadow: none !important;
	border: none;
	border-width: 0 !important;
}

.custom-tooltip-box {
	background: url('../images/icon/dataBg.png') no-repeat;
	background-size: 100% 100%;
	height: auto;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 18px 25px !important;
}

:where(.css-dev-only-do-not-override-mu9r37).ant-message
	.ant-message-notice
	.ant-message-notice-content {
	background-color: transparent;
	padding: 5px 5px;
	font-size: 18px;
}
:where(.css-dev-only-do-not-override-mu9r37)[class^='ant-message']
	[class^='ant-message'],
:where(.css-dev-only-do-not-override-mu9r37)[class*=' ant-message']
	[class^='ant-message'],
:where(.css-dev-only-do-not-override-mu9r37)[class^='ant-message']
	[class*=' ant-message'],
:where(.css-dev-only-do-not-override-mu9r37)[class*=' ant-message']
	[class*=' ant-message'] {
	display: flex;
	justify-content: center;
	align-items: center;
}
.mars3d-divGraphic {
	z-index: 1 !important;
}
/* .mars3d-divlayer{
  position: absolute;
  right: 200px;
  bottom: 30px;
  z-index: 9999;
}
.mars3d-divGraphic {
  position: static;
}
.mars3d-popup {
  width: 250px;
  height: 300px;
  background-color: aliceblue;
}
.mars3d-popup-tip-container{
  display: none;
}
.mars3d-popup-close-button {
  display: none;
}
.mars3d-popup-content-wrapper {
  height: 100%;
}
.mars3d-popup-content {
  max-height: 300px;
  font-size: 20px;
  line-height: 30px;
} */
