import styled from 'styled-components';

const LayoutContainer = styled.div`
	// display: flex;
	.tabs {
		margin-top: 10px;
		width: 100%;
		height: 30px;
		display: flex;
		cursor: pointer;
		align-items: center;
		.tabs-item {
			margin-left: 12px;
			width: 109px;
			height: 30px;
			line-height: 30px;
			transform: skewX(-25deg);
			background: rgba(232, 232, 232, 0.3);
			div {
				text-align: center;
				color: #ffffff;
				font-size: 20px;
				transform: skewX(25deg);
			}
			&.active {
				background: linear-gradient(90deg, #3ebdcb 0%, #174869 100%);
			}
		}
	}
	.down-container {
		display: flex;
		flex-direction: row;
	}
	.echarts-container {
		width: 35%;
		margin-top: 10px;
		display: flex;
		position: relative;
		.echarts {
			width: 200px;
			height: 190px;
		}
		.legend {
			position: absolute;
			right: 12%;
			.legend-item {
				display: flex;
				flex-direction: row;
				// margin-bottom: 10px;
				.circle {
					border-radius: 14px;
					display: block;
					width: 12px;
					height: 12px;
					margin-right: 5px;
				}
				.name {
					font-size: 14px;
					font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #e2f0ff;
				}
			}
		}
	}
	.line-eachart {
		width: 60%;
		height: 190px;
	}
`;
export default LayoutContainer;
