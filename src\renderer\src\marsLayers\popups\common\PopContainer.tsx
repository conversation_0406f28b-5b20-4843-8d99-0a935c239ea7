/**
 *  弹窗的公共内容样式
 */
import React from 'react';
import styled from 'styled-components';

import popupBg from '@renderer/images/layer/popupBg.png';
import PopLabel from './PopLabel';

export const PopStyle = styled.div<Style>`
	height: 645px;
	width: 1150px;
	color: #fff;
	font-size: 28px;
	background-image: url(${popupBg});
	background-position: 0 0;
	background-repeat: no-repeat;
	background-size: cover;
	/* background: rgba(0, 18, 46, 0.7);
  box-shadow: rgba(62, 94, 254, 0.93) 0px 0px 0.25rem 0.0625rem, rgba(5, 17, 58, 0.58) 0px 0px 0.5rem 0.5625rem inset;
  border-radius: 0.5rem;
  border: 0.0625rem solid rgba(54, 99, 250, 0.7); */
	position: absolute;
	top: 0;
	left: 0;
	margin: auto;
	right: 0;
	transform: translate(-50%, -50%);
	.title {
		height: 100px;
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 10px 5px 0px 30px;
		margin-bottom: 10px;
		box-sizing: border-box;
		background-size: 100% 100%;
		background-repeat: no-repeat;
		font-size: 30px;
		font-family: PingFang SC;
		font-weight: 400;
		color: #22f3e2;
		align-items: center;
	}
	span.text {
		display: inline-block;
		padding: 10px 15px;
		font-size: 28px;
		font-family: PingFangSC-Semibold, PingFang SC;
		font-weight: 600;
		z-index: 0;
		color: #4efad7;
		width: 60%;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}
	.back {
		cursor: pointer;
		color: #56d1ed;
		background: none;
		border: none;
		font-size: 40px;
	}
	.main {
		padding: 20px 34px 0;
		flex: 1;
		width: 100%;
		color: rgba(226, 240, 255, 0.7);
	}
`;

type Style = {
	size?: 'normal' | 'small';
};

type Props = {
	text: string;
	aloneRow?: {
		label: string;
		value: string;
		color?: string;
		tip?: boolean;
		filter?: boolean;
	}[];
	multiline?: {
		label: string;
		value: string;
		color?: string;
		tip?: boolean;
		filter?: boolean;
	}[];
	data?: any[];
	children?: any;
} & Style;

const PopContainer = (props: Props) => {
	const { text } = props;
	return (
		<PopStyle size={props?.size}>
			<div className="title">
				<span className="text" title={text}>
					{text ? text : '--'}
				</span>
			</div>
			<PopLabel
				multiline={props?.multiline}
				aloneRow={props?.aloneRow}
				data={props?.data}
			/>
			<div className="main">{props.children}</div>
		</PopStyle>
	);
};
export default PopContainer;
