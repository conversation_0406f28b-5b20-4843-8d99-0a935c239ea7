import { useEffect, useState, useContext } from 'react';
import Echarts from '@renderer/components/echarts';
import { Space, Button, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { DownOutlined } from '@ant-design/icons';

import {
	getEnterpriseOnlineTreemap,
	getEnterpriseComposition,
} from '@renderer/api';
import { PageContext } from '@renderer/context';
import { UserEnterpriseLeftSideStyled, LayoutContainer } from './styles';
import PieCharts from '../components/PieCharts';
import WidthTitle from '@renderer/components/ScrollListWithTitle';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const UserEnterpriseLeftSide = (props: Props) => {
	const { currentSelectedId, streetShipData } = props;
	const {
		currentTopNavMenu,
		selectRegionDate,
		regionName,
		streetName,
		affiliationId,
	} = useContext(PageContext);
	const [dataSum, setDataSum] = useState(0);
	const [optionsIP, setOptionsIP] = useState({}); //在线车辆饼图占比echarts
	const [optionsRA, setOptionsRA] = useState(); //区域分析echarts
	const [optionsIT, setOptionsIT] = useState(); //上线情况echarts
	const [optionsEL, setOptionsEL] = useState(); //上线情况echarts
	const [optionsVL, setOptionsVL] = useState(); //上线情况echarts

	// 用户企业分布概况
	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time || !end_time) return;
		const params: any = {
			start_time,
			end_time,
		};
		function getLevelOption() {
			return [
				{
					itemStyle: {
						borderWidth: 0,
						gapWidth: 5,
					},
				},
				{
					itemStyle: {
						gapWidth: 1,
					},
				},
				{
					colorSaturation: [0.25, 0.5],
					itemStyle: {
						gapWidth: 1,
						borderColorSaturation: 0.6,
					},
				},
			];
		}
		getEnterpriseOnlineTreemap(params).then((res) => {
			const option = {
				tooltip: {
					formatter: function (info) {
						var value = info.value;
						var treePathInfo = info.treePathInfo;
						var treePath = [];
						for (var i = 1; i < treePathInfo.length; i++) {
							treePath.push(treePathInfo[i].name);
						}
						return [
							'<div class="tooltip-title">' + treePath.join('-') + '</div>',
							'在线数量: ' + value + ' 辆',
						].join('');
					},
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '5%',
					top: '10%',
				},
				series: [
					{
						width: '100%',
						height: '100%',
						name: '用户企业分布概况',
						type: 'treemap',
						visibleMin: 10,
						label: {
							show: true,
							formatter: '{b}-{c}辆',
						},
						itemStyle: {
							borderColor: '#fff',
						},
						levels: getLevelOption(),
						data: res,
						breadcrumb: {
							show: false,
						},
					},
				],
			};
			setOptionsIP(option);
		});
	}, [selectRegionDate]);

	// 用户企业车辆构成概况
	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time) return;
		const params: any = {
			start_time,
			end_time,
			affiliation: affiliationId,
		};
		getEnterpriseComposition(params).then((res) => {
			setOptionsRA(res.brand);
			setOptionsIT(res.industry_type);
			setOptionsEL(res.emission_level);
			setOptionsVL(res.vehicle_level);
			setDataSum(res.sums);
		});
	}, [selectRegionDate, affiliationId]);

	return (
		<UserEnterpriseLeftSideStyled>
			<div className="Industrycontent">
				<div className="slidingLayer">
					<span className="slidtext">用户企业分布情况</span>
				</div>
				<LayoutContainer style={{ height: '40%' }}>
					<div className={'container'}>
						<div className="echarts-container">
							<div className="echarts">
								<Echarts option={optionsIP}></Echarts>
							</div>
						</div>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">用户企业车辆构成情况</span>
				</div>
				<span className="slidtext">联网车辆总数：{dataSum} 辆</span>
				<LayoutContainer style={{ height: '45%' }}>
					<div className="echarts-mid-half">
						<PieCharts
							data={optionsIT}
							style={{ height: '45%', flex: 1 }}
							title="行业构成"
						/>
						<PieCharts
							data={optionsEL}
							style={{ height: '45%', flex: 1 }}
							title="国标构成"
						/>
					</div>
					<div className="echarts-mid-half">
						<PieCharts
							data={optionsVL}
							style={{ height: '45%', flex: 1 }}
							title="轻重构成"
						/>
						<PieCharts
							data={optionsRA}
							style={{ height: '45%', flex: 1 }}
							title="厂商与型号构成"
						/>
					</div>
				</LayoutContainer>
			</div>
		</UserEnterpriseLeftSideStyled>
	);
};

export default UserEnterpriseLeftSide;
