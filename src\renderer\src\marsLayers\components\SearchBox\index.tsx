import React, { useEffect, useContext, useState, useRef } from 'react';
import * as Cesium from 'mars3d-cesium';
// import { getViolationRegisterTownList, getViolationTownList } from '@renderer/api'
import { MapContext } from '../../../context';
import { Button, Select, Table } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { SearchBoxStyled, SearchDetailsListStyled } from './style';

/**
 * 更多
 */

type Props = {
	showFlash: any;
	displaySearchList: boolean;
	setDisplaySearchList: React.Dispatch<React.SetStateAction<boolean>>;
	map: any;
	vehicleSearchData: any;
};

const SearchBox = (props: Props) => {
	const {
		showFlash,
		displaySearchList,
		setDisplaySearchList,
		vehicleSearchData,
		map,
	} = props;
	const { sceneMode, setCurrentInfo } = useContext(MapContext);
	const resultData = [
		{ vin: '', date: '', industry: '', vehicleModel: '', registrationArea: '' },
		{ vin: '', date: '', industry: '', vehicleModel: '', registrationArea: '' },
		{ vin: '', date: '', industry: '', vehicleModel: '', registrationArea: '' },
		{ vin: '', date: '', industry: '', vehicleModel: '', registrationArea: '' },
		{ vin: '', date: '', industry: '', vehicleModel: '', registrationArea: '' },
		{ vin: '', date: '', industry: '', vehicleModel: '', registrationArea: '' },
		{ vin: '', date: '', industry: '', vehicleModel: '', registrationArea: '' },
		{ vin: '', date: '', industry: '', vehicleModel: '', registrationArea: '' },
		{ vin: '', date: '', industry: '', vehicleModel: '', registrationArea: '' },
	];
	const [dataCreate, setDateCreate] = useState(resultData);
	const [data, setData] = useState({
		date: '2023',
		industryData: 'h',
		vehicleModelData: 'large',
		registrationAreaData: 'f',
	});
	const [isContentExpanded, setIsContentExpanded] = useState(true);
	const [activeItem, setActiveItem] = useState<string>('');

	const contentStyle = {
		maxHeight: isContentExpanded ? '825px' : '0',
		opacity: isContentExpanded ? 1 : 0,
		transition: 'max-height 0.3s ease, opacity 0.3s ease',
	};

	const dateData = [
		{ value: '2023', label: '2023' },
		{ value: '2022', label: '2022' },
		{ value: '2021', label: '2021' },
		{ value: '2020', label: '2020' },
	];
	const industryData = [
		{ value: 'k', label: '客车' },
		{ value: 'z', label: '渣土车' },
		{ value: 'h', label: '货车' },
		{ value: 'x', label: '校车' },
		{ value: 'hun', label: '混泥土车' },
		{ value: 'l', label: '垃圾车' },
		{ value: 'g', label: '公交车' },
		{ value: 'j', label: '加油车' },
	];
	const vehicleModelData = [
		{ value: 'small', label: '小型' },
		{ value: 'middle', label: '中型' },
		{ value: 'large', label: '大型' },
	];
	const registrationAreaData = [
		{ value: 'f', label: '丰台区' },
		{ value: 'z', label: '朝阳区' },
		{ value: 'h', label: '怀柔区' },
		{ value: 'c', label: '昌平区' },
		{ value: 'x', label: '西城区' },
		{ value: 'd', label: '东城区' },
		{ value: 'fang', label: '房山区' },
		{ value: 's', label: '石景山区' },
		{ value: 'm', label: '门头沟区' },
		{ value: 'hai', label: '海淀区' },
		{ value: 'shun', label: '顺义区' },
		{ value: 't', label: '通州区' },
		{ value: 'da', label: '大兴区' },
		{ value: 'p', label: '平谷区' },
		{ value: 'mi', label: '密云区' },
		{ value: 'y', label: '延庆区' },
	];

	const orientation = {
		heading: Cesium.Math.toRadians(0),
		pitch: Cesium.Math.toRadians(-35),
		roll: 0.0,
	};

	const queryData = () => {
		if (vehicleSearchData?.length) {
			setDateCreate(
				vehicleSearchData
					?.map((item) => item.VIN)
					?.map((item) => {
						return {
							vin: item,
							date: dateData.filter(
								(i) => i.value === Object.values(data)[0],
							)[0].label,
							industry: industryData.filter(
								(i) => i.value === Object.values(data)[1],
							)[0].label,
							vehicleModel: vehicleModelData.filter(
								(i) => i.value === Object.values(data)[2],
							)[0].label,
							registrationArea: registrationAreaData.filter(
								(i) => i.value === Object.values(data)[3],
							)[0].label,
						};
					}),
			);
		}
	};

	const close = () => {
		setDisplaySearchList(false);
	};

	const dataOnChange = (e, type) => {
		switch (type) {
			case '1':
				setData({
					...data,
					date: e,
				});
				break;
			case '2':
				setData({
					...data,
					industryData: e,
				});
				break;
			case '3':
				setData({
					...data,
					vehicleModelData: e,
				});
				break;
			case '4':
				setData({
					...data,
					registrationAreaData: e,
				});
				break;
			default:
				break;
		}
	};

	const getCurrentPosition = (e) => {
		if (e) {
			const o = vehicleSearchData?.find((obj) => obj.VIN === e);
			if (o?.LAT && o?.LON) {
				map.camera.flyTo({
					destination:
						sceneMode === 3
							? Cesium.Cartesian3.fromDegrees(Number(o.LON), Number(o.LAT), 800)
							: Cesium.Cartesian3.fromDegrees(
									Number(o.LON),
									Number(o.LAT),
									800,
							  ),
					orientation: sceneMode === 3 ? orientation : {},
					duration: 4,
					complete: () => {
						setCurrentInfo(o);
					},
				});
			}
		}
		setActiveItem(e);
	};

	return (
		<>
			{!showFlash && (
				<>
					<SearchBoxStyled>
						{displaySearchList ? (
							<SearchDetailsListStyled style={contentStyle}>
								<div className="middle-container">
									<div className="Violation"></div>
									<div className="close">
										<CloseOutlined rev={undefined} onClick={close} />
									</div>
									<div className="table">
										{dataCreate?.length && (
											<div className="sewage-treatment-content">
												<div className="sewage-treatment-settings">
													<div className="settings-option-size">
														<span className="settings-label">年份：</span>
														<Select
															className="settings-select"
															popupClassName="settings-select-popup"
															defaultValue={data.date}
															onChange={(e) => dataOnChange(e, '1')}
															style={{ width: '120px' }}
															options={dateData}
														/>
													</div>

													<div className="settings-option-size">
														<span className="settings-label">行业：</span>
														<Select
															className="settings-select"
															popupClassName="settings-select-popup"
															defaultValue={data.industryData}
															onChange={(e) => dataOnChange(e, '2')}
															style={{ width: '120px' }}
															options={industryData}
														/>
													</div>
													<div className="settings-option-size">
														<span className="settings-label">车型：</span>
														<Select
															className="settings-select"
															popupClassName="settings-select-popup"
															defaultValue={data.vehicleModelData}
															onChange={(e) => dataOnChange(e, '3')}
															style={{ width: '120px' }}
															options={vehicleModelData}
														/>
													</div>
													<div className="settings-option-size">
														<span className="settings-label">注册区域：</span>
														<Select
															className="settings-select"
															popupClassName="settings-select-popup"
															defaultValue={data.registrationAreaData}
															onChange={(e) => dataOnChange(e, '4')}
															style={{ width: '120px' }}
															options={registrationAreaData}
														/>
													</div>
													<div className="settings-button">
														<Button
															className="settings-button-query"
															onClick={queryData}
														>
															查询
														</Button>
													</div>
												</div>
												<div className="sewage-treatment-table">
													<div className="table">
														{dataCreate?.length && (
															<dl>
																<dt>
																	<span className="no1">VIN</span>
																	<span className="no2">年份</span>
																	<span className="no3">行业</span>
																	<span className="no4">车型</span>
																	<span className="no5">注册区域</span>
																</dt>
																{dataCreate?.map((item, key) => {
																	return (
																		<dd
																			key={key}
																			onClick={() => {
																				getCurrentPosition(item?.vin);
																				setActiveItem(item?.vin);
																			}}
																			className={
																				activeItem === item?.vin ? 'active' : ''
																			}
																		>
																			<span className="no1">{item?.vin}</span>
																			<span className="no2">{item?.date}</span>
																			<span className="no3">
																				{item?.industry}
																			</span>
																			<span className="no4">
																				{item?.vehicleModel}
																			</span>
																			<span className="no5">
																				{item?.registrationArea}
																			</span>
																		</dd>
																	);
																})}
															</dl>
														)}
													</div>
												</div>
											</div>
										)}
									</div>
								</div>
							</SearchDetailsListStyled>
						) : null}
					</SearchBoxStyled>
				</>
			)}
		</>
	);
};

export default SearchBox;
