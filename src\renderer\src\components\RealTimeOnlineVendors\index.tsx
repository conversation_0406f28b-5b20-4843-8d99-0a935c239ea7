import React, { useState, useEffect } from 'react';
import LayoutContainer from './style';
import Box from '../../baseUI/Box';
import Echarts from '@renderer/components/echarts';
import moment from 'moment';
import { getOnlinevendors } from '@renderer/api';

const RealTimeOnlineVendors = () => {
	const [lineOptions, setLineOptions] = useState({});

	useEffect(() => {
		let json = {
			start_time: moment().subtract(9, 'months').format('YYYY-MM-DD 00:00:00'),
			end_time: moment().format('YYYY-MM-DD 23:59:59'),
		};
		getOnlinevendors(json).then((res) => {
			// console.log(res, 'res')
			const lineOption = {
				tooltip: {
					trigger: 'axis',
					backgroundColor: 'rgba(13, 64, 71, 0.50)',
					borderColor: 'rgba(143, 225, 252, 0.60)',
					padding: 8,
					textStyle: {
						color: '#fff',
					},
					formatter: (params) => {
						// console.log(params, 'params')
						const api = res.find((item) => item.level === params[0].name);
						const count = res.length;
						if (api) return `${count}家,车辆占比${api.rate}`;
						return params[0].name;
					},
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '5%',
					top: '10%',
					containLabel: true,
				},
				xAxis: {
					type: 'category',
					// data: ['0-1000', '1000-5000', '5000-15000', '15000-30000', '30000以上']
					data: res.map((item) => item.level),
					axisLabel: {
						color: '#E8F4FF',
					},
				},
				yAxis: {
					type: 'value',
					boundaryGap: false,
					axisTick: {
						show: false, // 不显示坐标轴刻度线
					},
					splitLine: {
						show: false,
					},
					axisLine: {
						show: false,
					},
					axisLabel: {
						color: '#E8F4FF',
					},
				},
				series: [
					{
						data: res.map((item) => item.count),
						// data: [
						//   { name: '5家，车辆占比25%', value: 120 },
						//   { name: '5家，车辆占比25%', value: 200 },
						//   { name: '5家，车辆占比25%', value: 150 },
						//   { name: '5家，车辆占比25%', value: 80 },
						//   { name: '5家，车辆占比25%', value: 70 }
						// ],
						type: 'bar',
						barWidth: 24,
					},
				],
			};

			setLineOptions(lineOption);
		});
	}, []);

	return (
		<Box title="实时在线厂商">
			<LayoutContainer>
				{lineOptions ? <Echarts option={lineOptions}></Echarts> : null}
			</LayoutContainer>
		</Box>
	);
};

export default RealTimeOnlineVendors;
