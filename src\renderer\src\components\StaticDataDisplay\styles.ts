import styled from 'styled-components';

const StaticDataDisplayStyled = styled.div`
	width: 100%;
	height: 100%;
	margin: 0 auto;
	margin-top: 10px;
	.staticDataDisplayNav {
		width: auto;
		z-index: 9;
		background-color: rgba(0, 65, 94) !important;
		color: #fff !important;
		font-size: 15px !important;
	}
	/* .ant-segmented-item:hover {
    color: #111 !important;
  } */
	.ant-segmented-item-selected {
		background-color: rgba(0, 116, 145) !important;
	}
	.ant-segmented-item {
		width: 100%;
	}
`;

const ReferenceStyled = styled.div`
	width: 100%;
	display: flex;
	.selectAssembly {
		width: 37%;
		margin-left: auto;
		padding-right: 10px;
		padding-top: 3px;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
		}
		.ant-radio-button-wrapper {
			width: 80px;
			border: 0;
			border-radius: 5px;
			font-size: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			span:hover {
				color: #3adaff;
			}
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
	}
`;

export { StaticDataDisplayStyled, ReferenceStyled };
