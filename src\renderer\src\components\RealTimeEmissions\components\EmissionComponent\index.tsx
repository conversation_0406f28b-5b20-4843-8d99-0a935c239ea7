import React, { useEffect, useState } from 'react';
// import DelayUI from '@renderer/baseUI/DelayUI'
import { useFont } from '@renderer/hooks';
import LayoutContainer from './style';

interface Props {
	data: Array<any>;
}

export const EmissionComponent: React.FC<Props> = (props) => {
	const { data } = props;

	return (
		<>
			<LayoutContainer>
				<div className={'left-container' + ' ' + useFont()}>
					<div className="describe">
						{/* <img src={icon}></img> */}
						<dl className="no2">
							<dt>NOx总排放</dt>
							<dd>
								{data[0]?.value !== '--'
									? Number(data[0]?.value).toFixed(1)
									: '--'}
								<span>吨</span>
							</dd>
						</dl>
						<div className="describe-bottom">
							<dl className="no1">
								<dt>排放因子</dt>
								<dd>
									{data[1]?.value !== '--'
										? Number(data[1]?.value).toFixed(2)
										: '--'}
									<span>(G/KM)</span>
								</dd>
							</dl>
							<dl className="no3">
								<dt>单车排放</dt>
								<dd>
									{data[2]?.value !== '--'
										? Number(data[2]?.value).toFixed(5)
										: '--'}
									<span>(G)</span>
								</dd>
							</dl>
						</div>
					</div>
				</div>
			</LayoutContainer>
		</>
	);
};
