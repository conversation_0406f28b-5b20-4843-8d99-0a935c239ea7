import React, { useEffect, useRef } from 'react';

import { Props } from './type';
import { ScrollListUI } from './style';

export default function ScrollList(props: Props) {
	const scrollBox = useRef<any>(null);
	const common1 = useRef<any>(null);
	const common2 = useRef<any>(null);
	const timer = useRef<any>(null);
	const { data, columns, autoscroll } = props;
	useEffect(() => {
		if (!autoscroll) return;
		clearInterval(timer.current);
		scrollBox.current.scrollTop = 0;
		roll(60);
		return () => {
			if (common2 && common2.current) {
				common2.current.innerHTML = '';
			}
		};
	}, [props.data]);

	const roll = (t) => {
		if (scrollBox.current.clientHeight > common1.current.clientHeight) {
			return;
		}
		common2.current.innerHTML = common1.current.innerHTML;
		timer.current = setInterval(rollStart, t);
		// 鼠标移入div时暂停滚动
		scrollBox.current.onmouseover = function () {
			clearInterval(timer.current);
		};
		// 鼠标移出div后继续滚动
		scrollBox.current.onmouseout = function () {
			clearInterval(timer.current);

			if (scrollBox.current.clientHeight < common1.current.clientHeight) {
				timer.current = setInterval(rollStart, t);
			}
		};
	};
	// 开始滚动函数
	const rollStart = () => {
		if (!scrollBox.current) return;
		if (scrollBox.current.scrollTop >= common1.current.offsetHeight) {
			scrollBox.current.scrollTop = 0;
		} else {
			scrollBox.current.scrollTop = scrollBox.current.scrollTop + 2;
		}
	};

	const OnSelect = (data) => {
		if (!props.OnRowSelection) return;
		else {
			props.OnRowSelection(data);
		}
	};

	return (
		<ScrollListUI ref={scrollBox} {...props}>
			<div ref={common1} className="list-content">
				{data !== null &&
					data !== undefined &&
					Array.isArray(data) &&
					data?.map((ele, index) => {
						return (
							<ul
								key={index}
								className={
									props.rowClassName
										? `list-ul ${props.rowClassName}`
										: 'list-ul'
								}
								onClick={() => OnSelect(ele)}
							>
								{columns.map((item) => {
									const { titleTips, dataIndex } = item;
									if (item.render) {
										return (
											<li
												key={dataIndex}
												title={titleTips ? ele[titleTips] : ele[dataIndex]}
												className="list-li"
											>
												<> {item.render(ele[dataIndex], ele)}</>
											</li>
										);
									} else
										return (
											<li
												key={dataIndex}
												title={titleTips ? ele[titleTips] : ele[dataIndex]}
												className="list-li"
											>
												{ele[dataIndex]}
											</li>
										);
								})}
							</ul>
						);
					})}
			</div>
			<div ref={common2} className="list-content1"></div>
		</ScrollListUI>
	);
}
