import styled, { css } from 'styled-components';

type Props = {
	title?: string;
	width?: string;
	marginLeft?: string;
	style?: typeof css;
};

export const LevelTwoTitleContent = styled.div<Props>`
	font-size: 18px;
	font-weight: 400;
	color: #ffffff;
	line-height: 26px;
	padding-left: 10px;
	border-bottom: 2px solid;
	border-image: linear-gradient(
			270deg,
			rgba(4, 37, 69, 0.44),
			rgba(45, 155, 227, 1)
		)
		1 1;
	width: ${(props) => props.width || '100%'};
	margin-left: ${(props) => props.marginLeft || '0px'};
	margin-top: 10px;
	.line {
		width: 95px;
		height: 2px;
		border: 1px solid #ff8910;
		filter: blur(0.5px);
		position: relative;
		left: -10px;
		top: 2px;
	}
`;
const LevelTwoTitle: React.FC<Props> = ({
	title,
	...rest
}): React.ReactElement => {
	return (
		<LevelTwoTitleContent {...rest}>
			{title}
			<div className="line"></div>
		</LevelTwoTitleContent>
	);
};

export default LevelTwoTitle;
