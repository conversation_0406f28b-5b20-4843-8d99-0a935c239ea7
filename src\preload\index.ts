import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { electronAPI } from '@electron-toolkit/preload';

// Custom APIs for renderer
const api = {};

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
	try {
		contextBridge.exposeInMainWorld('electron', electronAPI);
		contextBridge.exposeInMainWorld('api', api);
		contextBridge.exposeInMainWorld('extent', {
			setExtent: (data: object) => ipcRenderer.send('extent', data),
		});
		contextBridge.exposeInMainWorld('layer', {
			setLayer: (data: object) => ipcRenderer.send('layer', data),
		});
		contextBridge.exposeInMainWorld('modules', {
			setModules: (list: string) => ipcRenderer.send('modules', list),
		});
	} catch (error) {
		console.error(error);
	}
} else {
	// @ts-ignore (define in dts)
	window.electron = electronAPI;
	// @ts-ignore (define in dts)
	window.api = api;
}

window.addEventListener('DOMContentLoaded', () => {
	ipcRenderer.on('update-counter', (_event, value) => {
		return value;
	});
});
