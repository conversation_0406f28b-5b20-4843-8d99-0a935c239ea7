import { useEffect, useState, useContext } from 'react';
import { AreaRightSideStyled, LayoutContainer } from './styles';
import Echarts from '@renderer/components/echarts';
import { Space, Button, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import {
	getEnterpriseUsersOnlineSituation,
	getNetworkUserIntervalStatistics,
	getAffiliationInfoByID,
} from '@renderer/api';
import { PageContext } from '@renderer/context';
import {
	COUNTY_ID_NAME_MAP,
	unitList,
} from '@renderer/marsLayers/components/AreaStreetStyles';
import industryClassification from '../components/IndustryClassification';
import {
	AreaRightSideTop,
	AreaRightSideCenter,
	AreaRightSideBottom,
} from '../components/EnterpriseAnalysis';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const AreaRightSide = (props: Props) => {
	const { currentSelectedId, streetShipData } = props;
	const { selectRegionDate, regionName, streetName } = useContext(PageContext);
	//图表
	const [option1, setOption1] = useState({});
	const [option2, setOption2] = useState({});
	const [option3, setOption3] = useState({});
	const [currentIndustryTop, setCurrentIndustryTop] = useState('全部');
	const [currentIndustry, setCurrentIndustry] = useState('全部');
	const [title1, setTitle1] = useState(null);

	const items: MenuProps['items'] = Object.keys(industryClassification()).map(
		(item, idx) => {
			return {
				label: item,
				key: `${idx}`,
			};
		},
	);

	const handleMenuClick: MenuProps['onClick'] = (e) => {
		setCurrentIndustry(Object.keys(industryClassification())[e.key]);
	};

	const handleMenuClickTop: MenuProps['onClick'] = (e) => {
		setCurrentIndustryTop(Object.keys(industryClassification())[e.key]);
	};

	const menuProps = {
		items,
		onClick: handleMenuClick,
	};

	const menuPropsTop = {
		items,
		onClick: handleMenuClickTop,
	};

	const getDropDownMenu = (params) => {
		const { type } = params;
		return (
			<Dropdown menu={menuProps} className={`dropDown${type}`} key={type}>
				<Button>
					<Space>
						{currentIndustry}
						<DownOutlined />
					</Space>
				</Button>
			</Dropdown>
		);
	};

	// 区域 企业联网情况
	const getUserEnterpriseNetworking = (params) => {
		getNetworkUserIntervalStatistics(params)
			.then((res) => {
				if (Object.keys(res)?.length > 0) {
					const adm = Object.keys(res);
					const nums = Object.values(res)?.map((item) => item?.len);
					const onlineData = Object.values(res)?.map((item) => item?.sum);
					setOption1(AreaRightSideTop({ adm, nums, onlineData }));
				} else {
					setOption1(AreaRightSideTop({}));
				}
			})
			.catch((err) => {
				setOption1(AreaRightSideTop({}));
			});
	};

	useEffect(() => {
		const params: any = {};
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		if (currentIndustryTop && currentIndustryTop !== '全部') {
			const idx = Object.keys(industryClassification()).findIndex(
				(i) => i === currentIndustryTop,
			);
			params.industry_name = Object.values(industryClassification())[idx].join(
				',',
			);
		}
		getUserEnterpriseNetworking(params);
	}, [regionName, currentIndustryTop, currentSelectedId]);

	useEffect(() => {
		const { start_time, time_type } = selectRegionDate.customDate;
		if (!start_time) return;
		const params: any = {
			top: 10,
			dateTime: start_time,
			time_type,
		};
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		if (currentIndustry && currentIndustry !== '全部') {
			const idx = Object.keys(industryClassification()).findIndex(
				(i) => i === currentIndustry,
			);
			params.industry_name = Object.values(industryClassification())[idx].join(
				',',
			);
		}
		getEnterpriseUsersOnlineSituation(params)
			.then((res) => {
				const newRES = res.sort((a, b) => {
					return a.SUM - b.SUM;
				});
				const affiliations = newRES?.map((item) => item.AFFILIATION);
				const onlineData = newRES?.map((item) => item.ONLINE);
				const sumData = newRES?.map((item) => item.SUM);

				const offlineData = sumData.map(
					(sum, index) => sum - onlineData[index],
				);

				const xdata = affiliations;
				const result = [
					{ name: '出行车辆', data: onlineData },
					{ name: '未出行车辆', data: offlineData },
				];

				getAffiliationInfoByID({
					affiliation_ids: affiliations.toString(),
				})
					.then((res) => {
						if (res.length) {
							let affiliations = [];
							xdata.forEach((element) => {
								let name = res.find((item) => {
									return item['AFFILIATION_ID'] == element;
								})['AFFILIATION'];
								affiliations.push(name);
							});
							const dataArr = { xdata: affiliations, result };
							setOption2(AreaRightSideCenter(dataArr));
							setOption3(
								AreaRightSideBottom({ onlineData, sumData, affiliations }),
							);
						}
					})
					.catch((error) => {
						console.log('error', error);
					});
			})
			.catch((err) => {
				setOption2(AreaRightSideCenter({}));
				setOption3(AreaRightSideBottom({}));
			});
	}, [selectRegionDate, currentIndustry, currentSelectedId, regionName]);

	return (
		<AreaRightSideStyled>
			<div className="Streetcontent">
				<div className="slidingLayer">
					<span className="slidtext">
						{currentSelectedId === 'area'
							? '用户企业联网情况'
							: `${title1 ? title1 : '全市'}企业情况`}
					</span>
					<Dropdown menu={menuPropsTop} className={`dropDown1`} key={1}>
						<Button>
							<Space>
								{currentIndustryTop}
								<DownOutlined />
							</Space>
						</Button>
					</Dropdown>
				</div>
				<LayoutContainer style={{ height: '20%' }}>
					<div className="echarts-line">
						<Echarts
							option={option1}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">
						{regionName ? regionName : '全市'}企业排行
					</span>
					{getDropDownMenu({ type: 2 })}
				</div>
				<LayoutContainer style={{ height: '35%' }}>
					<div className="echarts-line" style={{ width: '90%' }}>
						<Echarts
							option={option2}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">企业上线情况</span>
				</div>
				<LayoutContainer style={{ height: '25%' }}>
					<div className="echarts-line">
						<Echarts
							option={option3}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
			</div>
		</AreaRightSideStyled>
	);
};

export default AreaRightSide;
