/*
 * @Descripttion:
 * @version:
 * @Author: Gqq
 * @Date: 2024-07-23 16:38:01
 */
import ReactDOM from 'react-dom/client';
import { ConfigProvider, theme } from 'antd';
import { HashRouter } from 'react-router-dom';
import zhCN from 'antd/locale/zh_CN';

import App from './App';

import 'antd/dist/reset.css';
import './styles/index.css';

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
	<ConfigProvider
		locale={zhCN}
		theme={{
			algorithm: theme.darkAlgorithm, // 使用暗色算法
			components: {
				Slider: {
					railBg: '#d2e7e9',
					railHoverBg: '#98cfe5',
					handleColor: '#67f3ec',
					handleActiveColor: '#67f3ec',
					trackHoverBg: '#009BD8',
					trackBg: '#3ccdd7',
					railSize: 10,
					handleSize: 16,
					handleSizeHover: 18,
				},
			},
		}}
	>
		<HashRouter>
			<App />
		</HashRouter>
	</ConfigProvider>,
);
