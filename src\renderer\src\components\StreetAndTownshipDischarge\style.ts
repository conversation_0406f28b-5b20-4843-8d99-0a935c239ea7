import styled from 'styled-components';
import bg from '../../images/vehicleViolations/bg.png';
const LayoutContainer = styled.div`
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: row;
	position: relative;

	.left-container {
		width: 25%;
		height: 100%;
	}
	.middle-container,
	.right-container {
		margin-left: 10px;
		height: 100%;
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		.table {
			margin-top: 10px;
			> div {
				border: 1px solid #368ec1;
				border-radius: 4px 4px 0px 0px;
			}
		}
	}

	.register-area {
		display: flex;
		margin: 10px 0 10px;
		width: 100%;
		height: 100%;
		.map {
			width: 100%;
			height: 100%;
		}
	}

	.th_row {
		font-size: 14px;
	}
`;
export default LayoutContainer;
