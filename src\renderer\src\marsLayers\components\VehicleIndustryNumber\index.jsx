import { useState, useEffect, useContext, useRef } from 'react';
import * as echarts from 'echarts';
import styled from 'styled-components';

const VehicleIndustryNumberStyle = styled.div`
	width: 280px;
	height: 290px;
	display: inline-block;
	position: absolute;
	right: 100px;
	top: 240px;
	z-index: 4;
	background-color: '#00124394';
	background: linear-gradient(
		133deg,
		#0b2945b5 0%,
		#0b2945ba 45%,
		#0f334abd 100%
	);
	border-radius: 4px;
	border: 2px solid;
	border-image: linear-gradient(
			307deg,
			rgba(200, 200, 200, 0),
			rgba(1, 184, 255, 1),
			rgba(151, 151, 151, 0)
		)
		2 2;
`;

export default function VehicleIndustryNumber({ data, title }) {
	const pieRef = useRef(null);
	const pieChartRef = useRef(null);

	useEffect(() => {
		pieChartRef.current = echarts.init(pieRef.current);

		let sum = 0;
		let newData = [];
		data.forEach((item) => {
			sum += Number(item.value);
		});
		data.forEach((item) => {
			newData.push({ value: item.value, name: item.name });
			// 配置一个空值
			newData.push({
				name: '',
				value: sum / 100,
				itemStyle: { color: 'transparent' },
			});
		});

		const pieOption = {
			tooltip: {
				trigger: 'item',
			},
			title: {
				text: `{a|${title} ${sum}} {b|辆}`,
				left: 'center',
				top: 25,
				textStyle: {
					color: '#fff',
					fontSize: 18,
					rich: {
						a: {
							fontSize: 26,
							fontWeight: '600',
						},
						b: {
							fontSize: 20,
							color: '#ccc',
						},
					},
				},
			},

			legend: {
				show: false,
			},

			series: [
				{
					// 最外圆圈
					type: 'pie',
					zlevel: 1,
					// silent: true, //取消高亮
					radius: ['30%', '65%'],
					center: ['50%', '60%'],
					itemStyle: {
						normal: {
							color: function (params) {
								return data[params.dataIndex].color;
							},
							// borderRadius: 7,
							borderColor: 'rgb(9,36,63,0.5)',
							borderWidth: 2,
						},
						// borderRadius: 7,
						borderColor: 'rgb(9,36,63,0.5)',
						borderWidth: 2,
					},
					label: {
						show: false,
					},
					data: data,
				},
			],
		};

		pieChartRef.current.setOption(pieOption);

		return () => {
			if (pieChartRef.current) pieChartRef.current.dispose();
		};
	}, [data]);

	return <VehicleIndustryNumberStyle ref={pieRef} />;
}
