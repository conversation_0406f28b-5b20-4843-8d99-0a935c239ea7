import React, { useEffect, useState } from 'react';
import { DatePicker, Select } from 'antd';
// import 'moment/locale/zh-cn'
// import moment from 'moment'
import dayjs from 'dayjs';
import CustomPopUpsStyle from './style';
import icon from '@renderer/images/icon';

type Props = {
	showPop: boolean;
	right: string;
	top: string;
	setShowPop: React.Dispatch<React.SetStateAction<boolean>>;
	setTimeType: React.Dispatch<React.SetStateAction<string>>;
	setCustomDate: React.Dispatch<React.SetStateAction<CustomDate>>;
};

interface CustomDate {
	pointTime: string;
	timeType: string;
}

const CustomPopUps = (props: Props) => {
	const { showPop, setShowPop, setTimeType, setCustomDate, right, top } = props;
	const [timeTypeCustom, setTimeTypeCustom] = useState<string>('hour');
	const [startTime, setStartTime] = useState(
		dayjs().add(-1, 'day').format('YYYY-MM-DD HH:00:00'),
	);
	const defaultTimeType = [
		// { value: 'year', label: '年' },
		// { value: 'month', label: '月' },
		{ value: 'day', label: '日' },
		{ value: 'hour', label: '时' },
	];

	const dateOnChange = (type) => {
		setTimeTypeCustom(type);
		if (type === 'hour') {
			const eTime = dayjs().format('YYYY-MM-DD HH:00:00');
			const sTime = dayjs(eTime).add(-1, 'hour').format('YYYY-MM-DD HH:00:00');
			setStartTime(sTime);
		} else if (type === 'day') {
			const eTime = dayjs().format('YYYY-MM-DD 23:59:59');
			const sTime = dayjs(eTime).add(-1, 'day').format('YYYY-MM-DD 00:00:00');
			setStartTime(sTime);
		}
	};

	const startTimeOnChange = (time) => {
		if (timeTypeCustom === 'hour') {
			setStartTime(dayjs(time).format('YYYY-MM-DD HH:00:00'));
		} else if (timeTypeCustom === 'day') {
			setStartTime(dayjs(time).format('YYYY-MM-DD 00:00:00'));
		}
	};

	const onOkStart = (e) => {
		if (timeTypeCustom === 'hour') {
			setStartTime(dayjs(e).format('YYYY-MM-DD HH:00:00'));
		} else if (timeTypeCustom === 'day') {
			setStartTime(dayjs(e).format('YYYY-MM-DD 00:00:00'));
		}
	};

	const closePop = () => {
		setShowPop(false);
	};
	const dataSearch = () => {
		if (setTimeType) setTimeType(`custom`);
		setCustomDate({
			pointTime: startTime,
			timeType: timeTypeCustom,
		});
	};

	return showPop ? (
		<CustomPopUpsStyle right={right} top={top}>
			<img src={icon.close} alt="" className="close" onClick={closePop} />
			<div className="content">
				<div className="item">
					<p>时间粒度:</p>
					<Select
						className="settings-select"
						popupClassName="settings-select-popup"
						defaultValue={timeTypeCustom}
						onChange={dateOnChange}
						style={{ width: '100px', textAlign: 'center' }}
						options={defaultTimeType}
					/>
				</div>
				<div className="item">
					<p>时间选择:</p>
					{timeTypeCustom == 'hour' ? (
						<DatePicker
							className="settings-option-datePicker"
							showTime={{
								defaultValue: dayjs('00:00:00', 'HH:mm:ss'),
								format: 'HH:00:00',
							}}
							defaultValue={dayjs(startTime, '')}
							// disabledDate={() => {}}
							value={dayjs(startTime)}
							showToday={false}
							onOk={onOkStart}
							// onChange={startTimeOnChange}
						></DatePicker>
					) : (
						<DatePicker
							className="settings-option-datePicker"
							defaultValue={dayjs(startTime, '')}
							// disabledDate={() => {}}
							value={dayjs(startTime)}
							showToday={false}
							// onOk={onOkStart}
							onChange={startTimeOnChange}
						></DatePicker>
					)}
				</div>
			</div>
			<div className="search" onClick={dataSearch}>
				<p style={{ margin: '0' }}>数据查询</p>
			</div>
		</CustomPopUpsStyle>
	) : null;
};

export default CustomPopUps;
