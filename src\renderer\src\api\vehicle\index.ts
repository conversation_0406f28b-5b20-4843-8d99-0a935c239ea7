import Request from '../request';

// 北京重柴api（WebSocket版本）
const baseURL = import.meta.env.RENDERER_VITE_API_DOMAIN;

const request = new Request({ baseURL });

// 车流量
export const getPoiTrance = (config: Record<string, any> = {}) => {
	return request.get(`/v2/vehicle/poi/trance`, config);
};

// 车流量路网
export const getPoiRoad = (config: Record<string, any> = {}) => {
	return request.get(`/v2/vehicle/poi/road`, config);
};

// 街乡镇排放
export const getStatisticsNox = (config: Record<string, any> = {}) => {
	return request.get(`/statistics/nox/town`, config);
};

// 街乡镇违规排放量
export const getViolationTownList = (config: Record<string, any> = {}) => {
	return request.get(`/statistics/violation/create-town-list`, config);
};

// 街乡镇违规排放强度
export const getViolationRegisterTownList = (
	config: Record<string, any> = {},
) => {
	return request.get(`/statistics/violation/register-town-list`, config);
};

// 行业不同车型里程特征
export const getIndustryByTime = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/nox/industry-by-time`, config);
};

//区域不同车型排放里程特征、区域不同行业里程特征
export const getCountyByTime = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/nox/county_by_time`, config);
};

export const getMenu = (config: Record<string, any> = {}): any => {
	return request.get(`/screen/getScreenConfigEnabled`, config);
};

//单车轨迹
export const getVehicleTrack = (config: Record<string, any> = {}): any => {
	return request.get(`/vehicle/track`, config);
};

//单车详情
export const getVehicleDetail = (config: Record<string, any> = {}): any => {
	return request.get(`/vehicle/detail`, config);
};

//单车累计
export const getVehicleCamulative = (config: Record<string, any> = {}): any => {
	return request.get(`/vehicle/history_info_by_day`, config);
};

//单车最新状态
export const getVehicleLatestinfo = (config: Record<string, any> = {}): any => {
	return request.get(`/vehicle/latest_info`, config);
};

//单车基础信息
export const getVehicleinformation = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/vehicle/basic_info`, config);
};

//实时车辆按类型统计数量
export const getRealTimeVehicleStatisticsQuantity = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/vehicle/count_by_type`, config);
};

//实时车辆历史轨迹
export const getVehicleHistoricalTrajectory = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/vehicle/history-track-detail`, config);
};

//道路排行
export const getRoadRanking = (config: Record<string, any> = {}): any => {
	return request.get(`/statistics/road-ranking`, config);
};

// 获取实时离线数据
export const getRealTimeOffline = (config: Record<string, any> = {}): any => {
	return request.get(`/real_time_offline_data_from_redis`, config);
};

// 违规车辆列表-v2
export const getViolationJxhList = (config: Record<string, any> = {}): any => {
	return request.get(`/v2/vehicle/violation-jxh-list`, config);
};

// 更改屏幕状态
export const setChangeScreenStatus = (
	config: Record<string, any> = {},
): any => {
	return request.post(`/screen/ChangeScreenStatus`, config);
};

// 查询车辆基本信息
export const getVehicleBasicInformation = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/vehicle/basic-information`, config);
};

// 查询车辆企业名称、企业类型
export const getAffiliationInfoByID = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/vehicle/getAffiliationInfoByID`, config);
};

// 单车-活动水平
export const getVehicleActivity = (config: Record<string, any> = {}): any => {
	return request.get(`/v2/vehicle/vehicle-activity`, config);
};

// 单车-累计数据
export const getVehicleCumulativeData = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/vehicle/vehicle-cumulative-data`, config);
};

// 单车-违规查询
export const getVehicleViolations = (config: Record<string, any> = {}): any => {
	return request.get(`/v2/vehicle/vehicle-violations`, config);
};

// 车辆数量
export const getVehicleNumber = (config: Record<string, any> = {}): any => {
	return request.get(`/real_time_get_vehicle_data_from_redis`, config);
};

// 路段数据排行
export const getRoadRankingV2 = (config: Record<string, any> = {}): any => {
	return request.get(`/v2/road/road-ranking`, config);
};

// 路网数据分析
export const getRoadAnalyse = (config: Record<string, any> = {}): any => {
	return request.get(`/v2/road/road-analyse`, config);
};

// 获取某个路段信息
export const getRoadInfo = (config: Record<string, any> = {}): any => {
	return request.get(`/v2/road/road-info`, config);
};

// 行业排放量统计
export const getRoadStatisticsIndustryEmissions = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/road/statistics-industry-emissions`, config);
};

// 单车实时位置信息
export const getVehicleRealTimePositionInfo = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/vehicle/real-time-position-info`, config);
};

// 联网数据
export const getNetworked = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/wired`, config);
};

// 用车大户TopN
export const getUserCarTop = (config: Record<string, any> = {}) => {
	return request.get(`/statistics/register-by-user`, config);
};
// 厂商
export const getCompany = (config: Record<string, any> = {}) => {
	return request.get(`/statistics/register-by-company`, config);
};
//各行业排放
export const getIndustry = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/`, config);
};
//单车违规企业Top5
export const getCompanyTop = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/violation/company-top-n`, config);
};

//单车违规
export const getViolationVehicle = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/violation/vehicle`, config);
};
//单车违规占比
export const getViolationVehicleProportion = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/statistics/violation`, config);
};
//违规概况
export const getViolationOverview = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/violation/overview`, config);
};
//当日活动水平
export const getAtThatTime = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/online-by_time`, config);
};
//当日活动水平
export const getTodayOline = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/today-online`, config);
};

// 北京各区
export const getNoxCountyByType = (config: Record<string, any> = {}) => {
	return request.get(`statistics/nox/county-by-type`, config);
};

// 用户概况
export const getVehicleUser = (config: Record<string, any> = {}) => {
	return request.get(`/v2/vehicle/user`, config);
};
// 厂商概况
export const getVehicleBrand = (config: Record<string, any> = {}) => {
	return request.get(`/v2/vehicle/brand`, config);
};

// 企业数据分析
export const getEnterpriseDataAnalytics = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/vehicle/enterprise_data_analytics`, config);
};

// 行业分布
export const getIndustryDistribution = (config: Record<string, any> = {}) => {
	return request.getJSON(`/v2/statistics/register_by_industry`, config);
};

// 活动水平
export const getActivityLevel = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/online`, config);
};

// 车辆分布
export const getRegisterCounty = (config: Record<string, any> = {}) => {
	return request.getJSON(`/statistics/register-by-county`, config);
};

// 注册区域分布
export const getDistributionRegistrationAreas = (
	config: Record<string, any> = {},
) => {
	return request.getJSON(`/v2/statistics/vehicle-analysis`, config);
};

// 行业排放
export const getNox = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/nox`, config);
};

// 行业排放-类型
export const getEmissionIndustyListGroupByLevel = (
	config: Record<string, any> = {},
) => {
	return request.get(
		`/v2/statistics/emission/industy_list_group_by_level`,
		config,
	);
};

// 行业排放-行业
export const getEmissionIndustyListGroupByDatetime = (
	config: Record<string, any> = {},
) => {
	return request.get(
		`/v2/statistics/emission/industy_list_group_by_datetime`,
		config,
	);
};

// 行业违规-车辆类型
export const getIndustyListGroupBySecondTypeAndDatetime = (
	config: Record<string, any> = {},
) => {
	return request.get(
		`/v2/statistics/violation/industy-list-group-by-second-type-and-datetime`,
		config,
	);
};

// 行业违规-行业类型
export const getIndustyListGroupByFourthType = (
	config: Record<string, any> = {},
) => {
	return request.get(
		`/v2/statistics/violation/industy-list-group-by-fourth-type`,
		config,
	);
};

// 区域违规
export const statisticsViolationDodListGroupByAdm = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/statistics/violation/dod-list-group-by-adm`, config);
};

export const statisticsViolationIndustryListGroupByAdm = (
	config: Record<string, any> = {},
): any => {
	return request.get(
		`/statistics/violation/industry-list-group-by-adm`,
		config,
	);
};

// 区域排放-同比
export const regionalEmissionsYearOnYear = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/regional_emissions_yoy`, config);
};

// 区域排放-同比基准
export const regionalEmissionsBenchmark = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/regional_emissions_benchmark`, config);
};

// 区域排放-行业
export const regionalEmissionsIndustries = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/regional_emissions_industry`, config);
};

// 区域排放-排行
export const regionalEmissionsRanking = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/regional_emissions_ranking`, config);
};

// 累计里程特征
export const getAccumulateDistance = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/statistics/accumulate-distance`, config);
};

// 运行趋势统计
export const getRealtimeOnlineByTime = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/statistics/realtime_online_by_time`, config);
};
//每月新增联网车辆接口
export const getWiredByMonth = (config: Record<string, any> = {}): any => {
	return request.get(`/statistics/wired-by-month`, config);
};

//街乡镇里程特征接口
export const getStreetTownShip = (config: Record<string, any> = {}): any => {
	return request.get(`/v2/statistics/distance-by-town`, config);
};

//各区街乡镇实时在线
export const getStreetOnlineTownShip = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/statistics/online-by_county`, config);
};

//实时在线厂商
export const getOnlinevendors = (config: Record<string, any> = {}): any => {
	return request.get(`/v2/statistics/online-by-company`, config);
};

//实时行业数据
export const getRealTimeIndustryData = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/statistics/industry-stock`, config);
};

//实时行业详情
export const getRealTimeIndustryDetails = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/statistics/county-industry-analyse`, config);
};

//实时企业数据
export const getRealTimeEnterpriseDetails = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/enterprise/data-distribution`, config);
};

//实时企业数据
export const getRealTimeEnterpriseRanking = (
	config: Record<string, any> = {},
): any => {
	return request.get(`/v2/enterprise/ranking-list`, config);
};

//车流量-排放聚合
export const getpolymerization = (config: Record<string, any> = {}): any => {
	return request.get(`/v2/vehicle/poi/trance_polymerize`, config);
};

// 区域-指标-同比
export const getRegionalIndicator = (config: Record<string, any> = {}) => {
	return request.get(`/v2/region/regional_indexes_yoy`, config);
};
// 区域-道路top5
export const getRegionalViolationTop5 = (config: Record<string, any> = {}) => {
	return request.get(`/v2/region/region-violation-top5`, config);
};
// 区域行业占比
export const getRegionalIndustryProportion = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/statistics/industry/county-proportion`, config);
};

// 行业-指标-同比-charts
export const getIndustryIndicator = (config: Record<string, any> = {}) => {
	return request.get(`/v2/industry/industry_indexes_yoy`, config);
};
// 行业-左侧-全市总览-charts
export const getIndustryOverview = (config: Record<string, any> = {}) => {
	return request.get(`/v2/industry/overview`, config);
};
// 行业-左侧-全市总览-charts
export const getIndustryOnlineAnalyse = (config: Record<string, any> = {}) => {
	return request.get(`/v2/industry/online-analyse`, config);
};
// 行业-左侧-里程分析-charts
export const getIndustryDistanceAnalyse = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/industry/distance-analyse`, config);
};
// 行业-右侧-油耗分析-charts
export const getIndustryOilConsumption = (config: Record<string, any> = {}) => {
	return request.get(`/v2/industry/oil-consumption`, config);
};
// 行业-右侧-排放分析-charts
export const getIndustryNoxAnalyse = (config: Record<string, any> = {}) => {
	return request.get(`/v2/industry/nox-analyse`, config);
};

// 行业-右侧-违规分析-charts
export const getIndustryViolationAnalyse = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/industry/violation-analyse`, config);
};
// 街乡镇行业占比
export const getStreetTownshipIndustriesProportion = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/statistics/industry/town-proportion`, config);
};

// 区域分析
export const getRegionalAnalysis = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/industry/county-analyse`, config);
};

// 行业-上线率
export const getIndustryOnlineSituation = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/statistics/county-industry-online`, config);
};

// 企业用户  排行、上线
export const getEnterpriseUsersOnlineSituation = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/statistics/user-register-online`, config);
};

// 街乡镇行业各项指标统计
export const getSTIStats = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/industry/town-analyse`, config);
};

// 联网用户区间统计
export const getNetworkUserIntervalStatistics = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/region/statistics/wired-user-section`, config);
};

// 车流量车排放网格
export const getVehicleFlowEmissionGrid = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/vehicle/area-grid-analyse`, config);
};

// 静态数据分析-车辆数量
export const getStaticCarNums = (config: Record<string, any> = {}) => {
	return request.get(`/statistics/car-register-analyse`, config);
};

// 静态数据分析-企业数量
export const getStaticFirms = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/user-register-analyse`, config);
};

// 静态数据分析-行业情况
export const getStaticIndustrySituation = (
	config: Record<string, any> = {},
) => {
	return request.get(`/statistics/industry-register-analyse`, config);
};

// 违规分析-同比基准、地图落区
export const getViolationsIndexesYoy = (config: Record<string, any> = {}) => {
	return request.get(`/v2/violations/violations_indexes_yoy`, config);
};
// 违规分析--左侧栏-全市行业违规情况
export const getViolationsIndustryViolations = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/violations/industry_violations`, config);
};

// 违规分析--左侧栏-区域违规排行-区县
export const getViolationsRegionViolationsRanking = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/violations/region_violations_ranking`, config);
};
// 违规分析--左侧栏-区域违规排行-街乡镇
export const getViolationsTownAnalyse = (config: Record<string, any> = {}) => {
	return request.get(`/v2/violations/town-analyse`, config);
};

// 违规分析--左侧栏-历史违规情况
export const getViolationsHistory = (config: Record<string, any> = {}) => {
	return request.get(`/v2/violations/history_violations`, config);
};

// 违规分析--右侧栏-用户违规情况
export const getUserViolations = (config: Record<string, any> = {}) => {
	return request.get(`/v2/violations/user-violations`, config);
};

// 违规分析--右侧栏-用户违规排行
export const getUserViolationsRanking = (config: Record<string, any> = {}) => {
	return request.get(`/v2/violations/user_violations_ranking`, config);
};

// 违规分析--右侧栏-用户违规分析
export const getUserViolationsAnalyse = (config: Record<string, any> = {}) => {
	return request.get(`/v2/violations/user_violations_analyse`, config);
};

// 用户行业排放
export const getIndustryNoxUser = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/industry_nox_user`, config);
};
//获取最大数据时间
export const getMaxTime = (config: Record<string, any> = {}) => {
	return request.get<string>(`/v2/get-max-time`, config);
};
//区域不同车型排放里程特征、区域不同行业里程特征
export const getCountyByTimeNew = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/nox/county_by_time_new`, config);
};
// 全市行业数据
export const getCityIndustry = (config: Record<string, any> = {}) => {
	return request.get(`/v2/statistics/industry-data-of-city`, config);
};
