export const splitNubmerAndString = (text) => {
	const reg1 = /[^\d.]/gi;
	const reg2 = /[0-9]*(\.[0-9]*)?/g;
	const replacement = '';

	return [text.replace(reg2, replacement), text.replace(reg1, replacement)];
};

export enum timeFormat {
	now = 'YYYY-MM-DD HH:00:00',
	day = 'YYYY-MM-DD 00:00:00',
	month = 'YYYY-MM-01 00:00:00',
	year = 'YYYY-01-01 00:00:00',
}

export enum CarLegendColor {
	'轻型车' = '#85A9FF',
	'中型车' = '#8FD67F',
	'重型车' = '#61C6FF',
}

export enum CarTypeColor {
	'货车' = '#D62220',
	'渣土车' = '#30C43D',
	'工程车' = '#FBD34E',
	'公交车' = '#48AFF3',
	'其他客车' = '#CFB79C',
	'环卫车' = '#F2EDED',
	'其他用途' = '#DBDDF3',
}

export const getCarModelName = (type) => {
	const CarType = {
		货车: 'huoche.gltf',
		公交车: 'gongjiaoche.gltf',
		特殊用途车: '',
		特殊环卫车: 'huanweiche.glb',
		清扫车: '',
		工程作业车: '',
		邮政车: '',
		垃圾车: '',
		旅游大巴: '',
		冷藏车: 'lengcangce.gltf',
		渣土车: 'zhatuche.gltf',
		混凝土车: 'hunningtuche.glb',
		自卸货车: '',
		普通危险品运输车: '',
		医疗废弃物转运车: '',
		其它: '',
	};
	if (!CarType[type] || CarType[type] == '') return 'qiche.gltf';
	return CarType[type];
};

export const getTrackColor = (emissions: number, standard: string) => {
	if (standard === '5') {
		if (emissions < 800) {
			return '#01e602';
		} else if (emissions >= 800 && emissions < 1000) {
			return '#feff00';
		} else if (emissions >= 1000 && emissions < 1500) {
			return '#bd0604';
		} else if (emissions >= 1500 && emissions < 2000) {
			return '#681e0d';
		} else {
			return '#9a014a';
		}
	} else if (standard === '6') {
		if (emissions < 440) {
			return '#01e602';
		} else if (emissions >= 440 && emissions < 550) {
			return '#feff00';
		} else if (emissions >= 550 && emissions < 825) {
			return '#bd0604';
		} else if (emissions >= 825 && emissions < 1100) {
			return '#681e0d';
		} else {
			return '#9a014a';
		}
	}
};

export const getTargetList = (id: string) => {
	switch (id) {
		case 'road':
			return [
				{ id: 1, name: '在线车辆', active: true },
				{ id: 2, name: '排放量', active: false },
				{ id: 3, name: '里程', active: false },
				{ id: 4, name: '油耗', active: false },
			];
		case 'area':
			return [
				{ id: 5, name: '在线车辆', active: false },
				{ id: 2, name: '排放量', active: true },
				{ id: 3, name: '里程', active: false },
				{ id: 1, name: '油耗', active: false },
			];
		case 'industry':
			return [
				{ id: 5, name: '在线车辆', active: false },
				{ id: 2, name: '排放量', active: true },
				{ id: 3, name: '里程', active: false },
				{ id: 1, name: '油耗', active: false },
				{ id: 4, name: '排放强度', active: false },
				{ id: 6, name: '违规', active: false },
			];
		case 'streetTownship':
			return [
				{ id: 5, name: '在线车辆', active: false },
				{ id: 2, name: '排放量', active: true },
				{ id: 3, name: '里程', active: false },
				{ id: 1, name: '油耗', active: false },
				{ id: 6, name: '违规', active: false },
				{ id: 4, name: '排放强度', active: false },
			];
		case 'userEnterprise':
			return [
				{ id: 6, name: '联网车辆', active: false },
				{ id: 4, name: '在线车辆', active: true },
				{ id: 2, name: '排放量', active: false },
				{ id: 3, name: '里程', active: false },
				{ id: 1, name: '油耗', active: false },
				{ id: 5, name: '违规', active: false },
			];
		default:
			return [
				{ id: 5, name: '在线车辆', active: false },
				{ id: 2, name: '排放量', active: true },
				{ id: 3, name: '里程', active: false },
				{ id: 1, name: '油耗', active: false },
				{ id: 4, name: '排放强度', active: false },
			];
	}
};

export const ViolationNavList = [
	{
		id: 4,
		value: 'NOx排放异常',
		visible: true,
		layerId: 4,
	},
	{
		id: 3,
		value: 'Mil灯异常',
		visible: true,
		layerId: 3,
	},
	{
		id: 2,
		value: '尿素空异常',
		visible: true,
		layerId: 2,
	},
	// {
	//   id: 1,
	//   value: 'DPF异常',
	//   visible: true,
	//   layerId: 1,
	// },
	// {
	//   id: 5,
	// 	value: 'OBD监测项不全',
	// 	visible: true,
	// 	layerId: 5,
	// },
];

/**
 * echarts 中污染物下标展示,结合style里rich: {sub: {fontSize: 8,verticalAlign: 'bottom',}}
 * @param pollutantId
 * @returns
 */
export const pollutantNameFormat = (pollutantId: number): string => {
	switch (pollutantId) {
		case 134004:
			return 'PM{sub|2.5}';
		case 134002:
			return 'PM{sub|10}';
		case 121026:
			return 'SO{sub|2}';
		case 121005:
			return 'CO';
		case 121004:
			return 'NO{sub|2}';
		case 105024:
			return 'O{sub|3}';
		case 209002:
			return 'AQI';
		case 199054:
			return 'TVOC';
		case 134001:
			return 'TSP';
		case 121007:
			return 'CO{sub|2}';
	}
};

export const pollutionEchats = {
	134004: [
		{ min: 250, max: 9999999, label: '严重污染', color: '#800021' },
		{ min: 150, max: 250, label: '重度污染', color: '#9a004a' },
		{ min: 115, max: 150, label: '中度污染', color: '#ff0000' },
		{ min: 75, max: 115, label: '轻度污染', color: '#ff7f00' },
		{ min: 35, max: 75, label: '良', color: '#ffff00' },
		{ min: 0, max: 35, label: '优', color: '#00e600' },
	],
	134002: [
		{ min: 420, max: 9999999, label: '严重污染', color: '#800021' },
		{ min: 350, max: 420, label: '重度污染', color: '#9a004a' },
		{ min: 250, max: 350, label: '中度污染', color: '#ff0000' },
		{ min: 150, max: 250, label: '轻度污染', color: '#ff7f00' },
		{ min: 50, max: 150, label: '良', color: '#ffff00' },
		{ min: 0, max: 50, label: '优', color: '#00e600' },
	],
	121026: [
		{ min: 800, max: 9999999, label: '严重污染', color: '#800021' },
		{ min: 650, max: 800, label: '重度污染', color: '#9a004a' },
		{ min: 500, max: 650, label: '中度污染', color: '#ff0000' },
		{ min: 150, max: 500, label: '轻度污染', color: '#ff7f00' },
		{ min: 50, max: 150, label: '良', color: '#ffff00' },
		{ min: 0, max: 50, label: '优', color: '#00e600' },
	],
	121005: [
		{ min: 36, max: 48, label: '严重污染', color: '#800021' },
		{ min: 24, max: 36, label: '重度污染', color: '#9a004a' },
		{ min: 14, max: 24, label: '中度污染', color: '#ff0000' },
		{ min: 4, max: 14, label: '轻度污染', color: '#ff7f00' },
		{ min: 2, max: 4, label: '良', color: '#ffff00' },
		{ min: 0, max: 2, label: '优', color: '#00e600' },
	],
	121004: [
		{ min: 565, max: 9999999, label: '严重污染', color: '#800021' },
		{ min: 280, max: 565, label: '重度污染', color: '#9a004a' },
		{ min: 180, max: 280, label: '中度污染', color: '#ff0000' },
		{ min: 80, max: 180, label: '轻度污染', color: '#ff7f00' },
		{ min: 40, max: 80, label: '良', color: '#ffff00' },
		{ min: 0, max: 40, label: '优', color: '#00e600' },
	],
	105024: [
		{ min: 800, max: 9999999, label: '严重污染', color: '#800021' },
		{ min: 265, max: 800, label: '重度污染', color: '#9a004a' },
		{ min: 215, max: 265, label: '中度污染', color: '#ff0000' },
		{ min: 160, max: 215, label: '轻度污染', color: '#ff7f00' },
		{ min: 100, max: 160, label: '良', color: '#ffff00' },
		{ min: 0, max: 100, label: '优', color: '#00e600' },
	],
	209002: [
		{ min: 300, max: 9999999, label: '严重污染', color: '#800021' },
		{ min: 200, max: 300, label: '重度污染', color: '#9a004a' },
		{ min: 150, max: 200, label: '中度污染', color: '#ff0000' },
		{ min: 100, max: 150, label: '轻度污染', color: '#ff7f00' },
		{ min: 50, max: 100, label: '良', color: '#ffff00' },
		{ min: 0, max: 50, label: '优', color: '#00e600' },
	],
	199054: [
		{ min: 150, max: 9999999, label: '严重污染', color: '#800021' },
		{ min: 120, max: 150, label: '重度污染', color: '#9a004a' },
		{ min: 85, max: 120, label: '中度污染', color: '#ff0000' },
		{ min: 50, max: 85, label: '轻度污染', color: '#ff7f00' },
		{ min: 20, max: 50, label: '良', color: '#ffff00' },
		{ min: 0, max: 20, label: '优', color: '#00e600' },
	],
	134001: [
		{ min: 500, max: 9999999, label: '严重污染', color: '#800021' },
		{ min: 300, max: 500, label: '重度污染', color: '#9a004a' },
		{ min: 200, max: 300, label: '中度污染', color: '#ff0000' },
		{ min: 120, max: 200, label: '轻度污染', color: '#ff7f00' },
		{ min: 80, max: 120, label: '良', color: '#ffff00' },
		{ min: 0, max: 80, label: '优', color: '#00e600' },
	],
	//以下都是项目需要临时复制的请勿使用（134011，209100,209102）
	134011: [
		{ min: 501, max: 9999999, label: '严重污染', color: '#800021' },
		{ min: 301, max: 500, label: '重度污染', color: '#9a004a' },
		{ min: 201, max: 300, label: '中度污染', color: '#ff0000' },
		{ min: 121, max: 200, label: '轻度污染', color: '#ff7f00' },
		{ min: 81, max: 120, label: '良', color: '#ffff00' },
		{ min: 0, max: 80, label: '优', color: '#00e600' },
	],
	209100: [
		{ min: 501, max: 9999999, label: '', color: '#800021' },
		{ min: 301, max: 500, label: '', color: '#9a004a' },
		{ min: 201, max: 300, label: '', color: '#ff0000' },
		{ min: 121, max: 200, label: '', color: '#ff7f00' },
		{ min: 81, max: 120, label: '', color: '#ffff00' },
		{ min: 0, max: 80, label: '', color: '#00e600' },
	],
	209102: [
		{ min: 501, max: 9999999, label: '', color: '#800021' },
		{ min: 301, max: 500, label: '', color: '#9a004a' },
		{ min: 201, max: 300, label: '', color: '#ff0000' },
		{ min: 121, max: 200, label: '', color: '#ff7f00' },
		{ min: 81, max: 120, label: '', color: '#ffff00' },
		{ min: 0, max: 80, label: '', color: '#00e600' },
	],
	209103: [
		{ min: 501, max: 9999999, label: '', color: '#800021' },
		{ min: 301, max: 500, label: '', color: '#9a004a' },
		{ min: 201, max: 300, label: '', color: '#ff0000' },
		{ min: 121, max: 200, label: '', color: '#ff7f00' },
		{ min: 81, max: 120, label: '', color: '#ffff00' },
		{ min: 0, max: 80, label: '', color: '#00e600' },
	],
	209104: [
		{ min: 501, max: 9999999, label: '', color: '#800021' },
		{ min: 301, max: 500, label: '', color: '#9a004a' },
		{ min: 201, max: 300, label: '', color: '#ff0000' },
		{ min: 121, max: 200, label: '', color: '#ff7f00' },
		{ min: 81, max: 120, label: '', color: '#ffff00' },
		{ min: 0, max: 80, label: '', color: '#00e600' },
	],
	209008: [
		{ min: 33, max: 365, label: '', color: '#800021' },
		{ min: 25, max: 32, label: '', color: '#9a004a' },
		{ min: 19, max: 24, label: '', color: '#ff0000' },
		{ min: 13, max: 18, label: '', color: '#ff7f00' },
		{ min: 7, max: 12, label: '', color: '#ffff00' },
		{ min: 0, max: 6, label: '', color: '#00e600' },
	],
};

export const getPollutionUnit = (type: number) => {
	switch (type) {
		case 121005: // CO
			return 'mg/m³';
		case 209002: // AQI
			return '';
		case 199054: // TVOC
			return 'ppb';
		default:
			return 'μg/m³';
	}
};

export const COUNTY_COLOR = {
	北京市: '#2ec7c9',
	海淀区: '#b6a2de',
	朝阳区: '#5ab1ef',
	大兴区: '#ffb980',
	房山区: '#d87a80',
	石景山区: '#c14089',
	东城区: '#e5cf0d',
	西城区: '#97b552',
	平谷区: '#95706d',
	密云区: '#dc69aa',
	怀柔区: '#07a2a4',
	顺义区: '#9a7fd1',
	门头沟区: '#588dd5',
	通州区: '#f5994e',
	昌平区: '#59678c',
	延庆区: '#c9ab00',
	丰台区: '#7eb00a',
	开发区: '#91cc75',
	京外: '#808080',
};

export const COLORLIST = [
	'#00FFCC',
	'#00FFFF',
	'#00CCCC',
	'#00CCFF',
	'#009999',
	'#0099CC',
	'#0099FF',
	'#33FFCC',
	'#33FFFF',
	'#33CCCC',
	'#33CCFF',
	'#3399CC',
	'#3399FF',
	'#66FFFF',
	'#66CCFF',
	'#0066FF',
	'#0033FF',
	'#0000FF',
	'#3366FF',
	'#3300CC',
	'##3300FF',
];

export const COLORLIST2 = [
	'#96ED89',
	'#F9BE3E',
	'#96CA2D',
	'#00FFCC',
	'#EF5D31',
	'#9768D1',
	'#00CCFF',
	'#3399FF',
	'#35A69A',
	'#F49EB4',
	'#E75981',
	'#EC4697',
	'#50EAFF',
	'#FFB979',
	'#FFD630',
	'#FDA735',
	'#45BF54',
	'#2A62FF',
	'#FF873C',
	'#D17101',
	'#FDF176',
];
