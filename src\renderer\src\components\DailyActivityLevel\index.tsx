import * as React from 'react';
import { useState, useEffect } from 'react';
import moment from 'moment';
import Box from '../../baseUI/Box';
import LayoutContainer from './style';
import Echarts from '@renderer/components/echarts';
import up from '../../images/icon/up.png';
import down from '../../images/icon/down.png';
// import { cloneDeep } from 'lodash'
// import ScrollList from '../../components/ScrollList'
// import ScrollListWidthTitle from '@renderer/components/ScrollListWithTitle'
import { DailyActivityLevelData } from './DailyActivityLevelData';
import { getTodayOline, getAtThatTime } from '@renderer/api';
import { tranNumberS } from '@renderer/hooks';
/**
 * 当日活动水平
 */
type dataProps = {
	date_time: string;
	vehicle_type: string;
	nums: number;
	total_distanc: number | any;
	total_nox: number | any;
	total_oil: number | any;
	emissions_factor: number;
};
const DailyActivityLeve = (props) => {
	const [option, setOption] = useState({});
	const [yesterdayData2, setYesterdayData2] = useState({});
	const [todayData2, setTodayData2] = useState({});
	// const [moreData, setMoreData] = useState<any>(props);
	const [comparisonData, setComparisonData] = useState<Record<string, number>>(
		{},
	);
	const lineTitle = [
		{ name: '在线车辆', key: 'total_online', rateKey: 'total_online_rate' },
		{ name: '总排放（千克）', key: 'total_nox', rateKey: 'total_nox_rate' },
		{
			name: '总里程（万公里）',
			key: 'total_distance',
			rateKey: 'total_distance_rate',
		},
		{ name: '总油耗（吨）', key: 'total_oil', rateKey: 'total_oil_rate' },
		{
			name: '排放因子(克/公里)',
			key: 'emissions_factor',
			rateKey: 'emissions_factor_rate',
		},
	];
	const [legend, setLegend] = useState([
		{ name: '今日数据', color: '#5CE1A9' },
		{ name: '昨日同期', color: '#E3A07C' },
	]);

	const column = [
		// {
		//   align: 'center',
		//   dataIndex: 'vehicle_type',
		//   key: 'vehicle_type',
		//   title: '行业'
		// },
		{
			align: 'center',
			dataIndex: 'nums',
			key: 'total_online',
			title: '在线车辆 (辆)',
		},
		{
			align: 'center',
			dataIndex: 'total_nox',
			key: 'total_nox',
			title: '总排放 (千克)',
		},
		{
			align: 'center',
			dataIndex: 'total_distance',
			key: 'total_distance',
			title: '总里程 (万公里)',
		},
		{
			align: 'center',
			dataIndex: 'total_oil',
			key: 'total_oil',
			title: '总油耗 (吨)',
		},
		{
			align: 'center',
			dataIndex: 'emissions_factor',
			key: 'emissions_factor',
			title: '排放因子 (克/公里)',
		},
	];

	// let optionTable = {
	//   columns: column,
	//   width: '100%',
	//   height: '185px',
	//   fontSize: 12,
	//   thclassname: 'th_row',
	//   tablebgcolor: 'rgba(9,30,59,0)',
	//   trheight: '30px',
	//   thheight: '40px',
	//   customwidth: true,
	//   rowbgcolor: ['rgba(58,218,255,0.2);', 'rgba(9, 30, 47, 0.29)'],
	//   thbgcolor: '#081F38'
	// }

	// const isObjectEmpty = (obj) => {
	//   return Object.keys(obj).length
	// }

	const getValues = (data) => {
		data.forEach((item) => {
			item.emissions_factor = Number(item.emissions_factor).toFixed(4);
			item.total_distance = tranNumberS(item.total_distance, 2);
			item.total_nox = Number(item.total_nox).toFixed(1);
			item.total_oil = Number(item.total_oil).toFixed(1);
		});
	};

	useEffect(() => {
		let param = {
			date_time: moment().format('YYYY-MM-DD 00:00:00'),
		};
		//雷达图数据
		getTodayOline(param).then((res) => {
			let result = res as any;
			setRadarData(result);
		});
		let params = {
			start_time: moment().format('YYYY-MM-DD 00:00:00'),
			end_time: moment().format('YYYY-MM-DD 23:59:59'),
			time_type: 2,
		};
		//列表数据
		// getAtThatTime(params).then((res) => {
		// 	setMoreData({
		// 		...moreData,
		// 		data: {
		// 			dataList: (res as any).data,
		// 		},
		// 	});
		// });
	}, []);
	const contains = (arr, val) => {
		var i = arr.length;
		while (i--) {
			if (arr[i].name === val) {
				return i;
			}
		}
		return false;
	};
	const compareArrays = (arr1, arr2) => {
		var result: any = [];
		// 遍历较短的数组
		var length = Math.min(arr1.length, arr2.length);
		for (var i = 0; i < length; i++) {
			if (arr1[i] > arr2[i]) {
				result.push('大于');
			} else if (arr1[i] < arr2[i]) {
				result.push('小于');
			} else {
				result.push('等于');
			}
		}

		// 如果数组长度不一致，处理剩余的项
		if (arr1.length > arr2.length) {
			for (var j = length; j < arr1.length; j++) {
				result.push(arr1[j] + ' 大于 所有的 ' + arr2);
			}
		} else if (arr1.length < arr2.length) {
			for (var j = length; j < arr2.length; j++) {
				result.push(arr1 + ' 小于 所有的 ' + arr2[j]);
			}
		}
		return result;
	};
	const setRadarData = (result) => {
		if (!result) return;
		const todayData = lineTitle.map((item, index) => {
			return result.today[0][item.key];
		});

		const yesterdayData = lineTitle.map((item, index) => {
			return result.yesterday[0][item.key];
		});
		setTodayData2({
			title: '今日数据',
			data: result.today[0],
		});
		setYesterdayData2({
			title: '昨日同期',
			data: result.yesterday[0],
		});
		// 计算同比
		const YOY: Record<string, number> = {};
		Object.keys(result.today[0]).forEach((key) => {
			// 判断昨日同期与今日数据是否有相同的键
			if (key in result.yesterday[0] && key !== 'date_time') {
				const compareValue =
					((result.today[0][key] - result.yesterday[0][key]) /
						result.yesterday[0][key]) *
					100;
				YOY[key] = compareValue;
			}
		});
		setComparisonData(YOY);
		let compare = compareArrays(todayData, yesterdayData);
		const data = lineTitle.map((item, index) => {
			return {
				name: item.name,
				max: result.today[0][item.key],
				num: result.today[0][item.key].toFixed(2),
				percentage: result[item.rateKey] * 100,
				compare: compare[index],
			};
		});

		let option = {
			//backgroundColor: '#031a40',
			//color: ['#000'],
			tooltip: {
				show: false,
				trigger: 'item',
			},
			radar: {
				center: ['58%', '50%'],
				radius: '79%',
				name: {
					color: '#fff',
					padding: [-40, -25, -24, -20],
					nameGap: 8,
					formatter: function (value, indicator) {
						let i = contains(data, value);
						let f = '';
						if (data[i].compare == '大于') {
							f = 'd';
							if (data[i].name === '在线车辆') {
								return (
									'{a|' +
									value +
									'} ' +
									'{c|' +
									data[i].percentage.toFixed(1) +
									'%' +
									`}{${f}|}`
								);
							} else {
								return (
									'{a|' +
									value +
									'}\n' +
									'{c|' +
									data[i].percentage.toFixed(1) +
									'%' +
									`}{${f}|}`
								);
							}
						} else if (data[i].compare == '小于') {
							f = 'e';
							if (data[i].name === '在线车辆') {
								return (
									'{a|' +
									value +
									'} ' +
									'{c2|' +
									data[i].percentage.toFixed(1) +
									'%' +
									`}{${f}|}`
								);
							} else {
								return (
									'{a|' +
									value +
									'}\n' +
									'{c2|' +
									data[i].percentage.toFixed(1) +
									'%' +
									`}{${f}|}`
								);
							}
						}
						// return (
						//   '{a|' +
						//   value +
						//   '}\n{b|' +
						//   data[i].num +
						//   '}{c|' +
						//   data[i].percentage.toFixed(2) +
						//   '%' +
						//   `}{${f}|}`
						// )
					},

					rich: {
						a: {
							color: '#D8F1FF',
							fontSize: 12,
							padding: [6, -5, 0, -5],
						},
						b: {
							color: '#D8F1FF',
							fontSize: 12,
						},
						c: {
							color: '#FF0000',
							fontSize: 12,
							padding: [5, 0, 0, 6],
						},
						c2: {
							color: '#21A445',
							fontSize: 12,
							padding: [5, 0, 0, 6],
						},
						d: {
							width: 15,
							height: 15,
							backgroundColor: {
								image: up,
							},
						},
						e: {
							width: 15,
							height: 15,
							backgroundColor: {
								image: down,
							},
						},
					},
				},
				nameGap: 30,
				indicator: [
					{
						name: '在线车辆',
						max: Math.max(todayData[0], yesterdayData[0]) * 1.3,
					},
					{
						name: '总排放（千克）',
						max: Math.max(todayData[1], yesterdayData[1]) * 1.3,
					},
					{
						name: '总里程（万公里）',
						max: Math.max(todayData[2], yesterdayData[2]) * 1.3,
					},
					{
						name: '总油耗（吨）',
						max: Math.max(todayData[3], yesterdayData[3]) * 1.3,
					},
					{
						name: '排放因子(克/公里)',
						max: Math.max(todayData[4], yesterdayData[4]) * 1.3,
					},
				],
				//indicator:indicator,
				splitArea: {
					areaStyle: {
						color: [
							'rgba(10, 54, 102, 0.8)',
							'rgba(10, 54, 102, 0.8)',
							'rgba(10, 54, 102, 0.1)',
							'rgba(10, 54, 102, 0.8)',
							'rgba(10, 54, 102,0.1)',
						],
					},
				},
				// axisLabel:{//展示刻度
				//     show: true
				// },
				axisLine: {
					//指向外圈文本的分隔线样式
					lineStyle: {
						color: 'rgba(51,135,184,0.5)',
						width: 2,
					},
				},
				splitLine: {
					lineStyle: {
						width: 2,
						color: 'rgba(51,135,184,0.5)',
					},
				},
			},

			series: [
				{
					name: 's1',
					type: 'radar',
					tooltip: {
						trigger: 'item',
					},
					data: [
						{
							value: yesterdayData,
						},
					],
					symbol: 'circle',
					symbolSize: 8,
					itemStyle: {
						normal: {
							color: 'rgba(227, 160, 124, 0.8)',
							borderColor: 'rgba(227, 160, 124,0.2)',
							borderWidth: 0,
						},
					},
					areaStyle: {
						normal: {
							color: 'rgba(227, 160, 124,0.5)',
						},
					},
					lineStyle: {
						normal: {
							type: 'dashed',
							color: '#E3A07C',
							width: 1,
							curveness: 0.8,
						},
					},
					z: 3,
				},
				{
					name: 's1',
					type: 'radar',
					tooltip: {
						trigger: 'item',
					},
					data: [
						{
							value: todayData,
						},
					],
					symbol: 'circle',
					symbolSize: 6,
					smooth: true,
					itemStyle: {
						normal: {
							color: 'rgba(92, 225, 169,0.8)',
							borderColor: 'rgba(92, 225, 169,0.2)',
							borderWidth: 0,
						},
					},
					areaStyle: {
						normal: {
							color: 'rgba(92, 225, 169,0.5)',
						},
					},
					lineStyle: {
						normal: {
							type: 'dashed',
							color: 'rgba(92, 225, 169, 1)',
							width: 1,
						},
					},
					z: 3,
				},
			],
		};

		setOption(option);
	};

	return (
		<Box title="全市活动水平">
			<LayoutContainer>
				<div className="left-container">
					<DailyActivityLevelData
						column={column}
						yesterdayData={yesterdayData2}
						todayData={todayData2}
						comparisonData={comparisonData}
					/>
				</div>

				<div className="right-container">
					<div className="echarts">
						{option ? <Echarts option={option}></Echarts> : null}

						<div className="legend-container">
							{' '}
							{legend.map((it, i) => {
								return (
									<div className="legend-item" key={i}>
										<span style={{ background: it.color }}></span>{' '}
										<span>{it.name}</span>
									</div>
								);
							})}
						</div>
					</div>
				</div>
			</LayoutContainer>
		</Box>
	);
};
export default DailyActivityLeve;
