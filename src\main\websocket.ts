/*
 * @Descripttion:
 * @version:
 * @Author: Gqq
 * @Date: 2024-07-31 14:39:34
 */
import { EventEmitter } from 'events';
import WebSocket from 'ws';
import { isEmpty } from 'lodash';
import { messageList, vinList } from './data';
const host = import.meta.env.MAIN_VITE_API_DOMAIN;

enum WebsocketStatus {
	CONNECTING, // 0正在连接
	OPEN, // 1连接成功，可以通信了
	CLOSING, // 2连接正在关闭
	CLOSED, // 3连接已关闭，或者打开连接失败
}
const getStatus = (status: WebsocketStatus) => {
	switch (status) {
		case WebsocketStatus.CONNECTING:
			return '0: 正在连接';
		case WebsocketStatus.OPEN:
			return '1: 连接成功，可以通信了';
		case WebsocketStatus.CLOSING:
			return '2: 连接正在关闭';
		case WebsocketStatus.CLOSED:
			return '3: 连接已关闭，或者打开连接失败';
		default:
			return '未知状态';
	}
};

const fs = require('fs');
export class Websocket {
	private url: string = `ws://${host}/ws/real_time_online_data_from_redis`;
	private ws: WebSocket;
	eventEmitter: EventEmitter;
	VINList: Array<any> = [];
	timeout: any = null;
	fetchDataTimer: any = null;
	heartbeatTimer: any = null;
	jsonData: any;
	token: string;
	attachable: boolean;
	vehicleState: string;
	constructor({ jsonData = null, token = '' }) {
		this.jsonData = jsonData;
		this.token = token;
		this.eventEmitter = new EventEmitter();
		this.attachable = true;
		this.vehicleState = 'onLine';
		this.ws = new WebSocket(this.url);
		if (this.token !== '') this.connect();
	}

	actionTime = () => {
		if (this.timeout) {
			clearInterval(this.timeout);
			this.timeout = null;
		}
		this.timeout = setInterval(() => {
			console.log('!!!!!!!!!===data:VINList?????', vinList.length);
			this.eventEmitter.emit('data:VINList', vinList);
			// 推送给 data-handler
			this.eventEmitter.emit('data:websocket', messageList);
		}, 16000);
	};

	startHeartbeat() {
		if (this.heartbeatTimer) {
			clearInterval(this.heartbeatTimer);
			this.heartbeatTimer = null;
		}
		this.heartbeatTimer = setInterval(() => {
			if (this.ws.readyState === WebsocketStatus.OPEN) {
				this.ws.send(JSON.stringify({ msg: 'ping' }));
			}
		}, 2000);
	}

	startFetchData() {
		if (this.fetchDataTimer) {
			clearInterval(this.fetchDataTimer);
			this.fetchDataTimer = null;
		}
		this.fetchDataTimer = setInterval(() => {
			if (this.ws.readyState === WebsocketStatus.OPEN) {
				let nums = 190009;
				let times = 2;
				if (this.jsonData?.carNumber) nums = this.jsonData?.carNumber;
				if (this.jsonData?.onlineTime) times = this.jsonData?.onlineTime;
				this.ws.send(JSON.stringify({ msg: 'vin', nums, times }));
			}
		}, 7000);
	}

	async connect() {
		// return
		console.log('-----current status', getStatus(this.ws.readyState));
		if (this.ws.readyState !== WebsocketStatus.OPEN) {
			// console.log('-----connect websocket')
			this.startHeartbeat();
			this.startFetchData();
			await this.getKeys();
		}
	}

	reconnection({ vehicleState = 'onLine' }) {
		// this.connectAgain()
		this.vehicleState = vehicleState;
	}

	async connectAgain() {
		// this.actionTime()
		// return
		console.log('-----connectAgain', getStatus(this.ws.readyState));
		if (
			this.ws.readyState !== WebsocketStatus.OPEN &&
			this.ws.readyState !== WebsocketStatus.CONNECTING
		) {
			console.log('---connectAgain!!!--connect again websocket');
			this.VINList = [];
			this.attachable = true;
			this.startHeartbeat();
			this.startFetchData();
			this.ws = new WebSocket(this.url);
			await this.getKeys();
		}
	}

	async disconnect() {
		console.log(
			'???? disconnect !!!-----current status',
			getStatus(this.ws.readyState),
		);
		if (
			this.ws.readyState !== WebsocketStatus.CLOSED &&
			this.ws.readyState !== WebsocketStatus.CLOSING
		) {
			// console.log('-----close websocket')
			if (this.timeout) {
				clearInterval(this.timeout);
				this.timeout = null;
			}
			if (this.heartbeatTimer) {
				clearInterval(this.heartbeatTimer);
				this.heartbeatTimer = null;
			}
			if (this.fetchDataTimer) {
				clearInterval(this.fetchDataTimer);
				this.fetchDataTimer = null;
			}
			this.attachable = false;
			await this.ws.close();
		}
	}

	async getKeys() {
		this.ws.onmessage = (event) => {
			if (typeof event.data === 'string') {
				try {
					const all = JSON.parse(event.data);

					if (all.code != '2000') return;
					const list = all.result.map(
						(item: any) => `${item.VIN}|${item.type}|${item.lon}|${item.lat}`,
					);
					// const list = all.result.flatMap(item =>
					//   item.type === '货车'
					//     ? [`${item.VIN}|${item.type}|${item.lon}|${item.lat}`]
					//     : []
					// );
					if (isEmpty(this.VINList)) {
						this.VINList = list;
						this.eventEmitter.emit('data:VINList', this.VINList);
					}
					// 推送给 data-handler
					this.eventEmitter.emit('data:websocket', all.result);
				} catch (error) {}
			}
		};

		this.ws.onerror = (err) => {
			console.log('ws err >>>', err);
		};

		this.ws.onclose = (e) => {
			console.log('ws close <<<');
			if (this.attachable === true) {
				this.connectAgain();
			} else {
				if (this.timeout) {
					clearInterval(this.timeout);
					this.timeout = null;
				}
				if (this.heartbeatTimer) {
					clearInterval(this.heartbeatTimer);
					this.heartbeatTimer = null;
				}
				if (this.fetchDataTimer) {
					clearInterval(this.fetchDataTimer);
					this.fetchDataTimer = null;
				}
			}
		};
	}

	writeToFile = (...data) => {
		// fs追加数据到文件
		fs.appendFile(
			'errorData.json',
			`${JSON.stringify(data)}\n`,
			function (err: any) {
				if (err) {
					return console.error(err);
				}
			},
		);
	};
}
