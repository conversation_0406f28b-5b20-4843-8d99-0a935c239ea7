import { useEffect, useState } from 'react';
import styled from 'styled-components';
import Echarts from '@renderer/components/echarts';

export const Container = styled.div<Props>`
	width: ${(props) => props.width || '100%'};
	height: ${(props) => props.height || '215px'};
`;

type DataArr = {
	name?: string;
	value?: number;
	itemStyle?: object;
};

type EchartsData = {
	value: number;
	name: string;
	rate: number;
};

type Props = {
	width?: string;
	height?: string;
	data: EchartsData[];
	colorList?: Array<string>;
};

const IndustryAanalysisEcharts = (props) => {
	const { right, data, colorList } = props;
	// const [angle, setAngle] = useState<number>(270)
	// const [isShow, setIsShow] = useState<boolean>(false)
	// const [data, setData] = useState<EchartsData[]>([
	//   {
	//     value: 0,
	//     name: '货车',
	//     rate: 0
	//   },
	//   {
	//     value: 0,
	//     name: '客车',
	//     rate: 0
	//   },
	//   {
	//     value: 0,
	//     name: '公交',
	//     rate: 0
	//   }
	// ])
	// const _pie = () => {
	//   const dataArr: DataArr[] = []
	//   for (let i = 0; i < 8; i++) {
	//     if (i % 2 === 0) {
	//       dataArr.push({
	//         name: (i + 1).toString(),
	//         value: 60,
	//         itemStyle: {
	//           color: '#00FF9F',
	//           borderWidth: 0,
	//           borderColor: 'rgb(2,9,18)'
	//         }
	//       })
	//     } else {
	//       dataArr.push({
	//         name: (i + 1).toString(),
	//         value: 10,
	//         itemStyle: {
	//           color: 'rgb(2,9,18,0)',
	//           borderWidth: 0,
	//           borderColor: 'rgb(2,9,18)'
	//         }
	//       })
	//     }
	//   }
	//   return dataArr
	// }

	const option = {
		legend: {
			show: true,
			type: 'scroll',
			orient: 'vertical',
			//right: 0,
			right,
			height: 145,
			icon: 'circle',
			itemWidth: 14,
			itemHeight: 14,
			top: 25,
			bottom: 30,
			data,
			pageTextStyle: {
				color: '#fff',
			},
			pageIconColor: '#fff',
			pageIconInactiveColor: '#ccc',
			textStyle: {
				color: '#ffffff',
			},
		},
		tooltip: {
			trigger: 'item',
			formatter: (params) => {
				return params.name + ' ' + params.data.num + ' ' + params.value + '%';
			},
		},
		series: [
			{
				type: 'pie',
				zlevel: 1,
				startAngle: 90,
				radius: ['56%', '92%'],
				label: { show: false },
				labelLine: { show: false },
				animation: true,
				animationDuration: 800,
				avoidLabelOverlap: false,
				data,
				right: '35%',
				top: '5%',
				bottom: '10%',
				itemStyle: {
					borderRadius: 7,
					borderColor: 'rgb(9,36,63,0.5)',
					borderWidth: 2,
				},
			},
			// {
			//   type: 'pie',
			//   zlevel: 2,
			//   silent: true,
			//   startAngle: angle,
			//   radius: ['55%', '56%'],
			//   label: { show: false },
			//   labelLine: { show: false },
			//   data: _pie(),
			//   animation: true,
			//   right: '50%'
			// },
			// {
			//   type: 'pie',
			//   zlevel: 3,
			//   silent: true,
			//   startAngle: angle - 30 > 0 ? angle - 30 : angle + 330,
			//   radius: ['50%', '51%'],
			//   label: { show: false },
			//   labelLine: { show: false },
			//   data: _pie(),
			//   animation: true,
			//   right: '50%'
			// },
			// {
			//   type: 'pie',
			//   zlevel: 4,
			//   silent: true,
			//   startAngle: angle,
			//   radius: ['45%', '44%'],
			//   label: { show: false },
			//   labelLine: { show: false },
			//   data: _pie(),
			//   animation: true,
			//   right: '50%'
			// }
		],

		color: [
			'#00E6FF',
			'#E3A07C',
			'#A5C86A',
			'#FE9903',
			'#61C6FF',
			'#00FF96',
			'#85A9FF',
			'#CCCC33',
			'#ad4e00',
		],
		// animationDelayUpdate: function (idx) {
		//   // 越往后的数据延迟越大
		//   return idx * 100
		// }
	};

	useEffect(() => {}, [data]);

	return (
		<Container {...props}>
			<Echarts option={option}></Echarts>
		</Container>
	);
};
export default IndustryAanalysisEcharts;
