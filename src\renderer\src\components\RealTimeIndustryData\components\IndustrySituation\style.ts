import styled from 'styled-components';
import returnIcon from '@renderer/images/icon/return.png';

const IndustrySituationStyled = styled.div`
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	.industrySituationOptionTitle {
		width: 35%;
		height: 100%;
		display: flex;
		flex-direction: column;
		color: #fff;
		font-weight: 500;
		font-size: 18px;
		.tit {
			width: 100%;
			height: 30px;
			line-height: 30px;
			display: flex;
			justify-content: space-around;
		}
		.close {
			width: 26px;
			height: 26px;
			text-align: center;
			background-image: url(${returnIcon});
			background-repeat: no-repeat;
			background-size: 100%;
			border-radius: 10px;
			transition: all 0.3s;
			cursor: pointer;
			&:hover {
				background-color: rgba(0, 0, 0, 0.6);
			}
		}
	}
	.industrySituationOptionContent {
		width: 63%;
		height: 100%;
		display: flex;
		justify-content: space-between;
		.list-li {
			height: 100px;
			display: inline-block;
			max-width: 100%;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			text-align: center;
		}
		/* .ant-table-wrapper {
      margin-bottom: 0;
    } */
		/* .ant-table-cell {
      width: 80px !important;
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table-thead > tr > th {
      padding: 0;
      text-align: center;
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table-tbody > tr > td {
      padding: 0;
      text-align: center;
    }
    .ant-pagination {
      display: none;
    }
    .ant-table-wrapper {
      width: 108%;
      height: 100%;
      margin-left: auto;
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table {
      background: linear-gradient(
        to right,
        rgba(11, 41, 69, 0.8),
        rgba(15, 51, 74, 0.8)
      ) !important;
      border-radius: 5px;
      border: 0.1px solid rgba(9, 132, 182, 0.7);
    }
    .ant-table-thead {
      border: 1px solid rgba(9, 132, 182, 0.7);
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table-thead > tr > th {
      border: 1px solid rgba(9, 132, 182, 0.7) !important;
      background: linear-gradient(
        to right,
        rgba(11, 41, 69, 0.8),
        rgba(15, 51, 74, 0.8)
      ) !important;
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table-tbody > tr > th,
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table-tbody > tr > td {
      border: 1px solid rgba(9, 132, 182, 0.7);
    }
    .ant-table-content table {
      border-collapse: collapse;
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper
      .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(
        .ant-table-row-expand-icon-cell
      ):not([colspan])::before {
      background-color: transparent;
    }
    .ant-table-cell-row-hover {
      background-color: rgba(15, 51, 74, 1) !important;
    } */
		/* :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper table {
      min-height: 210px;
    } */
		/* .ant-table-body {
      &::-webkit-scrollbar {
        height: 12px;
        width: 2px;
        overflow-y: auto;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 5px;
        background: #939392;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: 0;
        border-radius: 0;
        background: #000;
      }
    } */
	}
`;

export default IndustrySituationStyled;
