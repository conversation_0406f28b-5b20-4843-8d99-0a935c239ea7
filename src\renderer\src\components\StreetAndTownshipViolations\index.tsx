// 街乡镇违规
import * as React from 'react';
import { useState, useEffect } from 'react';

import LayoutContainer from './style';
import Box from '../../baseUI/Box';
import LevelTwoTitle from '@renderer/components/LevelTwoTitle';
import RegistryAreaMap from '@renderer/components/StreetAndTownMap';
import ScrollListWidthTitle from '@renderer/components/ScrollListWithTitle';
import TimeTypeRadio from '@renderer/baseUI/TimeTypeRadio2';
import {
	getViolationRegisterTownList,
	getViolationTownList,
} from '@renderer/api';
import { useAuth } from '@renderer/hooks';

const sameColumn = [
	{
		align: 'center',
		dataIndex: 'rank_desc',
		key: 'rank_desc',
		width: '10%',
		title: '排名',
	},
	{
		align: 'center',
		dataIndex: 'adm_name',
		width: '20%',
		key: 'adm_name',
		title: '街乡镇',
	},
	{
		align: 'center',
		dataIndex: 'name',
		key: 'name',
		title: '所属区',
		width: '40%',
	},
];
const column = [
	...sameColumn,
	{
		align: 'center',
		dataIndex: 'count',
		key: 'count',
		width: '30%',
		title: '违规数量',
	},
];

const VehicleViolations = () => {
	const violatioData = [
		{
			num: '12',
			region: '海淀',
			street: '清水镇',
			rank: '1',
		},
		{},
		{},
		{},
		{},
	];
	const [data, setDate] = useState<any>(null);
	const [dataCreate, setDateCreate] = useState<any>(violatioData); // 行驶地违规数据
	const [dataRegister, setDateRegister] = useState<any>(violatioData); // 注册地违规数据
	const { token } = useAuth();

	let optionTable = {
		columns: column,
		width: '240px',
		height: '160px',
		fontSize: 12,
		thclassname: 'th_row',
		tablebgcolor: 'rgba(9,30,59,0)',
		trheight: '30px',
		thheight: '40px',
		customwidth: true,
		rowbgcolor: ['rgba(58,218,255,0.2);', 'rgba(9, 30, 47, 0.29)'],
		thbgcolor: '#081F38',
	};

	const [timeData, setTimeData] = useState<null | {
		start_time: string;
		end_time: string;
		timeType: string;
	}>(null);

	useEffect(() => {
		if (!timeData) return;
		const req = {
			start_time: timeData.start_time,
			end_time: timeData.end_time,
			time_type: timeData.timeType,
			token,
		};
		getViolationTownList(req).then((res) => {
			setDateCreate(res);
		});
		getViolationRegisterTownList(req).then((res) => {
			setDateRegister(res);
			let arr: [] = res as any;
			let data = arr.map((item: any, index) => {
				let color;
				let num: number = Number(item.count);
				// 30以下：绿色 30-60：黄色 60-90：橙色 90-120：红色 120以上：绿色
				if (num < 30) {
					color = 'rgba(58, 255, 74, 1)';
				} else if (num >= 30 && num < 60) {
					color = 'rgba(255,255,0,1)';
				} else if (num >= 60 && num < 90) {
					color = 'rgba(255,165,0,1)';
				} else if (num >= 90 && num < 120) {
					color = 'rgba(255,0,0,1)';
				} else if (num >= 120) {
					color = 'rgba(58,218,255,1)';
				}

				return {
					name: item.adm_name,
					itemStyle: {
						areaColor: color,
					},
				};
			});
			setDate(data);
		});
	}, [timeData]);

	return (
		<Box
			title="街乡镇违规"
			titlewidth="95%"
			height="100%"
			subTitle={
				<div style={{ marginRight: '50px' }}>
					<TimeTypeRadio
						defaultValue="1"
						timeType={(data) => setTimeData(data)}
					/>
				</div>
			}
		>
			<LayoutContainer>
				<div className="left-container">
					<div className="register-area">
						<div className="map">
							<RegistryAreaMap
								id="streetViolations"
								regions={data}
							></RegistryAreaMap>
						</div>
					</div>
				</div>
				<div className="middle-container">
					<LevelTwoTitle title="行驶地违规TOP"></LevelTwoTitle>
					<div className="table">
						<ScrollListWidthTitle data={dataCreate} {...optionTable} />
					</div>
				</div>
				<div className="right-container">
					<LevelTwoTitle title="注册地违规TOP"></LevelTwoTitle>
					<div className="table">
						<ScrollListWidthTitle data={dataRegister} {...optionTable} />
					</div>
				</div>
			</LayoutContainer>
		</Box>
	);
};
export default VehicleViolations;
