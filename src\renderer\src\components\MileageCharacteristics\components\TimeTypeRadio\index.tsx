import React, { useEffect, useState } from 'react';
import { Button, Radio } from 'antd';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

// import { TimeTypeRadioContent, LabelContent } from './style'
import { TimeTypeRadioContent } from './style';
import { timeFormat } from '@renderer/utils';
import CustomPopUps from '@renderer/baseUI/TimeTypeRadioCustom/components/CustomPopUps';
type TimeTypeProps = 'year' | 'month' | 'date';

type Props = {
	defaultValue: TimeTypeProps;
	onTimeTypeChange: Function;
	typeOptions: Array<any>;
};

export default function TimeSelect({
	defaultValue,
	onTimeTypeChange,
	typeOptions,
}: Props) {
	const [type, setType] = useState<TimeTypeProps>(defaultValue);
	const [defaultType, setDefaultType] = useState(defaultValue || 'year');

	const timeChange = (newTime, type) => {
		if (!newTime) return;
		let time;
		switch (type) {
			case 'year':
				time =
					dayjs(newTime).get('year') == dayjs().get('year')
						? {
								start_time: dayjs().format(timeFormat.year),
								end_time: dayjs().format(timeFormat.now),
								timeType: 'year',
						  }
						: {
								start_time: dayjs(newTime).format(timeFormat.year),
								end_time: dayjs(newTime)
									.add(1, 'year')
									.startOf('year')
									.format(timeFormat.now),
								timeType: 'year',
						  };
				break;
			case 'month':
				time = dayjs(newTime).isAfter(dayjs().startOf('month'))
					? {
							start_time: dayjs().subtract(1, 'month').format(timeFormat.month),
							end_time: dayjs().subtract(1, 'month').format(timeFormat.now),
							timeType: 'month',
					  }
					: {
							start_time: dayjs(newTime).format(timeFormat.month),
							end_time: dayjs(newTime)
								.add(1, 'month')
								.startOf('month')
								.format(timeFormat.now),
							timeType: 'month',
					  };
				break;
			case 'date':
				time = dayjs(newTime).isAfter(dayjs().startOf('d'))
					? {
							start_time: dayjs().subtract(1, 'day').format(timeFormat.day),
							end_time: dayjs().subtract(1, 'day').format(timeFormat.now),
							timeType: 'date',
					  }
					: {
							start_time: dayjs(newTime).format(timeFormat.day),
							end_time: dayjs(newTime)
								.add(1, 'd')
								.startOf('d')
								.format(timeFormat.now),
							timeType: 'date',
					  };
				break;
			default:
				time = null;
				break;
		}
		onTimeTypeChange(time, type == 'date' ? 'day' : type);
		if (newTime.target !== undefined) {
			setType(newTime.target.value);
			setDefaultType(newTime.target.value);
		}
	};

	useEffect(() => {
		//@ts-ignore
		timeChange(dayjs(), type);
	}, [type]);

	return (
		<TimeTypeRadioContent>
			<Radio.Group
				options={typeOptions}
				onChange={(value) => timeChange(value, type)}
				value={defaultType}
				optionType="button"
			/>
		</TimeTypeRadioContent>
	);
}
