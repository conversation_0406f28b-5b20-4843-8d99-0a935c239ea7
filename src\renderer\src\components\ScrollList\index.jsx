import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';

const ScrollListUI = styled.div`
	overflow: hidden;
	height: 100%;
	.list-content {
		width: 100%;
		height: 100%;
		overflow-y: auto;
	}
	.list-content::-webkit-scrollbar {
		display: none;
		/* width:3px;
      height: 100px;
      background: rgba(1,226,255,0.5);
      border-radius: 10px; */
	}

	.list-ul {
		height: 23px;
		line-height: 23px;
		display: flex;
		justify-content: space-around;
		margin-left: -1px;
		border-right: 1px solid #3488b9;
		border-bottom: 1px solid #3488b9;
		.list-li {
			flex: 1;
			text-align: center !important;
			cursor: pointer;
			color: #d8f1ff;
			font-size: 16px;
			// border-right: 1px solid #3488b9;
			overflow: hidden; //超出的文本隐藏
			text-overflow: ellipsis; //用省略号显示
			white-space: nowrap; //不换行
		}
	}
	.list-ul:nth-child(2n) {
		background: rgba(58, 218, 255, 0.2);
	}
`;

export default function ScrollList(props) {
	const scrollBox = useRef();
	const common1 = useRef();
	const common2 = useRef();
	const timer = useRef(null);
	const { data, column, onliClick } = props;

	useEffect(() => {
		clearInterval(timer.current);
		scrollBox.current.scrollTop = 0;
		roll(60);
		return () => {
			if (common2 && common2.current) {
				common2.current.innerHTML = '';
			}
		};
	}, [props.data]);

	const roll = (t) => {
		if (scrollBox.current.clientHeight > common1.current.clientHeight) {
			return;
		}
		common2.current.innerHTML = common1.current.innerHTML;
		timer.current = setInterval(rollStart, t);
		// 鼠标移入div时暂停滚动
		scrollBox.current.onmouseover = function () {
			clearInterval(timer.current);
		};
		// 鼠标移出div后继续滚动
		scrollBox.current.onmouseout = function () {
			clearInterval(timer.current);
			if (scrollBox.current.clientHeight < common1.current.clientHeight) {
				timer.current = setInterval(rollStart, t);
			}
		};
	};
	// 开始滚动函数
	const rollStart = () => {
		if (!scrollBox.current) return;
		if (scrollBox.current.scrollTop >= common1.current.offsetHeight) {
			scrollBox.current.scrollTop = 0;
		} else {
			scrollBox.current.scrollTop = scrollBox.current.scrollTop + 2;
		}
	};
	return (
		<ScrollListUI ref={scrollBox} className="bg-color-1">
			<div ref={common1} className="list-content">
				{props.data !== undefined &&
					props.data.map((ele, index) => {
						if (!ele) return null;
						else
							return (
								<ul key={index} style={{}} className="list-ul">
									{column.map((item) => {
										if (item.render) {
											return item.render(ele[item.key]);
										}
										return (
											<li
												key={item.key}
												title={ele[item.dataIndex]}
												className="list-li"
												onClick={() => onliClick(ele)}
											>
												{ele[item.dataIndex]}
											</li>
										);
									})}
								</ul>
							);
					})}
			</div>
			<div ref={common2} className="list-content"></div>
		</ScrollListUI>
	);
}
