import React from 'react';

import <PERSON>roll<PERSON>ist from './ScrollList';
import { Props } from './type';
import { StyledContainer } from './style';

function WidthTitle(props: Props) {
	const { columns, data, height } = props;

	// if (data?.length % 2 == 1) data.push({});

	const OnSelect = (data) => {
		if (!props.OnRowSelection) return;
		else {
			props.OnRowSelection(data);
		}
	};

	const OnSelectColumn = (data) => {
		if (!props.OnRowSelection) return;
		else {
			props.OnRowSelection(data);
		}
	};

	return (
		<StyledContainer {...props}>
			{props.showheader && (
				<ul
					className={
						props.thclassname
							? 'list-ul-title ' + props.thclassname
							: 'list-ul-title'
					}
				>
					{columns.map((item) => {
						if (item.titleRender) {
							return (
								<li className="titleLine2 title-li" key={item.dataIndex}>
									<> {item.titleRender()}</>
								</li>
							);
						} else
							return (
								<li
									key={item?.dataIndex}
									className="title-li"
									onClick={() => OnSelectColumn(item)}
								>
									{item?.title}
								</li>
							);
					})}
				</ul>
			)}
			{props.stickyTop &&
				props.stickyTopData !== undefined &&
				Array.isArray(props.stickyTopData) &&
				props.stickyTopData?.map((ele, index) => {
					return (
						<ul
							key={index}
							className={
								props.stickyTopClassName
									? 'stickyTop-ul ' + props.stickyTopClassName
									: 'stickyTop-ul'
							}
							onClick={() => OnSelect(ele)}
						>
							{columns.map((item) => {
								const { titleTips, dataIndex } = item;
								if (item.render) {
									return (
										<li
											key={dataIndex}
											title={titleTips ? ele[titleTips] : ele[dataIndex]}
											className="stickyTop-li"
										>
											<> {item.render(ele[dataIndex], ele)}</>
										</li>
									);
								} else
									return (
										<li
											key={dataIndex}
											title={titleTips ? ele[titleTips] || ele[dataIndex] : ''}
											className="stickyTop-li"
										>
											{ele[dataIndex]}
										</li>
									);
							})}
						</ul>
					);
				})}
			<div className="eventTable">
				<ScrollList
					{...props}
					columns={columns}
					data={data}
					key={columns?.length + '-' + data?.length + height}
					OnRowSelection={props.OnRowSelection}
					autoscroll={props.autoscroll}
				/>
			</div>
		</StyledContainer>
	);
}

WidthTitle.defaultProps = {
	width: '100%',
	height: '100%',
	thheight: '60px',
	trheight: '60px',
	showheader: true,
	autoscroll: true,
	scroll: true,
	fontSize: 18,
	fontWeight: 400,
	color: 'white',
};

export default WidthTitle;
