import { styled } from 'styled-components';
import search from '@renderer/images/search/search.png';
import bg1 from '@renderer/images/search/bg1.png';
import bg2 from '@renderer/images/search/bg2.png';

export default styled.div`
	position: absolute;
	top: 80px;
	right: 130px;
	/* right: 1600px; */
	z-index: 99;
	width: 367px;
	height: 80px;
	background: linear-gradient(
		180deg,
		rgba(7, 12, 18, 0.64) 0%,
		rgba(51, 82, 112, 0.54) 100%
	);
	box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.5);
	border-radius: 4px;
	border: 1px solid;
	border-image: linear-gradient(
			269deg,
			rgba(58, 218, 255, 1),
			rgba(93, 196, 255, 1),
			rgba(175, 234, 255, 1),
			rgba(90, 191, 255, 1),
			rgba(58, 218, 255, 1)
		)
		1 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 20px;
	.vinSearch {
		/* width: 238px; */
		height: 40px;
		display: flex;
		align-items: center;
		/* background-image: url(${bg2}); */
		background-repeat: no-repeat;
		background-size: 100% 100%;
		flex: 1;
		background-color: #0069a5;
		border-radius: 40px;
		.ant-input-search :where {
			background-color: transparent !important;
		}
		.ant-input-affix-wrapper {
			border: 0 !important;
		}
		.ant-input {
			background-color: transparent !important;
			font-size: 20px !important;
		}
		.ant-btn-default {
			background-color: transparent !important;
			border: 0 !important;
		}
		.ant-input-affix-wrapper {
			background-color: transparent !important;
		}
		.ant-input-group-addon {
			background-color: transparent !important;
		}
		.ant-input-search-button {
			background-image: url(${search}) !important;
			background-repeat: no-repeat !important;
			background-size: 80% 80% !important;
			background-position: center center !important;
			svg {
				display: none;
			}
		}
	}
	.moreSearch {
		display: none;
		margin-left: 10px;
		width: 79px;
		height: 40px;
		font-size: 20px;
		font-family: AlibabaPuHuiTi_2_55_Regular;
		color: #cbf5ff;
		line-height: 28px;
		/* display: flex; */
		justify-content: center;
		align-items: center;
		cursor: pointer;
		background-image: url(${bg1});
		background-repeat: no-repeat;
		background-size: cover;
	}
`;
