import { useEffect, useState } from 'react';
import Echarts from '@renderer/components/echarts';
import { useAuth } from '@renderer/hooks/useAuth';
import Box from '../../baseUI/Box';
import { Container } from './style';

export default function IndustryEmissionStageMileageCharacteristics() {
	const { token } = useAuth();
	const [option1, setOption1] = useState({});
	const [option2, setOption2] = useState({});

	useEffect(() => {
		let res = {
			times: [
				'1月',
				'2月',
				'3月',
				'4月',
				'5月',
				'6月',
				'7月',
				'8月',
				'9月',
				'10月',
				'11月',
				'12月',
			],
			echartsData1: [
				{
					name: '货车',
					value: [12, 13, 10, 14, 10, 20, 21, 10, 14, 10, 20, 21],
				},
				{
					name: '渣土车',
					value: [22, 8, 1, 20, 10, 10, 10, 10, 14, 10, 20, 21],
				},
				{
					name: '工程车',
					value: [15, 2, 20, 4, 10, 10, 11, 10, 14, 10, 20, 21],
				},
				{
					name: '公交车',
					value: [10, 2, 2, 5, 10, 10, 11, 10, 14, 10, 20, 21],
				},
				{
					name: '其他客车',
					value: [20, 12, 20, 15, 10, 10, 11, 10, 14, 10, 2, 2],
				},
				{
					name: '环卫车',
					value: [10, 12, 20, 15, 10, 30, 11, 10, 14, 10, 5, 7],
				},
				{
					name: '其他用途',
					value: [10, 32, 20, 15, 10, 2, 1, 10, 4, 10, 5, 7],
				},
			],
			echartsData2: [
				{
					name: '国六',
					value: [62, 13, 80, 14, 10, 20, 21, 10, 14, 10, 20, 21],
				},
				{
					name: '国五',
					value: [22, 8, 1, 20, 90, 10, 10, 50, 14, 10, 20, 21],
				},
			],
		};
		let series1: any = res?.echartsData1?.map((item) => {
			return {
				name: item.name,
				type: 'bar',
				stack: 'Ad',
				emphasis: {
					focus: 'series',
				},
				data: item.value,
			};
		});
		let series2: any = res?.echartsData2?.map((item) => {
			return {
				name: item.name,
				color: getColor(item.name),
				type: 'bar',
				stack: 'Ad',
				emphasis: {
					focus: 'series',
				},
				data: item.value,
			};
		});
		let option1 = {
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'shadow',
				},
			},
			legend: {
				right: '0%',
				orient: 'vertical',
				bottom: '5%',
				itemWidth: 10,
				itemHeight: 10,
				textStyle: {
					color: '#E2F0FF',
					fontSize: 12,
				},
			},
			grid: {
				left: '0',
				right: '20%',
				bottom: '0',
				top: '24%',
				containLabel: true,
			},
			xAxis: [
				{
					type: 'category',
					axisLabel: {
						color: '#E2F0FF',
						fontSize: 14,
					},
					data: res.times,
				},
			],
			yAxis: [
				{
					type: 'value',
					max: 100,
					name: '不同行业里程占比（%）',
					// nameLocation: 'middle',
					nameTextStyle: {
						color: '#fff',
						padding: [0, 0, 5, 300],
						fontSize: 16,
					},
					splitLine: {
						show: false,
					},
					axisLabel: {
						fontSize: 14,
						color: '#E2F0FF',
						// formatter: '{value}%'
					},
				},
			],
			series: series1,
		};
		let option2 = {
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'shadow',
				},
			},
			legend: {
				right: '0',
				orient: 'vertical',
				bottom: '5%',
				itemWidth: 10,
				itemHeight: 10,
				textStyle: {
					color: '#E2F0FF',
					fontSize: '14',
				},
			},
			grid: {
				left: '6%',
				right: '16%',
				bottom: '0',
				top: '30%',
				containLabel: true,
			},
			xAxis: [
				{
					type: 'category',
					axisLabel: {
						color: '#E2F0FF',
						fontSize: 14,
					},
					data: res.times,
				},
			],
			yAxis: [
				{
					type: 'value',
					name: '不同排放阶段重型车里程占比（%）',
					max: 100,
					// nameLocation: 'middle',
					nameTextStyle: {
						color: '#fff',
						padding: [0, 0, 5, 280],
						fontSize: 16,
					},
					splitLine: {
						show: false,
					},
					axisLabel: {
						fontSize: 14,
						color: '#E2F0FF',
						// formatter: '{value}%'
					},
				},
			],
			series: series2,
		};

		setOption1(option1);
		setOption2(option2);
	}, []);

	const getColor = (name) => {
		let color = '';
		switch (name) {
			case '国五':
				color = '#E3814C';
				break;
			case '国六':
				color = '#20B027';
				break;
		}
		return color;
	};
	return (
		<Box title="行业与排放阶段里程特征" titlewidth="95%" height="100%">
			<Container>
				<div className="echarts1">
					<Echarts option={option1} />
				</div>
				<div className="echarts2">
					<Echarts option={option2} />
				</div>
			</Container>
		</Box>
	);
}
