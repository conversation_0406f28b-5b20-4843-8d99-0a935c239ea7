import { useState, useEffect } from 'react';
import Echarts from '@renderer/components/echarts';
import { EchartsStyle } from './style';

export default function Dashecharts(props) {
	const { title, max, unit, value } = props;
	const [option, setOption] = useState({});

	useEffect(() => {
		let data = [
			{
				value: value || 0,
				name: '',
			},
		];
		let option = {
			grid: {
				top: '0%',
				left: '0%',
				right: '0%',
				bottom: '0%',
				containLabel: true,
			},
			series: [
				{
					name: 'Pressure',
					type: 'gauge',
					radius: '85%',
					min: 0, //最小刻度
					max: max || 100, //最大刻度
					splitNumber: max / 10 || 10,
					detail: {
						show: false,
					},
					axisLine: {
						lineStyle: {
							width: 3,
							color: [[1, '#81ECFE']],
							shadowColor: '#6DE8FF',
							shadowBlur: 10,
						},
					},
					splitLine: {
						length: 8,
						lineStyle: {
							width: 2,
							color: '#6DE8FF',
						},
					},
					axisTick: {
						lineStyle: {
							color: '#6DE8FF',
						},
					},
					axisLabel: {
						color: '#cbfff6',
						fontSize: 12,
					},
					data,
					pointer: {
						width: 3,
						length: '60%',
						itemStyle: {
							color: '#00D8FC',
						},
					},
				},
				{
					name: '最外部进度条',
					type: 'gauge',
					radius: '100%',
					startAngle: 200,
					axisLine: {
						lineStyle: {
							color: [
								[
									0.5,
									{
										type: 'linear',
										x: 0, //右
										y: 0, //下
										x2: 0, //左
										y2: 0.9, //上
										colorStops: [
											{
												offset: 0.3,
												color: 'rgba(0,0,0,0)', // 0% 处的颜色
											},
											{
												offset: 0.6,
												color: '#00D8FC', // 100% 处的颜色
											},
											{
												offset: 1,
												color: 'rgba(0,0,0,0)', // 100% 处的颜色
											},
										],
										global: false, // 缺省为 false
									},
								],
								[
									1,
									{
										type: 'linear',
										x: 0, //右
										y: 0, //下
										x2: 0.4, //左
										y2: 1, //上
										colorStops: [
											{
												offset: 0.3,
												color: 'rgba(0,0,0,0)', // 0% 处的颜色
											},
											{
												offset: 0.6,
												color: '#00D8FC', // 100% 处的颜色
											},
											{
												offset: 1,
												color: 'rgba(0,0,0,0)', // 100% 处的颜色
											},
										],
										global: false, // 缺省为 false
									},
								],
							],
							width: 5,
						},
					},
					axisLabel: {
						show: false,
					},
					axisTick: {
						show: false,
					},
					splitLine: {
						show: false,
					},
					itemStyle: {
						show: false,
					},
					detail: {
						show: false,
					},
					title: {
						// 标题
						show: false,
					},
					data,
					pointer: {
						show: false,
					},
					animationDuration: 4000,
				},
			],
		};
		setOption(option);
	}, []);

	return (
		<EchartsStyle>
			<div className="title">{title || '--'}</div>
			<div className="echarts">
				<Echarts option={option} />
			</div>
			<div className="count">
				<div>{value || 0}</div>
				<div>{unit || '--'}</div>
			</div>
		</EchartsStyle>
	);
}
