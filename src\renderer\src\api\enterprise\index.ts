import Request from '../request';

// 北京重柴api（WebSocket版本）
const baseURL = import.meta.env.RENDERER_VITE_API_DOMAIN;

const request = new Request({ baseURL });

// 用户企业统计表格
export const getEnterpriseStatistics = (config: Record<string, any> = {}) => {
	return request.get(`/v2/enterprise/statistics`, config);
};

// 用户企业分布概况
export const getEnterpriseOnlineTreemap = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/enterprise/online-treemap`, config);
};

// 用户企业活动水平
export const getEnterpriseOnlineActivity = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/enterprise/online-activity`, config);
};

// 用户企业排放水平
export const getEnterpriseOnlineEmissions = (
	config: Record<string, any> = {},
) => {
	return request.get(`/v2/enterprise/online-emissions`, config);
};

// 用户企业车辆构成概况
export const getEnterpriseComposition = (config: Record<string, any> = {}) => {
	return request.get(`/v2/enterprise/composition`, config);
};

// 用户企业用户企业违规情况
export const getEnterpriseViolation = (config: Record<string, any> = {}) => {
	return request.get(`/v2/enterprise/violation`, config);
};
