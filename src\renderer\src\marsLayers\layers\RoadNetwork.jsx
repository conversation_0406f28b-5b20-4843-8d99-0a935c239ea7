import { useState, useEffect } from 'react';
import * as echarts from 'echarts';
import {
	Cartesian3,
	Color,
	Entity,
	Material,
	JulianDate,
	PolylineGlowMaterialProperty,
} from 'mars3d-cesium';
import { getRoad } from '@renderer/api';

let roadNetwork = [];

const RoadNetwork = (props) => {
	const { map, showRoadNetwork } = props;
	const processGeoJSONData = (data) => {
		const result = [];
		for (let i = 0; i < data.length; i += 2) {
			const lngLat = [data[i], data[i + 1]];
			result.push(lngLat);
		}
		const coords = [];
		result.forEach((point) => {
			const position = Cartesian3.fromDegrees(point[0], point[1], 0);
			coords.push(position);
		});
		return coords;
	};

	const getRoadPolylineData = (o) => {
		let withAlpha = 0.15;
		let width = 1;
		let color = '#d7570f';
		switch (o.properties.fclass) {
			case 'secondary':
			case 'secondary_link':
				withAlpha = 0.15;
				width = 1;
				color = '#d7570f';
				break;
			case 'primary':
			case 'primary_link':
				withAlpha = 0.2;
				width = 2;
				color = '#28d98e';
				break;
			case 'trunk':
			case 'trunk_link':
				withAlpha = 0.25;
				width = 3;
				color = '#1396e0';
				break;
			case 'motorway':
			case 'motorway_link':
				withAlpha = 0.3;
				width = 3;
				color = '#1396e0';
				break;
			default:
				withAlpha = 0.15;
				width = 1;
				color = '#d7570f';
				break;
		}
		return {
			width,
			withAlpha,
			color,
		};
	};

	const getRoadpositions = (arr) => {
		if (arr.length) {
			let positions = [];
			arr.forEach((item, idx) => {
				positions.push(item[0]);
				positions.push(item[1]);
			});
			return positions;
		}
		return [];
	};

	const getRoadData = () => {
		getRoad({
			service: 'WFS',
			version: '1.0.0',
			request: 'GetFeature',
			typeName: 'hotgrid:road_beijing',
			outputFormat: 'application/json',
			cql_filter:
				"fclass='motorway' or fclass='primary' or fclass='secondary'  or fclass='trunk' or fclass='primary_link' or fclass='secondary_link' or fclass='motorway_link' or fclass='trunk_link'",
		}).then((res) => {
			const { features } = res;
			let hStep = 10 / (features.length - 1);
			let roadList = [];
			features.forEach((item, idx) => {
				roadList.push({
					positions: getRoadpositions(item.geometry.coordinates[0]),
					...getRoadPolylineData(item),
				});
			});
			drawRoad(roadList);
		});
	};

	const drawRoad = (roadData) => {
		let hStep = 50 / (roadData.length - 1);
		roadData.forEach((line, idx) => {
			let position = processGeoJSONData(line.positions).flat();
			roadNetwork[idx] = new Entity({
				id: `polyline${idx}`,
				name: 'RoadNetwork',
				polyline: {
					positions: position,
					width: line.width,
					material: Color.fromCssColorString(
						echarts.color.modifyHSL('#5A94DF', Math.round(hStep * idx)),
					).withAlpha(line.withAlpha),
					// material: Color.fromCssColorString(line.color).withAlpha(line.withAlpha)
				},
			});
			map.entities.add(roadNetwork[idx]);
		});
	};

	useEffect(() => {
		if (!map || !roadNetwork.length) return;
		if (!showRoadNetwork) {
			map.entities.values.forEach((entity) => {
				if (entity.name === 'RoadNetwork') {
					entity.show = false;
				}
			});
		} else {
			map.entities.values.forEach((entity) => {
				if (entity.name === 'RoadNetwork') {
					entity.show = true;
				}
			});
		}
	}, [map, showRoadNetwork]);

	useEffect(() => {
		if (!map) return;
		if (!showRoadNetwork) return;
		getRoadData();
		return () => {
			if (roadNetwork.length) {
				roadNetwork.forEach((item) => {
					map.entities.remove(item);
				});
			}
		};
	}, [map]);

	return <div></div>;
};

export default RoadNetwork;
