import { useState, useEffect, FC } from 'react';
import { layer } from 'mars3d';

import { getRoad } from '@renderer/api';
import { useMap } from '@renderer/hooks';

const { GeoJsonLayer } = layer;

interface IRoadLayerProps {
	showRoadNetwork: boolean;
}

const RoadBasicLayer: FC<IRoadLayerProps> = (props) => {
	const map = useMap();
	const { showRoadNetwork } = props;

	const [layer, setLayer] = useState<layer.GeoJsonLayer>(null);
	const [data, setData] = useState();

	useEffect(() => {
		const fetchData = async () => {
			const result = await getRoad({
				service: 'WFS',
				version: '1.0.0',
				request: 'GetFeature',
				typeName: 'hotgrid:road_beijing',
				outputFormat: 'application/json',
				cql_filter: `fclass in ('motorway', 'primary', 'secondary' , 'trunk', 'primary_link', 'secondary_link', 'motorway_link', 'trunk_link')`,
			});
			setData(result);
		};
		fetchData();
	}, []);

	useEffect(() => {
		if (map) {
			const layer = new GeoJsonLayer({
				id: 'road-basic-layer',
				data,
				crs: 'EPSG:4326',
				symbol: {
					type: 'polyline',
					callback: (attr, styleOpt) => {
						return getRoadPolylineData(attr);
					},
				},
			});

			map.addLayer(layer);
			setLayer(layer);

			return () => {
				map.removeLayer(layer);
			};
		}
	}, [map, data]);

	useEffect(() => {
		if (layer) {
			layer.show = showRoadNetwork;
		}
	}, [showRoadNetwork, layer]);

	const getRoadPolylineData = (o) => {
		let opacity = 0.15;
		let width = 1;
		let color = '#d7570f';
		switch (o.fclass) {
			case 'secondary':
			case 'secondary_link':
				opacity = 0.15;
				width = 1;
				color = '#d7570f';
				break;
			case 'primary':
			case 'primary_link':
				opacity = 0.2;
				width = 2;
				color = '#28d98e';
				break;
			case 'trunk':
			case 'trunk_link':
				opacity = 0.25;
				width = 3;
				color = '#1396e0';
				break;
			case 'motorway':
			case 'motorway_link':
				opacity = 0.3;
				width = 3;
				color = '#1396e0';
				break;
			default:
				opacity = 0.15;
				width = 1;
				color = '#d7570f';
				break;
		}
		return {
			width,
			opacity,
			color,
		};
	};

	return null;
};

export default RoadBasicLayer;
