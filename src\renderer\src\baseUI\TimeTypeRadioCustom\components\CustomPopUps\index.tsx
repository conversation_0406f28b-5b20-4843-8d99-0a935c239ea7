import React, { useEffect, useState } from 'react';
import { DatePicker, Select } from 'antd';
// import 'moment/locale/zh-cn'
// import moment from 'moment'
import dayjs from 'dayjs';
import CustomPopUpsStyle from './style';
import icon from '@renderer/images/icon';

type Props = {
	showPop: boolean;
	right: string;
	top: string;
	customStartTime: any;
	setShowPop: React.Dispatch<React.SetStateAction<boolean>>;
	setTimeType: React.Dispatch<React.SetStateAction<string>>;
	setCustomDate: React.Dispatch<React.SetStateAction<CustomDate>>;
};

interface CustomDate {
	startTime: string;
	endTime: string;
	timeType: string;
}

const CustomPopUps = (props: Props) => {
	const {
		showPop,
		setShowPop,
		setTimeType,
		setCustomDate,
		right,
		top,
		customStartTime,
	} = props;
	const [timeTypeCustom, setTimeTypeCustom] = useState<string>('hour');
	const [endTime, setEndTime] = useState(
		customStartTime?.end_time !== '' && customStartTime?.end_time !== undefined
			? customStartTime?.end_time
			: dayjs().format('YYYY-MM-DD HH:00:00'),
	);
	const [startTime, setStartTime] = useState(
		customStartTime?.start_time !== '' &&
			customStartTime?.start_time !== undefined
			? customStartTime?.start_time
			: dayjs(endTime).add(-1, 'day').format('YYYY-MM-DD HH:00:00'),
	);
	const defaultTimeType = [
		{ value: 'hour', label: '时' },
		{ value: 'day', label: '日' },
	];

	const dateOnChange = (type) => {
		setTimeTypeCustom(type);
		if (type === 'hour') {
			const eTime = dayjs().format('YYYY-MM-DD HH:00:00');
			const sTime = dayjs(eTime).add(-1, 'hour').format('YYYY-MM-DD HH:00:00');
			setStartTime(sTime);
			setEndTime(eTime);
		} else if (type === 'day') {
			const eTime = dayjs().format('YYYY-MM-DD 23:59:59');
			const sTime = dayjs(eTime).add(-1, 'day').format('YYYY-MM-DD 00:00:00');
			setStartTime(sTime);
			setEndTime(eTime);
		}
	};

	const startTimeOnChange = (time) => {
		if (time > dayjs(endTime)) {
			console.log('选中时间大于结束时间');
		} else {
			if (timeTypeCustom === 'hour') {
				setStartTime(dayjs(time).format('YYYY-MM-DD HH:00:00'));
			} else if (timeTypeCustom === 'day') {
				setStartTime(dayjs(time).format('YYYY-MM-DD 00:00:00'));
			}
		}
	};

	const endTimeOnChange = (time) => {
		if (time < dayjs(startTime)) {
			console.log('选中时间小于开始时间');
		} else {
			if (timeTypeCustom === 'hour') {
				setEndTime(dayjs(time).format('YYYY-MM-DD HH:00:00'));
			} else if (timeTypeCustom === 'day') {
				setEndTime(dayjs(time).format('YYYY-MM-DD 23:59:59'));
			}
		}
	};

	const onOkStart = (e) => {
		if (e > dayjs(endTime)) {
			console.log('选中时间大于结束时间');
		} else {
			if (timeTypeCustom === 'hour') {
				setStartTime(dayjs(e).format('YYYY-MM-DD HH:00:00'));
			} else if (timeTypeCustom === 'day') {
				setStartTime(dayjs(e).format('YYYY-MM-DD 00:00:00'));
			}
		}
	};

	const onOkEnd = (e) => {
		if (e < dayjs(startTime)) {
			console.log('选中时间小于开始时间');
		} else {
			if (timeTypeCustom === 'hour') {
				setEndTime(dayjs(e).format('YYYY-MM-DD HH:00:00'));
			} else if (timeTypeCustom === 'day') {
				setEndTime(dayjs(e).format('YYYY-MM-DD 23:59:59'));
			}
		}
	};

	const closePop = () => {
		setShowPop(false);
	};
	const dataSearch = () => {
		if (setTimeType) setTimeType(`custom`);
		setCustomDate({
			startTime: startTime,
			endTime: endTime,
			timeType: timeTypeCustom,
		});
	};

	return showPop ? (
		<CustomPopUpsStyle right={right} top={top}>
			<img src={icon.close} alt="" className="close" onClick={closePop} />
			<div className="content">
				<div className="item">
					<p>时间粒度:</p>
					<Select
						className="settings-select"
						popupClassName="settings-select-popup"
						defaultValue={timeTypeCustom}
						onChange={dateOnChange}
						style={{ width: '100px', textAlign: 'center' }}
						options={defaultTimeType}
					/>
				</div>
				<div className="item">
					<p>开始时间:</p>
					{timeTypeCustom == 'hour' ? (
						<DatePicker
							className="settings-option-datePicker"
							showTime={{
								defaultValue: dayjs('00:00:00', 'HH:mm:ss'),
								format: 'HH:00:00',
							}}
							defaultValue={dayjs(startTime, '')}
							// disabledDate={() => {}}
							value={dayjs(startTime)}
							showToday={false}
							onOk={onOkStart}
							// onChange={startTimeOnChange}
						></DatePicker>
					) : (
						<DatePicker
							className="settings-option-datePicker"
							defaultValue={dayjs(startTime, '')}
							// disabledDate={() => {}}
							value={dayjs(startTime)}
							showToday={false}
							// onOk={onOkStart}
							onChange={startTimeOnChange}
						></DatePicker>
					)}
				</div>
				<div className="item">
					<p>结束时间:</p>
					{timeTypeCustom == 'hour' ? (
						<DatePicker
							className="settings-option-datePicker"
							showTime={{
								defaultValue: dayjs('00:00:00', 'HH:mm:ss'),
								format: 'HH:00:00',
							}}
							defaultValue={dayjs(endTime, '')}
							// disabledDate={() => {}}
							value={dayjs(endTime)}
							showToday={false}
							onOk={onOkEnd}
							// onChange={endTimeOnChange}
						></DatePicker>
					) : (
						<DatePicker
							className="settings-option-datePicker"
							defaultValue={dayjs(endTime, '')}
							// disabledDate={() => {}}
							value={dayjs(endTime)}
							showToday={false}
							// onOk={onOkEnd}
							onChange={endTimeOnChange}
						></DatePicker>
					)}
				</div>
			</div>
			<div className="search" onClick={dataSearch}>
				<p>数据查询</p>
			</div>
		</CustomPopUpsStyle>
	) : null;
};

export default CustomPopUps;
