/**
 * 离线车辆图层
 */
import React, { useState, useEffect, useContext } from 'react';
import {
	Cesium,
	layer as Layer,
	PointTrans,
	LngLatPoint,
	graphic,
	EventType,
} from 'mars3d';

import { handleRawData, isInPolygon } from '@renderer/utils';
import { MapContext, PageContext } from '@renderer/context';
import { getCarColor } from '@renderer/marsLayers/components/ModeMenu/data';
import { getRealTimeOffline } from '@renderer/api';
import { useMap } from '@renderer/hooks';

const polygon = [
	[73.500226, 18.1608963],
	[135.0858829, 18.1608963],
	[135.0858829, 53.55791967],
	[73.500226, 53.55791967],
	[73.500226, 18.1608963],
];

const { GraphicLayer } = Layer;
const { PointPrimitive } = graphic;
const { wgs2gcj } = PointTrans;
const { dblClick } = EventType;

let layer;

const OfflineLayer = (props) => {
	const map = useMap();
	const { visible, layerId } = props;
	const { currentInfo, setCurrentInfo, setBickDate, selectRegion } =
		useContext(MapContext);
	const {
		selectMode,
		setOpenTrajectory,
		openTrajectory,
		RealTimeOfflineDataDB,
		countyId,
	} = useContext(PageContext);
	const [vinsData, setVinsData] = useState();
	// const [layer, setLayer] = useState<GraphicLayer>(null);

	const vinsHandleRawData = (data) => {
		const _VINList = data
			.filter((item) => {
				const { lon, lat } = item;
				return isInPolygon([lon, lat], polygon);
			})
			// .filter((item) =>
			// 	selectModeChild?.some((item2) => {
			// 		return item2.label == item.type;
			// 	}),
			// )
			.map((item) => `${item.VIN}|${item.type}`);
		const _vins = new Set(_VINList);
		const _data = handleRawData(_vins, data);
		return _data;
	};

	function selectRandomPoints(data, count) {
		if (count >= data.length) return data;

		const result = [];
		const indices = new Set();

		while (indices.size < count) {
			const index = Math.floor(Math.random() * data.length);
			if (!indices.has(index)) {
				indices.add(index);
				result.push(data[index]);
			}
		}

		return result;
	}

	const updatePointByZoom = (result) => {
		layer.clear(true);
		// 根据缩放级别决定显示多少点位
		let displayCount = 0;
		const zoomLevel = map.level;
		if (zoomLevel < 10) {
			// 小比例尺下只显示1%的点位
			displayCount = Math.max(1, Math.floor(result.length * 0.01));
		} else if (zoomLevel < 15) {
			// 中等比例尺下显示10%的点位
			displayCount = Math.max(1, Math.floor(result.length * 0.1));
		} else {
			// 大比例尺下显示全部点位
			displayCount = result.length;
		}
		const displayData = selectRandomPoints(result, displayCount);

		// TODO 根据车辆是否在线添加不同样式图标
		displayData.forEach((item) => {
			const gcj2wgs = new wgs2gcj([item.lon, item.lat]);
			const position = new LngLatPoint(gcj2wgs[0], gcj2wgs[1]);

			const graphicPoint = new PointPrimitive({
				position,
				style: {
					color: 'transparent',
					pixelSize: 6,
					outlineColor: getCarColor(item.type),
					outlineWidth: 2,
					distanceDisplayCondition: true,
					distanceDisplayCondition_far: Number.MAX_VALUE,
				},
				attr: item,
			});
			layer.addGraphic(graphicPoint);
		});
		console.log(`缩放级别: ${zoomLevel}, 显示点位数量: ${displayCount}`);
	};

	const getRealTimeOfflineData = async () => {
		let nums = 190009;
		let times = 2;
		let jsonData = sessionStorage.getItem('zcJsonData');
		if (jsonData) jsonData = JSON.parse(jsonData);
		nums = jsonData?.carNumber || nums;
		times = jsonData?.offlineTime || times;
		const data = await getRealTimeOffline({
			nums,
			times,
		});
		return data;
	};

	useEffect(() => {
		if (vinsData) {
			updatePointByZoom(vinsData);
			const viewer = map.viewer;

			// 监听相机变化事件（包含缩放、平移等）
			viewer.camera.changed.addEventListener(() => {
				updatePointByZoom(vinsData);
			});
		}
	}, [vinsData]);

	// useEffect(() => {
	// 	selectModeChild = selectMode.child;
	// 	vinsHandleRawData(realTimeData);
	// }, [selectMode.key]);

	useEffect(() => {
		if (!map && !visible) return;
		layer = new GraphicLayer();
		map.addLayer(layer);
		// setLayer(layer);
		layer.on(dblClick, function (event) {
			console.log('OnlineLayer =======> 监听layer， 双击了矢量对象', event);
			const cartesianPosition = event.graphic.position;
			const cartographic = Cesium.Cartographic.fromCartesian(cartesianPosition);
			setCurrentInfo({
				VIN: event.graphic.attr.VIN,
				type: event.graphic.id.split('|')[1],
				lon: Cesium.Math.toDegrees(cartographic.longitude),
				lat: Cesium.Math.toDegrees(cartographic.latitude),
			});
			setOpenTrajectory(true);
			setBickDate(true);
		});

		return () => {
			layer.clearDrawing();
			layer.clear(true);
			layer.enabledEvent = false;
			if (layer) map.removeLayer(layer);
		};
	}, [map, visible]);

	useEffect(() => {
		const fetchData = async () => {
			const value = await RealTimeOfflineDataDB.getItem('RealTimeOfflineData');
			if (!value) {
				const data = await getRealTimeOfflineData();
				setVinsData(vinsHandleRawData(data));
			} else {
				if (value?.length) {
					setVinsData(vinsHandleRawData(value));
				}
			}
		};
		visible && fetchData();
	}, [visible]);

	useEffect(() => {
		if (!currentInfo) {
			if (layer) layer.show = true;
			window.electron.ipcRenderer.send('layer', layerId);
		} else {
			if (layer) layer.show = false;
		}
	}, [currentInfo]);

	return null;
};

export default OfflineLayer;
