/// 单车仪表盘
import React, { useState, useEffect, useContext } from 'react';
import { Table } from 'antd';
import '@animxyz/core';
import { XyzTransitionGroup } from '@animxyz/react';
import Dashecharts from './Dashecharts';
import NoxEcharts from './NoxEcharts';
import { DashBoardStyle } from './style';
import MapContext from '@renderer/context';
import { getTrackColor } from '@renderer/utils/util';

let DashBoardTimer;

export default function DashBoardNew({ info, level = '5' }) {
	if (!info) {
		clearInterval(DashBoardTimer);
		return null;
	}
	const { carDetails } = useContext(MapContext);
	const [carData, setCarData] = useState({
		engineRotation: 0,
		nox_ppm: 0,
		speed: 0,
	});

	useEffect(() => {
		if (!Object.keys(carDetails)?.length) return;
		setCarData({
			engineRotation: carDetails.engineRotation,
			nox_ppm: carDetails.nox_ppm,
			speed: carDetails.speed,
		});
		console.log(carDetails);
	}, [carDetails]);

	return (
		<XyzTransitionGroup xyz="bottom-100% fade-100%">
			<DashBoardStyle>
				<div className="bike-center">
					<div>
						<Dashecharts
							title="车速"
							max={200}
							unit="km/h"
							value={Math.floor(carData.speed)}
						/>
					</div>
					<div>
						<Dashecharts
							title="发动机转速"
							max={5000}
							unit="1/min"
							value={Math.floor(carData.engineRotation)}
						/>
					</div>
					<div>
						<NoxEcharts
							title="NOX浓度"
							max={level == '5' ? 2000 : 1100}
							unit="PPM"
							value={carData.nox_ppm}
							color={getTrackColor(carData.nox_ppm, level)}
						/>
					</div>
				</div>
			</DashBoardStyle>
		</XyzTransitionGroup>
	);
}
