import { useEffect, useRef, useState, useContext } from 'react';
import * as echarts from 'echarts';
import axios from 'axios';
import { geoJson } from '@renderer/components/AreaViolation/AreaMap/data';
import { MapContext, PageContext } from '@renderer/context';
import { tranNumberS, tranNumber } from '@renderer/hooks';
import dataBg from '@renderer/images/icon/dataBg.png';
import up from '@renderer/images/icon/up.png';
import down from '@renderer/images/icon/down.png';

type Props = {
	data: Array<any>;
	layerId: string;
	targetId: number;
	customerBatteryCityData: any;
	dynamicEffectData: any;
	currentPosition: any;
	type: string;
	regionYOY: Array<any>;
	streetShipId: Array<number>;
	position: string;
	enableClick: boolean;
};

const RegistriesDistributionEcharts2d = (props: Props) => {
	const {
		data,
		layerId,
		currentPosition,
		customerBatteryCityData,
		dynamicEffectData,
		targetId,
		type,
		regionYOY,
		streetShipId,
		position,
		enableClick,
	} = props;

	const {
		regionName,
		setRegionName,
		streetName,
		setStreetName,
		setStreetShipData,
		BeiJingTownDataDB,
	} = useContext(PageContext);
	const [optionDefault, setOptionDefault] = useState<any>(null);
	const [townJson, setTownJson] = useState(null);
	// const [selectedRegionName, setSelectedRegionName] = useState<any>(selectRegion?.name)
	// const [chartVisible, setChartVisible] = useState(true)
	const echartsContainerRef = useRef(null);
	const echartsChartRef = useRef<any>(null);
	const img2 =
		'image://data:image/png;base64,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';
	let isStreetTownship = false;

	// 获取地区中心点
	// const getRegionalCenter = (name) => {
	//   const dataRegion = geoJson.features.find((item) => item.properties.name === name)
	//   return dataRegion?.properties.center
	// }

	// 重置地图状态
	const resetMap = () => {
		// 重置地图状态
		const option = echartsChartRef.current.getOption();
		// 遍历地图区域，先将所有区域恢复默认样式
		option.geo[0].regions.forEach(function (region, idx) {
			region.itemStyle = optionDefault?.geo[0].regions[idx].itemStyle;
			region.label = { show: false };
		});
		return option;
	};

	const getEmissionDisplayValues = (type: string, params: any) => {
		const value = tranNumber(Number(params.data[2].value), 2);
		const per = regionYOY?.find((i) => i.name === params.data[2].name)?.per;
		if (per > 0) {
			return `{fline|${value}}{up|}{upText|${per}%}`;
		} else {
			return `{fline|${value}}{down|}{downText|${per}%}`;
		}

		// switch (type) {
		//   case '1':
		//     f = 'd'
		//     return `{fline|${value}}{${f}|}{upText|10%}`
		//   case '2':
		//     return `{fline|${params.dataIndex + 1}}` + `{tline|${value}%}`
		//   case '3':
		//     return `{fline|${value} kg}`
		//   case '4':
		//     return `{fline|${value} %}`
		//   case '5':
		//     return `{fline|排行:}` + `{fline|${value}}`
		//   default:
		//     return
		// }
	};

	const getSpaceDisplayValues = (type: number, params: any) => {
		const value = params.value[2].value;
		return `{fline|${value}}`;
		// switch (type) {
		//   case 1:
		//     return `{fline|${value}}`
		//   case 2:
		//     return `{fline|${value}}`
		//   case 3:
		//     return `{fline|${value}}`
		//   case 4:
		//     return `{fline|${value}}`
		//   case 6:
		//     return `{fline|${value}}`
		//   default:
		//     return
		// }
	};

	const getDisplayValues = (layerId: string, type: any, params: any) => {
		switch (layerId) {
			case 'spatialDistribution':
				return getSpaceDisplayValues(type, params);
			case 'area':
			case 'industry':
			case 'userEnterprise':
				return getEmissionDisplayValues(type, params);
			case 'ViolationAnalysis':
				return getEmissionDisplayValues(type, params);
			default:
				return;
		}
	};

	const getTownJson = () => {
		BeiJingTownDataDB.getItem('BeiJingTownData')
			.then((value) => {
				console.log('BeiJingTownDataDB??????1');
				if (value) {
					setTownJson(value);
				}
			})
			.catch(() => {
				console.log('查询北京街乡镇数据异常');
			});
		// try {
		//   const response = await axios.get(`file:///public/data/townJson.json`)
		//   setTownJson(response.data)
		// } catch (error) {
		//   console.error(error)
		//   throw error
		// }
	};

	const filterStreetId = (objectArray, numberArray) => {
		const filteredArray = objectArray.filter((obj) => {
			// 检查 properties.region 是否在 numberArray 中
			return numberArray.includes(obj.properties.region);
		});
		return filteredArray;
	};

	useEffect(() => {
		if (layerId === 'streetTownship') getTownJson();
	}, [layerId]);

	useEffect(() => {
		// if (!customerBatteryCityData?.length) return
		if (layerId === 'streetTownship' && !townJson) return;
		if (layerId === 'streetTownship') {
			if (streetShipId.length !== 0) {
				const streetRegion = filterStreetId(townJson.features, streetShipId);
				echarts.registerMap('beijing2D', {
					type: 'FeatureCollection',
					features: streetRegion,
				});
				setStreetShipData({
					type: 'FeatureCollection',
					features: streetRegion,
				});
			} else {
				echarts.registerMap('beijing2D', townJson);
				setStreetShipData(townJson);
			}
			isStreetTownship = true;
		} else {
			echarts.registerMap('beijing2D', geoJson);
			isStreetTownship = false;
		}

		echartsChartRef.current = echarts.init(echartsContainerRef.current);
		const option = {
			animation: true,
			geo: [
				{
					// show: !isStreetTownship,
					show: true,
					map: 'beijing2D',
					aspectScale: 0.88,
					roam: true, // 是否允许缩放
					zoom: 1.15, // 默认显示级别
					layoutSize: '88%',
					layoutCenter: ['60%', '55%'],
					animationDurationUpdate: 0, //数据更新动画的时长。
					itemStyle: {
						normal: {
							areaColor: {
								type: 'linear-gradient',
								x: 0,
								y: 400,
								x2: 0,
								y2: 0,
								colorStops: [
									{
										offset: 0,
										color: 'rgba(37,108,190,0)', // 0% 处的颜色
									},
									{
										offset: 1,
										color: 'rgba(15,169,195,0)', // 50% 处的颜色
									},
								],
								global: true, // 缺省为 false
							},
							borderColor: '#25CBDD ',
							borderWidth: 4,
							shadowColor: 'rgba(74,56,153, 0.8)',
							shadowOffsetX: 0,
							shadowOffsetY: 0,
							shadowBlur: layerId === 'streetTownship' ? 0 : 20,
						},
						emphasis: {
							areaColor: 'rgba(0,254,233,0.6)',
							// borderWidth: 0
						},
					},
					emphasis: {
						label: {
							show: isStreetTownship,
							color: '#fff', //文字颜色
							fontSize: 18, //文字大小
							position: [20, 0],
							padding: [15, 25],
							alignText: 'center',
							lineHeight: 24,
							// backgroundColor: 'rgba(87, 51, 51, 0.35)', //透明度0清空文字背景
							borderColor: '#2ab8ff', //分界线颜色
							backgroundColor: {
								image: dataBg, // 图片的路径
								repeat: 'no-repeat',
							},
							formatter: function (params) {
								const region = data.find((item) => item.name === params.name);
								if (region) {
									const { value } = region;
									return `${params.name} ${tranNumberS(value, 2)} `;
								} else {
									return null;
								}
							},
						},
					},
					zlevel: 3,
					selectedMode: 'single', // 一次只允许选中一个区域
					select: {
						disabled: false,
						label: {
							show: isStreetTownship,
							color: '#fff', //文字颜色
							fontSize: 18, //文字大小
							position: [20, 0],
							padding: [15, 25],
							alignText: 'center',
							lineHeight: 24,
							// backgroundColor: 'rgba(87, 51, 51, 0.35)', //透明度0清空文字背景
							borderColor: '#2ab8ff', //分界线颜色
							backgroundColor: {
								image: dataBg, // 图片的路径
								repeat: 'no-repeat',
							},
							formatter: function (params) {
								const region = data.find((item) => item.name === params.name);
								if (region) {
									const { value } = region;
									return `${params.name} ${value}`;
								} else {
									return null;
								}
							},
						},
						itemStyle: {
							color: '#fff',
							// 选中区域颜色
							areaColor: 'rgba(0,254,233,0.4)',
							// 选中区域边框
							borderColor: '#0CDEFF',
							borderWidth: 5,
						},
					},
					regions:
						customerBatteryCityData?.length > 0 ? customerBatteryCityData : [],
				},
				// {
				//   type: 'map',
				//   map: 'beijing2D',
				//   zlevel: -2,
				//   aspectScale: 0.88,
				//   roam: true, // 是否允许缩放
				//   zoom: 1.15, // 默认显示级别
				//   layoutSize: '88%',
				//   layoutCenter: ['60%', '55%'],
				//   animationDurationUpdate: 0, //数据更新动画的时长。
				//   silent: true,
				//   itemStyle: {
				//     normal: {
				//       borderWidth: 6,
				//       borderColor: '#25CBDD',
				//       shadowColor: 'rgba(61,62,154,1)',
				//       shadowOffsetY: 0,
				//       shadowBlur: 45,
				//       areaColor: 'transpercent'
				//     }
				//   }
				// }
			],
			series: [
				//柱状体的主干
				{
					type: 'lines',
					zlevel: 5,
					effect: {
						show: false,
						symbolSize: 5, // 图标大小
					},
					lineStyle: {
						width: 6, // 尾迹线条宽度
						color: 'rgb(22,255,255, .6)',
						opacity: 1, // 尾迹线条透明度
						curveness: 0, // 尾迹线条曲直度
					},
					label: {
						show: 0,
						position: 'end',
						formatter: '245',
					},
					silent: true,
					data: dynamicEffectData[0],
				},
				// 柱状体的顶部
				{
					type: 'scatter',
					coordinateSystem: 'geo',
					geoIndex: 0,
					zlevel: 5,
					label: {
						normal: {
							show: true,
							formatter: function (params) {
								let areaType: any;
								if (layerId === 'spatialDistribution') {
									areaType = targetId;
								} else {
									areaType = type;
								}
								return getDisplayValues(layerId, areaType, params);
							},
							color: '#fff',
							rich: {
								fline: {
									color: '#fff',
									fontSize: 18,
									fontWeight: 1000,
								},
								tline: {
									padding: [0, 10],
									color: '#d9f4f7',
									fontSize: 14,
									fontWeight: 600,
								},
								up: {
									width: 25,
									height: 25,
									padding: [10, 0],
									backgroundColor: {
										image: up,
									},
								},
								upText: {
									padding: [0, -5],
									color: 'red',
									fontSize: 18,
									fontWeight: 1000,
								},
								downText: {
									padding: [0, -5],
									color: '#cbeeb9',
									fontSize: 18,
									fontWeight: 1000,
								},
								down: {
									width: 25,
									height: 25,
									padding: [10, 0],
									backgroundColor: {
										image: down,
									},
								},
							},
						},
						emphasis: {
							show: true,
							color: '#fff',
						},
					},
					itemStyle: {
						color: '#00FFF6',
						opacity: 1,
					},
					symbol: img2,
					symbolSize: [150, 50],
					symbolOffset: [0, -20],
					// z: 999,
					data: dynamicEffectData[1],
				},
				// 柱状体的底部
				{
					geoIndex: 0,
					zlevel: 4,
					type: 'effectScatter',
					coordinateSystem: 'geo',
					rippleEffect: {
						scale: 10,
						brushType: 'stroke',
					},
					showEffectOn: 'render',
					label: {
						normal: {
							formatter: '{b}',
							position: 'bottom',
							color: '#fff',
							fontSize: 18,
							distance: 10,
							show: true,
						},
					},
					symbol: 'circle',
					symbolSize: [10, 5],
					itemStyle: {
						// color: '#F7AF21',
						color: 'rgb(22,255,255, 1)',
						opacity: 1,
					},
					data: dynamicEffectData[2],
				},
				// 底部外框
				{
					type: 'scatter',
					coordinateSystem: 'geo',
					geoIndex: 0,
					zlevel: 4,
					label: {
						show: false,
					},
					symbol: 'circle',
					symbolSize: [1, 1],
					itemStyle: {
						color: {
							type: 'radial',
							x: 0.5,
							y: 0.5,
							r: 0.5,
							colorStops: [
								{
									offset: 0,
									color: 'rgb(22,255,255, 0)', // 0% 处的颜色
								},
								{
									offset: 0.75,
									color: 'rgb(22,255,255, 0)', // 100% 处的颜色
								},
								{
									offset: 0.751,
									color: 'rgb(22,255,255, 1)', // 100% 处的颜色
								},
								{
									offset: 1,
									color: 'rgb(22,255,255, 1)', // 100% 处的颜色
								},
							],
							global: false, // 缺省为 false
						},
						opacity: 1,
					},
					silent: true,
					data: dynamicEffectData[2],
				},
			],
		};
		// if (regionName) {
		//   const region = option.geo[0].regions.find((item) => item.name === regionName)
		//   region.itemStyle = {
		//     color: '#fff',
		//     areaColor: 'rgba(0,254,233,0.4)',
		//     borderColor: '#0CDEFF',
		//     borderWidth: 5
		//   }
		// }
		echartsChartRef.current.setOption(option);
		if (enableClick) {
			echartsChartRef.current.on('geoselectchanged', (e) => {
				if (e.name && setStreetName && layerId === 'streetTownship') {
					setStreetName(e.allSelected[0].name[0]);
					return;
				}
				if (e.name && setRegionName) setRegionName(e.allSelected[0].name[0]);
			});
		}
		if (!optionDefault) {
			setOptionDefault({ ...option });
		}
		// echartsChartRef.current.on('georoam', function (params) {
		//   const option = echartsChartRef.current.getOption() //获得option对象
		//   if (params.zoom != null && params.zoom != undefined) {
		//     //捕捉到缩放时
		//     option.geo[1].zoom = option.geo[0].zoom //下层geo的缩放等级跟着上层的geo一起改变
		//     option.geo[1].center = option.geo[0].center //下层的geo的中心位置随着上层geo一起改变
		//   } else {
		//     //捕捉到拖曳时
		//     option.geo[1].center = option.geo[0].center //下层的geo的中心位置随着上层geo一起改变
		//   }
		//   // echartsChartRef.current.dispatchAction({
		//   //   //来用程序主动渲染选框
		//   //   type: 'restore'
		//   // })
		//   echartsChartRef.current.setOption(option) //设置option
		// })
		return () => {
			if (echartsChartRef.current) {
				if (enableClick) {
					echartsChartRef.current.off('geoselectchanged');
				}
				// echartsChartRef.current.off('georoam')
				echartsChartRef.current.dispose();
			}
		};
	}, [customerBatteryCityData, townJson, type, streetShipId]);

	// useEffect(() => {
	//   if (!optionDefault) return
	//   if (!echartsChartRef.current || !selectRegion) return

	//   const option = resetMap()
	//   const region = option.geo[0].regions.find((item) => item.name === selectRegion.name)
	//   if (selectedRegionName === selectRegion?.name) {
	//     setChartVisible((prevVisible) => !prevVisible)
	//     if (chartVisible) {
	//       option.geo[0].center = undefined
	//       option.geo[0].layoutCenter = undefined
	//       option.geo[0].layoutSize = undefined
	//       echartsChartRef.current.setOption(option)
	//     } else {
	//       region.itemStyle = { areaColor: '#1869eb' }
	//       echartsChartRef.current.setOption(option)
	//     }
	//   } else {
	//     region.itemStyle = { areaColor: '#1869eb' }
	//     option.geo[0].center = getRegionalCenter(selectRegion.name)
	//     option.geo[0].layoutSize = '300%'
	//     option.geo[0].layoutCenter = ['50%', '50%']
	//     echartsChartRef.current.setOption(option)
	//   }
	//   // option.geo[0].center = getRegionalCenter(selectRegion.name)
	//   // option.geo[0].layoutSize = '300%'
	//   echartsChartRef.current.setOption(option)
	//   setSelectedRegionName(selectRegion?.name)
	// }, [selectRegion])

	useEffect(() => {
		const name = streetName ? streetName : regionName;
		if (!name || !echartsChartRef.current) return;
		echartsChartRef.current.dispatchAction({
			type: 'geoSelect',
			geoIndex: [0],
			geoName: 'beijing2D',
			name: name,
		});
	}, [regionName, streetName]);

	useEffect(() => {
		if (!currentPosition) return;
		if (!echartsChartRef.current) return;
		if (!optionDefault) return;
		const option = resetMap();
		const region = option.geo[0].regions.find(
			(item) => item.name === currentPosition.adm_name,
		);
		region.itemStyle = {
			areaColor: 'rgba(0,254,233,0.4)',
			borderColor: '#0CDEFF',
			borderWidth: 5,
		};
		region.label = {
			show: true,
			color: '#fff', //文字颜色
			fontSize: 18, //文字大小
			position: [20, 0],
			padding: [15, 25],
			alignText: 'center',
			lineHeight: 24,
			backgroundColor: {
				image: dataBg, // 图片的路径
				repeat: 'no-repeat',
			},
			formatter: function (params) {
				const region = data.find((item) => item.name === params.name);
				if (region) {
					const { value } = region;
					return `${params.name} ${value}`;
				} else {
					return null;
				}
			},
		};
		// 更新地图状态
		echartsChartRef.current.setOption(option);
	}, [currentPosition]);

	return (
		<div
			ref={echartsContainerRef}
			style={{
				position: 'absolute',
				// top: '5%',
				right: position == 'right' ? '-10%' : '10.3%',
				width: '100%',
				height: '100%',
				zIndex: 1,
			}}
		/>
	);
};

export default RegistriesDistributionEcharts2d;
