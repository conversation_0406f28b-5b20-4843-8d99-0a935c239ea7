import { useEffect, useState } from 'react';
import styled from 'styled-components';
import Echarts from '@renderer/components/echarts';
// import { List } from 'echarts'
// import Item from 'antd/es/list/Item'

export const Container = styled.div<Props>`
	width: ${(props) => props.width || '100%'};
	height: ${(props) => props.height || '215px'};
`;

type Props = {
	width?: string;
	height?: string;
	data?: Array<any>;
	colorList?: Array<string>;
	// carname?: Array<string>
	areadata?: Array<any>;
};

const RegistrationAreaEcharts = (props: Props) => {
	const { data, areadata } = props;

	// const [angle, setAngle] = useState<number>(270)
	// const [isShow, setIsShow] = useState<boolean>(false)
	const [registrationData, setRegistrationData] = useState<any>([]);
	// const [option, setOption] = useState({})
	useEffect(() => {
		const allData: any = [];
		data?.forEach((item) => {
			allData.push({
				name: item?.second_type,
				type: 'bar',
				stack: 'all',
				barWidth: '40%',
				data: item?.group_details.count,
			});
		});
		setRegistrationData(allData);
	}, [data]);

	const option = {
		//color: ["#70DDFF", "#FF9F6A", "#20D469", "#D083FF", "#FF8282", "#769CFF", "#FFCF55"],
		color: [
			'#1E90FF',
			'#40E0D0',
			'#6495ED',
			'#4682B4',
			'#00BFFF',
			'#87CEEB',
			'#00FFFF',
		],
		title: {
			textStyle: { color: '#ffffff', fontSize: 20, fontWeight: 'normal' },
			x: 'center',
			top: '2%',
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: { type: 'shadow' },
		},
		legend: {
			width: '76%',
			top: '7.5%',
			right: 0,
			orient: 'horizontal',
			type: 'scroll',
			textStyle: {
				color: '#FFFFFF',
			},
		},
		grid: {
			top: '24%',
			bottom: '20%',
			left: '13%',
			right: '8%',
		},
		xAxis: {
			type: 'category',
			boundaryGap: true,
			axisLine: {
				lineStyle: { color: '#C4C4C4' },
			},
			axisLabel: {
				interval: 2, // 显示标签数量
				textStyle: { color: '#ffffff', fontSize: 13 },
			},
			data: areadata,
		},
		yAxis: {
			name: '注册数量 (辆)',
			nameTextStyle: { color: '#ffffff', fontSize: 11 },
			type: 'value',
			splitLine: {
				lineStyle: { color: '#C4C4C4', type: 'dashed' },
			},
			axisLine: { show: false },
			axisTick: { show: false },
			axisLabel: {
				textStyle: { color: '#ffffff', fontSize: 11 },
			},
		},
		series: registrationData,
	};

	return (
		<Container {...props}>
			<Echarts option={option}></Echarts>
		</Container>
	);
};
export default RegistrationAreaEcharts;
