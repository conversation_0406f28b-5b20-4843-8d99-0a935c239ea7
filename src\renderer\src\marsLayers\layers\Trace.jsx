import { useEffect, useContext, useRef, useState } from 'react';
import * as mars3d from 'mars3d';
import moment from 'moment';
import { getMapExtent, getCarTypeModel } from '@renderer/utils';
import { MapContext, PageContext } from '@renderer/context';
import { getCarColor } from '../../../../main/data';
import { LayerType } from '../components/LayerMenu/layerDatas';
import {
	getVehicleNumber,
	getVehicleRealTimePositionInfo,
} from '@renderer/api';
import VehicleIndustryNumber from '../components/VehicleIndustryNumber';
import { COUNTY_ID_NAME_MAP } from '@renderer/marsLayers/components/AreaStreetStyles';

let Cesium = mars3d.Cesium;
let VehicleLocationData = null;
let graphicLayerTrace,
	graphicLayerModel,
	graphicModel,
	graphicShow = true,
	realVehicleTime = null,
	// messageTime = null,
	defaultPoint = {
		lng: 0,
		lat: 0,
	},
	axesNumber = 0.03; // 1000米的坐标系数: 0.008989

export default (props) => {
	const { map, visible, layerId } = props;
	const [pieData, setPieData] = useState([]);
	const vehicleState = 'onLine';
	let currentTime = Date.parse(new Date());
	// const [messageLength, setMessageLength] = useState(0)
	// 点击模型跟随
	// let handler = new ScreenSpaceEventHandler(map.scene.canvas)
	let is_illegal = layerId === LayerType.VIOLATING_VEHICLES ? '1' : '0';
	const {
		currentInfo,
		setCurrentInfo,
		setVehicleSearchData,
		setBickDate,
		currentLayerId,
		setCurrentLayerId,
		showFlash,
	} = useContext(MapContext);
	const {
		selectMode,
		setSwitchingPointsId,
		setOpenTrajectory,
		openTrajectory,
		countyId,
	} = useContext(PageContext);
	let messageList = [];

	const getMessage = () => {
		window.electron.ipcRenderer.on('message', (_event, message) => {
			if (Date.parse(new Date()) - currentTime < 6000) return;
			currentTime = Date.parse(new Date());
			// if (messageTime) {
			//   clearTimeout(messageTime)
			//   messageTime = null
			// }
			console.log('message', JSON.parse(message).length);
			messageList = JSON.parse(message);
			// setMessageLength(messageList.length)
			if (messageList && messageList.length > 0) {
				setVehicleSearchData(JSON.parse(message));
				changePosition(8, messageList);
			}
		});
	};

	const addDemoGraphics = (points) => {
		if (!graphicLayerTrace) {
			graphicLayerTrace = new mars3d.layer.GraphicLayer();
			map.addLayer(graphicLayerTrace);
		}
		for (let i = 0; i < points.length; i++) {
			const item = points[i];
			const VIN = item.split('|')[0];
			const attr = { VIN };
			const type = item.split('|')[1];
			const lon = item.split('|')[2];
			const lat = item.split('|')[3];
			const graphicPoint = new mars3d.graphic.PointPrimitive({
				id: `${item}`,
				position: new mars3d.PointTrans.wgs2gcj([lon, lat]),
				style: {
					color: getCarColor(type),
					pixelSize: 8,
					distanceDisplayCondition: true,
					distanceDisplayCondition_far: Number.MAX_VALUE,
				},
				attr,
			});
			graphicLayerTrace.addGraphic(graphicPoint);
		}
		// if (currentInfo) graphicLayerTrace.show = false
	};

	const changePosition = (time, messageList) => {
		const messageMap = new Map();
		messageList.forEach((element) => {
			messageMap.set(element.VIN, element);
		});

		graphicLayerTrace.eachGraphic((graphic) => {
			if (graphic.isPrivate) {
				return;
			}
			const obj = messageMap.get(graphic.attr.VIN);
			if (obj) {
				const gcj2wgs = new mars3d.PointTrans.wgs2gcj([obj.lon, obj.lat]);
				const point = new mars3d.LngLatPoint(gcj2wgs[0], gcj2wgs[1]);
				const lon_dist = Math.abs(graphic.attr.lon - obj.lon) || 1;
				const lat_dist = Math.abs(graphic.attr.lat - obj.lat) || 1;
				const isMove = lon_dist < axesNumber && lat_dist < axesNumber;
				graphic.addDynamicPosition(point, isMove ? time : 0);
				graphic.show = true;
				graphic.attr = obj;
			} else {
				graphic.show = false;
			}
		});
		// if (currentInfo) graphicLayerTrace.show = false
		// bindPopup(graphicLayerTrace)
	};

	const bindPopup = (graphic) => {
		graphic.bindPopup(
			function (event) {
				const attr = {};
				const formattedDate = moment(event.graphic.attr.gDate).format(
					'YYYY-MM-DD HH:mm:ss',
				);
				attr['车辆类型:'] = event.graphic.attr.type_of_industry;
				attr['发送时间:'] = formattedDate;
				attr['车辆速度:'] = Math.floor(event.graphic.attr.speed) + ' km/h';
				attr['排放浓度:'] =
					Math.floor(event.graphic.attr.downScrSensorOutput) + ' ppm';
				attr['发动机转速:'] = Math.floor(event.graphic.attr.engineRotation);

				return mars3d.Util.getTemplateHtml({
					title: '',
					template: 'all',
					attr,
				});
			},
			{ timeRender: true, closeButton: false },
		);
	};

	const clearGraphicLayer = () => {
		if (graphicLayerTrace) {
			graphicLayerTrace.clearDrawing();
			graphicLayerTrace.clear();
			graphicLayerTrace.enabledEvent = false;
			map.removeLayer(graphicLayerTrace);
		}
	};

	const addGraphicLayer = () => {
		graphicLayerTrace = new mars3d.layer.GraphicLayer();
		map.addLayer(graphicLayerTrace);

		graphicLayerTrace.on(mars3d.EventType.dblClick, function (event) {
			console.log('监听layer， 双击了矢量对象', event);
			// const cartesianPosition = event.graphic.position
			// const cartographic = Cesium.Cartographic.fromCartesian(cartesianPosition)
			const VIN = event.graphic.id.split('|')[0];
			setCurrentInfo({
				VIN: VIN,
				type: event.graphic.id.split('|')[1],
				lon: event.graphic.id.split('|')[2],
				lat: event.graphic.id.split('|')[3],
			});
			setOpenTrajectory(true);
			setBickDate(true);
			// viewRealTimeVehicleData(VIN)
			// graphicLayerTrace.show = false
			// event.graphic.show = true
		});
		//  在graphic数据上绑定右键菜单
		// graphicLayerTrace.bindContextMenu([
		//   {
		//     text: '查看车辆信息',
		//     callback: (e) => {
		//       const graphic = e.graphic
		//       if (graphic?.position) {
		//         graphicShow = false
		//         setOpenTrajectory(true)
		//         setBickDate(true)
		//         const cartesianPosition = graphic.position
		//         const cartographic = Cesium.Cartographic.fromCartesian(cartesianPosition)
		//         setCurrentInfo({
		//           VIN: graphic.id.split('|')[0],
		//           type: graphic.id.split('|')[1],
		//           lon: Cesium.Math.toDegrees(cartographic.longitude),
		//           lat: Cesium.Math.toDegrees(cartographic.latitude),
		//           layerId
		//         })
		//       }
		//     }
		//   }
		// ])
	};

	const viewRealTimeVehicleData = (vid) => {
		window.electron.ipcRenderer.send('layer', 'VehicleRealTimePosition');
		graphicLayerModel = new mars3d.layer.GraphicLayer();
		map.addLayer(graphicLayerModel);
		defaultPoint['lng'] = 0;
		defaultPoint['lat'] = 0;
		let minute = 1;
		let jsonData = sessionStorage.getItem('zcJsonData');
		if (jsonData) {
			jsonData = JSON.parse(jsonData);
			minute = jsonData?.vehicleDelayTime ? jsonData.vehicleDelayTime : minute;
		}
		getVehicleRealTimePositionInfoData(vid, minute);
	};

	const getVehicleRealTimePositionInfoData = (vid, minute) => {
		let time = moment()
			.subtract(minute, 'minute')
			.format('YYYY-MM-DD HH:mm:ss');
		getVehicleRealTimePositionInfo({
			vid,
			time,
		})
			.then((res) => {
				if (res.length) {
					let lng = res[0].lng;
					let lat = res[0].lat;
					if (res[0].lng == 0 || res[0].lat == 0) {
						lng = defaultPoint['lng'];
						lat = defaultPoint['lat'];
					} else {
						defaultPoint['lng'] = lng;
						defaultPoint['lat'] = lat;
					}
					const gcj2wgs2 = new mars3d.PointTrans.wgs2gcj([lng, lat]);
					const point = new mars3d.LngLatPoint(gcj2wgs2[0], gcj2wgs2[1]);
					const position = point;

					if (graphicModel) {
						graphicModel.addDynamicPosition(point, 6);
						graphicModel.attr = { ...res[0] };
						// 绑定popup
						bindPopup(graphicModel);
						graphicModel.openPopup();
					} else {
						graphicModel = new mars3d.graphic.ModelPrimitive({
							position,
							style: {
								url: getCarTypeModel(res[0].type_of_industry),
								size: 2,
								fill: true,
								color: getCarColor(res[0].type_of_industry),
								minimumPixelSize: 50,
								// minimumPixelSize: 40,
								// distanceDisplayCondition: true,
								// distanceDisplayCondition_far: 5000
							},
							// camera: {
							//   type: 'gs',
							//   pitch: -30,
							//   radius: 1000
							// },
							show: true,
							attr: { ...res[0] },
						});
						graphicLayerModel.addGraphic(graphicModel);
						graphicModel.addDynamicPosition(point, 0);
						// graphicModel.flyTo({
						//   pitch: -30,
						//   radius: 1000
						// })
					}
				}
				if (realVehicleTime) {
					clearTimeout(realVehicleTime);
					realVehicleTime = null;
				}
				realVehicleTime = setTimeout(() => {
					getVehicleRealTimePositionInfoData(vid, minute);
				}, 5000);
			})
			.catch((err) => {
				console.log(err);
				if (realVehicleTime) {
					clearTimeout(realVehicleTime);
					realVehicleTime = null;
				}
				realVehicleTime = setTimeout(() => {
					getVehicleRealTimePositionInfoData(vid, minute);
				}, 5000);
			});
	};

	useEffect(() => {
		if (!currentInfo) {
			// console.log('currentInfo?.VIN?????', false)
			if (graphicLayerTrace) graphicLayerTrace.show = true;
			window.electron.ipcRenderer.send('layer', layerId);
		} else {
			// console.log('currentInfo?.VIN?????', true)
			if (graphicLayerTrace) graphicLayerTrace.show = false;
		}
	}, [currentInfo]);

	useEffect(() => {
		// if (!visible) return;
		// let nums = 190009;
		// let times = 2;
		// let jsonData = sessionStorage.getItem('zcJsonData');
		// if (jsonData) jsonData = JSON.parse(jsonData);
		// nums = jsonData?.carNumber || nums;
		// times = jsonData?.offlineTime || times;
		// getVehicleNumber({
		// 	nums,
		// 	times,
		// 	target: 1,
		// 	county_name: COUNTY_ID_NAME_MAP[countyId],
		// })
		// 	.then((res) => {
		// 		if (res.length) {
		// 			const data = res.map((item) => {
		// 				return {
		// 					value: item.value,
		// 					name: item.type,
		// 					color: getCarColor(item.type),
		// 				};
		// 			});
		// 			setPieData(data);
		// 		}
		// 	})
		// 	.catch((error) => {
		// 		console.log('error', error);
		// 	});
	}, [countyId]);

	useEffect(() => {
		window.electron.ipcRenderer.on('VINs', (_event, VINs) => {
			console.log('VINs~~~~~~~~~~~', JSON.parse(VINs));
			let _VINs = JSON.parse(VINs);
			if (_VINs && _VINs.length > 0) {
				clearGraphicLayer();
				addGraphicLayer();
				addDemoGraphics(_VINs);
				VehicleLocationData = JSON.parse(VINs);
				// console.log('22222222222222+++++++++++++')
				getMessage();
			}
			_VINs = [];
		});

		return () => {
			window.electron.ipcRenderer.removeAllListeners('VINs');
			window.electron.ipcRenderer.removeAllListeners('message');
			// if (messageTime) {
			//   clearTimeout(messageTime)
			//   messageTime = null
			// }
		};
	}, []);

	useEffect(() => {
		if (!visible) return;
		// if (localStorage.getItem('selectMode') !== selectMode.key) {
		//   localStorage.setItem('selectMode', selectMode.key)
		//   window.electron.ipcRenderer.removeAllListeners('message')
		// }
		window.electron.ipcRenderer.send('extent', {
			extent: getMapExtent(map),
			type: selectMode,
			is_illegal,
			vehicleState,
		});

		// 推送extent
		let timer;
		let moveEndListener = map.scene.camera.moveEnd.addEventListener(
			function () {
				if (timer) {
					return;
				}
				timer = setTimeout(function () {
					window.electron.ipcRenderer.send('extent', {
						extent: getMapExtent(map),
						type: selectMode,
						is_illegal,
						vehicleState,
					});
					timer = null;
				}, 3000);
			},
		);

		return () => {
			moveEndListener();
			clearInterval(timer);
		};
	}, [selectMode.key]);

	useEffect(() => {
		if (!visible) return;
		setCurrentLayerId(layerId);
		setSwitchingPointsId(layerId);
		if (VehicleLocationData) {
			addDemoGraphics(VehicleLocationData);
			// getMessage()
		}
		return () => {
			// console.log('==============Trace!!!!====return==================');
			clearGraphicLayer();
			// 取消推送extent
			window.electron.ipcRenderer.removeAllListeners('VINs');
			window.electron.ipcRenderer.removeAllListeners('message');
			// if (messageTime) {
			//   clearTimeout(messageTime)
			//   messageTime = null
			// }
		};
	}, [visible]);

	return (
		<>
			{!showFlash && !openTrajectory && pieData.length > 0 ? (
				<VehicleIndustryNumber data={pieData} title={'实时在线'} />
			) : null}
		</>
	);
};
