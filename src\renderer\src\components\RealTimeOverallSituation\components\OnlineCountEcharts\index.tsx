import { useEffect, useState } from 'react';
import styled from 'styled-components';
import Echarts from '@renderer/components/echarts';
import * as echarts from 'echarts';
import DelayUI from '../../../../baseUI/DelayUI';

export const Container = styled.div`
	width: 180px;
	height: 200px;
	position: relative;
	.echarts-title {
		position: absolute;
		top: 35%;
		left: 30%;
		display: flex;
		flex-direction: column;
		div {
			text-align: center;
		}
		.online-title {
			font-size: 18px;
			font-family: 'TimesNewRoman', PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.count {
			font-size: 18px;
			color: rgba(1, 226, 255, 1);
			font-weight: 700;
		}
		.percent {
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
`;

const OnlineCountEcharts = (props) => {
	const { accumulateData, data, showEcharts } = props;

	//const [data,setData] =useState()

	useEffect(() => {
		if (showEcharts) {
			//setData(accumulateData)
		}
	}, [showEcharts]);

	const _pie = () => {
		let dataArr = [];
		for (var i = 0; i < 8; i++) {
			if (i % 2 === 0) {
				// @ts-ignore
				dataArr.push({
					name: (i + 1).toString(),
					value: 30,
					itemStyle: {
						color: 'rgba(184, 231, 241, 0.32)',
						borderWidth: 0,
						borderColor: 'rgb(2,9,18)',
					},
				});
			} else {
				// @ts-ignore
				dataArr.push({
					name: (i + 1).toString(),
					value: 40,
					itemStyle: {
						color: 'rgb(2,9,18,0)',
						borderWidth: 0,
						borderColor: 'rgb(2,9,18)',
					},
				});
			}
		}
		return dataArr;
	};
	const _pie1 = () => {
		let dataArr = [];
		for (var i = 0; i < 6; i++) {
			if (i % 2 === 0) {
				// @ts-ignore
				dataArr.push({
					name: (i + 1).toString(),
					value: 30,
					itemStyle: {
						color: 'rgba(184, 231, 241, 0.32)',
						borderWidth: 0,
						borderColor: 'rgb(2,9,18)',
					},
				});
			} else {
				// @ts-ignore
				dataArr.push({
					name: (i + 1).toString(),
					value: 40,
					itemStyle: {
						color: 'rgb(2,9,18,0)',
						borderWidth: 0,
						borderColor: 'rgb(2,9,18)',
					},
				});
			}
		}
		return dataArr;
	};
	const color = ['rgba(255, 255, 255, 1)', 'rgba(1, 226, 255, 1)'];
	const color1 = ['rgba(255, 255, 255, 1)', 'rgba(1, 226, 255, 1)'];
	const color2 = ['rgba(255, 255, 255, 0.6)', 'rgba(70, 177, 226, 0.8)'];
	const angle = 330;
	const chartData = [];
	for (let i = 0; i < data.length; i++) {
		chartData.push(
			// @ts-ignore
			{
				value: 0,
				name: '',
				itemStyle: {
					normal: {
						label: {
							show: false,
						},
						labelLine: {
							show: false,
						},
						color: 'rgba(0,0,0,0)',
					},
				},
			},
			//  小圆圈
			{
				value: 0,
				name: '',
				label: {
					position: 'inner',
					backgroundColor: color1[i],
					width: 4,
					height: 4,
					borderWidth: 4,
					borderTYpe: 'solid',
					borderColor: color[i],
					borderRadius: 300,
				},
			},
			//  数据
			{
				value: data[i].value,
				name: data[i].name,
				itemStyle: {
					normal: {
						borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
							{ offset: 0, color: color2[i] },
							{ offset: 1, color: color1[i] },
						]),
						color: color[i],
						borderRadius: 300,
						borderWidth: 4,
					},
				},
				label: {
					show: false,
				},
			},
		);
	}
	let option = {
		series: [
			{
				name: '',
				type: 'pie',
				radius: ['130%', '132%'],
				center: ['50%', '50%'],
				clockWise: false,
				hoverAnimation: false,
				emphasis: {
					disabled: true,
				},
				top: 'center',
				data: chartData,
			},
			{
				type: 'pie',
				silent: true, //图形是否不响应和触发鼠标事件，默认为 false，即响应和触发鼠标事件
				startAngle: angle - 30 > 0 ? angle - 30 : angle + 330,
				radius: ['84%', '88%'],
				center: ['50%', '50%'],
				label: { show: false },
				labelLine: { show: false },
				data: _pie(),
				animation: false, //是否开启动画
			},
			{
				type: 'pie',
				silent: true, //图形是否不响应和触发鼠标事件，默认为 false，即响应和触发鼠标事件
				startAngle: angle > 0 ? angle : angle + 330,
				radius: ['57%', '60%'],
				center: ['50%', '50%'],
				label: { show: false },
				labelLine: { show: false },
				data: _pie1(),
				animation: false, //是否开启动画
			},
		],
		legend: {
			type: 'plain',
			orient: 'horizontal',
			icon: 'circle',
			itemWidth: 12,
			itemHeight: 12,
			itemGap: 40,
			textStyle: {
				color: '#ffffff',
			},
			pageTextStyle: {
				color: '#fff',
			},
			pageIconColor: '#fff',
			pageIconInactiveColor: '#ccc',
			bottom: -4,
			data,
			left: 30,
			formatter: function (name) {
				return `${name}`;
			},
		},
		// animationDelay: function (idx) {
		//   // 越往后的数据延迟越大
		//   return idx * 1000
		// }
	};
	return (
		<Container>
			<Echarts option={option}></Echarts>
			<div className="echarts-title">
				<div className="online-title">累计上线</div>
				<div className="count">
					<DelayUI
						targetNumber={accumulateData?.accumulate_total_online}
						style={{ fontSize: '18px', color: '#E2F0FF' }}
						delayTime={5000}
					/>
				</div>
				<div className="percent">
					<DelayUI
						targetNumber={accumulateData?.accumulate_total_online_rate}
						style={{ fontSize: '18px', color: '#e2f0ff' }}
						delayTime={5000}
					/>
					%
				</div>
			</div>
		</Container>
	);
};

export default OnlineCountEcharts;
