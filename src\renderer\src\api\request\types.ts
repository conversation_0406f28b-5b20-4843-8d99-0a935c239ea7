import type { AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * 拦截器
 *
 * @property requestInterceptors - 请求拦截器
 * @property requestInterceptorsCatch - 请求失败拦截器
 * @property responseInterceptors - 响应拦截器
 * @property responseInterceptorsCatch - 响应失败拦截器
 */
export interface AxiosInterceptors {
	// 请求拦截
	requestInterceptors?: (config: AxiosRequestConfig) => AxiosRequestConfig;
	requestInterceptorsCatch?: (err: any) => any;

	// 响应拦截
	// responseInterceptors?: (config: AxiosResponse) => AxiosResponse
	// 类拦截器中已经将返回的类型改变，所以需要为拦截器传递一个泛型
	responseInterceptors?: <T = AxiosResponse>(response: T) => T;
	responseInterceptorsCatch?: (err: any) => any;
}

/**
 * 自定义请求参数
 *
 * @property 【interceptors】 - 实例拦截器
 */
export interface CustomRequestConfig extends AxiosRequestConfig {
	interceptors?: AxiosInterceptors;
}
