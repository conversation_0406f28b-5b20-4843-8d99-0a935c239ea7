import { useState } from 'react';

export const useLocalStorage = <T>(keyName: string, defaultValue: T) => {
	const [storedValue, setStoredValue] = useState(() => {
		try {
			const value = window.localStorage.getItem(keyName);
			if (value) {
				return JSON.parse(value);
			} else {
				window.localStorage.setItem(keyName, JSON.stringify(defaultValue));
			}
		} catch (e) {
			return defaultValue;
		}
	});

	const setValue = (value: T) => {
		try {
			window.localStorage.setItem(keyName, JSON.stringify(value));
		} catch (e) {}
		setStoredValue(value);
	};

	return [storedValue, setValue];
};
