import { useEffect, useContext, useState } from 'react';
import * as mars3d from 'mars3d';
import { MapContext, PageContext } from '@renderer/context';
import { getCarColor } from '../../../../main/data';
import { getEnterpriseStatistics } from '@renderer/api';
import ScrollListWidthTitle from '@renderer/components/SlidingLayer/DailyActivityLevel/components/ScrollListWidthTitle';
import styled from 'styled-components';
import { tranNumberS } from '@renderer/hooks/index';
import background from '@renderer/components/SlidingLayer/DailyActivityLevel/components/ScrollListWidthTitle/background.png';
import title from '@renderer/components/SlidingLayer/DailyActivityLevel/components/ScrollListWidthTitle/title.png';
let UserVehicleLocationData = null;
let graphicEnterpriseLayer,
	axesNumber = 0.03; // 1000米的坐标系数: 0.008989

export default (props) => {
	const { map, visible, layerId } = props;
	const [columns, setColumns] = useState([]);
	const [data, setData] = useState([]);
	let currentTime = Date.parse(new Date());
	const [name, setName] = useState('');
	const {
		currentInfo,
		setCurrentInfo,
		setVehicleSearchData,
		setBickDate,
		currentLayerId,
		setCurrentLayerId,
		showFlash,
	} = useContext(MapContext);
	const {
		currentTopNavMenu,
		selectRegionDate,
		selectMode,
		setSwitchingPointsId,
		setOpenTrajectory,
		openTrajectory,
		countyId,
		setCurrentSelectedId,
		affiliationId,
		setAffiliationId,
	} = useContext(PageContext);
	let messageList = [];

	const getTopicIndex = {
		联网车辆: 6,
		在线车辆: 4,
		排放量: 2,
		里程: 3,
		油耗: 1,
		违规: 5,
	};

	const addDemoGraphics = (points) => {
		if (!graphicEnterpriseLayer) {
			graphicEnterpriseLayer = new mars3d.layer.GraphicLayer();
			map.addLayer(graphicEnterpriseLayer);
		}
		for (let i = 0; i < points.length; i++) {
			const item = points[i];
			const VIN = item.split('|')[0];
			const attr = { VIN };
			const type = item.split('|')[1];
			const lon = item.split('|')[2];
			const lat = item.split('|')[3];
			const graphicPoint = new mars3d.graphic.PointPrimitive({
				id: `${item}`,
				position: new mars3d.PointTrans.wgs2gcj([lon, lat]),
				style: {
					color: getCarColor(type),
					pixelSize: 8,
					distanceDisplayCondition: true,
					distanceDisplayCondition_far: Number.MAX_VALUE,
				},
				attr,
			});
			graphicEnterpriseLayer.addGraphic(graphicPoint);
		}
	};

	const changePosition = (time, messageList) => {
		const messageMap = new Map();
		messageList.forEach((element) => {
			messageMap.set(element.VIN, element);
		});

		graphicEnterpriseLayer.eachGraphic((graphic) => {
			if (graphic.isPrivate) {
				return;
			}
			const obj = messageMap.get(graphic.attr.VIN);
			if (obj) {
				const gcj2wgs = new mars3d.PointTrans.wgs2gcj([obj.lon, obj.lat]);
				const point = new mars3d.LngLatPoint(gcj2wgs[0], gcj2wgs[1]);
				const lon_dist = Math.abs(graphic.attr.lon - obj.lon) || 1;
				const lat_dist = Math.abs(graphic.attr.lat - obj.lat) || 1;
				const isMove = lon_dist < axesNumber && lat_dist < axesNumber;
				graphic.addDynamicPosition(point, isMove ? time : 0);
				graphic.show = true;
				graphic.attr = obj;
			} else {
				graphic.show = false;
			}
		});
	};

	const clearGraphicLayer = () => {
		if (graphicEnterpriseLayer) {
			graphicEnterpriseLayer.clearDrawing();
			graphicEnterpriseLayer.clear();
			graphicEnterpriseLayer.enabledEvent = false;
			map.removeLayer(graphicEnterpriseLayer);
		}
	};

	const addGraphicLayer = () => {
		graphicEnterpriseLayer = new mars3d.layer.GraphicLayer();
		map.addLayer(graphicEnterpriseLayer);

		graphicEnterpriseLayer.on(mars3d.EventType.dblClick, function (event) {
			const VIN = event.graphic.id.split('|')[0];
			setCurrentInfo({
				VIN: VIN,
				type: event.graphic.id.split('|')[1],
				lon: event.graphic.id.split('|')[2],
				lat: event.graphic.id.split('|')[3],
			});
			setOpenTrajectory(true);
			setBickDate(true);
		});
	};

	useEffect(() => {
		if (!currentInfo) {
			if (graphicEnterpriseLayer) graphicEnterpriseLayer.show = true;
			window.electron.ipcRenderer.send('layer', layerId);
		} else {
			if (graphicEnterpriseLayer) graphicEnterpriseLayer.show = false;
		}
	}, [currentInfo]);

	useEffect(() => {
		window.electron.ipcRenderer.on('message', (_event, message) => {
			if (Date.parse(new Date()) - currentTime < 6000) return;
			let _VINs = JSON.parse(message).map(
				(item) => `${item.VIN}|${item.type}|${item.lon}|${item.lat}`,
			);
			if (_VINs && _VINs.length > 0) {
				clearGraphicLayer();
				addGraphicLayer();
				addDemoGraphics(_VINs);
				UserVehicleLocationData = _VINs;
			}
			currentTime = Date.parse(new Date());
			messageList = JSON.parse(message);
			if (messageList && messageList.length > 0) {
				setVehicleSearchData(JSON.parse(message));
				changePosition(8, messageList);
			}
		});

		return () => {
			window.electron.ipcRenderer.removeAllListeners('message');
		};
	}, []);

	useEffect(() => {
		if (!visible) return;
		setCurrentLayerId(layerId);
		setCurrentSelectedId(layerId);
		setSwitchingPointsId(layerId);
		if (UserVehicleLocationData) {
			addDemoGraphics(UserVehicleLocationData);
			// getMessage()
		}
		return () => {
			// console.log('==============Trace!!!!====return==================');
			clearGraphicLayer();
			// 取消推送extent
			window.electron.ipcRenderer.removeAllListeners('VINs');
			window.electron.ipcRenderer.removeAllListeners('message');
		};
	}, [visible]);

	useEffect(() => {
		if (affiliationId === '') return;
		window.electron.ipcRenderer.send('change:affiliation', affiliationId);
	}, [affiliationId]);

	useEffect(() => {
		if (selectRegionDate.customDate.start_time == '' || currentTopNavMenu == '')
			return;
		const params = {
			start_time: selectRegionDate.customDate.start_time,
			end_time: selectRegionDate.customDate.end_time,
			topic: getTopicIndex[currentTopNavMenu],
		};
		getEnterpriseStatistics(params).then((res) => {
			res.data =
				getTopicIndex[currentTopNavMenu] === 5
					? res.data.map((item) => {
							return {
								...item,
								rate: (item.rate * 100).toFixed(2) + '%',
							};
					  })
					: res.data;
			let arr = [];
			res.columns.map((i) => {
				if (i.dataIndex == 'value') {
					arr.push({
						title: i.title,
						dataIndex: i.dataIndex,
						render: (text, record, index) => {
							return getTopicIndex[currentTopNavMenu] == 4 ||
								getTopicIndex[currentTopNavMenu] == 6
								? Math.trunc(Number(text))
								: text;
						},
					});
				} else {
					arr.push({
						title: i.title,
						dataIndex: i.dataIndex,
					});
				}
			});
			setColumns(arr);
			res.data.map((item) => (item.value = tranNumberS(item.value, 2))),
				setData(res.data);
			setAffiliationId(res.data[0].affiliation);
			setName(res.data[0].name);
		});
	}, [currentTopNavMenu, selectRegionDate]);

	return (
		<>
			<UserterpriseContainer>
				<div className="user-enterprise-list">
					<ScrollListWidthTitle
						columns={columns}
						data={data}
						OnRowSelection={(record) => {
							setAffiliationId(record.affiliation);
							setName(record.name);
						}}
						{...optionTable}
					/>
				</div>
			</UserterpriseContainer>
			{name && (
				<CompanyNameContainer>
					<div className="title">
						<div></div>
					</div>
					<div className="name"> {name}</div>
				</CompanyNameContainer>
			)}
		</>
	);
};

const UserterpriseContainer = styled.div`
	.user-enterprise-list {
		width: 400px;
		position: absolute;
		z-index: 5;
		right: 120px;
		top: 90px;
	}
`;

const optionTable = {
	width: '100%',
	height: '600px',
	fontSize: 18,
	thclassname: 'th_row',
	tablebgcolor: 'rgba(9,30,59,0.7)',
	trheight: '40px',
	thheight: '40px',
	customwidth: true,
	rowbgcolor: [
		'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
	],
	thbgcolor: '#081F38',
};

const CompanyNameContainer = styled.div`
	position: absolute;
	width: 24%;
	height: 10%;
	bottom: 0;
	left: 38%;
	z-index: 10;
	.title {
		width: 100%;
		height: 40%;
		div {
			background-image: url(${title});
			width: 8%;
			height: 100%;
			margin: 0 auto;
			background-repeat: no-repeat;
			background-size: 100% 100%;
		}
	}
	.name {
		width: 100%;
		height: 58%;
		font-size: 22px;
		text-align: center;
		color: #fff;
		margin-bottom: 4px;
		padding: 14px;
		background-image: url(${background});
		background-repeat: no-repeat;
		background-size: 100% 100%;
	}
`;
