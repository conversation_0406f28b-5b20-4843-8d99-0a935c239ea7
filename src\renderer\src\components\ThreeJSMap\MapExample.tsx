import React, { useState, useEffect } from 'react';
import ThreeJSMapEnhanced from './ThreeJSMapEnhanced';
import {
	beijingGeoJSON,
	generateTestData,
	dataTypeConfig,
	colorSchemes,
} from './testData';
import styled from 'styled-components';

// 示例样式
const ExampleContainer = styled.div`
	padding: 20px;
	background: #1a1a1a;
	min-height: 100vh;
	color: #fff;
`;

const Title = styled.h1`
	text-align: center;
	margin-bottom: 20px;
	color: #fff;
`;

const ControlPanel = styled.div`
	display: flex;
	gap: 20px;
	margin-bottom: 20px;
	flex-wrap: wrap;
`;

const ControlGroup = styled.div`
	display: flex;
	flex-direction: column;
	gap: 8px;
`;

const Label = styled.label`
	font-size: 14px;
	color: #ccc;
`;

const Select = styled.select`
	padding: 8px;
	border: 1px solid #333;
	background: #2a2a2a;
	color: #fff;
	border-radius: 4px;
`;

const Button = styled.button`
	padding: 8px 16px;
	background: #0066cc;
	color: #fff;
	border: none;
	border-radius: 4px;
	cursor: pointer;

	&:hover {
		background: #0052a3;
	}
`;

const InfoPanel = styled.div`
	position: fixed;
	top: 20px;
	right: 20px;
	background: rgba(0, 0, 0, 0.8);
	border: 1px solid #333;
	border-radius: 8px;
	padding: 16px;
	max-width: 300px;
	z-index: 100;
`;

const MapExample: React.FC = () => {
	const [dataType, setDataType] = useState('population');
	const [colorScheme, setColorScheme] = useState('blue');
	const [mapData, setMapData] = useState(generateTestData('population'));
	const [selectedRegion, setSelectedRegion] = useState<string | null>(null);
	const [hoveredRegion, setHoveredRegion] = useState<string | null>(null);

	// 当数据类型改变时更新数据
	useEffect(() => {
		setMapData(generateTestData(dataType));
		// 根据数据类型自动切换颜色方案
		const config = dataTypeConfig[dataType];
		if (config) {
			setColorScheme(config.colorScheme);
		}
	}, [dataType]);

	const handleRegionClick = (regionName: string, data: any) => {
		setSelectedRegion(regionName);
		console.log('点击区域:', regionName, data);
	};

	const handleRegionHover = (regionName: string | null, data: any) => {
		setHoveredRegion(regionName);
	};

	const refreshData = () => {
		setMapData(generateTestData(dataType));
	};

	const getDataTypeLabel = (type: string) => {
		const config = dataTypeConfig[type];
		return config ? `${config.label}（${config.unit}）` : '数值';
	};

	return (
		<ExampleContainer>
			<Title>Three.js 3D地图可视化示例</Title>

			<ControlPanel>
				<ControlGroup>
					<Label>数据类型:</Label>
					<Select
						value={dataType}
						onChange={(e) => setDataType(e.target.value)}
					>
						<option value="population">人口数量</option>
						<option value="gdp">GDP</option>
						<option value="pollution">污染指数</option>
					</Select>
				</ControlGroup>

				<ControlGroup>
					<Label>颜色方案:</Label>
					<Select
						value={colorScheme}
						onChange={(e) => setColorScheme(e.target.value)}
					>
						<option value="blue">蓝色</option>
						<option value="red">红色</option>
						<option value="green">绿色</option>
						<option value="purple">紫色</option>
					</Select>
				</ControlGroup>

				<ControlGroup>
					<Label>操作:</Label>
					<Button onClick={refreshData}>刷新数据</Button>
				</ControlGroup>
			</ControlPanel>

			<ThreeJSMapEnhanced
				geoJsonData={beijingGeoJSON}
				mapData={mapData}
				width={1200}
				height={700}
				colorScale={colorSchemes[colorScheme]}
				extrudeHeight={0.2}
				cameraPosition={[0, 8, 8]}
				showLegend={true}
				showControls={true}
				enableTooltip={true}
				onRegionClick={handleRegionClick}
				onRegionHover={handleRegionHover}
			/>

			{(selectedRegion || hoveredRegion) && (
				<InfoPanel>
					<h3>区域信息</h3>
					{selectedRegion && (
						<div>
							<strong>选中区域:</strong> {selectedRegion}
							<br />
							<strong>数据:</strong>{' '}
							{mapData.find((d) => d.name === selectedRegion)?.value || 0}
							<br />
							<strong>类型:</strong> {getDataTypeLabel(dataType)}
						</div>
					)}
					{hoveredRegion && hoveredRegion !== selectedRegion && (
						<div style={{ marginTop: '10px', color: '#ccc' }}>
							<strong>悬停区域:</strong> {hoveredRegion}
							<br />
							<strong>数据:</strong>{' '}
							{mapData.find((d) => d.name === hoveredRegion)?.value || 0}
						</div>
					)}

					<div style={{ marginTop: '15px', fontSize: '12px', color: '#999' }}>
						<strong>操作说明:</strong>
						<br />
						• 鼠标拖拽：旋转视角
						<br />
						• 滚轮：缩放
						<br />
						• 点击区域：选择
						<br />• 悬停：查看信息
					</div>
				</InfoPanel>
			)}
		</ExampleContainer>
	);
};

export default MapExample;
