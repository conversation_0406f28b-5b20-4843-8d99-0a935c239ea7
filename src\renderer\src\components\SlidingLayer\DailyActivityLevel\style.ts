import styled from 'styled-components';
import bg from '@renderer/images/vehicleViolations/bg.png';
const LayoutContainer = styled.div`
	position: relative;
	.middle-container {
		margin-left: 10px;
		margin-top: 20px;
		padding-bottom: 20px;
		// .table {
		//   margin-top: 10px;
		//   width: 100%;
		//   height: 560px;
		//   overflow: hidden;
		//   background: linear-gradient(180deg, rgba(13, 130, 234, 0.31) 0%, rgba(9, 30, 59, 0) 100%);
		//   border-radius: 4px 4px 0px 0px;
		//   border: 1px solid rgba(54, 142, 193, 0.56);
		//   .list-content .list-ul .list-li {
		//     flex: 1;
		//     text-align: center;
		//     cursor: pointer;
		//     color: #d8f1ff;
		//     font-size: 16px;
		//     overflow: hidden;
		//     text-overflow: ellipsis;
		//   }
		//   .list-content .list-ul .list-li:nth-child(2) {
		//     flex: 1;
		//     text-align: left;
		//     cursor: pointer;
		//     color: #d8f1ff;
		//     font-size: 16px;
		//     overflow: hidden; //超出的文本隐藏
		//     text-overflow: ellipsis; //用省略号显示
		//     white-space: nowrap; //不换行
		//   }
		//   ul {
		//     padding: 0;
		//     margin: 0;
		//     list-style: none;
		//   }
		//   .column-ul {
		//     display: flex;
		//     flex-direction: row;
		//     justify-content: space-around;
		//     margin-left: -1px;
		//     border-right: 1px solid #3488b9;
		//     border-bottom: 1px solid #3488b9;
		//     .column-li {
		//       flex: 1;
		//       color: #d8f1ff;
		//       font-size: 16px;
		//       height: 20px;
		//       line-height: 20px;
		//       text-align: center;
		//       border-top: 1px solid #3488b9;
		//       border-left: 1px solid #3488b9;
		//       box-sizing: border-box;
		//     }
		//   }
		// }
		ul {
			padding: 0;
			margin: 0;
			list-style: none;
		}
		.column-ul {
			display: flex;
			flex-direction: row;
			justify-content: space-around;
			margin-left: -1px;
			border-right: 1px solid #3488b9;
			border-bottom: 1px solid #3488b9;
			background: #081f38;
			.column-li {
				flex: 1;
				color: #d8f1ff;
				font-size: 16px;
				height: 40px;
				line-height: 40px;
				text-align: center;
				//border-top: 1px solid #3488b9;
				//border-left: 1px solid #3488b9;
				box-sizing: border-box;
			}
		}
	}

	.right-container {
		margin-left: 10px;
		// .enterprise {
		//   display: flex;
		//   flex-direction: column;
		//   margin-top: 10px;
		//   .enterprise-item {
		//     display: flex;
		//     flex-direction: row;
		//     justify-content: space-between;
		//     padding-bottom: 5px;
		//     .up {
		//       line-height: 28px;
		//       span:nth-child(1) {
		//         font-size: 14px;
		//         color: #fe2c46;
		//         font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
		//         padding-right: 10px;
		//       }
		//       span:nth-child(2) {
		//         font-size: 16px;
		//         color: #ffffff;
		//         font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
		//       }
		//     }
		//     .down {
		//       display: flex;
		//       margin-left: 12px;
		//       height: 28px;
		//       display: flex;
		//       align-items: center;
		//       img {
		//         width: 9px;
		//         height: 7px;
		//         margin-right: 3px;
		//       }
		//       span:nth-child(2) {
		//         padding-right: 3px;
		//         font-size: 14px;
		//         color: #d1d1d1;
		//         font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
		//       }
		//       span:nth-child(3) {
		//         //margin-left: 0px;
		//         font-size: 14px;
		//         color: #4ce6ff;
		//         font-family: 'TimesNewRoman', DINAlternate-Bold;
		//         padding-right: 10px;
		//       }

		//       span:nth-child(4) {
		//         font-size: 14px;
		//         color: #d1d1d1;
		//         font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
		//         padding-right: 3px;
		//       }
		//       span:nth-child(5) {
		//         font-size: 14px;
		//         color: #4ce6ff;
		//         font-family: 'TimesNewRoman', DINAlternate-Bold;
		//       }
		//     }
		//   }
		// }
	}

	// .detailsPop {
	//   width: 500px;
	//   height: 239px;
	//   background-image: url(${bg});
	//   background-repeat: no-repeat;
	//   position: absolute;
	//   bottom: -200%;
	//   left: 50%;
	//   transform: translate(-50%, -50%);
	//   transition: all 0.5s;
	//   &.active {
	//     bottom: -51%;
	//   }
	//   .details-container {
	//     display: flex;
	//     height: 239px;
	//     flex-direction: column;
	//     justify-content: center;
	//     margin-left: 57px;
	//     .item {
	//       height: 30px;
	//       line-height: 30px;
	//       span:nth-child(1) {
	//         color: #d8f1ff;
	//         font-size: 16px;
	//         margin-right: 15px;
	//       }
	//       span:nth-child(2) {
	//         color: #d8f1ff;
	//         font-size: 16px;
	//       }
	//     }
	//   }
	// }
`;
export default LayoutContainer;
