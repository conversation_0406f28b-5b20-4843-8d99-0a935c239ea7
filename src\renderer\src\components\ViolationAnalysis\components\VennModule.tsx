/**
 * 韦恩图
 */
import { useEffect, useRef } from 'react';
import * as d3 from 'd3';
import { VennDiagram, sortAreas } from '@upsetjs/venn.js';

const VennModule = () => {
	const divRef = useRef<div>(null);

	const sets = [
		{ sets: ['A'], size: 12 },
		{ sets: ['B'], size: 12 },
		{ sets: ['C'], size: 12 },
		{ sets: ['A', 'B'], size: 2 },
		{ sets: ['A', 'C'], size: 2 },
		{ sets: ['B', 'C'], size: 2 },
		{ sets: ['A', 'B', 'C'], size: 2 },
	];

	useEffect(() => {
		var chart = VennDiagram().width(200).height(200);

		var div = d3.select(divRef.current);
		div.datum(sets).call(chart);

		var tooltip = d3.select('body').append('div').attr('class', 'venntooltip');

		div
			.selectAll('path')
			.style('stroke-opacity', 0)
			.style('stroke', '#fff')
			.style('stroke-width', 3);

		div
			.selectAll('g')
			.on('mouseenter', function (d, i) {
				sortAreas(div, i);

				tooltip.transition().duration(400).style('opacity', 0.9);
				tooltip.text(i.size + ' users');

				var selection = d3.select(this).transition('tooltip').duration(400);
				selection
					.select('path')
					.style('fill-opacity', i.sets.length == 1 ? 0.4 : 0.1)
					.style('stroke-opacity', 1);
			})

			.on('mousemove', function (event) {
				tooltip
					.style('left', event.pageX + 'px')
					.style('top', event.pageY - 28 + 'px');
			})

			.on('mouseleave', function (d, i) {
				tooltip.transition().duration(400).style('opacity', 0);
				var selection = d3.select(this).transition('tooltip').duration(400);
				selection
					.select('path')
					.style('fill-opacity', i.sets.length == 1 ? 0.25 : 0.0)
					.style('stroke-opacity', 0);
			});
	}, []);

	return <div ref={divRef}></div>;
};

export default VennModule;
