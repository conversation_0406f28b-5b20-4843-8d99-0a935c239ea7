import { tranNumber } from '@renderer/hooks/index';

export const AreaRightSideTop = (data) => {
	// console.log(data)

	return {
		tooltip: {
			trigger: 'axis',
			// axisPointer: {
			// 	type: 'cross',
			// 	crossStyle: {
			// 		color: '#999',
			// 	},
			// },
		},
		grid: {
			top: 30,
			bottom: 25,
		},
		legend: {
			data: ['企业数', '联网车辆'],
			top: 0, // 调整图例的位置，使其移动到图的上方
			right: 100, // 调整图例的位置，使其移动到图的右侧
			textStyle: {
				color: '#fff',
				fontSize: 16,
			},
			itemWidth: 10, // 设置图例项的宽度
			itemHeight: 10, // 设置图例项的高度
			itemShape: 'circle', // 设置图例项的形状为圆形
		},
		xAxis: [
			{
				type: 'category',
				data: data?.adm,
				axisPointer: {
					type: 'shadow',
				},
				axisLabel: {
					textStyle: {
						color: '#E2F0FF',
						fontSize: 16,
					},
				},
			},
		],
		yAxis: [
			{
				name: `单位: 辆`,
				nameTextStyle: {
					color: 'rgba(212, 232, 254, 1)',
					fontSize: 12,
				},
				type: 'value',
				splitLine: {
					lineStyle: {
						type: 'dashed',
					},
				},
				axisLabel: {
					textStyle: {
						color: '#E2F0FF',
						fontSize: 16,
					},
					formatter: function (value, index) {
						return tranNumber(Number(value), 2);
					},
				},
			},
			{
				type: 'value',
				name: '企业数',
				nameTextStyle: {
					color: '#d8e2ef',
				},
				// interval: 1025,
				axisLabel: {
					formatter: function (value, index) {
						return tranNumber(Number(value), 2) + ' 家';
					},
					textStyle: {
						fontSize: '12',
						color: '#d8e2ef',
					},
				},
				splitLine: {
					show: false,
				},
				axisLine: {
					show: false,
				},
			},
		],
		series: [
			{
				name: '联网车辆',
				type: 'bar',
				tooltip: {},
				barWidth: 20,
				itemStyle: {
					// 设置柱状图的颜色为绿色
					color: 'rgb(102,204,204)',
				},
				data: data?.onlineData,
			},
			{
				name: '企业数',
				type: 'line',
				yAxisIndex: 1,
				itemStyle: {
					// 设置温度系列的样式
					color: 'rgb(42, 112, 252)', // 修改温度系列的颜色为橙色
				},
				data: data?.nums,
			},
		],
	};
};
export const AreaRightSideCenter = (value) => {
	const defaultData = {
		xdata: [],
		result: [
			{ name: '出行车辆', data: [] },
			{ name: '未出行车辆', data: [] },
		],
	};

	// 如果传入的值为空，使用默认数据
	const dataArr = Object.keys(value).length === 0 ? defaultData : value;
	const enterpriseArr = dataArr.xdata;

	function truncateString(str, maxLength) {
		if (str.length <= maxLength) {
			return str; // 如果字符串长度不超过 maxLength，则直接返回原字符串
		} else {
			return str.slice(0, maxLength) + '...'; // 截取字符串前 maxLength 个字符，并添加省略号
		}
	}

	// 遍历 affiliations 数组，在每个元素上应用 truncateString 方法，得到截取后的数组
	const truncatedEnterpriseArr = enterpriseArr.map((item) =>
		truncateString(item, 4),
	);

	// 头部菱形
	const diamondData = dataArr.result.reduce((pre, cur, index) => {
		pre[index] = cur.data.map(
			(el, id) => el + (pre[index - 1] ? pre[index - 1][id] : 0),
		);
		return pre;
	}, []);

	// color
	const color = [
		[
			{ offset: 0, color: 'rgba(0, 193, 246, 0.6)' },
			{ offset: 0.5, color: 'rgba(0, 248, 184, 1)' },
			{ offset: 0.5, color: 'rgba(0, 193, 246, 0.6)' },
			{ offset: 1, color: 'rgba(0, 248, 184, 1)' },
		],
		[
			{ offset: 0, color: 'rgba(29, 170, 236, 0.6)' },
			{ offset: 0.5, color: 'rgba(29, 142, 236, 1)' },
			{ offset: 0.5, color: 'rgba(29, 170, 236, 0.6)' },
			{ offset: 1, color: 'rgba(29, 142, 236, 1)' },
		],
		// [{ offset: 0, color: "rgba(251, 164, 0, 0.4)", }, { offset: 0.5, color: "rgba(238, 237, 0, 1)", }, { offset: 0.5, color: "rgba(251, 164, 0, 0.4)", }, { offset: 1, color: "rgba(238, 237, 0, 1)", }],
		// [{ offset: 0, color: "rgba(219, 94, 19, 0.4)", }, { offset: 0.5, color: "rgba(219, 23, 19, 1)", }, { offset: 0.5, color: "rgba(219, 94, 19, 0.4)", }, { offset: 1, color: "rgba(219, 23, 19, 1)", }],
	];
	const color1 = [
		'rgba(29, 142, 236, 1)',
		'rgba(238, 237, 0, 1)',
		// "rgba(219, 23, 19, 1)",
		//    "rgba(219, 23, 19, 1)",
	];
	// series
	const series = dataArr.result.reduce((p, c, i, array) => {
		p.push(
			{
				z: i + 1,
				stack: '总量',
				type: 'bar',
				name: c.name,
				barGap: '-100%',
				barWidth: 15,
				data: c.data,
				itemStyle: {
					color: {
						type: 'linear',
						x: 0,
						x2: 0,
						y: 0,
						y2: 1,
						colorStops: color[i],
					},
				},
			},
			{
				z: i + 10,
				type: 'pictorialBar',
				symbolPosition: 'end',
				symbol: 'diamond',
				symbolOffset: [5, 0],
				symbolSize: [10, 15],
				data: diamondData[i],
				itemStyle: { color: color1[i] },
				tooltip: { show: false },
			},
		);

		// 是否最后一个了？
		if (p.length === array.length * 2) {
			p.push({
				z: array.length * 2,
				type: 'pictorialBar',
				symbolPosition: 'start',
				data: dataArr.result[0].data,
				symbol: 'diamond',
				symbolOffset: [-5, 0],
				symbolSize: [10, 15],
				itemStyle: { color: 'rgba(0, 248, 184, 1)' },
				tooltip: { show: false },
			});
			return p;
		}

		return p;
	}, []);

	// tooltip
	const tooltip = {
		trigger: 'axis',
		formatter: function (params) {
			let tooltipContent = ''; // 初始化提示框内容字符串

			params.forEach(function (item) {
				if (item.seriesName === '出行车辆') {
					// 公司名
					tooltipContent += enterpriseArr[item.dataIndex] + '<br>';
					// 出行车辆
					tooltipContent += '出行车辆: ' + item.value + '<br>';
				} else if (item.seriesName === '未出行车辆') {
					// 未出行车辆
					tooltipContent += '未出行车辆: ' + item.value + '<br>';
				}
			});

			return tooltipContent; // 返回自定义的提示框内容
		},
	};

	// legend
	const legend = {
		data: dataArr.result.map((item) => item.name),
		textStyle: { fontSize: 16, color: '#fff' },
		itemWidth: 25,
		itemHeight: 15,
		itemGap: 15,
		bottom: 0,
		// 禁止点击
		selectedMode: false,
	};
	// grid
	const grid = { top: '7%', left: '15%', right: '3%', bottom: '15%' };

	// xAxis
	const xAxis = {
		splitLine: { lineStyle: { color: 'rgba(255,255,255, .05)' } },
		axisLine: { show: false },
		axisLabel: { textStyle: { fontSize: 16, color: '#fff' } },
		// min: 0,
		// max: 2500,
		// interval: 500
	};

	// yAxis
	const yAxis = [
		{
			name: `单位: 辆`,
			nameTextStyle: {
				color: '#D8DBDE',
				fontSize: 14,
				padding: [-10, 0],
			},
			axisTick: { show: true },
			axisLine: { lineStyle: { color: 'rgba(255,255,255, .2)' } },
			axisLabel: { textStyle: { fontSize: 16, color: '#E2F0FF' } },
			data: truncatedEnterpriseArr,
		},
	];

	return { tooltip, xAxis, yAxis, series, grid, legend };
};
export const AreaRightSideBottom = (value) => {
	if (!Object.keys(value)?.length) return {};
	const { onlineData, sumData, affiliations } = value;
	let truncatedAffiliations: any = []; // 定义一个空数组
	// 如果不为空，定义截取字符串函数 truncateString
	function truncateString(str, maxLength) {
		if (str.length <= maxLength) {
			return str; // 如果字符串长度不超过 maxLength，则直接返回原字符串
		} else {
			return str.slice(0, maxLength) + '...'; // 截取字符串前 maxLength 个字符，并添加省略号
		}
	}

	// 检查 affiliations 数组是否为空
	if (Array.isArray(affiliations) && affiliations.length > 0) {
		// 遍历 affiliations 数组，在每个元素上应用 truncateString 方法，得到截取后的数组
		truncatedAffiliations = affiliations.map((item) => truncateString(item, 3));
	}
	return {
		tooltip: {
			trigger: 'axis',
			// axisPointer: {
			// 	type: 'cross',
			// 	crossStyle: {
			// 		color: '#999',
			// 	},
			// },
			formatter: function (params) {
				let tooltipContent = ''; // 初始化提示框内容字符串
				// 遍历 params 数组，按照你想要的顺序添加每个数据项的提示信息
				params.forEach(function (item) {
					if (item.seriesName === '总量') {
						// 公司名
						tooltipContent += affiliations[item.dataIndex] + '<br>';
						tooltipContent += '总量: ' + item.value + '<br>';
					} else if (item.seriesName === '上线率') {
						// 上线率
						tooltipContent += '上线率: ' + item.value + '%' + '<br>';
					}
				});
				return tooltipContent; // 返回自定义的提示框内容
			},
		},
		grid: {
			top: 35,
			bottom: 45,
		},
		legend: {
			data: ['上线率', '总量'],
			top: 0, // 调整图例的位置，使其移动到图的上方
			right: 100, // 调整图例的位置，使其移动到图的右侧
			textStyle: {
				color: '#fff',
				fontSize: 16,
			},
			itemWidth: 10, // 设置图例项的宽度
			itemHeight: 10, // 设置图例项的高度
			itemShape: 'circle', // 设置图例项的形状为圆形
		},

		xAxis: [
			{
				type: 'category',
				data: truncatedAffiliations,
				axisPointer: {
					type: 'shadow',
				},
				axisLabel: {
					textStyle: {
						color: '#E2F0FF',
						fontSize: 16,
					},
				},
			},
		],
		yAxis: [
			{
				name: `单位: 辆`,
				nameTextStyle: {
					color: 'rgba(212, 232, 254, 1)',
					fontSize: 12,
				},
				type: 'value',
				splitLine: {
					lineStyle: {
						type: 'dashed',
					},
				},
				axisLabel: {
					textStyle: {
						color: '#E2F0FF',
						fontSize: 16,
					},
				},
			},
			{
				type: 'value',
				name: '上线率',
				nameTextStyle: {
					color: '#d8e2ef',
				},
				// interval: 1025,
				axisLabel: {
					formatter: function (value, index) {
						return tranNumber(Number(value), 2) + ' %';
					},
					textStyle: {
						fontSize: '12',
						color: '#d8e2ef',
					},
				},
				splitLine: {
					show: false,
				},
				axisLine: {
					show: false,
				},
			},
		],
		series: [
			{
				name: '总量',
				type: 'bar',
				tooltip: {},
				barWidth: 20,
				itemStyle: {
					// 设置柱状图的颜色为绿色
					color: 'rgb(102,204,204)',
				},
				data: sumData,
			},
			{
				name: '上线率',
				type: 'line',
				yAxisIndex: 1,
				itemStyle: {
					// 设置温度系列的样式
					color: 'rgb(42, 112, 252)', // 修改温度系列的颜色为橙色
				},
				data: onlineData.map((item, index) =>
					((item / sumData[index]) * 100).toFixed(0),
				),
			},
		],
	};
};
