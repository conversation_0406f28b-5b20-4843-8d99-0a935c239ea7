import React, { useEffect, useState } from 'react';
import * as echarts from 'echarts';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import TimeTypeRadio from '@renderer/baseUI/TimeTypeRadioCustom';
import Echarts from '@renderer/components/echarts';
import SectorEchart from '@renderer/baseUI/SectorEchart';
import RankingOfCityProportion from './components/RankingOfCityProportion';
import { getAtThatTime, getNoxCountyByType } from '@renderer/api';
import SpatialDistributionStyle from './style';
import { useAuth } from '@renderer/hooks/useAuth';
import { useSpaceTimeJson, useSpaceTimeParams } from '@renderer/hooks/index';

interface CustomDate {
	startTime: string;
	endTime: string;
	timeType: string;
}

interface mapValue {
	name: string;
	value: number;
}

type Props = {
	setMapData: React.Dispatch<React.SetStateAction<never[]>>;
	setTargetId: React.Dispatch<React.SetStateAction<number>>;
	regionName: string;
	mapData: mapValue[];
};

const SpatialDistribution = ({
	setMapData,
	setTargetId,
	regionName,
	mapData,
}: Props) => {
	const [timeType, setTimeType] = useState('7day');
	const { token } = useAuth();
	const [result, setResult] = useState<any>(null);
	const [title, setTitle] = useState<any>([]);
	const [options, setOptions] = useState({});
	const [typeId, setTypeId] = useState(1);
	const [typeCarId, setTypeCarId] = useState('货车');
	const [typeOptions, setTypeOptions] = useState([
		{
			label: '当日',
			value: 'day',
		},
		{
			label: '昨日',
			value: 'yesterday',
		},
		{
			label: '7天',
			value: '7day',
		},
		{
			label: '自定义',
			value: 'custom',
		},
	]);
	const colorList = [
		'#FD9546',
		'#8A40FF',
		'#E3A07C',
		'#491DFF',
		'#8EFAFF',
		'#A3EE63',
		'#63A4EE',
		'#6863EE',
	];
	const colorList2 = ['#FD9546', '#8A40FF', '#E3A07C', '#8EFAFF', '#A3EE63'];
	const [lineOptions, setLineOptions] = useState({});
	const tabsList = [
		{ id: 1, name: '在线车辆', key: 'nums', value: 1 },
		{ id: 2, name: '总排放', key: 'total_nox', value: 3 },
		{ id: 3, name: '总里程', key: 'total_distance', value: 2 },
		{ id: 4, name: '总油耗', key: 'total_oil', value: 4 },
		{ id: 5, name: '排放因子', key: 'emissions_factor', value: 6 },
	];
	const [lineData, setLineData] = useState<any>([]);
	const [active, setActive] = useState(0);
	const [active2, setActive2] = useState(0);
	const [data, setData] = useState<any>([]);
	const [customDate, setCustomDate] = useState<CustomDate>({
		startTime: '',
		endTime: '',
		timeType: '',
	});
	const [unit, setUnit] = useState<string>('辆');
	const [rankingOption, setRankingOption] = useState({});
	const [tabsTypeList, sestTabsTypeList] = useState<any>([]);
	// const mapData = [
	//   { name: '东城区', value: 5616 },
	//   { name: '西城区', value: 1785 },
	//   { name: '朝阳区', value: 5764 },
	//   { name: '海淀区', value: 2670 },
	//   { name: '丰台区', value: 5316 },
	//   { name: '石景山区', value: 720 },
	//   { name: '门头沟区', value: 978 },
	//   { name: '房山区', value: 11750 },
	//   { name: '通州区', value: 12825 },
	//   { name: '顺义区', value: 11403 },
	//   { name: '大兴区', value: 9298 },
	//   { name: '昌平区', value: 15069 },
	//   { name: '平谷区', value: 2981 },
	//   { name: '怀柔区', value: 4459 },
	//   { name: '密云区', value: 3815 },
	//   { name: '延庆区', value: 2197 },
	//   { name: '开发区', value: 586 }
	// ]

	const handleTabs = (item) => {
		setActive(item.id - 1);
		setTypeId(item.value);
	};

	const tabsTypeClick = (item) => {
		setActive2(item.id);
		setTypeCarId(item.name);
	};

	const hexToRgba = (hex, opacity) => {
		if (!hex) hex = '#ededed';
		const rgba =
			'rgba(' +
			parseInt('0x' + hex.slice(1, 3)) +
			',' +
			parseInt('0x' + hex.slice(3, 5)) +
			',' +
			parseInt('0x' + hex.slice(5, 7)) +
			',' +
			(opacity || '1') +
			')';
		return rgba;
	};

	function generateAreaStyle(name, lineData, idx) {
		return {
			name: name,
			type: 'line',
			symbol: 'circle',
			symbolSize: 8,
			showSymbol: true,
			color: hexToRgba(colorList[idx], 1),
			lineStyle: {
				normal: {
					width: 2,
				},
			},
			areaStyle: {
				//区域填充样式
				normal: {
					//线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
					color: new echarts.graphic.LinearGradient(
						0,
						0,
						0,
						1,
						[
							{
								offset: 0,
								// color: "rgba(25,163,223,.3)"
								color: hexToRgba(colorList2[active], 0.3),
							},
							{
								offset: 1,
								color: 'rgba(25,163,223, 0)',
							},
						],
						false,
					),
					//shadowColor: 'rgba(25,163,223, 0.5)', //阴影颜色
					shadowColor: hexToRgba(colorList2[active], 0.5),
					shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
				},
			},
			data: lineData,
		};
	}

	// 判断是否有小数
	function hasDecimal(number) {
		const numberStr = number.toString();
		return numberStr.includes('.');
	}

	const getVehicleData = (type) => {
		if (!result) return;
		// 创建一个对象来存储合并后的数据
		const consolidatedData = {};

		// 遍历数组并根据车型进行数据合并
		result.data?.forEach((item) => {
			const { vehicle_type, [type]: property } = item;
			if (!consolidatedData[vehicle_type]) {
				consolidatedData[vehicle_type] = [];
			}
			consolidatedData[vehicle_type].push(property);
		});
		// 将合并后的车型s数据对象转换为数组
		const resultArray = Object.entries(consolidatedData).map(
			([vehicle_type, data]) => ({
				vehicle_type,
				data,
			}),
		);
		return resultArray;
	};

	// 获取单位名称
	const getUnit = (targetId: number) => {
		switch (targetId) {
			case 1:
				return '辆';
			case 2:
				return 'km';
			case 3:
				return 'kg';
			case 4:
			case 6:
				return 'L';
			default:
				return '';
		}
	};

	useEffect(() => {
		setUnit(getUnit(typeId));

		getAtThatTime(useSpaceTimeParams(timeType, customDate))
			.then((res) => {
				console.log('历史活动数据--', res);
				setResult(res as any);
			})
			.catch(() => {
				setResult(null);
			});

		getNoxCountyByType(useSpaceTimeJson(token, timeType, typeId, customDate))
			.then((res) => {
				//  console.log('历史活动数据--', res)
				console.log(
					'============getNoxCountyByType 空间分布========================',
				);
				console.log(res);
				if (res?.length) {
					const mapData = res?.map((item) => {
						return {
							name: item.COUNTY_NAME,
							value: hasDecimal(item.sum)
								? Number(item.sum.toFixed(2))
								: Number(item.sum),
						};
					});
					setMapData(mapData);
					setTargetId(typeId);
				} else {
					setMapData([]);
				}
				console.log(
					'=============getNoxCountyByType 空间分布=======================',
				);
			})
			.catch(() => {
				setMapData([]);
			});
	}, [timeType, typeId, customDate]);

	useEffect(() => {
		//debugger
		// console.log('result--', active, result)
		if (!result) return;
		const peiData = result?.rate?.map((item, index) => {
			switch (active) {
				case 0: //在线数量
					return {
						name: item.vehicle_type,
						value: (item.nums_rate * 100).toFixed(0),
					};
				case 1: //总排放
					return {
						name: item.vehicle_type,
						value: (item.total_nox_rate * 100).toFixed(0),
					};
				case 2: //总里程
					return {
						name: item.vehicle_type,
						value: (item.total_distance_rate * 100).toFixed(0),
					};
				case 3: //总油耗
					return {
						name: item.vehicle_type,
						value: (item.total_oil_rate * 100).toFixed(0),
					};
				case 4: //排放因子
					return {
						name: item.vehicle_type,
						value: (item.emissions_factor_rate * 100).toFixed(0),
					};
			}
		});
		setData(peiData);

		let lineValue: any = [];

		switch (active) {
			case 0: //在线数量
				lineValue = getVehicleData('nums');
				break;
			case 1: //总排放
				lineValue = getVehicleData('total_nox');
				break;
			case 2: //总里程
				lineValue = getVehicleData('total_distance');
				break;
			case 3: //总油耗
				lineValue = getVehicleData('total_oil');
				break;
			case 4: //排放因子
				lineValue = getVehicleData('emissions_factor');
				break;
		}
		setLineData(lineValue);

		setTitle(
			result?.data
				?.map((item) => item.date_time)
				.slice(0, lineValue[0].data.length),
		);

		// setMapData(mapData.sort((a, b) => a.value - b.value).reverse())
	}, [result, active]);

	useEffect(() => {
		if (lineData?.length)
			sestTabsTypeList(
				lineData?.map((item, idx) => {
					return {
						id: idx,
						name: item.vehicle_type,
						key: idx * Math.random(),
						value: idx,
					};
				}),
			);

		const lineTypeData = generateAreaStyle(
			typeCarId,
			lineData.find((item) => item.vehicle_type === typeCarId)?.data,
			active2,
		);
		const lineOption = {
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					// 坐标轴指示器，坐标轴触发有效
					type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
				},
				backgroundColor: 'rgba(13, 64, 71, 0.50)',
				borderColor: 'rgba(143, 225, 252, 0.60)',
				padding: 8,
				textStyle: {
					color: '#fff',
				},
			},
			// legend: {
			//   align: 'left',
			//   right: '10%',
			//   width: '70%',
			//   top: '0%',
			//   type: 'scroll',
			//   textStyle: {
			//     color: '#7ec7ff',
			//     fontSize: 14
			//   },
			//   pageTextStyle: {
			//     color: '#fff' // 文字样式
			//   },
			//   icon: 'circle',
			//   itemGap: 25,
			//   itemWidth: 18
			// },
			grid: {
				left: '3%',
				right: '8%',
				bottom: '12%',
				top: '16%',
				// height: '66%',
				containLabel: true,
			},
			xAxis: [
				{
					type: 'category',
					data: title,
					boundaryGap: false,
					axisTick: {
						show: false, // 不显示坐标轴刻度线
					},
					splitLine: {
						show: false,
					},
					axisLine: {
						show: false,
					},
					axisLabel: {
						color: '#D8DBDE',
						fontSize: 12,
						formatter: function (params) {
							if (timeType == '7day' || timeType == 'day') {
								return moment(params).format('YYYY-MM-DD');
							} else {
								return moment(params).format('HH:mm:ss');
							}
						},
					},
				},
			],
			yAxis: [
				{
					name: `${unit}`,
					// min: 0,
					// max: 15,
					nameTextStyle: {
						color: '#D8DBDE',
						fontSize: 14,
					},
					type: 'value',
					splitNumber: 4,
					//y右侧文字
					axisLabel: {
						color: '#D8DBDE',
						fontSize: 16,
						formatter: (params) => {
							return `${params}`;
						},
					},
					// y轴的分割线
					splitLine: {
						show: true,
						lineStyle: {
							type: 'dashed',
							color: 'rgba(230, 247, 255, 0.20)',
						},
					},
				},
			],
			dataZoom: [
				{
					start: 0, //默认为0
					end: 100 - 1500 / 31, //默认为100
					type: 'slider',
					show: true,
					xAxisIndex: [0],
					handleSize: 0, //滑动条的 左右2个滑动条的大小
					height: 8, //组件高度
					left: 50, //左边的距离
					right: 40, //右边的距离
					bottom: 6, //右边的距离
					handleColor: '#ddd', //h滑动图标的颜色
					handleStyle: {
						borderColor: '#cacaca',
						borderWidth: '1',
						shadowBlur: 2,
						background: '#ddd',
						shadowColor: '#ddd',
					},
					fillerColor: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
						{
							//给颜色设置渐变色 前面4个参数，给第一个设置1，第四个设置0 ，就是水平渐变
							//给第一个设置0，第四个设置1，就是垂直渐变
							offset: 0,
							color: '#1eb5e5',
						},
						{
							offset: 1,
							color: '#5ccbb1',
						},
					]),
					backgroundColor: '#ddd', //两边未选中的滑动条区域的颜色
					showDataShadow: false, //是否显示数据阴影 默认auto
					showDetail: false, //即拖拽时候是否显示详细数值信息 默认true
					handleIcon:
						'M-292,322.2c-3.2,0-6.4-0.6-9.3-1.9c-2.9-1.2-5.4-2.9-7.6-5.1s-3.9-4.8-5.1-7.6c-1.3-3-1.9-6.1-1.9-9.3c0-3.2,0.6-6.4,1.9-9.3c1.2-2.9,2.9-5.4,5.1-7.6s4.8-3.9,7.6-5.1c3-1.3,6.1-1.9,9.3-1.9c3.2,0,6.4,0.6,9.3,1.9c2.9,1.2,5.4,2.9,7.6,5.1s3.9,4.8,5.1,7.6c1.3,3,1.9,6.1,1.9,9.3c0,3.2-0.6,6.4-1.9,9.3c-1.2,2.9-2.9,5.4-5.1,7.6s-4.8,3.9-7.6,5.1C-285.6,321.5-288.8,322.2-292,322.2z',
					filterMode: 'filter',
				},
				//下面这个属性是里面拖到
				{
					type: 'inside',
					show: true,
					xAxisIndex: [0],
					start: 0, //默认为1
					end: 100 - 1500 / 31, //默认为100
				},
			],
			series: lineTypeData,
		};

		setLineOptions(lineOption);
	}, [lineData, typeCarId]);

	useEffect(() => {
		const option = {
			// backgroundColor: '#061740',
			color: colorList,
			tooltip: {
				show: true,
				formatter: (params) => {
					return `<span style="font-size: larger;">${params.name} ${params.value}%</span>`;
				},
			},
			// title: {
			//   text: '本月总数',
			//   left: '24%',
			//   top: 'center',
			//   textStyle: {
			//     color: '#ffffff',
			//     fontWeight: 'bold',
			//     fontSize: '16px'
			//   }
			// },
			// tooltip: {
			//   trigger: 'item'
			// },
			legend: [
				{
					orient: 'vertical',
					// data: nameArray,
					right: '5%',
					top: 'center',
					align: 'left',
					itemGap: 14,
					// textStyle: {
					//   color: '#fff',
					//   fontSize: '14px'
					// },
					//图例标记的图形高度
					itemHeight: 10,
					//图例标记的图形宽度
					itemWidth: 10,
					formatter: function (name) {
						for (let i = 0; i < data.length; i++) {
							if (name == data[i].name) {
								return '{name|' + name + '}';
							}
						}
					},
					textStyle: {
						rich: {
							name: {
								fontSize: 14,
								fontWeight: 400,
								width: 100,
								height: 20,
								padding: [0, 0, 0, 5],
								color: '#fff',
								// fontSize: '14px',
								fontFamily: 'Source Han Sans CN-Regular',
							},
							// num: {
							//   fontSize: 14,
							//   fontWeight: 500,
							//   height: 20,
							//   width: 50,
							//   align: 'left',
							//   color: 'rgba(0, 0, 0, 0.65)',
							//   fontFamily: 'Source Han Sans CN-Regular'
							// },
							value: {
								fontSize: 14,
								fontWeight: 500,
								height: 20,
								width: 50,
								align: 'left',
								color: '#fff',
								// fontSize: '14px',
								fontFamily: 'Source Han Sans CN-Regular',
							},
						},
					},
				},
			],
			series: [
				{
					name: '',
					type: 'pie',
					clockwise: false,
					radius: ['100%', '170%'],
					width: '65%',
					height: '35%',
					emphasis: {
						scale: false,
					},
					center: ['56%', '49%'],
					top: 'center',
					label: {
						show: false,
						position: 'inside',
						color: '#fff',
						// formatter: function (params) {
						//   console.log(params)
						//   return params.percent + '%'
						// }
					},
					data,
				},
				{
					name: '',
					type: 'pie',
					clockwise: false,
					radius: ['180%', '175%'],
					center: ['56%', '49%'],
					width: '65%',
					height: '35%',
					top: 'center',
					avoidLabelOverlap: false,
					itemStyle: {
						borderRadius: 1000,
					},
					label: {
						position: 'outside',
						color: '#E6F7FF',
						lineHeight: 18,
						formatter: (params) => {
							const { data } = params;
							return data.name + ' ' + data.value + '%';
						},
					},
					emphasis: {
						disabled: true,
					},
					// 占位样式
					emptyCircleStyle: {
						color: 'rgba(255,255,255,0)',
					},
					data,
				},
			],
		};
		setOptions(option);
	}, [data]);

	return (
		<SpatialDistributionStyle>
			<div className="spatial-distribution-top">
				<TimeTypeRadio
					type={'7day'}
					typeOptions={typeOptions}
					timeType={(e) => setTimeType(e)}
					setTimeType={setTimeType}
					setCustomDate={setCustomDate}
				/>
			</div>
			<div className="tabs">
				{tabsList.map((item, index) => {
					return (
						<a
							onClick={() => handleTabs(item)}
							key={item.id}
							className={active == index ? 'tabs-item active' : 'tabs-item'}
						>
							<div>{item.name}</div>
						</a>
					);
				})}
			</div>
			<div className="down-container">
				<div className="echarts-container">
					<div className="echarts">
						{data ? <Echarts option={options}></Echarts> : null}
					</div>
				</div>
				<div className="botLine"></div>
				<div className="tabs" style={{ padding: '5px 0' }}>
					{tabsTypeList?.length &&
						tabsTypeList.map((item, index) => {
							return (
								<a
									onClick={() => tabsTypeClick(item)}
									key={item.id}
									className={
										active2 == index ? 'tabs-item active' : 'tabs-item'
									}
								>
									<div style={{ fontSize: '12px' }}>{item.name}</div>
								</a>
							);
						})}
				</div>
				<div className="line-eachart">
					{lineOptions ? <Echarts option={lineOptions}></Echarts> : null}
				</div>
				<div className="botLine"></div>
				<div className="ranking-eachart">
					<div className="rankingTitle">
						<p>全市占比总量</p>
					</div>
					{rankingOption ? <Echarts option={rankingOption}></Echarts> : null}
				</div>
				<RankingOfCityProportion
					mapData={mapData}
					setRankingOption={setRankingOption}
				/>
			</div>
		</SpatialDistributionStyle>
	);
};
export default SpatialDistribution;
