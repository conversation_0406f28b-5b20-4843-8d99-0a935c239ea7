import { useEffect, useState, useContext, useRef } from 'react';
import * as echarts from 'echarts';
import moment from 'moment';
import icon from '@renderer/images/icon';
import TimeTypeRadio from '@renderer/components/StaticDataDisplay/components/TimeTypeRadio';
import { COLORLIST2 } from '@renderer/utils/util';
import {
	getVehicleBasicInformation,
	getAffiliationInfoByID,
	getVehicleActivity,
	getVehicleCumulativeData,
} from '@renderer/api';
import { PageContext } from '@renderer/context';
import { isEmpty } from 'lodash';
import { CarLeftSideStyled, LayoutContainer } from './styles';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const typeOptions = [
	{
		label: '排放',
		value: 1,
		unit: '克',
	},
	{
		label: '里程',
		value: 2,
		unit: '米',
	},
	{
		label: '油耗',
		value: 3,
		unit: '升',
	},
	{
		label: '上线时长',
		value: 4,
		unit: '秒',
	},
];

const AreaLeftSide = (props: Props) => {
	const {
		currentVehicleInfo,
		setCurrentVehicleInfo,
		historicalStartTime,
		historicalEndTime,
	} = useContext(PageContext);
	const pieRef = useRef(null);
	const pieChartRef = useRef(null);
	const lineRef = useRef(null);
	const lineChartRef = useRef(null);
	const [currentIndustry, setCurrentIndustry] = useState(1);
	const [infoData, setInfoData] = useState({});
	const [userData, setUserData] = useState({});

	const onTypeChange = (type) => {
		setCurrentIndustry(type);
	};

	const getVehicleBasicInformationData = () => {
		console.log('getVehicleBasicInformationData???', currentVehicleInfo);

		getVehicleBasicInformation({
			vin: currentVehicleInfo.VIN,
		})
			.then((res) => {
				if (res?.VIN_NUMBER && res?.VIN_NUMBER !== '') {
					setInfoData(res);
					getAffiliationInfoByIDData(res.AFFILIATION);
				}
			})
			.catch((error) => {
				console.log('error', error);
			});
	};

	const getAffiliationInfoByIDData = (affiliation_id) => {
		getAffiliationInfoByID({
			affiliation_id,
		})
			.then((res) => {
				if (res?.AFFILIATION && res?.AFFILIATION !== '') {
					setUserData(res);
				}
			})
			.catch((error) => {
				console.log('error', error);
			});
	};

	const getVehicleActivityData = async (start_time, end_time) => {
		let pieData1 = await getVehicleActivity({
			vid: currentVehicleInfo.VIN,
			start_time,
			end_time,
			factor: 1,
		});
		let pieData2 = await getVehicleActivity({
			vid: currentVehicleInfo.VIN,
			start_time,
			end_time,
			factor: 2,
		});
		try {
			pieData1 = pieData1
				.filter((item) => item.name !== '' && item.value != '')
				.map((item) => {
					return { name: item.name, value: Math.floor(item.value) };
				});
			pieData2 = pieData2
				.filter((item) => item.name !== '' && item.value != '')
				.map((item) => {
					return { name: item.name, value: Math.floor(item.value) };
				});
			pieChartRef.current = echarts.init(pieRef.current);
			const pieOption = {
				tooltip: {
					trigger: 'item',
					formatter: '{a} <br/>{b} : {c}米 ({d}%)',
				},
				title: [
					{
						left: '75%',
						top: '45%',
						textAlign: 'center',
						text: '区县里程占比',
						textStyle: {
							fontSize: 18,
							color: '#ffffff',
						},
					},
					{
						left: '25%',
						top: '45%',
						textAlign: 'center',
						text: '街乡镇里程占比',
						textStyle: {
							fontSize: 18,
							color: '#ffffff',
						},
					},
				],
				series: [
					{
						name: '区县里程占比',
						type: 'pie',
						radius: ['55%', '85%'],
						center: ['75%', '50%'],
						data: pieData2,
						label: {
							// normal: {
							//   show: false
							// }
							color: '#ffffff',
						},
						// 环图之间间隔
						itemStyle: {
							normal: {
								borderWidth: 2,
								borderColor: '#00225d',
							},
						},
					},
					{
						name: '街乡镇里程占比',
						type: 'pie',
						radius: ['55%', '85%'],
						center: ['25%', '50%'],
						data: pieData1,
						label: {
							// normal: {
							//   show: false
							// }
							color: '#ffffff',
						},
						// 环图之间间隔
						itemStyle: {
							normal: {
								borderWidth: 2,
								borderColor: '#00225d',
							},
						},
					},
				],
				color: COLORLIST2,
			};
			pieChartRef.current.setOption(pieOption);
		} catch (error) {
			console.log(error);
		}
	};

	const getVehicleCumulative = (start_time, end_time) => {
		getVehicleCumulativeData({
			vid: currentVehicleInfo.VIN,
			start_time,
			end_time,
			target: currentIndustry,
		})
			.then((res) => {
				console.log('getVehicleCumulativeData????', res);
				lineChartRef.current = echarts.init(lineRef.current);
				if (res.length < 1) return;
				const dateList = res.map((item) => {
					return item.name;
				});
				const valueList = res.map((item) => {
					return Math.floor(item.value);
				});
				const lineOption = {
					title: {
						text: `单位：${typeOptions[currentIndustry - 1].unit}`,
						left: 30,
						// 文字颜色
						textStyle: {
							color: '#ffffff',
						},
					},
					color: ['#3398DB'],
					tooltip: {
						trigger: 'axis',
						axisPointer: {
							// 坐标轴指示器，坐标轴触发有效
							type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
						},
					},
					grid: {
						left: '3%',
						right: '4%',
						bottom: '3%',
						containLabel: true,
					},
					xAxis: [
						{
							type: 'category',
							data: dateList,
							axisTick: {
								alignWithLabel: true,
							},
							//x底部文字
							axisLabel: {
								textStyle: {
									color: '#ffffff',
									fontSize: 18,
								},
							},
						},
					],
					yAxis: [
						{
							type: 'value',
							//y右侧文字
							axisLabel: {
								textStyle: {
									color: '#ffffff',
									fontSize: 18,
								},
							},
							// y轴的分割线
							splitLine: {
								show: false,
							},
						},
					],
					series: [
						{
							name: typeOptions[currentIndustry - 1].label,
							type: 'line',
							smooth: true,
							tooltip: {
								trigger: 'axis',
								axisPointer: {
									// 坐标轴指示器，坐标轴触发有效
									type: 'line', // 默认为直线，可选为：'line' | 'shadow'
								},
							},
							areaStyle: {
								opacity: 0.2,
								color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
									{
										offset: 0,
										color: '#36d9ab',
									},
									{
										offset: 1,
										color: '#02137c',
									},
								]),
							},
							data: valueList,
						},
					],
				};
				lineChartRef.current.setOption(lineOption);
			})
			.catch((error) => {
				console.log('error', error);
			});
	};

	useEffect(() => {
		if (isEmpty(currentVehicleInfo)) return;
		const startTime = `${historicalStartTime} 00:00:00`;
		const endTime = `${historicalEndTime} 23:59:59`;
		getVehicleCumulative(startTime, endTime);
	}, [currentIndustry]);

	useEffect(() => {
		if (isEmpty(currentVehicleInfo)) return;
		const startTime = `${historicalStartTime} 00:00:00`;
		const endTime = `${historicalEndTime} 23:59:59`;
		getVehicleBasicInformationData();
		getVehicleActivityData(startTime, endTime);
		getVehicleCumulative(startTime, endTime);
		return () => {
			if (pieChartRef.current) pieChartRef.current.dispose();
			if (lineChartRef.current) lineChartRef.current.dispose();
		};
	}, [currentVehicleInfo, historicalStartTime, historicalEndTime]);

	return (
		<CarLeftSideStyled>
			<div className="Carcontent">
				<div className="content-top">
					<div className="topModule">
						<div className="slidingLayer">
							<span
								className="slidtext"
								style={{
									background: `url(${icon.carIcon}) 10px 8px / 20px 20px no-repeat`,
								}}
							>
								基础数据
							</span>
							<div className="line"></div>
						</div>
						<div className="topModuleWarp">
							<LayoutContainer>
								<div className="single">
									<div>VIN</div>
									<div>
										<span>
											{infoData['VIN_NUMBER'] ? infoData['VIN_NUMBER'] : '--'}
										</span>
									</div>
								</div>
								<div className="double">
									<div>企业名称</div>
									<div>
										<span
											title={
												userData['AFFILIATION'] ? userData['AFFILIATION'] : '--'
											}
										>
											{userData['AFFILIATION'] ? userData['AFFILIATION'] : '--'}
										</span>
									</div>
								</div>
								<div className="single">
									<div>注册区</div>
									<div>
										<span>
											{infoData['VEHICLE_DISTRICT']
												? infoData['VEHICLE_DISTRICT']
												: '--'}
										</span>
									</div>
								</div>
								<div className="double">
									<div>用户属性</div>
									<div>
										<span>
											{userData['VEHICLE_USER_TYPE']
												? userData['VEHICLE_USER_TYPE']
												: '--'}
										</span>
									</div>
								</div>
								<div className="single">
									<div>总质量</div>
									<div>
										<span>
											{infoData['TOTAL_MASS_KG']
												? infoData['TOTAL_MASS_KG']
												: '--'}
										</span>
									</div>
								</div>
							</LayoutContainer>
							<LayoutContainer>
								<div className="single">
									<div>行业类型</div>
									<div>
										<span>
											{infoData['TYPE_OF_INDUSTRY']
												? infoData['TYPE_OF_INDUSTRY']
												: '--'}
										</span>
									</div>
								</div>
								<div className="double">
									<div>发动机型号</div>
									<div>
										<span>
											{infoData['ENGINE_SPECIFICATION_MODEL']
												? infoData['ENGINE_SPECIFICATION_MODEL']
												: '--'}
										</span>
									</div>
								</div>
								<div className="single">
									<div>注册时间</div>
									<div>
										<span>
											{infoData['VEHICLE_REGISTRATION_TIME']
												? infoData['VEHICLE_REGISTRATION_TIME']
												: '--'}
										</span>
									</div>
								</div>
								<div className="double">
									<div>排放水平</div>
									<div>
										<span>
											{infoData['EMISSION_LEVEL']
												? infoData['EMISSION_LEVEL']
												: '--'}
										</span>
									</div>
								</div>
								<div className="single">
									<div>车辆型号</div>
									<div>
										<span>
											{infoData['MODEL_OF_THE_VEHICLE']
												? infoData['MODEL_OF_THE_VEHICLE']
												: '--'}
										</span>
									</div>
								</div>
							</LayoutContainer>
						</div>
					</div>
				</div>
				<div className="slidingLayer">
					<span
						className="slidtext"
						style={{
							background: `url(${icon.space}) 10px 8px / 20px 20px no-repeat`,
						}}
					>
						空间活动水平
					</span>
					<div className="line"></div>
				</div>
				<div className="space-activity-level" ref={pieRef} />
				<div className="slidingLayer">
					<span
						className="slidtext"
						style={{
							background: `url(${icon.cumulative}) 10px 8px / 20px 20px no-repeat`,
						}}
					>
						累计数据
					</span>
					<TimeTypeRadio
						typeOptions={typeOptions}
						defaultValue={1}
						onTypeChange={onTypeChange}
					/>
					<div className="line"></div>
					<div className="cumulative-data" ref={lineRef} />
					{/* <Dropdown menu={menuProps} className="dropDown">
            <Button>
              <Space>
                {currentIndustry}
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown> */}
				</div>
			</div>
		</CarLeftSideStyled>
	);
};

export default AreaLeftSide;
