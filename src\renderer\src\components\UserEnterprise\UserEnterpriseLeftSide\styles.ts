import styled, { keyframes } from 'styled-components';
import imgs from '@renderer/images/background';
import icon from '@renderer/images/icon';

const fadeInAnimation = keyframes`
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
`;

const UserEnterpriseLeftSideStyled = styled.div`
	width: 21.7%;
	height: 1080px;
	position: absolute;
	left: 0;
	bottom: 0;
	z-index: 10;
	background: url(${imgs.areaLeftSide}) no-repeat;
	background-size: 100% 100%;
	animation: ${fadeInAnimation} 1s ease-in-out;
	display: flex;
	justify-content: center;
	align-items: center;
	/* backdrop-filter: blur(5px); */
	.Industrycontent {
		width: 93.5%;
		height: 95%;
		backdrop-filter: blur(15px);
		border-radius: 0 45px 45px 0;
		box-shadow: 25px 0 25px 25px rgb(3 27 60 / 54%);
		.slidingLayer {
			display: flex;
			flex-direction: column;
			background: url(${icon.right}) no-repeat;
			background-size: 100% 100%;
			width: 90%;
			height: 70px;
			margin: 0 10px 0 0;
			.dropDown {
				position: absolute;
				right: 6%;
				top: 70%;
			}
			:where(.css-dev-only-do-not-override-mu9r37).ant-btn-default {
				width: 120px;
				height: 35px;
				background-color: transparent;
				border: 0;
				background: url(${icon.selectRegionBg});
				background-repeat: no-repeat;
				background-size: 100% 100%;
				font-size: 18px;
				margin: 0 10px;
			}
			display: flex;
			align-items: center;
		}
		.slidtext {
			width: 100%;
			height: 100%;
			overflow-wrap: break-word;
			color: rgba(255, 255, 255, 1);
			font-size: 24px;
			font-family: PingFangSC-Regular;
			text-align: left;
			white-space: nowrap;
			line-height: 38px;
			margin: 12px 0 0 70px;
		}
	}
`;

const LayoutContainer = styled.div`
	margin-left: 10px;
	height: 25%;
	display: flex;
	flex-direction: row;
	position: relative;
	.container {
		width: 100%;
		/* margin-top: 14px; */
		display: flex;
		flex-direction: row;
		.echarts-container {
			display: flex;
			flex-direction: row;
			position: relative;
			flex: 1;
			padding-right: 20px;
			.echarts {
				width: 100%;
				height: 100%;
			}
		}
	}
	.echarts-line {
		width: 55%;
		display: flex;
	}
	.echarts-mid,
	.echarts-btm {
		width: 100%;
	}

	.echarts-mid-half {
		width: 55%;
	}
`;

export { LayoutContainer, UserEnterpriseLeftSideStyled };
