import { useEffect, useState, useContext } from 'react';
import { PageContext } from '@renderer/context';
import Echarts from '@renderer/components/echarts';
import dayjs from 'dayjs';
import { Segmented, DatePickerProps, DatePicker } from 'antd';
import Box from '@renderer/baseUI/Box';
import { getCountyByTimeNew } from '@renderer/api';
import { tranNumber, tranNumberS } from '@renderer/hooks';
import RealTimeIndustryDataStyled from './styles';
import { INDUSTRY_COLOR, INDUSTRY_ORDER } from '@renderer/constants/color';

type Align =
	| 'online_count'
	| 'nox'
	| 'distance'
	| 'oil_consumption'
	| 'online_times';
const RealTimeIndustryData = (props) => {
	const { maxTime } = useContext(PageContext);
	const [data, setData] = useState<any>([]);
	const [alignValue, setAlignValue] = useState<Align>('online_count');
	const [pointTime, setPointTime] = useState(maxTime);

	const [options, setOptions] = useState({});

	const colorList = [
		'#29BCFD', // 轻
		'#2D7BE5', // 中
		'#85A9FF', // 重
		'#73DEBD',
		'#26C978',
		'#8CDF6C',
		'#FBD657',
		'#F56679',
		'#E07BCE',
		'#9D50E0',
		'#634FDA',
	];
	const items = [
		{
			label: '车辆数量',
			value: 'online_count',
			unit: '辆',
		},
		{
			label: '排放量',
			value: 'nox',
			unit: 'kg',
		},
		{
			label: '里程',
			value: 'distance',
			unit: 'km',
		},
		{
			label: '油耗',
			value: 'oil_consumption',
			unit: 'kg',
		},
	];

	const onChange: DatePickerProps['onChange'] = (date, dateString) => {
		setPointTime(dateString);
	};

	useEffect(() => {
		getCountyByTimeNew({
			time: pointTime,
		})
			.then((res: any) => {
				setData(res);
			})
			.catch((err) => {
				setData([]);
			});
	}, [pointTime]);

	useEffect(() => {
		if (data.length <= 0) return;
		const xData = data[0]?.group_details?.adm;
		const series = data
			?.map((item) => {
				return {
					name: item.second_type,
					data: item.group_details[alignValue],
					stack: '总量',
					type: 'bar',
					color: INDUSTRY_COLOR[item.second_type],
				};
			})
			.sort((a, b) => INDUSTRY_ORDER[a.name] - INDUSTRY_ORDER[b.name]);

		const options = {
			title: {
				text: `单位: ${items.find((i) => i.value === alignValue)?.unit}`,
				// left: 19,
				left: '14%',
				top: 23,
				textStyle: {
					color: '#fff',
					fontSize: 12,
					fontWeight: 300,
				},
			},
			color: colorList,
			tooltip: {
				trigger: 'axis',
				confine: true,
				appendToBody: true,
				borderWidth: 0,
				axisPointer: {
					lineStyle: {
						color: 'rgba(11, 208, 241, 1)',
						type: 'slider',
					},
				},
				textStyle: {
					color: '#243d55',
					fontSize: 14,
				},
				formatter: function (params) {
					let relVal = '<strong>' + params[0].name + '</strong><br/>';
					for (let i = 0, l = params.length; i < l; i++) {
						const formattedValue = params[i].value;
						relVal += `${params[i].marker} ${params[i].seriesName}: ${formattedValue}<br/>`;
					}
					return relVal;
					// if (alignValue == 'online_count') {
					// } else {
					// 	let relVal = '<strong>' + params[0].name + '</strong><br/>';
					// 	for (let i = 0, l = params.length; i < l; i++) {
					// 		const formattedValue = tranNumberS(params[i].value, 2);
					// 		relVal += `${params[i].marker} ${params[i].seriesName}: ${formattedValue}<br/>`;
					// 	}
					// 	return relVal;
					// }
				},
			},
			legend: {
				// top: 25,
				// left: 'center'
				top: '15%',
				left: '0%',
				orient: 'vertical',
				type: 'scroll',
				itemWidth: 10,
				itemHeight: 10,
				textStyle: {
					fontSize: 14,
					color: '#E8F4FF',
					padding: [3, 0, 0, 0],
				},
				formatter: function (name) {
					return name.length > 5 ? name.substr(0, 5) + '...' : name;
				},
				pageTextStyle: {
					color: '#fff', // 文字样式
				},
			},
			grid: {
				left: '15%',
				right: '4%',
				top: '26%',
				bottom: '-4%',
				containLabel: true,
			},
			xAxis: [
				{
					type: 'category',
					data: xData,
					axisLabel: {
						textStyle: {
							fontSize: '12',
							color: '#fff',
						},
						//展示角度
						rotate: 30,
						interval: 0,
					},
					axisTick: {
						show: false,
					},
					axisLine: {
						//坐标轴轴线相关设置。数学上的x轴
						show: true,
						lineStyle: {
							color: 'rgba(108, 166, 219, 0.5)',
						},
					},
				},
			],
			yAxis: [
				{
					nameTextStyle: {
						color: 'rgba(212, 232, 254, 1)',
						fontSize: 12,
					},
					type: 'value',
					splitLine: {
						lineStyle: {
							color: 'rgba(108, 166, 219, 0.5)',
							type: 'dashed',
						},
					}, //设置横线样式
					axisLine: {
						show: false,
						lineStyle: {
							color: '#233653',
						},
					},
					axisLabel: {
						textStyle: {
							fontSize: '12',
							color: '#fff',
						},
						// formatter: function (value, index) {
						// 	return tranNumber(Number(value), 2);
						// },
					},
				},
			],
			series: series,
		};

		setOptions(options as any);
	}, [data, alignValue]);

	return (
		<Box
			title={'全市各区活动水平'}
			titlewidth="95%"
			height="100%"
			subTitle={
				<div className="customDate">
					<DatePicker
						onChange={onChange}
						showTime={{
							defaultValue: dayjs(maxTime),
							format: 'HH',
						}}
						defaultValue={dayjs(maxTime)}
					/>
				</div>
			}
		>
			<RealTimeIndustryDataStyled>
				<Segmented
					value={alignValue}
					className="RealTimeIndustryNav"
					style={{ width: '93%', position: 'absolute', top: '13%' }}
					onChange={(value) => setAlignValue(value as Align)}
					options={items}
				/>
				<Echarts
					style={{
						width: '100%',
						height: '100%',
					}}
					option={options}
				/>
			</RealTimeIndustryDataStyled>
		</Box>
	);
};

export default RealTimeIndustryData;
