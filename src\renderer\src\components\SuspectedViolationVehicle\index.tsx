import { useEffect, useState } from 'react';
import Echarts from '@renderer/components/echarts';
import { useAuth } from '@renderer/hooks/useAuth';
import Box from '../../baseUI/Box';
import { Container } from './style';

export default function SuspectedViolationVehicle() {
	const { token } = useAuth();
	const [option1, setOption1] = useState({});
	const [option2, setOption2] = useState({});

	useEffect(() => {
		let res = [
			{
				name: '在线',
				rate: 60,
				value: [220, 182, 191, 234, 290, 330, 310],
				type: [
					'货车',
					'渣土车',
					'工程车',
					'公交车',
					'其他客车',
					'环卫车',
					'其他用途',
				],
			},
			{
				name: '现存',
				rate: 80,
				value: [120, 132, 101, 134, 90, 230, 210],
				type: [
					'货车',
					'渣土车',
					'工程车',
					'公交车',
					'其他客车',
					'环卫车',
					'其他用途',
				],
			},
			{
				name: '新增',
				rate: 40,
				value: [150, 232, 201, 154, 190, 330, 410],
				type: [
					'货车',
					'渣土车',
					'工程车',
					'公交车',
					'其他客车',
					'环卫车',
					'其他用途',
				],
			},
		];
		const barColor = [
			'rgba(78, 167, 249, 1)',
			'rgba(0, 255, 255, 1)',
			'rgba(255, 201, 95, 1)',
		];
		const maxRadius = 70;
		const barWidth = 8;
		const barGap = 12;
		let series1: any = [];
		let series2: any = [];
		res.map((item, i) => {
			// function calculateLabelLineLength(i) {
			//   if (i === 0) {
			//     return 60
			//   } else if (i === 1) {
			//     return 45
			//   } else {
			//     return 65
			//   }
			// }
			// let length = calculateLabelLineLength(i)
			// const length2 = 30
			series1.push({
				type: 'pie',
				clockWise: false, //顺时加载
				hoverAnimation: false, //鼠标移入变大
				radius: [
					maxRadius - i * (barGap + barWidth) + '%',
					maxRadius - (i + 1) * barWidth - i * barGap + '%',
				],
				labelLayout: {
					hideOverlap: false,
				},
				center: ['45%', '60%'],
				label: {
					show: false,
				},
				itemStyle: {
					borderRadius: 100,
					label: {
						show: false,
					},
					labelLine: {
						show: false,
					},
					borderWidth: 5,
				},
				data: [
					{
						value: item.rate,
						name: item.name,
						itemStyle: {
							color: barColor[i],
						},
						// label: {
						//   show: true,
						//   color: '#fff',
						//   position: 'outer',
						//   alignTo: 'labelLine',
						//   formatter(param) {
						//     return '{b|' + param.name + '}\n{d|' + param.percent + '%}'
						//   },
						//   rich: {
						//     icon: {
						//       fontSize: 14
						//     },
						//     d: {
						//       fontSize: 14,
						//       padding: [5, -20, 0, 0],
						//       color: "#fff",
						//     },
						//     b: {
						//       fontSize: 14,
						//       padding: [0, -20, 5, 0],
						//       color: "#fff",
						//     },
						//   },
						// }
						// labelLine: {
						//   show: true,
						//   length: i === 0 && item.rate <= 10 ? 20 : 40,
						//   length2: 50,
						//   smooth: false
						// }
					},
					{
						value: 100 - Number(item.rate),
						name: '',
						itemStyle: {
							color: 'transparent',
						},
						tooltip: {
							show: false,
						},
						hoverAnimation: false,
					},
				],
			});
			series1.push({
				name: '',
				type: 'pie',
				silent: true,
				z: 0,
				radius: [
					maxRadius - i * (barGap + barWidth) + '%',
					maxRadius - (i + 1) * barWidth - i * barGap + '%',
				],
				center: ['45%', '60%'],
				label: {
					show: false,
				},
				itemStyle: {
					label: {
						show: false,
					},
					labelLine: {
						show: false,
					},
				},
				data: [
					{
						value: 1,
						itemStyle: {
							color: 'rgba(235, 239, 247, 0.4)',
						},
						tooltip: {
							show: false,
						},
						hoverAnimation: false,
					},
				],
			});
			series2.push({
				name: item.name,
				type: 'bar',
				stack: 'Ad',
				emphasis: {
					focus: 'series',
				},
				data: item.value,
				barWidth: 12,
			});
		});
		let option1 = {
			legend: {
				show: true,
				orient: 'horizontal',
				data: ['在线', '现存', '新增'],
				right: '23%',
				top: '6%',
				textStyle: {
					color: '#E8F4FF',
				},
			},
			tooltip: {
				trigger: 'item',
				formatter: (params) => {
					return params.name + ' ' + params.value + '%';
				},
			},
			xAxis: [
				{
					show: false,
				},
			],
			yAxis: [
				{
					type: 'category',
					inverse: true,
					axisLine: {
						show: false,
					},
					axisTick: {
						show: false,
					},
					axisLabel: {
						show: false,
					},
					data: res.map((x) => x.rate + '%'),
				},
			],
			series: series1,
		};
		let option2 = {
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'shadow',
				},
			},
			legend: {
				left: '20%',
				top: '7%',
				textStyle: {
					color: '#E2F0FF',
					fontSize: '14',
				},
			},
			grid: {
				left: '0',
				right: '10%',
				bottom: '0',
				top: '30%',
				containLabel: true,
			},
			xAxis: [
				{
					type: 'category',
					axisLabel: {
						color: '#E8F4FF',
						fontSize: 14,
					},
					data: res[0].type,
				},
			],
			yAxis: [
				{
					name: '数量 (辆)',
					nameTextStyle: { color: '#ffffff', fontSize: 13 },
					type: 'value',
					splitLine: {
						show: false,
					},
					axisLabel: {
						fontSize: 14,
						color: '#E8F4FF',
					},
				},
			],
			series: series2,
		};
		setOption1(option1);
		setOption2(option2);
	}, []);

	return (
		<Box title="疑似违规车辆" titlewidth="95%" height="100%">
			<Container>
				<div className="echarts1">
					<Echarts option={option1} />
				</div>
				<div className="echarts2">
					<Echarts option={option2} />
				</div>
			</Container>
		</Box>
	);
}
