/*
 * @Descripttion:
 * @version:
 * @Author: Gqq
 * @Date: 2024-07-23 09:32:41
 */
// 此文件用做重柴的图层文件。
// 菜单支持一级和二级。

import React, { memo, useContext } from 'react';
import { LayerType } from '../components/LayerMenu/layerDatas';
import { setChangeScreenStatus } from '@renderer/api';
import OnlineLayer from '@renderer/layers/OnlineLayer'; //车辆实时在线
import OfflineLayer from '@renderer/layers/OfflineLayer'; //车辆实时离线
import ViolationLayer from '@renderer/layers/ViolationLayer'; // 违规车辆
import ViolationAnalysisLayer from '@renderer/layers/ViolationAnalysisLayer'; // 违规分析
import TrafficVolumeDistribution from './TrafficVolumeDistributionLayers'; //车流量情况路网图
import HeatmapLayers from './HeatmapLayers'; // 热力图
import PolymerizationLayers from './PolymerizationLayers'; // 聚合
import Area from './Area'; // 区域
import StreetTownship from './StreetTownship'; // 街乡镇
import Industry from './Industry'; // 行业
import Point from './Point'; // 点位
import HotGrid from './HotGrid'; // 热点网格
import AirLayer from './AirLayer'; // 气象图层
import RoadLayer from './RoadLayer'; // 道路
import RoadGridLayer from './RoadGridLayer'; // 道路网格
import UserEnterprise from './UserEnterprise'; //用户企业
import { PageContext } from '@renderer/context';

export default memo((props) => {
	if (!props.visible) return;
	// // 展示顶部行业筛选框
	// const { setShowModeMenu } = useContext(PageContext);
	// const showModeMenu =
	// 	props?.showModeMenu && props.showModeMenu == true ? true : false;
	// setShowModeMenu(showModeMenu);
	// 匹配id与模块id 左右栏模块替换
	const id = props?.id && props.id !== '' ? props.id : '';
	if (id) {
		const config = { id, status: 1 };
		setChangeScreenStatus(config).then((res) => {
			console.log('setChangeScreenStatus', res);
		});
	}
	switch (props.layerId) {
		case LayerType.VEHICLE_ONLINE: // 车辆实时在线
			return <OnlineLayer {...props} />;
		case LayerType.VIOLATING_VEHICLES: // 违章车辆
			return <ViolationLayer {...props} />;
		case LayerType.VEHICLE_OFFLINE: // 离线车辆
			return <OfflineLayer {...props} />;
		case LayerType.TRAFFIC_VOLUME_DISTRIBUTION: // 车流量情况路网图
		case LayerType.VEHICLE_EMISSIONS_DISTRIBUTION: // 车排放情况路网图
			return <TrafficVolumeDistribution {...props} />;
		case LayerType.TRAFFIC_VOLUME_THERMODYNAMICS: //车流量情况热力图
		case LayerType.VEHICLE_EMISSIONS_THERMODYNAMICS: //车排放情况热力图
			return <HeatmapLayers {...props} />;
		case LayerType.TRAFFIC_VOLUME_Hotspot_Grid: //车流量情况网格图
		case LayerType.VEHICLE_EMISSIONS_Hotspot_Grid: //车排放情况网格图
			return <RoadGridLayer {...props} />;
		case LayerType.TRAFFIC_POLYMERIZATION: // 车流量聚合
		case LayerType.VEHICLE_POLYMERIZATION: // 车辆排放聚合
			return <PolymerizationLayers {...props} />;
		case LayerType.VIOLATING_ANALYSIS: // 违规分析
			return <ViolationAnalysisLayer {...props} />;
		case LayerType.USER_ENTERPRISE:
			return <UserEnterprise {...props} />;
		case LayerType.AREA: // 区域
			return <Area {...props} />;
		case LayerType.Street_Township: // 街乡镇
			return <StreetTownship {...props} />;
		case LayerType.INDUSTRY: // 行业
			return <Industry {...props} />;
		case LayerType.STDG: // 国控站
		case LayerType.STDS: // 市控站
		case LayerType.HIGHT: // 高密度站
		case LayerType.WEATHERSTATION: // 气象站
			return <Point {...props} />;
		case LayerType.GRIDS: // 热点网格
			return <HotGrid {...props} />;
		case LayerType.AIR: // 气象分析
			return <AirLayer {...props} />;
		case LayerType.ROAD: // 道路
			return <RoadLayer {...props} />;
		default:
			return [];
	}
});
