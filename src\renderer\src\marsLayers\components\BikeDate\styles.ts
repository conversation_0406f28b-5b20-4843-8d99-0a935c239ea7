import styled from 'styled-components';
import background from '@renderer/images/background';

const BikeDateStyled = styled.div`
	position: absolute;
	top: 0;
	left: 50%;
	z-index: 101;
	transform: translateX(-50%);
	width: 15%;
	height: 60px;
	background: url(${background.bannerDropdown}) no-repeat;
	background-size: 100% 100%;
	margin: 0 auto;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fff;
	border-radius: 15px;
	font-size: 20px;
	font-weight: 600;
	.selectAssembly {
		width: 50%;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		padding-bottom: 10px;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
		}
		.ant-radio-button-wrapper {
			width: 80px;
			border: 0;
			border-radius: 10px;
			font-size: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			span:hover {
				color: #3adaff;
			}
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
	}
`;

export { BikeDateStyled };
