import moment from 'moment';
import { getCarColorTwo, COUNTY_COLOR } from '../../../../../../main/data';
import { formatNumber, tranNumber } from '@renderer/hooks/index';

export const UserEnterpriseRightSideTop = (data) => {
	if (!data || JSON.stringify(data) == '{}') return;
	const seriesList: any = [];
	const colorList = [
		'#FD9546',
		'#8A40FF',
		'#E3A07C',
		'#491DFF',
		'#8EFAFF',
		'#A3EE63',
		'#63A4EE',
		'#6863EE',
	];
	data?.map((item, index) => {
		seriesList.push({
			name: item.name,
			type: 'line',
			stack: 'total',
			symbol: 'none',
			color: getCarColorTwo(item.name),
			areaStyle: {
				color: getCarColorTwo(item.name),
			},
			data: item.value?.map((i) => i.toFixed(0)),
		});
	});

	return {
		tooltip: {
			trigger: 'axis',
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',
			padding: 8,
			textStyle: {
				color: '#fff',
			},
		},
		legend: {
			width: '80%',
			right: '10%',
			// top: '10%',
			textStyle: {
				fontSize: 16,
				color: '#E2F0FF',
			},
			type: 'scroll',
			pageTextStyle: {
				color: '#fff', // 文字样式
			},
		},
		grid: {
			top: 70,
			bottom: 22,
			left: '10%',
			right: '10%',
		},
		xAxis: {
			type: 'category',
			data: data[0]?.time,
			axisLabel: {
				textStyle: {
					color: '#E2F0FF',
					fontSize: 18,
				},
				// formatter: function (value, index) {
				// 	if (value.length > 4) {
				// 		return value.slice(0, 4) + '...';
				// 	}
				// 	return value;
				// },
			},
		},
		yAxis: {
			name: `单位: L`,
			nameTextStyle: {
				color: '#D8DBDE',
				fontSize: 14,
			},
			type: 'value',
			splitLine: {
				show: true,
				lineStyle: {
					color: 'rgba(222, 227, 239, 0.3)',
					type: [2, 4],
				},
			},
			axisLabel: {
				textStyle: {
					color: 'rgba(212, 232, 254, 1)',
					fontSize: 18,
				},
				formatter: function (value, index) {
					return tranNumber(Number(value), 2);
				},
			},
		},
		dataZoom: [
			//Y轴滑动条
			{
				type: 'inside', //滑动条
				show: true, //开启
				xAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
				left: '93%', //滑动条位置
			},
			//y轴内置滑动
			{
				type: 'inside', //内置滑动，随鼠标滚轮展示
				xAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
			},
		],
		series: seriesList,
	};
};
export const UserEnterpriseRightSideMid = (data) => {
	if (!data || JSON.stringify(data) == '{}') return;
	const seriesList: any = [];
	const colorList = [
		'#FD9546',
		'#8A40FF',
		'#E3A07C',
		'#491DFF',
		'#8EFAFF',
		'#A3EE63',
		'#63A4EE',
		'#6863EE',
	];
	data?.map((item, index) => {
		seriesList.push({
			name: item.name,
			type: 'line',
			stack: 'total',
			smooth: true,
			symbol: 'none',
			data: item.value?.map((i) => i.toFixed(3)),
		});
	});

	return {
		tooltip: {
			trigger: 'axis',
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',
			padding: 8,
			textStyle: {
				color: '#fff',
			},
		},
		legend: {
			width: '80%',
			right: '10%',
			// top: '10%',
			textStyle: {
				fontSize: 16,
				color: '#E2F0FF',
			},
			type: 'scroll',
			pageTextStyle: {
				color: '#fff', // 文字样式
			},
		},
		grid: {
			top: 70,
			bottom: 22,
			left: '10%',
			right: '0%',
		},
		xAxis: {
			type: 'category',
			// data: data[0]?.time.map((item) => moment(item).format('MM-DD HH时')),
			data: data[0]?.time.map((item) => item),
			axisLabel: {
				textStyle: {
					color: '#E2F0FF',
					fontSize: 18,
				},
				formatter: (params) => {
					if (typeof params !== 'string') {
						return params;
					}
					// 检查 params 是否包含时间部分 (HH:mm 或 HH:mm:ss)
					const hasTimePart = /\s\d{2}:\d{2}(?::\d{2})?/.test(params);
					const momentDate = moment(
						params,
						hasTimePart ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD',
						true,
					);
					if (!momentDate.isValid()) {
						return params;
					}
					return hasTimePart
						? momentDate.format('HH:mm')
						: momentDate.format('MM/DD');
				},
			},
		},
		yAxis: {
			name: `单位: kg`,
			nameTextStyle: {
				color: '#D8DBDE',
				fontSize: 14,
			},
			type: 'value',
			splitLine: {
				show: true,
				lineStyle: {
					color: 'rgba(222, 227, 239, 0.3)',
					type: [2, 4],
				},
			},
			axisLabel: {
				textStyle: {
					color: 'rgba(212, 232, 254, 1)',
					fontSize: 18,
				},
				formatter: function (value, index) {
					return tranNumber(Number(value), 2);
				},
			},
		},
		dataZoom: [
			//Y轴滑动条
			{
				type: 'inside', //滑动条
				show: true, //开启
				xAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
				left: '93%', //滑动条位置
			},
			//y轴内置滑动
			{
				type: 'inside', //内置滑动，随鼠标滚轮展示
				xAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
			},
		],
		series: seriesList,
	};
};
export const UserEnterpriseRightSideBottom = (data) => {
	if (!data) return;
	const seriesList: any = [];
	const colorList = [
		'#FD9546',
		'#8A40FF',
		'#E3A07C',
		'#491DFF',
		'#8EFAFF',
		'#A3EE63',
		'#63A4EE',
		'#6863EE',
	];
	data?.map((item, index) => {
		seriesList.push({
			name: item.name,
			type: 'line',
			stack: 'total',
			symbol: 'none',
			color: getCarColorTwo(item.name),
			areaStyle: {
				color: colorList[index],
			},
			data: item.value,
		});
	});

	return {
		tooltip: {
			trigger: 'axis',
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',
			padding: 8,
			textStyle: {
				color: '#fff',
			},
		},
		legend: {
			width: '80%',
			right: '10%',
			// top: '10%',
			textStyle: {
				fontSize: 16,
				color: '#E2F0FF',
			},
			type: 'scroll',
			pageTextStyle: {
				color: '#fff', // 文字样式
			},
		},
		grid: {
			top: 70,
			bottom: 22,
			left: '10%',
			right: '10%',
		},
		xAxis: {
			type: 'category',
			data: data[0]?.time,
			axisLabel: {
				textStyle: {
					color: '#E2F0FF',
					fontSize: 18,
				},
			},
		},
		yAxis: {
			name: `单位: 辆`,
			nameTextStyle: {
				color: '#D8DBDE',
				fontSize: 14,
			},
			type: 'value',
			splitLine: {
				show: true,
				lineStyle: {
					color: 'rgba(222, 227, 239, 0.3)',
					type: [2, 4],
				},
			},
			axisLabel: {
				textStyle: {
					color: 'rgba(212, 232, 254, 1)',
					fontSize: 18,
				},
				formatter: function (value, index) {
					return tranNumber(Number(value), 2);
				},
			},
		},
		dataZoom: [
			//Y轴滑动条
			{
				type: 'inside', //滑动条
				show: true, //开启
				xAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
				left: '93%', //滑动条位置
			},
			//y轴内置滑动
			{
				type: 'inside', //内置滑动，随鼠标滚轮展示
				xAxisIndex: 0,
				filterMode: 'filter',
				height: 4,
				bottom: 10,
				handleSize: '300%',
			},
		],
		series: seriesList,
	};
};
