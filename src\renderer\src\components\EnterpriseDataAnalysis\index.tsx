import { useEffect, useRef, useState } from 'react';
import { Segmented, Table, Radio, Tooltip } from 'antd';
import type { TableProps } from 'antd';
import * as echarts from 'echarts';
import dayjs from 'dayjs';
import TimeTypeRadio from '@renderer/baseUI/TimeTypeRadioCustom';
import Box from '@renderer/baseUI/Box';
import CustomPopUps from '@renderer/baseUI/TimeTypeRadioCustom/components/CustomPopUps';
import {
	getRealTimeEnterpriseDetails,
	getRealTimeEnterpriseRanking,
	getAffiliationInfoByID,
	getEnterpriseDataAnalytics,
} from '@renderer/api';
import { tranNumber } from '@renderer/hooks';
import ScrollListWidthTitle from '../SlidingLayer/DailyActivityLevel/components/ScrollListWidthTitle';
import RealTimeEnterpriseDataStyled from './styles';
import styled from 'styled-components';

type Align2 = '图形' | '列表';

interface DataType {
	AFFILIATION: string;
	DISTANCE: number;
	NOX_EMISSIONS: number;
	OIL_CONSUMPTION: number;
	ONLINE_PERIOD: number;
	count: number;
}

interface OptionDataType {
	num: Array<string>;
	lable: Array<string>;
	value: Array<string>;
}

const colorList = [
	'#FFD076',
	'#45F4F5',
	'#07A6FF',
	'#FFFFFF',
	'#00C2FF',
	'#46FFE9',
	'#A3F9FE',
	'#0084FF',
	'#0578B9',
];
const colorList1 = [
	'#ffd076',
	'',
	'#45F4F5',
	'',
	'#07A6FF',
	'',
	'#FFFFFF',
	'',
	'#00C2FF',
	'',
	'#46FFE9',
	'',
	'#A3F9FE',
	'',
	'#0084FF',
	'',
	'#0578B9',
	'',
];
const colorList2 = [
	'rgba(255, 208, 118, 0.4)',
	'',
	'rgba(69, 244, 245, 0.4)',
	'',
	'rgba(7, 166, 255, 0.4)',
	'',
	'rgba(255, 255, 255, 0.4)',
	'',
	'rgba(0, 194, 255, 0.4)',
	'',
	'rgba(163, 249, 254, 0.4)',
	'',
	'rgba(0, 132, 255, 0.4)',
	'',
	'rgba(5, 120, 185, 0.4)',
	'',
];

const EnterpriseDataAnalysis = () => {
	// const [timeType, setTimeType] = useState('year')
	const [alignValue, setAlignValue] = useState('在线车辆');
	const [selectedButton, setSelectedButton] = useState('default');
	const [alignValue2, setAlignValue2] = useState<Align2>('图形');
	const [showPop, setShowPop] = useState(false);
	const [customDate, setCustomDate] = useState({});
	const [topic, setTopic] = useState('1');
	const [unitName, setUnitName] = useState('单位: 辆');
	const [optionData, setOptionData] = useState<OptionDataType>([]);
	const [data, setData] = useState<Array<DataType>>([]);
	// const echartsRealTimeEnterpriseContainerRef = useRef(null)
	const pieRef = useRef(null);
	const barRef = useRef(null);
	const pieChartRef = useRef<any>(null);
	const barChartRef = useRef<any>(null);
	const items = ['在线车辆', '排放量', '里程', '油耗', '运行时长'];
	const graphSwitching = ['图形', '列表'];
	const columns: TableProps<DataType>['columns'] = [
		{
			title: '企业名称',
			dataIndex: 'AFFILIATION',
			key: 'AFFILIATION',
			// render: (text) => (
			//   <Tooltip title={text}>
			//     <span>{text}</span>
			//   </Tooltip>
			// )
		},
		{
			title: '在线车辆',
			dataIndex: 'count',
			key: 'count',
		},
		{
			title: '排放量',
			dataIndex: 'NOX_EMISSIONS',
			key: 'NOX_EMISSIONS',
		},
		{
			title: '里程',
			dataIndex: 'DISTANCE',
			key: 'DISTANCE',
		},
		{
			title: '油耗',
			dataIndex: 'OIL_CONSUMPTION',
			key: 'OIL_CONSUMPTION',
		},
		{
			title: '运行时长',
			dataIndex: 'ONLINE_PERIOD',
			key: 'ONLINE_PERIOD',
		},
	];
	const optionTable = {
		columns: columns,
		width: '100%',
		height: '186px',
		fontSize: 20,
		thclassname: 'th_row',
		tablebgcolor: 'rgba(9,30,59,0)',
		trheight: '40px',
		thheight: '40px',
		customwidth: true,
		rowbgcolor: [
			'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
		],
		thbgcolor: '#081F38',
	};

	const handleClickReference = (value) => {
		setSelectedButton(value);
		if (value === 'custom') {
			setShowPop(true);
		} else {
			setShowPop(false);
		}
	};

	// 获取企业排名
	const getEnterpriseRanking = () => {
		let json = {
			time_type: 1,
		};
		if (selectedButton !== 'default') {
			if (selectedButton == 'all') {
				//累计
				json.time_type = 2;
			} else {
				json.time_type = 3;
				(json.start_time = customDate.startTime),
					(json.end_time = customDate.endTime);
			}
		}
		getRealTimeEnterpriseRanking(json).then((res) => {
			let affiliations = [];
			let formatData = res.map((item) => {
				affiliations.push(item.AFFILIATION);
				return {
					AFFILIATION: item.AFFILIATION,
					DISTANCE: tranNumber(item.DISTANCE, 2) + ' km',
					NOX_EMISSIONS: tranNumber(item.NOX_EMISSIONS, 2) + ' g',
					OIL_CONSUMPTION: tranNumber(item.OIL_CONSUMPTION, 2) + ' L',
					ONLINE_PERIOD: tranNumber(item.ONLINE_PERIOD, 2) + ' h',
					count: tranNumber(item.count, 2) + ' 辆',
				};
			});
			getAffiliationInfoByID({
				affiliation_ids: affiliations.toString(),
			})
				.then((res) => {
					if (res.length) {
						// let affiliations = []
						// xdata.forEach(element => {
						//   let name = res.find((item) => {
						//     return item['AFFILIATION_ID'] == element
						//   })['AFFILIATION']
						//   affiliations.push(name)
						// })
						// const dataArr = {xdata: affiliations, result }
						// setOption2(AreaRightSideCenter(dataArr))
						// setOption3(AreaRightSideBottom({ onlineData, sumData, affiliations }))
						setData(
							formatData.map((item, idx) => {
								return {
									...item,
									AFFILIATION:
										res.find((i) => i['AFFILIATION_ID'] == item['AFFILIATION'])
											?.AFFILIATION || '--',
								};
							}),
						);
					}
				})
				.catch((error) => {
					console.log('error', error);
				});
		});
	};

	useEffect(() => {
		let json = {
			time_type: 1,
			topic,
		};
		if (selectedButton !== 'default') {
			if (selectedButton == 'all') {
				//累计
				json.time_type = 2;
			} else {
				json.time_type = 3;
				(json.start_time = customDate.startTime),
					(json.end_time = customDate.endTime);
			}
		}
		getEnterpriseDataAnalytics(json).then((res) => {
			if (res.length) {
				const optionData = res.map((item) => {
					return {
						name: item.name,
						value: item.number,
						percent: item.rate * 100,
						num: item.number,
					};
				});
				setOptionData(optionData);
			}
		});
	}, [selectedButton, customDate, topic]);

	useEffect(() => {
		if (!optionData.length) return;
		pieChartRef.current = echarts.init(pieRef.current);
		let data = optionData;

		const pieOption = {
			// backgroundColor: '#243c54',
			tooltip: {
				trigger: 'item',
			},
			title: {
				text: `{a|区间范围} {b|${unitName}}`,
				right: 0,
				top: 10,
				textStyle: {
					color: '#fff',
					fontSize: 18,
					rich: {
						a: {
							fontSize: 18,
							fontWeight: 'normal',
						},
						b: {
							fontSize: 16,
							color: 'gray',
						},
					},
				},
			},

			legend: {
				icon: 'none',
				orient: 'vertical',
				top: 40,
				right: 10,
				itemWidth: 12,
				itemHeight: 12,
				formatter: (name) => {
					const arr = [`{iconName|}{name|${name}}`];
					return arr.join('');
				},
				textStyle: {
					color: '#FFF',
					fontSize: 16,
					rich: {
						name: {
							color: '#FFF',
							fontSize: 16,
							// width: 'auto',
							padding: [0, 0, 0, 10],
						},
						value: {
							color: '#2BDFD4',
							fontFamily: 'PangMenZhengDao',
							fontSize: 16,
							// width: 'auto',
							textAlign: 'right',
							padding: [0, 0, 0, 10],
						},
						percent: {
							color: '#2BDFD4',
							fontFamily: 'PangMenZhengDao',
							fontSize: 16,
							padding: [0, 0, 0, 10],
						},
						unit: {
							color: '#ACDCE4',
							fontSize: 16,
							padding: [0, 0, 0, 5],
						},
					},
				},
				data: data.map((dItem, dIndex) => {
					return {
						...dItem,
						textStyle: {
							rich: {
								iconName: {
									width: 16,
									height: 16,
									borderRadius: 2,
									backgroundColor: colorList[dIndex],
								},
								percent: {
									color: colorList[dIndex],
								},
							},
						},
					};
				}),
			},

			series: [
				{
					// 饼图圈
					type: 'pie',
					zlevel: 3,
					radius: ['35%', '62%'],
					center: ['35%', '50%'],
					itemStyle: {
						normal: {
							//  borderColor: '#0A1934',
							// borderWidth:5,
							color: function (params) {
								return colorList2[params.dataIndex];
							},
						},
					},
					labelLine: {
						length: 20,
						length2: 20,
						lineStyle: {
							width: 2,
						},
					},
					label: {
						formatter(item) {
							if (item.name === '') {
								return '';
							}
							return `${item.percent}%`;
						},
						textStyle: {
							fontSize: 14,
							color: '#ffffff',
						},
					},
					data: optionData,
				},
				{
					// 最外圆圈
					type: 'pie',
					zlevel: 1,
					silent: true, //取消高亮
					radius: ['69%', '73%'],
					center: ['35%', '50%'],
					itemStyle: {
						normal: {
							//  borderColor: '#0A1934',
							// borderWidth:5,
							color: function (params) {
								return colorList1[params.dataIndex];
							},
						},
					},
					label: {
						show: false,
					},
					data: optionData,
				},
				{
					type: 'pie',
					radius: ['79%', '81%'],
					center: ['35%', '50%'],
					// radius: '90%',
					hoverAnimation: false,
					clockWise: false,

					itemStyle: {
						normal: {
							shadowBlur: 1,
							shadowColor: 'rgba(15, 79, 150,0.61)',
							color: 'rgba(23,138,173,1)',
						},
					},
					label: {
						show: false,
					},
					data: optionData,
				},
			],
		};

		pieChartRef.current.setOption(pieOption);

		barChartRef.current = echarts.init(barRef.current);

		const barOption = {
			// backgroundColor: '#002653',
			tooltip: {
				trigger: 'axis',
				show: false,
				axisPointer: {
					// 坐标轴指示器，坐标轴触发有效
					type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
				},
			},
			title: {
				text: `用户企业数量`,
				right: 0,
				top: 10,
				textStyle: {
					color: '#fff',
					fontSize: 18,
					fontWeight: 'normal',
				},
			},
			legend: {
				show: false,
			},
			grid: {
				left: '5%',
				right: '25%',
				bottom: '15%',
				top: '15%',
				containLabel: false,
			},
			xAxis: [
				{
					splitLine: {
						show: false,
					},
					type: 'value',
					show: false,
				},
			],
			yAxis: [
				{
					show: false,
					splitLine: {
						show: false,
					},
					axisLine: {
						//y轴
						show: false,
					},
					type: 'category',
					axisTick: {
						show: false,
					},
					inverse: true,
					data: data && data.length > 0 ? data.map((i) => i.num) : [],
					axisLabel: {
						color: '#FFF',
						fontSize: 16,
					},
				},
			],
			series: [
				{
					name: '',
					type: 'bar',
					barWidth: 12, // 柱子宽度
					label: {
						show: true,
						position: 'right', // 位置
						color: '#FFAA00',
						fontSize: 16,
						distance: 5, // 距离
						// formatter: '{c}家' // 这里是数据展示的时候显示的数据
						formatter: (o) => {
							return `${o.data.value}家`;
						},
					}, // 柱子上方的数值
					itemStyle: {
						barBorderRadius: [20, 20, 20, 20], // 圆角（左上、右上、右下、左下）
						color: function (params) {
							return colorList[params.dataIndex];
						},
					},
					data,
				},
			],
		};

		barChartRef.current.setOption(barOption);
	}, [optionData]);

	useEffect(() => {
		switch (alignValue) {
			case '里程':
				setTopic('3');
				setUnitName('单位: km');
				break;
			case '运行时长':
				setTopic('5');
				setUnitName('单位: h');
				break;
			case '油耗':
				setTopic('4');
				setUnitName('单位: L');
				break;
			case '排放量':
				setTopic('2');
				setUnitName('单位: g');
				break;
			case '在线车辆':
				setTopic('1');
				setUnitName('单位: 辆');
				break;
			default:
				break;
		}
	}, [alignValue]);

	useEffect(() => {
		if (alignValue2 === '图形') return;
		getEnterpriseRanking();
	}, [alignValue2, selectedButton, customDate]);

	return (
		<Box
			title={'企业数据分析'}
			titlewidth="95%"
			height="100%"
			subTitle={
				<div className="customDate">
					<ReferenceStyled>
						<div className="selectAssembly">
							<Radio.Button
								value="default"
								onClick={() => handleClickReference('default')}
								style={{
									color: selectedButton === 'default' ? '#3ADAFF' : '#e2e5e5',
									marginRight:
										selectedButton === 'all' || selectedButton === 'custom'
											? '10px'
											: '0',
								}}
							>
								实时
							</Radio.Button>
							{/* <Radio.Button
								value="all"
								onClick={() => handleClickReference('all')}
								style={{
									color:
										selectedButton === 'all' || selectedButton === 'custom'
											? '#3ADAFF'
											: '#e2e5e5',
								}}
							>
								累计
							</Radio.Button> */}
							<Radio.Button
								value="custom"
								onClick={() => handleClickReference('custom')}
								style={{
									display:
										selectedButton === 'all' || selectedButton === 'custom'
											? 'block'
											: 'none',
									color: selectedButton === 'custom' ? '#3ADAFF' : '#e2e5e5',
								}}
							>
								自定义
							</Radio.Button>
						</div>
						<CustomPopUps
							top={'30px'}
							right={'0'}
							showPop={showPop}
							setShowPop={setShowPop}
							setTimeType={null}
							setCustomDate={setCustomDate}
						/>
					</ReferenceStyled>
				</div>
			}
		>
			<RealTimeEnterpriseDataStyled>
				<div className="enterpriseContent">
					<div className="topNav">
						<Segmented
							defaultValue="在线车辆"
							className="RealTimeIndustryNav"
							style={{ opacity: alignValue2 === '列表' ? 0 : 1, width: '80%' }}
							onChange={(value) => setAlignValue(value as any)}
							options={items}
						/>
						<Segmented
							defaultValue="图形"
							className="RealTimeIndustryNav"
							// style={{ position: 'absolute', top: '13%', right: '4%' }}
							onChange={(value) => setAlignValue2(value as Align2)}
							options={graphSwitching}
						/>
					</div>
					{alignValue2 === '列表' ? (
						<div className="enterpriseSituationOptionContent">
							{/* <Table
                columns={columns}
                dataSource={data}
                // pagination={{ pageSize: 10 }}
                scroll={{ y: '150px' }}
              /> */}
							<ScrollListWidthTitle
								columns={columns}
								data={data}
								{...optionTable}
							></ScrollListWidthTitle>
						</div>
					) : null}
					<div
						// ref={echartsRealTimeEnterpriseContainerRef}
						style={{
							width: '100%',
							height: '100%',
							display: alignValue2 === '列表' ? 'none' : 'block',
						}}
					>
						<div
							ref={pieRef}
							style={{
								width: '70%',
								height: '100%',
								display: 'inline-block',
							}}
						></div>
						<div
							ref={barRef}
							style={{
								width: '30%',
								height: '100%',
								display: 'inline-block',
							}}
						></div>
					</div>
				</div>
			</RealTimeEnterpriseDataStyled>
		</Box>
	);
};

export default EnterpriseDataAnalysis;

const ReferenceStyled = styled.div`
	.selectAssembly {
		width: 100%;
		margin-left: auto;
		padding-right: 20px;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
		}
		.ant-radio-button-wrapper {
			width: 80px;
			border: 0;
			border-radius: 5px;
			font-size: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			span:hover {
				color: #3adaff;
			}
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
	}
`;
