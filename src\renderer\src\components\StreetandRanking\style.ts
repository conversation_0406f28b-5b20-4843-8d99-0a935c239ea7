import styled from 'styled-components';
import background from '@renderer/images/background';

export const RankingDeregulationListStyled = styled.div`
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	color: #fff;
	flex-direction: column;
	height: 500px;
	width: 100%;

	.middle-container {
		width: 100%;
		padding-right: 10px;
		box-sizing: border-box;
		padding-top: 10px;
		.Violation {
			font-size: 18px;
			font-weight: 400;
			color: #ffffff;
			padding-left: 10px;
			border-bottom: 2px solid;
			width: 100%;
			border-image: linear-gradient(
					340deg,
					rgba(255, 255, 255, 0),
					rgba(255, 255, 255, 0.1),
					rgba(255, 255, 255, 1),
					rgba(255, 255, 255, 0),
					rgba(151, 151, 151, 1)
				)
				1 1;
			margin-left: 0px;
			position: relative;
			height: 38px;
			&::before {
				content: '';
				position: absolute;
				top: -12px;
				left: -13px;
				background: url(${background.arrow}) no-repeat;
				width: 47px;
				height: 56px;
			}
			span {
				position: absolute;
				right: 34%;
				top: 2px;
				font-size: 22px;
				background: linear-gradient(
					180deg,
					#ffffff 0%,
					#c3dff4 46%,
					#78d9ff 100%
				);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				font-weight: 600;
			}
			.Violation1 {
				border-bottom: none;
				font-size: 20px;
				font-weight: 400;
				color: #c3dff4;
				line-height: 32px;
				padding-left: 21px;
				letter-spacing: 1px;
			}
			.line {
				width: 95px;
				height: 2px;
				border: none;
				filter: blur(0.5px);
				position: relative;
				left: -10px;
				top: 2px;
			}
		}
	}
	.right-container {
		padding-bottom: 10px;
	}
	.table {
		border-radius: 7px 7px 0 0;
	}
	dl {
		border: 2px solid rgba(9, 132, 182, 0.7);
		border-radius: 7px 7px 0 0;
		border-bottom: 0;
		width: 100%;
		box-sizing: border-box;
		margin-top: 10px;
		margin-bottom: 0;
		dt {
			display: flex;
			background-color: #060026d4;
			font-family: PingFangSC-Regular, PingFang SC;
			color: #d8f1ff;
			letter-spacing: 1px;
			font-size: 20px;
			padding: 4px 0;
			span {
				text-align: center;
				line-height: 26px;
				&.no1 {
					width: 18%;
				}
				&.no2 {
					width: 29%;
				}
				&.no3 {
					width: 25%;
				}
				&.no4 {
					width: 28%;
				}
				&.no5 {
					width: 24%;
				}
			}
		}
		dd {
			display: flex;
			border-bottom: 1px solid rgba(9, 132, 182, 0.7);
			background-color: #0f334a;
			margin: 0;
			height: 38px;
			color: #d4d4d4;
			cursor: pointer;
			align-items: center;
			&:hover {
				background: rgba(58, 218, 255, 0.2);
			}
			span {
				text-align: center;
				line-height: 30px;
				&.no1 {
					width: 18%;
					&.top0 {
						color: rgb(213, 46, 76);
					}
					&.top1 {
						color: rgb(229, 101, 16);
					}
					&.top2 {
						color: rgb(203, 146, 28);
					}
				}
				&.no2 {
					width: 27%;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				&.no3 {
					width: 28%;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				&.no4 {
					width: 28%;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				&.no5 {
					width: 28%;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
		}
	}
`;
