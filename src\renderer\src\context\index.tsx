import { createContext, Dispatch } from 'react';
import { Map } from 'mars3d';
import { SceneMode } from 'mars3d-cesium';

// 应用级状态
export type IAppContext = {
	font: string;
	[key: string]: any;
};

export const AppContext = createContext<IAppContext>(null);

// 地图状态
export type IMapContext = {
	map: Map;
	mounted: boolean;
	sceneMode: SceneMode;
	setSceneMode: Dispatch<SceneMode>;
};

export const MapContext = createContext<IMapContext>(null);

// 权限
export type IAuthContext = {
	token: string;
	login: (token: string) => Promise<void>;
	logout: () => void;
};
export const AuthContext = createContext<IAuthContext>(null);

export type IPageContext = {
	[key: string]: any;
};

export const PageContext = createContext<IPageContext>(null);

export default MapContext;
