# VehicleHistoricalTrajectory.jsx 改造总结

## 改造目标
将实时轨迹功能改造为每5秒获取一次当前数据，累积收集轨迹点，然后使用FixedRoute来实现连续轨迹播放。调用函数即自动开始播放，无需额外控制。

## 主要修改

### 1. 数据收集策略
- **新策略**: 每5秒获取一次当前位置数据
- **累积机制**: 将每次获取的数据点累积到轨迹数组中
- **轨迹限制**: 最多保存120个点（约10分钟的轨迹数据）

### 2. 轨迹点存储
```javascript
// 新增全局变量
realTimeTrajectoryPoints = [], // 存储实时轨迹点
maxTrajectoryPoints = 120; // 最多保存120个点

// 轨迹点数据结构
const newPoint = {
  position: [gcj2wgs2[0], gcj2wgs2[1]], // 转换后的坐标
  speed: latestPoint.speed || 0,        // 速度信息
  time: latestPoint.gDate,              // 时间戳
  data: latestPoint                     // 原始数据
};
```

### 3. 动态轨迹更新
- **智能更新**: 首次创建时重新创建FixedRoute，后续使用addDynamicPosition添加点
- **保持状态**: 不重置播放状态和镜头位置
- **连续轨迹**: 使用累积的所有点创建连续的轨迹线
- **自动播放**: 轨迹准备完成后自动开始播放

### 4. 定时器调整
- **获取频率**: 每5秒获取一次数据 (`5000ms`)
- **轨迹更新**: 每次获取数据后立即更新轨迹

### 5. 简化设计
- **无需手动控制**: 调用 `viewRealTimeVehicleData()` 即自动开始
- **自动清理**: 切换车辆或组件卸载时自动清空轨迹
- **最小化UI**: 不显示额外的控制按钮或状态信息

## 技术细节

### 核心实现逻辑
```javascript
const getVehicleRealTimePositionInfoData = (vid, minute) => {
  // 1. 获取当前位置数据
  getVehicleRealTimePositionInfo({ vid, time })
    .then((res) => {
      if (res.length) {
        const latestPoint = res[0];

        // 2. 添加到轨迹数组
        realTimeTrajectoryPoints.push({
          position: [gcj2wgs2[0], gcj2wgs2[1]],
          speed: latestPoint.speed || 0,
          time: latestPoint.gDate,
          data: latestPoint
        });

        // 3. 检查是否需要重建轨迹
        let needRecreate = false;
        if (realTimeTrajectoryPoints.length > maxTrajectoryPoints) {
          realTimeTrajectoryPoints.shift();
          needRecreate = true;
        }

        // 4. 智能更新轨迹
        if ((realTimeTrajectoryPoints.length === 2 && !graphicModel) || needRecreate) {
          // 首次创建或重建：创建完整FixedRoute
          // 自动开始播放
        } else if (realTimeTrajectoryPoints.length > 2 && graphicModel && !needRecreate) {
          // 动态添加：使用addDynamicPosition，保持播放状态
          const point = new mars3d.LngLatPoint(newPoint.position[0], newPoint.position[1]);
          graphicModel.addDynamicPosition(point, latestPoint.gDate);
        }
      }

      // 5. 设置下次获取（5秒后）
      setTimeout(() => {
        getVehicleRealTimePositionInfoData(vid, minute);
      }, 5000);
    });
};
```

### FixedRoute 智能更新策略
- **首次创建**: 当有2个点时创建初始FixedRoute并开始播放
- **动态添加**: 后续点使用addDynamicPosition方法添加，保持播放状态
- **重建机制**: 只有在超过最大点数限制时才重新创建轨迹
- **状态保持**: 不重置镜头位置和播放进度

### 数据管理
- **点数限制**: 最多保存120个点（10分钟数据）
- **自动清理**: 超出限制时移除最旧的点
- **内存管理**: 组件卸载时清空所有轨迹数据

## 使用方式
```javascript
// 只需调用这个函数即可开始实时轨迹
viewRealTimeVehicleData();

// 轨迹会：
// 1. 每5秒获取一次位置数据
// 2. 累积轨迹点
// 3. 动态更新并播放轨迹
// 4. 自动循环直到停止
```

## 优势
1. **简单易用**: 一个函数调用即可开始
2. **连续轨迹**: 显示完整的移动路径
3. **实时更新**: 每5秒更新一次轨迹
4. **状态保持**: 不重置播放状态和镜头位置
5. **性能优化**: 智能选择创建或添加点的方式
6. **内存优化**: 自动限制轨迹点数量

## 注意事项
1. 需要至少2个轨迹点才能开始播放
2. 轨迹会随着新数据不断更新和重播
3. 切换车辆时会自动清空之前的轨迹数据
