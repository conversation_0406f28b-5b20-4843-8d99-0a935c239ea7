// 里程特征

import * as React from 'react';
import * as echarts from 'echarts';

import { useState, useEffect, useRef } from 'react';
import LayoutContainer from './style';
import Box from '../../baseUI/Box';

// import TimeTypeRadio from '@renderer/baseUI/TimeTypeRadio'
import { getStatisticsNox } from '@renderer/api';
import { useAuth } from '@renderer/hooks';
import moment from 'moment';
import { type } from 'os';

// import AllSelect from '../RealTimeOnlineDetail/AllSelect'
import TimeTypeRadio from './components/TimeTypeRadio';

// let my_data = {
//   xData: [
//     "海淀",
//     "朝阳",
//     "密云",
//     "昌平",
//     "顺义",
//     "石景山",
//     "丰台",
//     "通州",
//     "房山",
//   ],
//   legend: [
//     "公交车",
//     "其他",
//     "冷藏车",
//     "加油车",
//     "医疗废弃物转运车",
//     "垃圾车",
//     "客车",
//   ],
//   data: [
//     [1320, 1302, 901, 634, 1390, 1330, 1320, 1000, 500],
//     [320, 302, 301, 334, 390, 330, 320, 100, 50],
//     [320, 302, 301, 334, 390, 330, 320, 100, 50],
//     [320, 302, 301, 334, 390, 330, 320, 100, 50],
//     [320, 302, 301, 334, 390, 330, 320, 100, 50],
//     [320, 302, 301, 334, 390, 330, 320, 100, 50],
//     [320, 302, 301, 334, 390, 330, 320, 100, 50],
//   ],
// };
// let colorList = [
//   "#29BCFD",// 轻
//   "#2D7BE5",// 中
//   "#F5803D",// 重
//   "#73DEBD",
//   "#26C978",
//   "#8CDF6C",
//   "#FBD657",
//   "#F56679",
//   "#E07BCE",
//   "#9D50E0",
//   "#634FDA",
// ];

let colorList = [
	'#29BCFD', // 轻
	'#2D7BE5', // 中
	'#85A9FF', // 重
	'#73DEBD',
	'#26C978',
	'#8CDF6C',
	'#FBD657',
	'#F56679',
	'#E07BCE',
	'#9D50E0',
	'#634FDA',
];

type Props = {
	title: string;
	data: { xData: string[]; data: number[][]; legend: string[] };
	onTimeChange: Function;
	defaultValue?: 'year' | 'month' | 'date';
	colors?: string[];
	legendColors?: string[];
	barWidth?: string;
	interval?: number;
};

const typeOptions = [
	{
		label: '日',
		value: 'date',
	},
	{
		label: '月',
		value: 'month',
	},
	{
		label: '年',
		value: 'year',
	},
];

const MileageCharacteristics = ({
	title,
	barWidth = '45%',
	data = my_data,
	onTimeChange,
	colors = colorList,
	defaultValue = 'year',
	interval = 0,
}: Props) => {
	const { token } = useAuth();
	const echartRef = useRef<any>(null);
	const myChart = useRef<any>(null);

	useEffect(() => {
		myChart.current = echarts.init(echartRef.current);
		let option = {
			title: {
				text: '总里程（千KM）',
				// left: 19,
				left: '14%',
				top: 25,
				textStyle: {
					color: '#fff',
					fontSize: 12,
					fontWeight: 300,
				},
			},
			tooltip: {
				trigger: 'axis',
				axisPointer: { type: 'shadow' },
				formatter: (params) => {
					const title = params[0].name;
					const data = params
						.map(function (item) {
							return `${item.marker}${item.seriesName}: ${item.value}`;
						})
						.join('<br>');
					return `${title}<br>${data}`;
				},
			},
			color: colors,
			legend: {
				// top: 25,
				// left: 'center'
				top: '20%',
				left: '2%',
				orient: 'vertical',
				itemWidth: 10,
				itemHeight: 10,
				// itemStyle: {
				//   color: legendColors ? legendColors : undefined,
				// },
				textStyle: {
					fontSize: 14,
					color: '#E8F4FF',
					padding: [3, 0, 0, 0],
				},
				data: data?.legend,
			},
			grid: {
				left: '15%',
				right: '4%',
				bottom: '3%',
				containLabel: true,
			},
			xAxis: {
				type: 'category',
				axisLabel: {
					color: '#E8F4FF',
					interval: interval,
				},
				axisLine: {
					lineStyle: {
						color: '#96A4F4',
					},
					width: 5,
				},
				axisTick: {
					show: false,
				},
				data: data?.xData,
			},
			yAxis: {
				type: 'value',
				axisLabel: {
					color: '#E8F4FF',
				},
				axisLine: {
					lineStyle: {
						color: '#96A4F4',
					},
					width: 5,
				},
				axisTick: {
					show: false,
				},
				splitLine: {
					lineStyle: {
						color: 'rgba(150, 164, 244, 0.3)',
					},
				},
			},
			series: [],
		};
		for (let i = 0; i < data?.legend?.length; i++) {
			// @ts-ignore
			option.series.push({
				name: data?.legend[i],
				type: 'bar',
				stack: '总量',
				barWidth: barWidth,
				label: {
					show: false,
					position: 'insideRight',
				},
				data: data?.data[i].map((number) => number / 1000),
			});
		}
		myChart.current.setOption(option, true);
	}, [data]);

	// useEffect(() => {
	//   onTimeChange(timeData)
	//   // console.log('timeData--', timeData)
	//   const req = {
	//     token,
	//     start_time: moment().subtract(7, 'day').format('YYYY-MM-DD HH:00:00'),
	//     end_time: moment().format('YYYY-MM-DD HH:00:00'),
	//     data_type: 2,
	//     timeType: 2,
	//     accumulated: 1,

	//   }
	//   // getStatisticsNox(req).then(res => {
	//   //   // setDate(res)
	//   // })
	// }, [timeData])
	return (
		// <Box title={title} height='100%' subTitle={<div style={{ marginRight: '50px' }}><TimeTypeRadio defaultValue={defaultValue} onTimeTypeChange={onTimeChange} /></div>} >
		<Box
			title={title}
			titlewidth="95%"
			height="100%"
			subTitle={
				<div style={{ display: 'flex' }}>
					<TimeTypeRadio
						typeOptions={typeOptions}
						defaultValue={defaultValue}
						onTimeTypeChange={onTimeChange}
					/>
				</div>
			}
		>
			<LayoutContainer>
				<div className="MileageCharacteristicsEchart" ref={echartRef}></div>
			</LayoutContainer>
		</Box>
	);
};
export default MileageCharacteristics;
