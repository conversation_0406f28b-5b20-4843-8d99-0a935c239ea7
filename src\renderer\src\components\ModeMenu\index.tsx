import { useState, useEffect, useContext, FC } from 'react';
import { Tabs } from 'antd';
import '@animxyz/core';
import { XyzTransitionGroup } from '@animxyz/react';

import { MapContext, PageContext } from '@renderer/context';
import layers from '@renderer/images/icon/layers';
import { carData } from '@renderer/constants';

import { ModeMenuContent } from './styles';

let timeout;

interface IProps {
	parsentFlash: boolean;
	openTrajectory: boolean;
}

const ModeMenu: FC<IProps> = (props) => {
	const { parsentFlash, openTrajectory } = props;
	const { selectMode, setSelectMode, switchingPointsId, hideModeMenu } =
		useContext(PageContext);
	const [isVisible, setIsVisible] = useState(false);
	const [targetId, setTargetId] = useState(switchingPointsId);
	const [mouseStatus, setMouseStatus] = useState(false);
	const [items, setItems] = useState([]);
	const [loading, setLoading] = useState(true);

	const [childList, setChildList] = useState(carData);
	const [nameList, setNameList] = useState({
		all: '全部',
		freight: '货运',
		passenger: '客运',
		workingVehicle: '工程',
		other: '其他',
	});
	const targetValues = {
		vehicleOnline: 1,
		violatingVehicles: 2,
	};
	const timeOut = () => {
		timeout = setTimeout(() => {
			setIsVisible(false);
		}, 3000);
	};

	let updatedItems = [];

	useEffect(() => {
		setTargetId(targetValues[switchingPointsId]);
		setLoading(true);
		getCarData(carData);
		if (!isVisible) return;
		if (timeout) clearTimeout(timeout);
	}, [switchingPointsId]);

	useEffect(() => {
		setIsVisible(!parsentFlash);
		if (!parsentFlash) {
			setTimeout(() => {
				setLoading(false);
				setIsVisible(false);
			}, 5000);
		}
	}, [parsentFlash]);

	useEffect(() => {
		setSelectMode({
			key: 'all',
			child: childList['all'],
		});
	}, []);

	useEffect(() => {
		if (!mouseStatus && !loading) {
			timeOut();
		}
	}, [mouseStatus, loading]);

	const onChange = (key) => {
		setSelectMode({
			key,
			child: childList[key],
		});
	};

	const handleIsVisible = () => {
		setIsVisible(!isVisible);
	};

	const onMouseLeave = () => {
		if (loading) return;
		timeOut();
		setMouseStatus(false);
	};

	const getCarData = (data) => {
		updatedItems = [];
		for (let key in layers) {
			updatedItems.push({
				key,
				label: (
					<div className="layersButton">
						<img
							src={layers[key]}
							width="24px"
							height="24px"
							alt={nameList[key]}
							title={nameList[key]}
						/>
						<div>{nameList[key] || '--'}</div>
					</div>
				),
				children: (
					<div className="children">
						{data[key]?.map((item, index) => (
							<div className="item" key={index}>
								<div style={{ background: item.color }}></div>
								<div>
									{item.label} {item.num}
								</div>
							</div>
						))}
					</div>
				),
			});
		}
		setItems(updatedItems);
	};

	return (
		<ModeMenuContent hidden={hideModeMenu || openTrajectory ? true : false}>
			<XyzTransitionGroup xyz="fade up-100%">
				{!parsentFlash && !isVisible && (
					<div className="layermenuButton" onClick={handleIsVisible}></div>
				)}
			</XyzTransitionGroup>
			<XyzTransitionGroup xyz="fade up-100%">
				{isVisible && (
					<div
						className="layerMenuContent"
						onMouseLeave={() => onMouseLeave()}
						onMouseMove={() => {
							if (timeout) {
								clearTimeout(timeout);
							}
							setIsVisible(true);
						}}
					>
						<Tabs
							activeKey={selectMode.key}
							type="card"
							items={items}
							onChange={onChange}
						/>
					</div>
				)}
			</XyzTransitionGroup>
		</ModeMenuContent>
	);
};

export default ModeMenu;
