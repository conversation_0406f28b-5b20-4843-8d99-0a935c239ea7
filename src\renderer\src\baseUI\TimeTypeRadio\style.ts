import styled from 'styled-components';

const TimeTypeRadioContent = styled.div`
	.ant-radio-group {
		border: 1px solid #2d8987;
		border-radius: 5px;
	}
	.ant-radio-button-wrapper {
		background: none;
		color: #e2f0ff;
		border: none;
		height: 30px;
		font-size: 17px;
		padding: 0 15px;
		line-height: 26px;
	}
	.ant-radio-button-wrapper:hover {
		color: #fff;
		border: none;
	}
	.ant-radio-button-wrapper-checked {
		background: rgba(58, 218, 255, 0.5);
		border: none;
	}
	.ant-radio-button-wrapper:not(:first-child)::before {
		width: 0;
	}
`;
export { TimeTypeRadioContent };
