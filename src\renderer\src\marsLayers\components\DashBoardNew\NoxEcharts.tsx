import { useState, useEffect } from 'react';
import * as echarts from 'echarts';
import Echarts from '@renderer/components/echarts';
import { EchartsStyle } from './style';

export default function Dashecharts(props) {
	const { title, max, unit, value, color = '#666' } = props;
	const [option, setOption] = useState({});

	useEffect(() => {
		const v = Math.floor(value);
		const option = {
			series: [
				{
					// 进度条
					type: 'gauge',
					splitNumber: 10,
					radius: '100%',
					min: 0,
					max: max,
					pointer: {
						show: false,
						width: 5,
						length: '60%',
						itemStyle: {
							color: 'auto',
						},
					},
					axisLabel: {
						show: false,
					},
					axisLine: {
						show: true,
						lineStyle: {
							// 仪表的宽度
							width: 12,
							color: [
								[
									1,
									{
										// 仪表背景颜色
										type: 'linear',
										x: 0.5,
										y: 0.5,
										r: 0.5,
										colorStops: [
											{
												offset: 0,
												color: color, // 0% 处的颜色
											},
											{
												offset: 1,
												color: color, // 100% 处的颜色
											},
										],
										globalCoord: false, // 缺省为 false
									},
								],
							],
						},
						// lineStyle: {
						//   width: 12,
						//   color: [
						//     [0.3, 'rgba(103,224,227)'],
						//     [0.7, 'rgba(55,162,218)'],
						//     [1, 'lightcoral']
						//   ],
						//   borderColor: '#000',
						//   borderWidth: '10'
						// }
					},
					axisTick: {
						show: false,
					}, //刻度样式
					splitLine: {
						show: false,
						length: '20%',
						lineStyle: {
							color: '#3699FF',
							width: 2,
						},
					}, //分隔线样式
					detail: {
						valueAnimation: true,
						formatter: '{value}',
						textStyle: {
							fontFamily: 'SourceHanSansCN-Regular, SourceHanSansCN',
							fontSize: 36,
							fontWeight: 'bold',
							color: '#fff',
						},
						offsetCenter: ['0', '-25%'],
					},
					title: {
						show: true,
						// valueAnimation: true,
						color: '#fff',
						formatter: '{name}',
						fontSize: 20,
						offsetCenter: ['0', '25%'],
						fontWeight: 'bold',
					},
					data: [
						{
							value: Math.floor(v),
							name: 'NOx排放浓度\nppm',
						},
					],
				},
				{
					// 彩色部分
					type: 'gauge',
					splitNumber: 10,
					radius: '112%',
					min: 0,
					max: 55,
					pointer: {
						show: false,
						width: 7,
						length: '80%',
						color: 'auto',
					},
					axisLine: {
						show: true,
						lineStyle: {
							width: 25,
							color: [
								[0.3, 'transparent'],
								[0.7, 'transparent'],
								[1, 'transparent'],
							],
							borderColor: 'transparent',
							borderWidth: '1',
						},
					},
					axisLabel: {
						show: false,
					}, //刻度标签。
					axisTick: {
						show: false,
						splitNumber: 5,
						lineStyle: {
							color: '#EBF3FE',
							width: 1,
						},
					}, //刻度样式
					splitLine: {
						show: false,
						length: '12%',
						lineStyle: {
							color: '#fff',
							width: 2,
						},
					},
					detail: {
						show: false,
					},
					title: {
						show: false,
					},
				},
			],
		};

		setOption(option);
	}, [value]);

	return (
		<EchartsStyle>
			<div className="echarts">
				<Echarts option={option} />
			</div>
		</EchartsStyle>
	);
}
