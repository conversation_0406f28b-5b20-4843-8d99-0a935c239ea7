import { useEffect, useState } from 'react';
import { Radio } from 'antd';
import { TimeTypeRadioContent } from '../../baseUI/TimeTypeRadio/style';

export default function IndustryEmissions(props) {
	const { type, typeOptions, onlineType } = props;

	const defaultTypeOptions = [
		{
			label: '实时在线',
			value: 'realTime',
		},
		{
			label: '累计上线',
			value: 'Accumulate',
		},
	];
	const [typeOption, setTypeOptions] = useState(defaultTypeOptions);

	const [defaultType, setDefaultType] = useState(type ? type : 'day');
	useEffect(() => {
		typeOptions && setTypeOptions(typeOptions);
		onlineType && onlineType(type);
	}, [type]);

	const onTypeChange = (e) => {
		onlineType(e.target.value);
		setDefaultType(e.target.value);
	};
	return (
		<TimeTypeRadioContent>
			<Radio.Group
				options={typeOption}
				onChange={onTypeChange}
				value={defaultType}
				optionType="button"
			/>
		</TimeTypeRadioContent>
	);
}
