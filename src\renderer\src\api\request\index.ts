import axios from 'axios';
import { isEmpty } from 'lodash';
import localForage from 'localforage';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { AxiosInterceptors, CustomRequestConfig } from './types';
import { UncachedPath } from './pathList';

const Authorization = import.meta.env.RENDERER_VITE_Authorization;

let enableCache = false;
const CheckCacheSwitch = () => {
	let jsonData: any = sessionStorage.getItem('zcJsonData');
	if (jsonData) {
		jsonData = JSON.parse(jsonData);
		enableCache =
			jsonData?.enableCache && jsonData.enableCache === true ? true : false;
	}
};
const GetDBData = (baseURL, url, params) => {
	return new Promise(function (resolve, reject) {
		const CommonDataDB = localForage.createInstance({
			name: 'CommonDataDB',
			storeName: `${baseURL}${url}`,
		});
		let p = '{}';
		if (!isEmpty(params)) {
			p = JSON.stringify(params);
		}
		CommonDataDB.getItem(p)
			.then((value) => {
				const isEmptyData = checkisEmptyData(value);
				if (isEmptyData) resolve(null);
				resolve(value);
			})
			.catch((err) => {
				resolve(null);
			});
	});
};
const checkisEmptyData = (obj) => {
	if (Array.isArray(obj)) {
		if (obj.length) return false;
		return true;
	} else if (typeof obj === 'object' && obj !== null) {
		if (isEmpty(obj)) return true;
		return false;
	}
	return true;
};
const SaveDBData = (response, data) => {
	if (UncachedPath.includes(response.config.url)) return;
	const CommonDataDB = localForage.createInstance({
		name: 'CommonDataDB',
		storeName: `${response.config.baseURL}${response.config.url}`,
	});
	let params = '{}';
	if (response?.config?.params && !isEmpty(response.config.params)) {
		params = JSON.stringify(response.config.params);
	}
	CommonDataDB.setItem(params, data)
		.then((value) => {})
		.catch((err) => {});
};
class Request {
	instance: AxiosInstance;
	interceptorsObj?: AxiosInterceptors;
	constructor(config: CustomRequestConfig) {
		if (!config.baseURL) {
			throw new Error('必须传入baseURL!');
		}
		this.instance = axios.create({
			timeout: 600000,
			headers: {
				Authorization,
				'Content-Type': 'application/json;charset=utf-8',
			},
			...config,
		});
		this.interceptorsObj = config.interceptors;
		// 使用实例拦截器
		// 拦截器的执行顺序为实例请求→类请求→类响应→实例响应;这样就可以在实例拦截时做出一些不同的拦截
		this.instance.interceptors.request.use(
			this.interceptorsObj?.requestInterceptors,
			this.interceptorsObj?.requestInterceptorsCatch,
		);

		this.instance.interceptors.request.use(
			async (config: CustomRequestConfig) => {
				CheckCacheSwitch();
				const { baseURL, url, params } = config;
				if (enableCache) {
					if (UncachedPath.includes(url)) return config;
					const data = await GetDBData(baseURL, url, params);
					try {
						if (data)
							return Promise.reject({
								cached: true,
								data,
							});
						return config;
					} catch (error) {
						return config;
					}
				} else {
					return config;
				}
			},
			(error: any) => error,
		);
		this.instance.interceptors.response.use(
			this.interceptorsObj?.responseInterceptors,
			this.interceptorsObj?.responseInterceptorsCatch,
		);

		this.instance.interceptors.response.use(
			(response: AxiosResponse) => {
				CheckCacheSwitch();
				if (
					response.data.hasOwnProperty('code') ||
					response.data.hasOwnProperty('data')
				) {
					const code = response.data.code || response.data.data.code;
					if (code == 2000 || code == 200) {
						const data =
							response.data.result ?? response.data.data ?? response.data;
						if (enableCache) SaveDBData(response, data);
						return Promise.resolve(data);
					} else {
						return Promise.reject(response.data ?? response.data.data);
					}
				} else {
					if (enableCache) SaveDBData(response, response.data);
					return Promise.resolve(response.data);
				}
			},
			(error: any) => {
				if (error.cached) {
					return Promise.resolve(error.data);
				} else {
					return Promise.reject(error);
				}
			},
		);
	}

	request<T>(config: AxiosRequestConfig): Promise<T> {
		return this.instance.request(config);
	}

	get<T>(url: string, params: Record<string, any>): Promise<T> {
		return this.instance.get(url, { params });
	}

	getJSON<T>(url: string, params: Record<string, any>): Promise<T> {
		const param = {
			json: params,
		};
		return this.get(url, param);
	}

	post<T>(url: string, params: Record<string, any>): Promise<T> {
		return this.instance.post(url as string, JSON.stringify({ ...params }));
	}

	postFormData<T>(url: string, params: Record<string, any>): Promise<T> {
		let formData = new FormData();
		for (const key in params) {
			formData.append(key, params[key]);
		}
		return this.instance.post(url as string, formData, {
			headers: {
				'content-type': 'multipart/form-data',
			},
		});
	}

	postFormUrlencoded<T>(url: string, params: Record<string, any>): Promise<T> {
		return this.instance.post(url as string, params, {
			headers: {
				'content-type': 'application/x-www-form-urlencoded',
			},
		});
	}
}

export default Request;
