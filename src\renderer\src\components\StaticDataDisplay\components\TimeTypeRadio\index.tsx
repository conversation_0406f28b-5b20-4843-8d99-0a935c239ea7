import { useState } from 'react';
import { Radio } from 'antd';
import { TimeTypeRadioContent } from './style';

type Props = {
	defaultValue: string;
	onTypeChange: (e) => void;
	typeOptions: Array<any>;
};

export default function TimeSelect({
	defaultValue,
	onTypeChange,
	typeOptions,
}: Props) {
	const [defaultType, setDefaultType] = useState(defaultValue);

	const typeChange = (e) => {
		onTypeChange(e.target.value);
		setDefaultType(e.target.value);
	};

	return (
		<TimeTypeRadioContent className="time-type-radio">
			<Radio.Group
				options={typeOptions}
				onChange={(value) => typeChange(value)}
				value={defaultType}
				optionType="button"
			/>
		</TimeTypeRadioContent>
	);
}
