// 三级分类
enum CarType {
	BUS = '公交车',
	REFRIGERATED = '冷藏车',
	FUEL = '加油车',
	MEDICAL = '医疗废弃物转运车',
	GARBAGE = '垃圾车',
	COACH = '客车',
	CONSTRUCTION = '工程作业车',
	TOURISM = '旅游大巴',
	DANGEROUS = '普通危险品运输车',
	AIRPORT = '机场大巴',
	SCHOOL = '校车',
	CONCRETE = '混凝土车',
	CLEAN = '清扫车',
	SLAG = '渣土车',
	SPECIAL = '特殊环卫车',
	SPECIAL_USE = '特殊用途车',
	FREIGHT = '货车',
	POSTAL = '邮政车',
	DUMP = '自卸货车',
	TRACTOR = '牵引车',
	OTHER = '其它',
}

// 获取三级车辆颜色
export const getCarColor = (carType: string) => {
	switch (carType) {
		case CarType.BUS:
			return '#2ec7c9';
		case CarType.REFRIGERATED:
			return '#b6a2de';
		case CarType.FUEL:
			return '#5ab1ef';
		case CarType.MEDICAL:
			return '#ffb980';
		case CarType.GARBAGE:
			return '#d87a80';
		case CarType.COACH:
			return '#c14089';
		case CarType.CONSTRUCTION:
			return '#e5cf0d';
		case CarType.TOURISM:
			return '#97b552';
		case CarType.DANGEROUS:
			return '#95706d';
		case CarType.AIRPORT:
			return '#dc69aa';
		case CarType.SCHOOL:
			return '#07a2a4';
		case CarType.CONCRETE:
			return '#9a7fd1';
		case CarType.CLEAN:
			return '#588dd5';
		case CarType.SLAG:
			return '#f5994e';
		case CarType.SPECIAL:
			return '#c05050';
		case CarType.SPECIAL_USE:
			return '#59678c';
		case CarType.FREIGHT:
			return '#c9ab00';
		case CarType.POSTAL:
			return '#7eb00a';
		case CarType.DUMP:
			return '#91cc75';
		case CarType.TRACTOR:
			return '#FFFF00';
		case CarType.OTHER:
			return '#808080';
		default:
			return '#808080';
	}
};

export const carTypeIndex = {
	1: '公交车',
	2: '冷藏车',
	3: '加油车',
	4: '医疗废弃物转运车',
	5: '垃圾车',
	6: '客车',
	7: '工程作业车',
	8: '旅游大巴',
	9: '普通危险品运输车',
	10: '机场大巴',
	11: '校车',
	12: '混凝土车',
	13: '清扫车',
	14: '渣土车',
	15: '特殊环卫车',
	16: '特殊用途车',
	17: '货车',
	18: '邮政车',
	19: '自卸货车',
	20: '牵引车',
	21: '其它',
};

export const carData = {
	all: [
		{ label: '货车', color: '#FF8989', num: '' },
		{ label: '工程车', color: '#FAAF57', num: '' },
		{ label: '公交车', color: '#64BAFF', num: '' },
		{ label: '渣土车', color: '#BCA169', num: '' },
		{ label: '环卫车', color: '#81FFCF', num: '' },
		{ label: '其它用途', color: '#FEFF7E', num: '' },
		{ label: '其他客车', color: '#7EFAFF', num: '' },
	],
	freight: [
		{
			label: CarType.REFRIGERATED,
			color: getCarColor(CarType.REFRIGERATED),
			num: '',
		},
		{ label: CarType.TRACTOR, color: getCarColor(CarType.TRACTOR), num: '' },
		{ label: CarType.DUMP, color: getCarColor(CarType.DUMP), num: '' },
		{ label: CarType.FREIGHT, color: getCarColor(CarType.FREIGHT), num: '' },
		{ label: CarType.POSTAL, color: getCarColor(CarType.POSTAL), num: '' },
	],
	passenger: [
		{ label: CarType.BUS, color: getCarColor(CarType.BUS), num: '' },
		{ label: CarType.COACH, color: getCarColor(CarType.COACH), num: '' },
		{ label: CarType.TOURISM, color: getCarColor(CarType.TOURISM), num: '' },
		{ label: CarType.SCHOOL, color: getCarColor(CarType.SCHOOL), num: '' },
	],
	workingVehicle: [
		{
			label: CarType.CONSTRUCTION,
			color: getCarColor(CarType.CONSTRUCTION),
			num: '',
		},
		{ label: CarType.SLAG, color: getCarColor(CarType.SLAG), num: '' },
		{ label: CarType.CONCRETE, color: getCarColor(CarType.CONCRETE), num: '' },
	],
	other: [
		{ label: CarType.FUEL, color: getCarColor(CarType.FUEL), num: '' },
		{ label: CarType.MEDICAL, color: getCarColor(CarType.MEDICAL), num: '' },
		{ label: CarType.GARBAGE, color: getCarColor(CarType.GARBAGE), num: '' },
		{
			label: CarType.DANGEROUS,
			color: getCarColor(CarType.DANGEROUS),
			num: '',
		},
		{ label: CarType.CLEAN, color: getCarColor(CarType.CLEAN), num: '' },
		{ label: CarType.SPECIAL, color: getCarColor(CarType.SPECIAL), num: '' },
		{
			label: CarType.SPECIAL_USE,
			color: getCarColor(CarType.SPECIAL_USE),
			num: '',
		},
	],
};
