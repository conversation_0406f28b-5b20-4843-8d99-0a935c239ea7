import styled from 'styled-components';

export const LayerChooseStyle = styled.div`
	width: 600px;
	height: 40px;
	left: 0;
	right: 0;
	bottom: 20px;
	margin: auto;
	position: absolute;
	display: flex;
	font-size: 22px;
	font-family: PingFangSC-Regular, PingFang SC;
	font-weight: 400;
	color: #dbecff;
	z-index: 5;

	> div {
		flex: 1;
		background: rgba(112, 146, 172, 0.3);
		border-radius: 2px;
		border: 1px solid #89c0c4;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		padding: 0 10px;
		box-sizing: border-box;

		> span {
			cursor: pointer;
			white-space: nowrap;
		}

		> div {
			display: none;
			width: 100%;
			position: absolute;
			bottom: 40px;
			padding-bottom: 5px;

			> div {
				height: 40px;
				background: rgba(112, 146, 172, 0.3);
				border-radius: 2px;
				border: 1px solid #89c0c4;
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
			}

			> div.active {
				background: #569ab1;
				box-shadow: inset 0px 0px 12px 0px #94e1e8;
			}
		}
	}

	> div.active {
		background: #569ab1;
		box-shadow: inset 0px 0px 12px 0px #94e1e8;
	}

	> div:hover {
		> div {
			display: block;
		}
	}
`;
