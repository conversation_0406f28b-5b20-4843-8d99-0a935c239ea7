import styled, { keyframes } from 'styled-components';
import background from '@renderer/images/background';
import icon from '@renderer/images/icon';

const fadeInAnimation = keyframes`
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
`;

const fadeOutAnimation = keyframes`
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-100%);
  }
`;

const RegionalBannerStyled = styled.div`
	position: absolute;
	left: 50%;
	top: 150px;
	z-index: 100;
	width: 34%;
	/* height: 300px; */
	height: auto;
	transform: translateX(-50%);
	.bannerMenu {
		width: 15%;
		height: 60px;
		background: url(${background.bannerDropdown}) no-repeat;
		background-size: 100% 100%;
		margin: 0 auto;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		border-radius: 15px;
		font-size: 16px;
		font-weight: 600;
		cursor: pointer;
	}
	.top {
		width: 100%;
		height: 80px;
		background: url(${background.regionalBanner}) no-repeat;
		background-size: 100% 100%;
		display: flex;
		align-items: center;
		justify-content: space-evenly;
		padding: 0 50px 15px;
		.searchRegion {
			width: 148px;
			height: 40px;
			display: flex;
			align-items: center;
			background-image: url(${icon.searchRegionBg});
			background-repeat: no-repeat;
			background-size: 100% 100%;
			/* border-image: linear-gradient(180deg, rgba(0, 184, 255, 1), rgba(53, 142, 193, 0.16)) 1 1; */
			:where(.css-dev-only-do-not-override-mu9r37).ant-input-group .ant-input {
				border: 0 !important;
				background-color: transparent;
			}
			:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
				> .ant-input:first-child,
			:where(.css-dev-only-do-not-override-mu9r37).ant-input-group
				.ant-input-group-addon:first-child {
				border-start-end-radius: 0;
				border-end-end-radius: 0;
				border: 0;
				background-color: transparent;
			}
		}
		.violation-nav {
			width: auto;
			display: flex;
			align-items: center;
			justify-content: space-around;
			position: static;
			font-size: 18px;
			margin: 0;
			> div {
				min-width: 100px;
				width: auto;
				padding: 0 15px;
				height: 40px;
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				color: #fff;
				background-image: url(${icon.topNavFrame});
				background-repeat: no-repeat;
				background-size: 100% 100%;
				background-color: transparent;
				border: 0;
				box-shadow: none;
				&.active {
					background-image: url(${icon.topNavBg});
					background-repeat: no-repeat;
					background-size: 100% 100%;
				}
			}
		}
		.topNav {
			width: auto;
			display: flex;
			align-items: center;
			justify-content: space-around;
			/* padding: 0 10px; */
			/* margin: 0 10px; */
			.item {
				min-width: 100px;
				width: auto;
				padding: 0 15px;
				height: 40px;
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				color: #fff;
			}
		}
		.selectTime {
			:where(.css-dev-only-do-not-override-mu9r37).ant-btn-default {
				width: 120px;
				height: 40px;
				background-color: transparent;
				border: 0;
				background: url(${icon.selectRegionBg});
				background-repeat: no-repeat;
				background-size: 100% 100%;
				font-size: 18px;
				margin: 0 10px;
			}
			display: flex;
			align-items: center;
		}
	}
`;

export default RegionalBannerStyled;
