import { useState, useEffect, useContext, useRef } from 'react';
import { Slider } from 'antd';
import { XyzTransitionGroup } from '@animxyz/react';
import moment from 'moment';

import { MapContext } from '@renderer/context';
import icon from '@renderer/images/icon';

import { TimeBarContent } from './style';

let timer;

function LayerMenu(props) {
	const { showFlash } = props;
	//showFlash false
	const [currentTime, setCurrentTime] = useState(0); //选中的时间
	const { setTimeBarEndTime } = useContext(MapContext);
	const [isVisible, setIsVisible] = useState(false);
	const [buttonClicked, setButtonClicked] = useState(false);
	const lastSelectedTimeRef = useRef(0);
	const [isAutoScroll, setIsAutoScroll] = useState(true);

	const [sliderFirstLoad, setSliderFirstLoad] = useState(true);
	const [hasSliderBeenClicked, setHasSliderBeenClicked] = useState(false);

	const [mouseLeft, setMouseLeft] = useState(false);

	useEffect(() => {
		let time = moment().format('YYYY-MM-DD ');
		if (currentTime < 10) {
			time = time + '0' + currentTime + ':00:00';
		} else {
			time = time + currentTime + ':00:00';
		}
		setTimeBarEndTime(
			moment()
				.subtract(24 - currentTime, 'h')
				.format('YYYY-MM-DD HH:00:00'),
		);

		let timeout;
		if (currentTime > 22 && !buttonClicked) {
			timeout = setTimeout(() => {
				setIsVisible(false);
			}, 5000);
		}

		return () => {
			if (timeout) {
				clearTimeout(timeout);
			}
		};
	}, [currentTime, buttonClicked]);

	useEffect(() => {
		// if (isVisible) return
		let i = 0;
		if (timer) clearInterval(timer);

		if (isVisible && !buttonClicked) {
			// timer = setInterval(() => {
			//   setCurrentTime(i++)
			//   if (i > 23) {
			//     clearInterval(timer)
			//   }
			// }, 5000)
			//不从0开始轮播
			timer = setInterval(() => {
				setCurrentTime((prevTime) => {
					const newTime = prevTime + 1;
					if (newTime > 23) {
						clearInterval(timer);
					}
					return newTime;
				});
			}, 5000);
		}

		return () => {
			if (timer) clearInterval(timer);
		};
	}, [isVisible, buttonClicked]);

	useEffect(() => {
		setIsVisible(!showFlash);
		if (!showFlash) {
			setTimeout(() => {
				setIsVisible(true);
			}, 5000);
		}
	}, [showFlash]);

	useEffect(() => {
		return () => {
			if (timer) clearInterval(timer);
		};
	}, []);

	// 滑动条数据源
	const getTimeFormat = () => {
		let timeList = {};
		for (let i = 0; i <= 23; i++) {
			timeList[i] = moment()
				.subtract(24 - i, 'h')
				.format('HH时');
		}
		return timeList;
	};

	// 当主动点击 Slider 点位时，设置 isAutoScroll 为 false
	const onTimeChange = (value) => {
		if (timer) clearInterval(timer);
		setCurrentTime(value);
		lastSelectedTimeRef.current = value; // 保存当前选择的时间
		setIsAutoScroll(false);
		setHasSliderBeenClicked(true); // 用户主动点击了Slider的点位
	};

	const handleIsVisible = () => {
		setButtonClicked(true);

		//拿到上一次被点击的点位
		if (isVisible) {
			setIsVisible(false);
		} else {
			setIsVisible(true);
			if (currentTime >= 22) {
				setCurrentTime(lastSelectedTimeRef.current);
			}
			// 自动轮播时开启
			if (isAutoScroll && !isVisible) {
				setButtonClicked(false);
			}
		}

		if (timer) clearInterval(timer);

		setIsVisible(!isVisible);
	};

	const onMouseMove = () => {
		if (buttonClicked && isVisible) {
			if (timer) {
				clearTimeout(timer);
			}
			timer = setTimeout(() => {
				setIsVisible(true);
			}, 3000);
		}

		if (sliderFirstLoad && !buttonClicked && hasSliderBeenClicked) {
			// 在这里可以执行第一次加载时的操作
			console.log('Slider 第一次加载完成');
			if (isVisible) {
				console.log(isVisible, 'isvisible');
				if (timer) {
					clearTimeout(timer);
				}
				timer = setTimeout(() => {
					setIsVisible(true);
				}, 3000);
			}
			onSliderLoad(); // 标记为已加载
		}

		if (mouseLeft && !buttonClicked && isVisible && hasSliderBeenClicked) {
			// 在这里可以执行再次进入时的操作
			console.log('鼠标再次进入');
			if (isVisible) {
				console.log(isVisible, 'isvisible');
				if (timer) {
					clearTimeout(timer);
				}
				timer = setTimeout(() => {
					setIsVisible(true);
				}, 3000);
			}
			setMouseLeft(false); // 重置鼠标状态
		}
	};

	const onMouseLeave = () => {
		if (
			!sliderFirstLoad &&
			!buttonClicked &&
			hasSliderBeenClicked &&
			isVisible
		) {
			// 在这里可以执行第一次加载时的操作
			console.log('鼠标离开 第二次加载完成');
			if (isVisible) {
				console.log(isVisible, 'isvisible');
				if (timer) {
					clearTimeout(timer);
				}
				timer = setTimeout(() => {
					setIsVisible(false);
				}, 3000);
			}
			onSliderLoad(); // 标记为已加载
		}
		if (!buttonClicked && !isVisible) {
			if (timer) {
				clearTimeout(timer);
			}
			timer = setTimeout(() => {
				setIsVisible(false);
			}, 3000);
		}
		if (buttonClicked && isVisible) {
			if (timer) {
				clearTimeout(timer);
			}
			timer = setTimeout(() => {
				setIsVisible(false);
			}, 3000);
		}

		// 设置鼠标状态为离开
		setMouseLeft(true);
	};

	//当被点击时触发
	const onSliderAfterChange = () => {
		if (timer) {
			clearTimeout(timer);
		}
		timer = setTimeout(() => {
			setIsVisible(false);
		}, 5000);
	};

	// 当 Slider 组件第一次加载完成时，将状态变量设置为 false
	const onSliderLoad = () => {
		setSliderFirstLoad(false);
	};

	return (
		<TimeBarContent>
			<XyzTransitionGroup xyz="fade down-100%">
				{!showFlash && !isVisible && (
					<div className="tiebarButton" onClick={handleIsVisible}></div>
				)}
			</XyzTransitionGroup>
			<XyzTransitionGroup xyz="fade down-100%">
				{isVisible && (
					<div
						className="layerMenuContent"
						onMouseOver={() => onMouseMove()}
						onMouseLeave={() => onMouseLeave()}
					>
						<Slider
							marks={getTimeFormat()}
							min={0}
							max={23}
							tooltip={{
								open: true,
								formatter: (v) => (
									<div
										style={{
											background: `url(${icon.timebar})`,
											backgroundSize: '100% 100%',
											width: '20px',
											height: '20px',
										}}
									></div>
								),
							}}
							value={currentTime}
							onChange={onTimeChange}
							onAfterChange={onSliderAfterChange}
						/>
					</div>
				)}
			</XyzTransitionGroup>
		</TimeBarContent>
	);
}
export default LayerMenu;
