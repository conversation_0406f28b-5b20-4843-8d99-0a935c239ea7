import Request from '../request';
// 气象子系统
const baseURL = import.meta.env.RENDERER_VITE_API_WEATHER;

const request = new Request({ baseURL });

// 重构后获取气象六参图片地址
export const getMeteorAllPic = (config: Record<string, any> = {}) => {
	return request.get(`/weather/GetMeteorAllPic`, config);
};

// 根据气象站/区获取指定时间的气象数据
export const getMeteStationByCity = (config: Record<string, any> = {}) => {
	return request.getJSON(`/weather/getMeteStationByCity`, config);
};
