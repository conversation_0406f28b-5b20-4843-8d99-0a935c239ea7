import React, { useState, useEffect, useContext } from 'react';
import * as mars3d from 'mars3d';
import moment from 'moment';
import { isInPolygon } from '@renderer/utils';
import { COLORLIST } from '@renderer/utils/util';
import { MapContext, PageContext } from '@renderer/context';
import { getViolationJxhList } from '@renderer/api';
import VehicleIndustryNumber from '../components/VehicleIndustryNumber';
import icon from '@renderer/images/icon/point.png';
import { COUNTY_ID_NAME_MAP } from '@renderer/marsLayers/components/AreaStreetStyles';

let Cesium = mars3d.Cesium,
	graphicLayerViolationPoint,
	realTimeData = [];

const polygon = [
	[73.500226, 18.1608963],
	[135.0858829, 18.1608963],
	[135.0858829, 53.55791967],
	[73.500226, 53.55791967],
	[73.500226, 18.1608963],
];

const Violation = (props) => {
	const { map, visible, layerId } = props;
	const [pieData, setPieData] = useState([]);
	const { setCurrentInfo, setBickDate } = useContext(MapContext);
	const { setOpenTrajectory, openTrajectory } = useContext(PageContext);
	const [vinsData, setVinsData] = useState([]);
	const vinsHandleRawData = (data) => {
		const _VINList = data.filter((item) => {
			const { lon, lat } = item;
			return isInPolygon([lon, lat], polygon);
		});
		setVinsData(_VINList);
	};
	const createPoints = () => {
		if (!graphicLayerViolationPoint) return;
		graphicLayerViolationPoint.clear();
		graphicLayerViolationPoint.enabledEvent = false; // 关闭事件，大数据addGraphic时影响加载时间
		for (let j = 0; j < vinsData.length; ++j) {
			const obj = vinsData[j];
			const gcj2wgs = new mars3d.PointTrans.wgs2gcj([obj.lon, obj.lat]);
			const position = new mars3d.LngLatPoint(gcj2wgs[0], gcj2wgs[1]);
			const graphicPoint = new mars3d.graphic.BillboardEntity({
				position,
				style: {
					image: icon,
					scale: 1,
					horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
					verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
				},
				attr: obj,
			});
			graphicLayerViolationPoint.addGraphic(graphicPoint);
		}
		graphicLayerViolationPoint.enabledEvent = true; // 恢复事件
	};

	const getViolationJxhListData = () => {
		getViolationJxhList({
			start_time: moment().subtract(1, 'day').format('YYYY-MM-DD 00:00:00'),
			//start_time: moment().subtract(6, 'day').format('YYYY-MM-DD HH:mm:ss'),
			// end_time: moment().format('YYYY-MM-DD 23:59:59'),
			// start_time: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
			end_time: moment().format('YYYY-MM-DD HH:mm:ss'),
		})
			.then((res) => {
				if (res?.length) {
					realTimeData = res.map((item) => {
						return {
							...item,
							lon: Number(item.longitude),
							lat: Number(item.latitude),
							county_name: COUNTY_ID_NAME_MAP[item.county_id],
						};
					});
					const result = [];
					const countyCount = {};

					realTimeData.forEach((item) => {
						if (!countyCount[item.county_name]) {
							countyCount[item.county_name] = 0;
						}
						countyCount[item.county_name]++;
					});
					let idx = 0;
					for (const county in countyCount) {
						result.push({
							name: county,
							value: countyCount[county],
							color: COLORLIST[idx],
						});
						idx = idx + 1;
					}
					vinsHandleRawData(realTimeData);
					setPieData(result);
				}
			})
			.catch((error) => {
				console.log('error', error);
				vinsHandleRawData([]);
			});
	};

	useEffect(() => {
		createPoints();
	}, [vinsData]);

	useEffect(() => {
		if (!visible) return;
		getViolationJxhListData();
		graphicLayerViolationPoint = new mars3d.layer.GraphicLayer();
		map.addLayer(graphicLayerViolationPoint);
		graphicLayerViolationPoint.on(mars3d.EventType.dblClick, function (event) {
			console.log('监听layer， 双击了矢量对象', event);
			const cartesianPosition = event.graphic.position;
			const cartographic = Cesium.Cartographic.fromCartesian(cartesianPosition);
			setCurrentInfo({
				VIN: event.graphic.attr.vin,
				type: event.graphic.id.split('|')[1],
				lon: Cesium.Math.toDegrees(cartographic.longitude),
				lat: Cesium.Math.toDegrees(cartographic.latitude),
			});
			setOpenTrajectory(true);
			setBickDate(true);
		});
		return () => {
			graphicLayerViolationPoint.clearDrawing();
			graphicLayerViolationPoint.clear(true);
			graphicLayerViolationPoint.enabledEvent = false;
			if (graphicLayerViolationPoint)
				map.removeLayer(graphicLayerViolationPoint);
			realTimeData = [];
		};
	}, [visible]);

	return (
		<>
			{!openTrajectory && pieData.length > 0 ? (
				<VehicleIndustryNumber data={pieData} title={'今日违规车辆'} />
			) : null}
		</>
	);
};

export default Violation;
