import React from 'react';
import styled from 'styled-components';
import { formatPolluNameSub, pollutionLevelColorMap } from '@renderer/utils';
/**
 * 污染物列表
 */
const PollutantStyle = styled.div`
	.pollutant {
		display: flex;
		margin: 10px 0;
	}
	.pollutant-item {
		margin-right: 50px;
		width: 150px;
		display: flex;
		flex-direction: column;
		cursor: pointer;
		opacity: 1;

		.one {
			border-top-left-radius: 4px;
			border-top-right-radius: 4px;
			background: #88fc0b;
			height: 23px;
			color: #2d2b2b;
			font-size: 22px;
			text-align: center;
			height: 30px;
			line-height: 30px;
		}
		.two {
			border-bottom-left-radius: 4px;
			border-bottom-right-radius: 4px;
			text-align: center;
			color: #ffffff;
			background: #599eb5;
			font-size: 18px;
			height: 30px;
			line-height: 30px;
		}
	}
`;

const PollutantListCon = (props) => {
	const { data, changePollutantId, pollutantId } = props;
	const changeFontColorAndLabel = (id: number, value: number): any => {
		const { color, label } = pollutionLevelColorMap[id].find(
			(it) => it.min <= value && it.max >= value,
		) || { color: '#999' };
		return { color, label };
	};
	return (
		<PollutantStyle>
			<ul className="pollutant">
				{data.map((item, index) => {
					const { a, b } = formatPolluNameSub(item.pollutant_id);
					let { color, label } = changeFontColorAndLabel(
						item.pollutant_id,
						item.value,
					);
					return (
						<div
							key={'pollutant-' + index}
							className="pollutant-item"
							onClick={() => {
								changePollutantId(item.pollutant_id);
							}}
							style={{
								opacity: pollutantId == item.pollutant_id ? 1 : 0.6,
							}}
						>
							<span
								className="one"
								style={{
									background: color,
									color: label == '严重' || label == '重度' ? 'white' : 'black',
								}}
							>
								{Number(item.value) <= 0 ? '--' : item.value}
							</span>
							<span className="two">
								{a}
								<sub>{b}</sub>
							</span>
						</div>
					);
				})}
			</ul>
		</PollutantStyle>
	);
};
export default PollutantListCon;
