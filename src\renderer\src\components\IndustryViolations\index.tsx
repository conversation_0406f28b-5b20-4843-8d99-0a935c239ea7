import { useEffect, useState } from 'react';
import Echarts from '@renderer/components/echarts';
import { useAuth } from '@renderer/hooks/useAuth';
import moment from 'moment';
import Box from '../../baseUI/Box';
import TimeTypeRadio from '../../baseUI/TimeTypeRadio2';
import {
	getIndustyListGroupBySecondTypeAndDatetime,
	getIndustyListGroupByFourthType,
} from '@renderer/api';
import {
	getCarColorTwo,
	industryViolationsCarsColor,
} from '../../../../main/data';

import { Container } from './style';
import Color from 'cesium/Source/Core/Color';

const typeList = [
	{ id: '2', name: '行业' },
	{ id: '1', name: '类型' },
];

export default function BJVehicleCountMonitoring() {
	const { token } = useAuth();
	const [timeData, setTimeData] = useState({
		start_time: '',
		end_time: '',
		time_type: '',
	});
	const [option1, setOption1] = useState({});
	const [option2, setOption2] = useState({});
	const [type, setType] = useState('1');

	useEffect(() => {
		if (!timeData.start_time || !timeData.end_time) return;
		const json = {
			token,
			...timeData,
		};
		getIndustyListGroupBySecondTypeAndDatetimeInfo(json);
		getIndustyListGroupByFourthTypeInfo(json);
		console.log('timeData', timeData);
	}, [timeData]);

	// 行业类型
	const getIndustyListGroupByFourthTypeInfo = (json) => {
		getIndustyListGroupByFourthType(json).then((res) => {
			const result = res as any;
			const series: any = [];
			Array.isArray(result) &&
				result?.map((item) => {
					if (item.fourth_type !== '排放因子') {
						const color = getIndustyListGroupByFourthTypeColor(
							item.fourth_type,
						);
						series.push({
							name: item.fourth_type,
							stack: 'total',
							type: 'bar',
							label: {
								show: false,
							},
							emphasis: {
								focus: 'series',
							},
							data: item.group_details.count,
							// 自定义样式
							itemStyle: {
								normal: {
									color: function (params) {
										return {
											type: 'linear',
											x: 1,
											y: 0,
											x2: 0,
											y2: 0,
											colorStops: [
												{
													offset: 0,
													color: `rgba(${color},0.4)`, // 0% 处的颜色
												},
												{
													offset: 0.5,
													color: `rgba(0,0,0,0)`, // 50% 处的颜色
												},
												{
													offset: 1,
													color: `rgba(${color},0.4)`, // 100% 处的颜色
												},
											],
										};
									},
									borderWidth: 2,
									borderColor: `rgba(${color},0.9)`,
								},
							},
						});
					}
					// else {
					//   series.push({
					//     name: item.fourth_type,
					//     type: 'line',
					//     stack: 'Total',
					//     symbol: 'circle',
					//     symbolSize: 6,
					//     itemStyle: {
					//       color: '#FFFFFF'
					//     },
					//     data: item.group_details.count
					//   })
					// }
				});
			const option1 = {
				legend: {
					show: true,
					left: '15%',
					bottom: '0',
					align: 'right',
					textStyle: {
						color: '#E2F0FF',
						fontSize: '14',
					},
					data: [
						{ name: '中型', itemStyle: { color: 'rgb(255,204,133)' } },
						{ name: '大型', itemStyle: { color: 'rgb(93,190,244)' } },
						{ name: '小型', itemStyle: { color: 'rgb(133,169,255)' } },
						{ name: '轻型', itemStyle: { color: 'rgb(26, 87, 255)' } },
						{ name: '重型', itemStyle: { color: 'rgb(12, 251, 255)' } },
						// { name: '排放因子' }
					],
					itemWidth: 16, // 设置宽度
					itemHeight: 4, // 设置高度
				},
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						//type: "shadow",
						textStyle: {
							color: '#fff',
						},
					},
				},
				title: {
					text: '单位: 千克',
					left: 10, // 标题
					bottom: 0,
					textStyle: {
						color: '#D8DBDE',
						fontSize: 16,
					},
				},
				grid: {
					top: '1%',
					left: '4%',
					right: '4%',
					bottom: '13%',
					containLabel: true,
				},
				animation: true,
				animationDuration: 1000,
				animationDelay: 1500,
				xAxis: {
					type: 'value',
					axisLine: {
						lineStyle: {
							color: 'rgba(198, 199, 199, 0.32)',
						},
					},
					splitLine: {
						show: false,
					},
					axisTick: {
						show: false,
					},
					axisLabel: {
						show: true,
						interval: 'auto',
						textStyle: {
							color: '#E2F0FF',
							fontSize: 16,
						},
					},
				},
				yAxis: {
					type: 'category',
					data: result[0]?.group_details?.second_type,
					axisTick: {
						show: false,
					},
					axisLine: {
						lineStyle: {
							color: 'rgba(198, 199, 199, 0.32)',
						},
					},
					axisLabel: {
						color: '#E2F0FF',
						fontSize: '16',
					},
				},
				series,
			};
			setOption1(option1);
		});
	};

	// 车辆类型
	const getIndustyListGroupBySecondTypeAndDatetimeInfo = (json) => {
		getIndustyListGroupBySecondTypeAndDatetime(json).then((res) => {
			const result = res as any;
			const xData = result[0]?.group_details?.datetime.map((item) =>
				moment(item).format('YYYY-MM-DD HH:00:00'),
			);
			const series =
				Array.isArray(result) &&
				result?.map((item) => {
					// const color = getCarColorTwo(item.second_type)
					const color = industryViolationsCarsColor(item.second_type);
					const colorNum = color.slice(4).slice(0, color.slice(4).length - 1);
					return {
						type: 'bar',
						// smooth: true, // 是否曲线
						stack: '排名',
						name: item.second_type, // 图例对应类别
						symbol: 'none', // 不显示连接点
						barWidth: 16,
						color: industryViolationsCarsColor(item.second_type),
						data: item.group_details.count, // 纵坐标数据
						// areaStyle: {
						//   color: {
						//     type: 'linear',
						//     x: 0, //右
						//     y: 0, //下
						//     x2: 0, //左
						//     y2: 1, //上
						//     colorStops: [
						//       {
						//         offset: 0.1,
						//         color: `rgba(${colorNum}, 0.8)` // 0% 处的颜色
						//       },
						//       {
						//         offset: 1,
						//         color: `rgba(${colorNum}, 0.1)` // 100% 处的颜色
						//       }
						//     ]
						//   }
						// }
					};
				});
			const option2 = {
				grid: {
					top: '9%',
					left: '3%',
					right: '4%',
					bottom: '13%',
					height: '80%',
					containLabel: true,
				},
				legend: {
					show: true,
					left: '9%',
					bottom: '0',
					align: 'right',
					icon: 'circle',
					textStyle: {
						color: '#E8F4FF',
						fontSize: '12',
					},
				},
				tooltip: {
					trigger: 'axis',
				},
				title: {
					text: '单位: 辆',
					right: 30, // 标题水平左对齐
					top: -3,
					textStyle: {
						color: '#D8DBDE',
						fontSize: 16,
					},
				},
				animation: true,
				animationDuration: 1000,
				animationDelay: 1500,
				xAxis: {
					type: 'category',
					// boundaryGap: false,
					axisLine: {
						lineStyle: {
							color: 'rgba(198, 199, 199, 0.32)',
							fontSize: 12,
						},
					},
					axisLabel: {
						color: '#E8F4FF',
						fontSize: 12,
						formatter: (value, index) => {
							const time =
								timeData.time_type === '1'
									? moment(value).format('HH:00')
									: moment(value).format('MM-DD');
							return time;
						},
					},
					axisTick: {
						show: false,
					},
					data: xData,
				},
				yAxis: {
					type: 'value',
					name: '58544',
					axisLine: {
						lineStyle: {
							color: 'rgba(198, 199, 199, 0.32)',
							fontSize: 12,
						},
					},
					splitLine: {
						show: true,
						lineStyle: {
							color: 'rgba(230, 247, 255, 0.20)',
							type: 'dashed',
						},
					},
					axisTick: {
						show: false,
					},
					axisLabel: {
						fontSize: 16,
						color: '#E8F4FF',
					},
				},
				series: series,
			};
			setOption2(option2);
		});
	};

	// 行业类型颜色
	const getIndustyListGroupByFourthTypeColor = (type) => {
		let color;
		switch (type) {
			case '大型':
				color = '93,190,244';
				break;
			case '中型':
				color = '255,204,133';
				break;
			case '小型':
				color = '133,169,255';
				break;
			case '轻型':
				color = '26,87,255';
				break;
			case '重型':
				color = '12,251,255';
				break;
		}
		return color;
	};

	return (
		<Box
			title="行业违规"
			titlewidth="95%"
			height="100%"
			subTitle={
				<TimeTypeRadio
					defaultValue="1"
					timeType={(e) => setTimeData(e)}
					industryViolationsType={type}
				/>
			}
		>
			<Container>
				<div className="echarts-line">
					<div className="echarts-title">
						{typeList.map((item) => (
							<span
								key={item.id}
								className={`${
									type == item.id ? 'title-select' : 'title-un-select'
								}`}
								onClick={() => setType(item.id)}
							>
								{item.name}
							</span>
						))}
					</div>
					<div className={`${type === '1' ? 'echarts1' : 'echarts2'}`}>
						<Echarts
							option={type === '1' ? option1 : option2}
							style={{ height: 220, width: 700 }}
							notMerge={true}
						></Echarts>
					</div>
				</div>
				{/* <div className="echarts1">
          <Echarts option={option1} />
        </div>
        <div className="echarts2">
          <Echarts option={option2} />
        </div> */}
			</Container>
		</Box>
	);
}
