import { useEffect, useState, useContext } from 'react';
import { RightSideStyled, LayoutContainer } from './styles';
import Echarts from '@renderer/components/echarts';
// import { Space, Button, Dropdown } from 'antd'
// import type { MenuProps } from 'antd'
// import { DownOutlined } from '@ant-design/icons'
import {
	getEnterpriseUsersOnlineSituation,
	getNetworkUserIntervalStatistics,
	getUserViolations,
	getUserViolationsRanking,
	getUserViolationsAnalyse,
} from '@renderer/api';
import { getAffiliationInfoByID } from '@renderer/api';
import { PageContext } from '@renderer/context';
import {
	COUNTY_ID_NAME_MAP,
	unitList,
} from '@renderer/marsLayers/components/AreaStreetStyles';
import industryClassification from '../components/IndustryClassification';
import {
	AreaRightSideTop,
	AreaRightSideCenter,
	AreaRightSideBottomViolationsAnalyse,
	AreaRightSideTopUserViolations,
} from '../components/EnterpriseAnalysis';
import ScrollListWidthTitle from '@renderer/components/SlidingLayer/DailyActivityLevel/components/ScrollListWidthTitle';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const optionTable = {
	width: '100%',
	height: '350px',
	fontSize: 18,
	thclassname: 'th_row',
	tablebgcolor: 'rgba(9,30,59,0)',
	trheight: '40px',
	thheight: '40px',
	customwidth: true,
	rowbgcolor: [
		'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
	],
	// thbgcolor: '#081F38',
};

const columns = [
	{
		title: '排行',
		dataIndex: 'RANK',
	},
	{
		title: '用户名称',
		dataIndex: 'NAME',
	},
	{
		title: '所属区域',
		dataIndex: 'district',
	},
	{
		title: '违规次数',
		dataIndex: 'nums_vio',
	},
	{
		title: '车辆数量',
		dataIndex: 'nums_all',
	},
];

const RightSide = (props: Props) => {
	const { currentSelectedId, streetShipData } = props;
	const {
		selectRegionDate,
		regionName,
		streetName,
		currentIndustry,
		currentIndustryList,
		currentIdList,
	} = useContext(PageContext);
	//图表
	const [option1, setOption1] = useState({});
	const [option2, setOption2] = useState({});
	const [option3, setOption3] = useState({});
	const [data, setData] = useState([]);
	// const [currentIndustryTop, setCurrentIndustryTop] = useState('全部')
	// const [currentIndustry, setCurrentIndustry] = useState('全部')
	const [title1, setTitle1] = useState(null);

	// const items: MenuProps['items'] = Object.keys(industryClassification()).map((item, idx) => {
	//   return {
	//     label: item,
	//     key: `${idx}`
	//   }
	// })

	// const handleMenuClick: MenuProps['onClick'] = (e) => {
	//   setCurrentIndustry(Object.keys(industryClassification())[e.key])
	// }

	// const handleMenuClickTop: MenuProps['onClick'] = (e) => {
	//   setCurrentIndustryTop(Object.keys(industryClassification())[e.key])
	// }

	// const menuProps = {
	//   items,
	//   onClick: handleMenuClick
	// }

	// const menuPropsTop = {
	//   items,
	//   onClick: handleMenuClickTop
	// }

	// const getDropDownMenu = (params) => {
	//   const { type } = params
	//   return (
	//     <Dropdown menu={menuProps} className={`dropDown${type}`} key={type}>
	//       <Button>
	//         <Space>
	//           {currentIndustry}
	//           <DownOutlined />
	//         </Space>
	//       </Button>
	//     </Dropdown>
	//   )
	// }

	// 区域 企业联网情况
	const getUserEnterpriseNetworking = (params) => {
		// getNetworkUserIntervalStatistics(params)
		getUserViolations(params)
			.then((res) => {
				if (Object.keys(res)?.length > 0) {
					const adm = Object.keys(res);
					const nums = Object.values(res)?.map((item) => item?.len);
					const onlineData = Object.values(res)?.map((item) => item?.sum);
					setOption1(AreaRightSideTopUserViolations({ adm, nums, onlineData }));
				} else {
					setOption1(AreaRightSideTopUserViolations({}));
				}
			})
			.catch((err) => {
				setOption1(AreaRightSideTopUserViolations({}));
			});
	};

	useEffect(() => {
		const { start_time, end_time } = selectRegionDate.customDate;
		const params: any = {
			topic: currentIdList.toString(),
			start_time,
			end_time,
		};
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		if (currentIndustry && currentIndustry !== '全部') {
			// const idx = Object.keys(industryClassification()).findIndex((i) => i === currentIndustry)
			params.industry_name = currentIndustryList.join(',');
		}
		getUserEnterpriseNetworking(params);
	}, [regionName, currentIndustry, currentSelectedId, currentIdList]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		const params: any = {
			top: 5,
			start_time,
			end_time,
			// time_type
			topic: currentIdList.toString(),
		};
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		if (currentIndustry && currentIndustry !== '全部') {
			// const idx = Object.keys(industryClassification()).findIndex((i) => i === currentIndustry)
			params.industry_name = currentIndustryList.join(',');
		}
		getUserViolationsRanking(params)
			// getEnterpriseUsersOnlineSituation(params)
			.then((res) => {
				const ids = res.map((item) => item.id);
				getAffiliationInfoByID({
					affiliation_ids: ids.toString(),
				})
					.then((o) => {
						if (o.length) {
							const _data = o.map((item, i) => {
								return {
									RANK: i + 1,
									NAME: item.AFFILIATION,
									district: res[i].district,
									nums_vio: res[i].nums_vio,
									nums_all: res[i].nums_all,
								};
							});
							setData(_data);
						}
					})
					.catch((error) => {
						console.log('error', error);
					});
				// const affiliations = res?.map((item) => item.AFFILIATION)
				// const onlineData = res?.map((item) => item.ONLINE)
				// const sumData = res?.map((item) => item.SUM)

				// const offlineData = sumData.map((sum, index) => sum - onlineData[index])

				// const xdata = affiliations
				// const result = [
				//   { name: '出行车辆', data: onlineData },
				//   { name: '未出行车辆', data: offlineData }
				// ]
				// const dataArr = { xdata, result }
				// const _data = res.map((item, i) => {
				//   return {
				//     RANK: i + 1,
				//     NAME: item.AFFILIATION,
				//     AREA: '朝阳区',
				//     VIOLATION_NUMBER: item.ONLINE,
				//     VEHICLE_NUMBER: item.SUM
				//   }
				// })
				// setData(_data)
				// setOption2(AreaRightSideCenter(dataArr))
				// // setOption3(AreaRightSideBottom({ onlineData, sumData, affiliations }))
				// setOption3(
				//   AreaRightSideBottom({
				//     onlineData,
				//     sumData,
				//     affiliations: [
				//       'Top1',
				//       'Top2',
				//       'Top3',
				//       'Top4',
				//       'Top5',
				//       'Top6',
				//       'Top7',
				//       'Top8',
				//       'Top9',
				//       'Top10'
				//     ]
				//   })
				// )
			})
			.catch((err) => {
				setData([]);
				// setOption2(AreaRightSideCenter({}))
				// setOption3(AreaRightSideBottom({}))
			});

		getUserViolationsAnalyse(params)
			.then((res) => {
				console.log('getUserViolationsAnalyse ???????', params, res);
				const ids = res.map((item) => item.id);
				getAffiliationInfoByID({
					affiliation_ids: ids.toString(),
				})
					.then((o) => {
						if (o.length) {
							const affiliations = o.map((item, i) => item.AFFILIATION);
							const vioData = res?.map((item) => item.nums_vio);
							const allData = res?.map((item) => item.nums_all);
							const rateData = res?.map((item) => item.vio_rate * 100);
							setOption3(
								AreaRightSideBottomViolationsAnalyse({
									vioData,
									allData,
									rateData,
									affiliations,
								}),
							);
						}
					})
					.catch((error) => {
						console.log('error', error);
					});
			})
			.catch((err) => {
				// setOption2(AreaRightSideCenter({}))
				setOption3(AreaRightSideBottomViolationsAnalyse({}));
			});
	}, [
		selectRegionDate,
		currentIndustry,
		currentSelectedId,
		regionName,
		currentIdList,
	]);

	return (
		<RightSideStyled>
			<div className="Streetcontent">
				<div className="slidingLayer">
					<span className="slidtext">用户违规情况</span>
					{/* <Dropdown menu={menuPropsTop} className={`dropDown1`} key={1}>
            <Button>
              <Space>
                {currentIndustryTop}
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown> */}
				</div>
				<LayoutContainer style={{ height: '20%' }}>
					<div className="echarts-line">
						<Echarts
							option={option1}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">用户违规排行</span>
					{/* {getDropDownMenu({ type: 2 })} */}
				</div>
				<LayoutContainer style={{ height: '35%', right: '-5%' }}>
					<div
						className="echarts-line"
						style={{ width: '95%', overflow: 'hidden' }}
					>
						{/* <Echarts option={option2} style={{ height: '100%', flex: 1 }} notMerge={true}></Echarts> */}
						<ScrollListWidthTitle
							columns={columns}
							data={data}
							{...optionTable}
						/>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">用户违规分析</span>
				</div>
				<LayoutContainer style={{ height: '25%' }}>
					<div className="echarts-line">
						<Echarts
							option={option3}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
			</div>
		</RightSideStyled>
	);
};

export default RightSide;
