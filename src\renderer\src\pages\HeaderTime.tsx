import React, { useState, useEffect } from 'react';
import moment from 'moment';
import styled from 'styled-components';

import { getWeek } from '@renderer/utils';

const HeaderTime = () => {
	const [year, setYear] = useState<string>('');
	const [month, setMonth] = useState<string>('');
	const [day, setDay] = useState<string>('');
	const [hour, setHour] = useState<string>('');
	const [minute, setMinute] = useState<string>('');
	const [second, setSecond] = useState<string>('');
	const [week, setWeek] = useState<string>('');

	useEffect(() => {
		const timer = setInterval(() => {
			setDatetime();
		}, 1000);

		return () => {
			clearInterval(timer);
		};
	}, []);

	const setDatetime = () => {
		const datetime = moment();
		setYear(datetime.format('YYYY'));
		setMonth(datetime.format('MM'));
		setDay(datetime.format('DD'));
		setHour(datetime.format('HH'));
		setMinute(datetime.format('mm'));
		setSecond(datetime.format('ss'));
		setWeek(getWeek(datetime.day()));
	};

	return (
		<TimerContainer>
			<div className="date">
				<div>{year}</div>
				<div>/{month}</div>
				<div>/{day}</div>
			</div>
			<div className="time">
				<div>{hour.split('')[0]}</div>
				<div>{hour.split('')[1]}</div>:<div>{minute.split('')[0]}</div>
				<div>{minute.split('')[1]}</div>:<div>{second.split('')[0]}</div>
				<div>{second.split('')[1]}</div>
			</div>
			<div className="week">{week}</div>
		</TimerContainer>
	);
};

const TimerContainer = styled.div`
	height: 100%;
	width: 100%;
	display: flex;
	line-height: 40px;
	font-family: 'TimesNewRoman', monospace;
	.date {
		font-family: 'electronicFont';
		font-size: 40px;
		color: #2eeef6;
		display: flex;
	}
	.time {
		font-family: 'electronicFont';
		font-size: 60px;
		color: #2eeef6;
		padding-left: 20px;
		font-weight: 500;
		display: flex;
		div {
			width: 30px;
			text-align: center;
		}
	}
	.week {
		font-size: 22px;
		color: #2eeef6;
		margin-right: 10px;
		margin-left: 30px;
	}
`;

export default HeaderTime;
