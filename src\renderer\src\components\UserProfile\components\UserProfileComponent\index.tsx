/*
 * @Descripttion:
 * @version:
 * @Author: Gqq
 * @Date: 2024-07-23 09:32:41
 */
import React from 'react';
import { useFont } from '@renderer/hooks';
import LayoutContainer from './style';

interface Props {
	data: Array<any>;
	// userCar: any
}

export const UserProfileComponent: React.FC<Props> = (props) => {
	const { data } = props;
	return (
		<>
			<LayoutContainer>
				<div className={'left-container' + ' ' + useFont()}>
					<div className="describe">
						<dl className="no2">
							<dt>用户数量</dt>
							<dd>
								{data?.user_num || '--'}
								<span>家</span>
							</dd>
						</dl>
						<div className="describe-bottom">
							<dl className="no1">
								<dt>用车大户</dt>
								<dd>{data?.big_user || '--'}家</dd>
							</dl>
							<dl className="no3">
								<dt>车辆占比</dt>
								<dd>{data?.vehicle_rate} %</dd>
							</dl>
						</div>
					</div>
				</div>
			</LayoutContainer>
		</>
	);
};
