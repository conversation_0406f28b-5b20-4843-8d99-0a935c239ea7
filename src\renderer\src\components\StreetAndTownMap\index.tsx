import { useEffect, useRef, useState } from 'react';

import * as echarts from 'echarts';

import { MapEcharts } from './style';
import data from './townJson';

type Props = {
	id?: string;
	regions?:
		| null
		| {
				name: string;
				itemStyle: {
					areaColor: string;
				};
		  }[];
};
const RegistryAreaMap = ({ id, regions = null }: Props) => {
	const [jsonData, setJsonData] = useState<any>(data);
	const echartRef = useRef<any>(null);
	const myChart = useRef<any>(null);

	let option = {
		geo: {
			show: true,
			map: 'beijing',
			selectedMode: 'single',
			zoom: 1.2,
			roam: 'scale',
			label: {
				color: '#fff',
				show: false,
			},
			scaleLimit: {
				//缩放级别
				min: 0.5,
				max: 4,
			},
			itemStyle: {
				normal: {
					areaColor: 'rgb(59, 167, 2)',
					borderColor: '#000000',
					borderWidth: 1,
					shadowColor: 'rgba(59, 167, 2,0.3)',
					shadowBlur: 20,
				},
			},
			emphasis: {
				label: {
					show: true,
					color: '#fff',
				},
				itemStyle: {
					areaColor: '#005e17',
					borderColor: '#51eb8c',
					borderWidth: 3,
				},
			},
			select: {
				disabled: true,
				smooth: true,
				label: {
					show: true,
					color: '#fff',
				},
				itemStyle: {
					areaColor: '#005e17',
					borderColor: '#51eb8c',
					borderWidth: 3,
				},
			},

			regions: [],
		},
		grid: {
			left: '0%',
			right: '0%',
			top: '0%',
			bottom: '0%',
		},
	};

	useEffect(() => {
		myChart.current = echarts.init(echartRef.current);
		myChart.current.showLoading();
		echarts.registerMap('beijing', jsonData);
		myChart.current.hideLoading();
		myChart.current.setOption(option, true);
	}, []);
	useEffect(() => {
		if (!regions) return;
		let option = {
			geo: {
				show: true,
				map: 'beijing',
				selectedMode: 'single',
				zoom: 1.2,
				roam: 'scale',
				label: {
					color: '#fff',
					show: false,
				},
				scaleLimit: {
					//缩放级别
					min: 0.5,
					max: 4,
				},
				itemStyle: {
					normal: {
						areaColor: 'rgb(59, 167, 2)',
						borderColor: '#000000',
						borderWidth: 1,
						shadowColor: 'rgba(59, 167, 2,0.3)',
						shadowBlur: 20,
					},
				},
				emphasis: {
					label: {
						show: true,
						color: '#fff',
					},
					itemStyle: {
						areaColor: '#005e17',
						borderColor: '#51eb8c',
						borderWidth: 3,
					},
				},
				select: {
					disabled: true,
					smooth: true,
					label: {
						show: true,
						color: '#fff',
					},
					itemStyle: {
						areaColor: '#005e17',
						borderColor: '#51eb8c',
						borderWidth: 3,
					},
				},

				regions: regions,
			},
			grid: {
				left: '0%',
				right: '0%',
				top: '0%',
				bottom: '0%',
			},
		};
		echarts.registerMap('beijing', jsonData);
		myChart.current.setOption(option, false, false);
	}, [regions]);

	return (
		<MapEcharts>
			<div id={id} className="streetEchart" ref={echartRef}></div>
		</MapEcharts>
	);
};
export default RegistryAreaMap;
