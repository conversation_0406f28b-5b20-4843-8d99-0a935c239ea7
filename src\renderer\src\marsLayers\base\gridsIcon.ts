import * as mars3d from 'mars3d';
import BaseMarsLayers from './index';
import pointData from '@renderer/marsLayers/handleData';
const { Cesium } = mars3d;
export default class GridsIcon extends BaseMarsLayers {
	props: any;

	constructor(props) {
		super(props, 'GraphicLayer');
		this.props = props;
	}

	renders([data, setPopupData]) {
		if (!data || data.length === 0) {
			this.remove();
			return;
		}
		data.forEach((item) => {
			const options = pointData[this.props.name];
			const { icon, text, scale, scaleByDistance, labels } =
				options.getStyle(item);
			const graphic = new mars3d.graphic.BillboardPrimitive({
				position: Cesium.Cartesian3.fromDegrees(
					item.wgs84_lng,
					item.wgs84_lat,
					0,
				),
				style: {
					image: icon,
					scale: scale || 1,
					horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
					verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
					scaleByDistance: scaleByDistance || false,
				},
				attr: { ...item, LayerId: this.props.name },
			});
			this.layer.addGraphic(graphic);
		});
		// this.layer.bindPopup(
		//   () => `<div id="${this.props.name}Popup"  style="width:100%;height:100%"></div>`,
		//   {
		//     offsetX: 0,
		//     offsetY: -10,
		//     className: 'pop',
		//     scaleByDistance: false,
		//     template: `<div><div class='closeButton'>x</div>{content}</div>`
		//   }
		// )
		// let openCount = 0 //切换弹窗时，弹窗内容不更新，增加此变量判断是否切换弹窗

		// this.layer.on(mars3d.EventType.popupOpen, function (event) {
		//   console.log('popupOpen------', event.attr, openCount++)

		//   setPopupData({ ...event.attr, openCount: openCount++ })
		// })

		// this.layer.on(mars3d.EventType.popupClose, function (event) {
		//   setPopupData(false)
		// })
	}
}
