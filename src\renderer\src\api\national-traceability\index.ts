import Request from '../request';

// 20大数据接入服务
const baseURL = import.meta.env.RENDERER_VITE_API_NATIONAL_TRACEABILITY;

const request = new Request({ baseURL });

// 违规车辆列表 (abandon)
export const getClueList = (config: Record<string, any> = {}): any => {
	return request.get(`/v1/clue-list`, config);
};

// 违规车辆详情
export const getClueSpotDetails = (config: Record<string, any> = {}): any => {
	return request.get(`/v1/clue-spot-details`, config);
};
