import { useEffect, useRef, useState, useContext } from 'react';
import * as echarts from 'echarts';
import { Radio } from 'antd';
import Box from '@renderer/baseUI/Box';
import CustomPopUps from './components/CustomTimeRadio';
import TimeTypeRadio from './components/TimeTypeRadio';
import { getBarChart, getLineChart } from './components/StaticDataEcharts';
import {
	getStaticCarNums,
	getStaticFirms,
	getStaticIndustrySituation,
} from '@renderer/api';
import { COUNTY_ID_NAME_MAP } from '@renderer/marsLayers/components/AreaStreetStyles';
import { PageContext } from '@renderer/context';
import { StaticDataDisplayStyled, ReferenceStyled } from './styles';

type Align = 'firms' | 'carNums' | 'industrialDistribution';

const StaticDataDisplay = () => {
	const { regionName } = useContext(PageContext);
	const staticDataDisplayRef = useRef(null);
	const myChartRef = useRef<any>(null);
	const [alignValue, setAlignValue] = useState<Align>('firms');
	const [selectedButton, setSelectedButton] = useState('default');
	const [showPop, setShowPop] = useState(false);
	const [customDate, setCustomDate] = useState({});

	const defaultData = {
		total: [],
		date: [],
		new: [],
		tab: ['', ''],
	};

	const typeOptions = [
		{
			label: '企业数量',
			value: 'firms',
			unit: '家',
		},
		{
			label: '车辆数量',
			value: 'carNums',
			unit: '辆',
		},
		{
			label: '行业情况',
			value: 'industrySituation',
			unit: '',
		},
	];

	const handleClickReference = (value) => {
		setSelectedButton(value);
		if (value === 'custom') {
			setShowPop(true);
		} else {
			setShowPop(false);
		}
	};

	const getData = (type, select) => {
		let params = {
			start_time: new Date().getFullYear() - 5,
			end_time: new Date().getFullYear() - 1,
			time_type: 1,
		};

		if (select !== 'default' && Object.keys(customDate)?.length) {
			params = {
				start_time: customDate.startTime,
				end_time: customDate.endTime,
				time_type: customDate.timeType === 'year' ? 1 : 2,
			};
		}
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		switch (type) {
			case 'firms':
				getStaticFirms(params)
					.then((res) => {
						if (res?.total?.length > 0) {
							myChartRef.current.setOption(getBarChart(res, '家'), true);
						} else {
							myChartRef.current.setOption(
								getBarChart(defaultData, '家'),
								true,
							);
						}
					})
					.catch((error) => {
						myChartRef.current.setOption(getBarChart(defaultData, '家'), true);
					});
				break;
			case 'carNums':
				getStaticCarNums(params)
					.then((res) => {
						if (res?.total?.length > 0) {
							myChartRef.current.setOption(getBarChart(res, '辆'), true);
						} else {
							myChartRef.current.setOption(
								getBarChart(defaultData, '辆'),
								true,
							);
						}
					})
					.catch((error) => {
						myChartRef.current.setOption(getBarChart(defaultData, '辆'), true);
					});
				break;
			case 'industrySituation':
				getStaticIndustrySituation(params)
					.then((res) => {
						if (Object.keys(res)?.length > 0) {
							const xData = Object.keys(res);
							const resultArray: any = [];
							for (const yearData of Object.values(res)) {
								for (const vehicle of yearData) {
									const { nums, NAME } = vehicle;
									const index = resultArray.findIndex(
										(item) => item.name === NAME,
									);
									if (index === -1) {
										resultArray.push({ name: NAME, value: [nums] });
									} else {
										resultArray[index].value.push(nums);
									}
								}
							}
							myChartRef.current.setOption(
								getLineChart({ xData, resultArray }, '辆'),
								true,
							);
						} else {
							myChartRef.current.setOption(getLineChart({}, '辆'), true);
						}
					})
					.catch((error) => {
						myChartRef.current.setOption(getLineChart({}, '辆'), true);
					});
				break;
			default:
				break;
		}
	};

	const onTypeChange = (type) => {
		getData(type);
		setAlignValue(type);
	};

	useEffect(() => {
		myChartRef.current = echarts.init(staticDataDisplayRef.current);
		return () => {
			if (myChartRef.current) {
				myChartRef.current.dispose();
			}
		};
	}, []);

	useEffect(() => {
		if (!Object.keys(customDate)?.length && selectedButton === 'custom') return;
		getData(alignValue, selectedButton);
	}, [customDate, selectedButton, regionName]);

	return (
		<Box
			title={'静态数据分析'}
			titlewidth="95%"
			height="100%"
			subTitle={
				<div className="customDate">
					<ReferenceStyled>
						<div className="selectAssembly">
							<Radio.Button
								value="default"
								onClick={() => handleClickReference('default')}
								style={{
									color: selectedButton === 'default' ? '#3ADAFF' : '#e2e5e5',
									marginRight: '10px',
								}}
							>
								默认
							</Radio.Button>
							<Radio.Button
								value="custom"
								onClick={() => handleClickReference('custom')}
								style={{
									color: selectedButton === 'custom' ? '#3ADAFF' : '#e2e5e5',
								}}
							>
								自定义
							</Radio.Button>
						</div>
						<TimeTypeRadio
							typeOptions={typeOptions}
							defaultValue={'firms'}
							onTypeChange={onTypeChange}
						/>
						<CustomPopUps
							top={'28px'}
							right={'250px'}
							showPop={showPop}
							setShowPop={setShowPop}
							setTimeType={null}
							setCustomDate={setCustomDate}
						/>
					</ReferenceStyled>
				</div>
			}
		>
			<StaticDataDisplayStyled>
				<div
					ref={staticDataDisplayRef}
					style={{
						width: '100%',
						height: '100%',
					}}
				/>
			</StaticDataDisplayStyled>
		</Box>
	);
};

export default StaticDataDisplay;
