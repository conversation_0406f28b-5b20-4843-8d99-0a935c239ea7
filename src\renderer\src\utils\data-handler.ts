import {
	Cartesian2,
	Math,
	Cartesian3,
	Transforms,
	Ellipsoid,
	Matrix4,
} from 'mars3d-cesium';

export function getMapExtent(viewer) {
	const extend = viewer.camera.computeViewRectangle();
	const params = {};
	if (typeof extend === 'undefined') {
		const coordToLonlat = (viewer, x, y) => {
			const { camera, scene } = viewer;
			const d2 = new Cartesian2(x, y);
			const ellipsoid = scene.globe.ellipsoid;
			//2D转3D世界坐标
			const d3 = camera.pickEllipsoid(d2, ellipsoid);
			//3D世界坐标转弧度
			const upperLeftCartographic =
				scene.globe.ellipsoid.cartesianToCartographic(d3);
			//弧度转经纬度
			const lon = Math.toDegrees(upperLeftCartographic.longitude);
			const lat = Math.toDegrees(upperLeftCartographic.latitude);
			return { lon, lat };
		};
		const canvas = viewer.scene.canvas;
		const upperLeftLonLat = coordToLonlat(viewer, 0, 0);
		const lowerRightLonLat = coordToLonlat(
			viewer,
			canvas.clientWidth,
			canvas.clientHeight,
		);
		params['xmin'] = upperLeftLonLat.lon;
		params['xmax'] = lowerRightLonLat.lon;
		params['ymin'] = upperLeftLonLat.lat;
		params['ymax'] = lowerRightLonLat.lat;
	} else {
		//三维视图
		params['xmax'] = Math.toDegrees(extend.east);
		params['ymax'] = Math.toDegrees(extend.north);
		params['xmin'] = Math.toDegrees(extend.west);
		params['ymin'] = Math.toDegrees(extend.south);
	}
	return params;
}

export function getModelMatrix(pointA, pointB) {
	const vector2 = Cartesian3.subtract(pointB, pointA, new Cartesian3());
	const normal = Cartesian3.normalize(vector2, new Cartesian3());
	const rotationMatrix3 = Transforms.rotationMatrixFromPositionVelocity(
		pointA,
		normal,
		Ellipsoid.WGS84,
	);
	const modelMatrix4 = Matrix4.fromRotationTranslation(rotationMatrix3, pointA);
	return modelMatrix4;
}

export const handleExtents = (extent) => {
	const { xmin, ymin, xmax, ymax } = extent;
	// const polygon = [
	//   [ymin, xmin],
	//   [ymin, xmax],
	//   [ymax, xmax],
	//   [ymax, xmin]
	// ]
	const polygon = [
		[xmin, ymin],
		[xmax, ymin],
		[xmax, ymax],
		[xmin, ymax],
		[xmin, ymin],
	];
	return polygon;
};

export const handleRawData = (vins, VINDataList) => {
	const lists = VINDataList.map((item, index) => {
		return {
			...item,
			order: item.id,
		};
	}).filter((o) => vins.has(`${o.VIN}|${o.type}`));
	return lists;
};

export const getCarTypeModel = (type) => {
	const modelUrl = 'file:///public/model/';
	if (type === '渣土车') {
		return `${modelUrl}zhatuche2.glb`;
	} else if (type === '公交车') {
		return `${modelUrl}ceshi.glb`;
	}
	return `${modelUrl}huoche2.glb`;
};
