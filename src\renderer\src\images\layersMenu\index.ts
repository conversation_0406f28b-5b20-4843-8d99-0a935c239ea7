/*
 * @Descripttion:
 * @version:
 * @Author: Gqq
 * @Date: 2024-07-23 09:32:41
 */
import trafficVolume from './trafficVolume.png';
import trafficVolume_active from './trafficVolume_active.png';
import vehicleEmissions from './vehicleEmissions.png';
import vehicleEmissions_active from './vehicleEmissions_active.png';
import vehicleOnline from './vehicleOnline.png';
import vehicleOnline_active from './vehicleOnline_active.png';
import violatingVehicles from './violatingVehicles.png';
import violatingVehicles_active from './violatingVehicles_active.png';
import area from './area.png';
import area_active from './area_active.png';
import userEnterprise from './enterprise.png';
import userEnterprise_active from './enterprise_active.png';
import child from './child.png';
import child_active from './child_active.png';
import dashBoard from './dashBoard.png';
import threelayers from './threelayers.png';
import service from './service.png';
import service_active from './service_active.png';
import level from './level.png';
import level_active from './level_active.png';
import industry from './industry.png';
import industry_active from './industry_active.png';
import short from './short.png';
import short_active from './short_active.png';
import radio from './radio.png';
import radiocenter from './radiocenter.png';
import air from './air.png';
import air_active from './air_active.png';
import road from './road.png';
import road_active from './road_active.png';
import site_type from './site_type.png';
import site_type_active from './site_type_active.png';
import streetTownship from './streetTownship.png';
import streetTownship_active from './streetTownship_active.png';
import ViolationAnalysis from './ViolationAnalysis.png';
import ViolationAnalysis_active from './ViolationAnalysis_active.png';

export default {
	trafficVolume,
	trafficVolume_active,
	vehicleEmissions,
	vehicleEmissions_active,
	vehicleStatus: vehicleOnline,
	vehicleStatus_active: vehicleOnline_active,
	violatingVehicles,
	violatingVehicles_active,
	area,
	area_active,
	child,
	child_active,
	dashBoard,
	threelayers,
	service,
	service_active,
	level,
	level_active,
	industry,
	industry_active,
	short,
	short_active,
	radio,
	radiocenter,
	air,
	air_active,
	road,
	road_active,
	site_type,
	site_type_active,
	streetTownship,
	streetTownship_active,
	ViolationAnalysis,
	ViolationAnalysis_active,
	userEnterprise,
	userEnterprise_active,
};
