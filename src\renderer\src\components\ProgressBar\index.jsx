import React, { useState, useEffect } from 'react';
import { ProgressBarContainer } from './style';
import DelayUI from '@renderer/baseUI/DelayUI';
const ProgressBar = (props) => {
	const { data } = props;
	return (
		<ProgressBarContainer {...props}>
			<div className="progress">
				<div className="name">
					{data?.vehicle_level ? data?.vehicle_level : '--'}
				</div>
				<div className="bar"></div>
				<div className="num">
					<span>
						<DelayUI
							targetNumber={data?.nums ? data?.nums : '--'}
							style={{ fontSize: '20px', color: '#FFFFFF' }}
							delayTime={5000}
						/>
					</span>
					<span>辆</span>
					<span>{(data?.rate * 100).toFixed(1)}%</span>
				</div>
			</div>
		</ProgressBarContainer>
	);
};

export default ProgressBar;
