import styled from 'styled-components';
import background from '@renderer/images/background';
const LayoutContainer = styled.div`
	display: flex;
	position: relative;
	width: 100%;
	.left-container {
		flex: 1;
		display: flex;
		flex-direction: row;
		padding: 6px;
		.describe-left {
			flex: 1;
			margin-top: 30%;
		}
		.describe-center {
			text-align: center;
			position: relative;
			div {
				position: absolute;
				top: 38%;
				left: 32%;
				font-size: 32px;
			}
			span {
				position: absolute;
				top: 60%;
				left: 48%;
				font-size: 16px;
			}
			flex: 2;
			background-image: url(${background.real_time_online1});
			background-repeat: no-repeat;
			background-size: 104% 98%;
		}
		.describe-right {
			flex: 1;
			/* margin-right: 30%; */
			padding-left: 10px;
		}
		.describe-title {
			background-image: url(${background.title_b});
			background-repeat: no-repeat;
			background-size: 100%;
			background-position: left bottom, left bottom;
			margin-bottom: 10px;
			line-height: 3px;
			height: 20px;
			font-weight: 500;
			font-size: 16px;
			color: #d9fbff;
			position: relative;
			.describe-title-left {
				position: relative;
				width: 50px;
				height: 50px;
				background-image: url(${background.title_c});
				background-repeat: no-repeat;
				background-size: 100%;
				left: 100%;
			}
			.describe-title-right {
				position: relative;
				width: 50px;
				height: 50px;
				background-image: url(${background.title_c});
				background-repeat: no-repeat;
				background-size: 100%;
				right: 54%;
			}
		}
	}
	.container-middle {
		flex: 1;
		position: relative;
		.background {
			width: 56%;
			height: 100%;
			position: absolute;
			top: -10px;
			right: 42%;
			background-image: url(${background.echarts_b});
			background-repeat: no-repeat;
			background-size: 100%;
			z-index: -99;
		}
	}
`;
export default LayoutContainer;
