import styled from 'styled-components';

export const TraceContentStyle = styled.div`
	position: absolute;
	right: 150px;
	top: 200px;
	z-index: 6;
	.selectAssembly {
		width: 100%;
		margin-left: auto;
		padding-right: 20px;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
		}
		.ant-radio-button-wrapper {
			width: 80px;
			border: 0;
			border-radius: 5px;
			font-size: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			span:hover {
				color: #3adaff;
			}
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
	}
`;
