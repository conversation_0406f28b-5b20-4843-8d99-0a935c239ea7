import { useState, useEffect, useContext } from 'react';
import { SceneMode } from 'mars3d-cesium';
import { MapContext, PageContext } from '../../../context';

import { styled } from 'styled-components';

export const DimensionalStyle = styled.div`
	position: relative;
	top: 880px;
	left: 235px;
	z-index: 99;
	width: 80px;
	border-radius: 43px;
	display: flex;
	flex-direction: row;
	.right-nav-item {
		width: 55px;
		height: 50px;
		text-align: center;
		line-height: 42px;
		font-size: 26px;
		color: rgb(11, 185, 246);
		padding: 4px 8px;
		background: #00436c;
		margin: 0 10px;
		font-family: PingFangSC-Semibold, PingFang SC;
		cursor: pointer;
		position: absolute;
		top: -50px;
		left: -5px;
		user-select: none;
		&:nth-child(1) {
			z-index: 2;
		}
		&:nth-child(2) {
			z-index: 1;
		}
	}

	/* .active {
    background: rgba(51, 82, 112, 1);
    color: rgb(11, 185, 246);
  } */
`;

let info = {};
export default function Dimensional({ map, changeMode, showFlash }) {
	const [list, setList] = useState([
		{ id: SceneMode.SCENE3D, name: '3D' },
		{ id: SceneMode.SCENE2D, name: '2D' },
	]);
	const { sceneMode, setSceneMode } = useContext(MapContext);
	const { hideModeMenu } = useContext(PageContext);
	const [isShow, setIsShow] = useState(!showFlash);
	const [currentMode, setCurrentMode] = useState(SceneMode.SCENE3D);

	useEffect(() => {
		if (!map) return;
		const camera = map.getCameraView();
		info = {
			camera,
		};
	}, [map, sceneMode]);

	const toggleMode = () => {
		const newMode =
			currentMode === SceneMode.SCENE2D ? SceneMode.SCENE3D : SceneMode.SCENE2D;
		setCurrentMode(newMode);
		setSceneMode(newMode);
		const updatedList = list.map((item) => ({
			...item,
			name: item.id === newMode ? '3D' : '2D',
		}));

		setList(updatedList);
		switch (newMode) {
			case SceneMode.SCENE2D:
				map.scene.morphTo2D(0);
				break;
			case SceneMode.SCENE3D:
				map.scene.morphTo3D(0);
				break;
			default:
				break;
		}
	};

	useEffect(() => {
		if (!map) return;
		map.scene.morphComplete.addEventListener(() => {
			if (map.scene.mode === SceneMode.SCENE2D) {
				console.log('2d');
			} else {
				console.log('3d');
			}
			changeMode(map.scene.mode);
		});
	}, [map, currentMode, changeMode]);

	useEffect(() => {
		setIsShow(!showFlash);
		setIsShow(true);
	}, []);

	return (
		<DimensionalStyle>
			{isShow &&
				!hideModeMenu &&
				list.map((item) => (
					<div
						className={
							currentMode === item.id
								? 'right-nav-item active'
								: 'right-nav-item'
						}
						key={item.id}
						onClick={() => {
							toggleMode();
						}}
					>
						{item.name}
					</div>
				))}
		</DimensionalStyle>
	);
}
