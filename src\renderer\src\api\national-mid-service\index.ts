import Request from '../request';

// 20大项目中间层服务
const baseURL = import.meta.env.RENDERER_VITE_API_NATIONAL_MIDSERVICE;

const request = new Request({ baseURL });

// 国控、市控站
export const getStandardStation = (config: Record<string, any> = {}) => {
	return request.get(`/air_quality/map_data/standard-station-api`, config);
};

// 高密度站
export const getHighValueApi = (config: Record<string, any> = {}) => {
	return request.get(`/air_quality/map_data/qc-data-high-value-api`, config);
};

// 热点网格
export const getWarnGrid = (config: Record<string, any> = {}) => {
	return request.get(`/home_page/layer/warn-grid`, config);
};

// 国控、市控站点位弹窗数据
export const getStdStationData = (config: Record<string, any> = {}) => {
	return request.get(`/air_quality/map_data/sta-data-popup-window-api`, config);
};

// 高密度站点位弹窗数据
export const getHightStationData = (config: Record<string, any> = {}) => {
	return request.get(`/air_quality/map_data/qc-data-popup-window-api`, config);
};
