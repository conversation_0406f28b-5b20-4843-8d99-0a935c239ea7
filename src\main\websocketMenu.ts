import WebSocket from 'ws';
import { app, BrowserWindow } from 'electron';

let heartbeatMenuTimer;
const host = import.meta.env.MAIN_VITE_API_DOMAIN;

export function createWsClient(mainWindow: BrowserWindow) {
	const ws = new WebSocket(`ws://${host}/ws/GetRealTimeConfig`);

	startHeartbeat(ws);
	ws.on('error', console.error);

	ws.on('message', function message(data) {
		try {
			const all = JSON.parse(data.toString());

			if (all.code !== 2000) return;
			if (all.result) {
				mainWindow.webContents.send('modulesList', all.result);
			}
		} catch (error) {
			console.log('ws.onmessage_2', error);
		}
	});

	app.on('window-all-closed', () => {
		if (process.platform !== 'darwin') {
			ws.close();
		}
	});

	return ws;
}

const startHeartbeat = (ws: WebSocket): void => {
	if (heartbeatMenuTimer) return;
	heartbeatMenuTimer = setInterval(() => {
		if (ws.readyState === WebSocket.OPEN) {
			ws.send(JSON.stringify({ msg: 'ok' }));
		}
	}, 2000);
};
