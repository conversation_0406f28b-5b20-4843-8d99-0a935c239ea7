import styled from 'styled-components';

const FontColor = '#E2E6ED';
const BGColor = 'none';
const BorderColor = '#2d8987';

const TimeTypeRadioContent = styled.div`
	margin-left: 5px;

	.ant-dropdown-trigger {
		background: none;
		color: ${FontColor};
		border-radius: 3px;
		height: 30px;
		font-size: 17px;
		line-height: 26px;
		background: ${BGColor};
		border: 1px solid ${BorderColor};
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10px 2px 10px 10px;
		&.ant-btn:hover {
			color: ${FontColor};
		}
	}

	.ant-picker {
		margin-left: 5px;
		color: ${FontColor};
		background: ${BGColor};
		border: 1px solid ${BorderColor};
		padding: 0px 11px;
		border-radius: 3px;
		width: 150px;
		.ant-picker-input {
			input,
			.ant-picker-suffix {
				color: ${FontColor};
				font-size: 18px;
			}
		}
	}
`;

const LabelContent = styled.span`
	display: inline-block;
	width: 100%;
	height: 100%;
	display: flex;
	font-size: 18px;
	justify-content: center;
	align-items: center;
	line-height: 100%;
	color: ${FontColor};
`;

export { TimeTypeRadioContent, LabelContent };
