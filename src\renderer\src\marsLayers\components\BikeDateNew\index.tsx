import { useEffect, useState, useContext } from 'react';
import { BikeDateStyled } from './styles';
import { DatePicker, Select } from 'antd';
import { MapContext, PageContext } from '@renderer/context';
import dayjs from 'dayjs';

const BikeDateNew = ({ map }) => {
	const {
		historicalStartTime,
		setHistoricalStartTime,
		historicalEndTime,
		setHistoricalEndTime,
		setOpenTrajectory,
	} = useContext(PageContext);
	const { currentInfo, setCurrentInfo, sceneMode, setCarDetails, setBickDate } =
		useContext(MapContext);
	// const [selectedButton, setSelectedButton] = useState('default')
	// const [showPop, setShowPop] = useState(false)
	// const [customDate, setCustomDate] = useState({})
	const [startTime, setStartTime] = useState(historicalStartTime);
	const [endTime, setEndTime] = useState(historicalEndTime);
	let lastClickCar;
	// const handleClickReference = (value) => {
	//   setSelectedButton(value)
	//   if (value === 'custom') {
	//     setShowPop(true)
	//   } else {
	//     setShowPop(false)
	//   }
	// }

	const onOkStart = (e) => {
		setStartTime(dayjs(e).format('YYYY-MM-DD'));
	};

	const startTimeOnChange = (e) => {
		setStartTime(dayjs(e).format('YYYY-MM-DD'));
	};

	const onOkEnd = (e) => {
		setEndTime(dayjs(e).format('YYYY-MM-DD'));
	};

	const endTimeOnChange = (e) => {
		setEndTime(dayjs(e).format('YYYY-MM-DD'));
	};

	const dataSearch = () => {
		setHistoricalStartTime(startTime);
		setHistoricalEndTime(endTime);
	};

	const btnReturn = () => {
		console.log('================btnReturn====================');
		console.log(lastClickCar);
		console.log('=================btnReturn===================');
		if (lastClickCar) {
			lastClickCar.circle.show = false;
			lastClickCar = null;
		}
		map.trackedEntity = undefined;
		setCurrentInfo(null);
		setOpenTrajectory(false);
		setBickDate(false);
	};

	return (
		<BikeDateStyled>
			<div className="selectAssembly">
				<div className="content">
					<div className="item">
						<p>开始时间:</p>

						<DatePicker
							className="settings-option-datePicker"
							defaultValue={dayjs(startTime, '')}
							// disabledDate={() => {}}
							value={dayjs(startTime)}
							showToday={false}
							onOk={onOkStart}
							onChange={startTimeOnChange}
						></DatePicker>
					</div>
					<div className="item">
						<p>结束时间:</p>
						<DatePicker
							className="settings-option-datePicker"
							defaultValue={dayjs(endTime, '')}
							// disabledDate={() => {}}
							value={dayjs(endTime)}
							showToday={false}
							onOk={onOkEnd}
							onChange={endTimeOnChange}
						></DatePicker>
					</div>
					<div className="search" onClick={dataSearch}>
						<p>数据查询</p>
					</div>
					<div className="btn-return" onClick={btnReturn}>
						<p>返回</p>
					</div>
				</div>
			</div>
		</BikeDateStyled>
	);
};

export default BikeDateNew;
