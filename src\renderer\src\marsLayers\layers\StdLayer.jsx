// 站点类型-国控站
// 站点类型-市控站
// 站点类型-高密度站
// 站点类型-气象站
import React, { useEffect, useContext, useState, useRef } from 'react';
import * as mars3d from 'mars3d';
import moment from 'moment';
import styled from 'styled-components';
import {
	getStandardStation,
	getHighValueApi,
	getMeteStationByCity,
} from '@renderer/api';
import { useAuth } from '@renderer/hooks/useAuth';
import { MapContext } from '@renderer/context';
import pointImg from '@renderer/images/layer/std/chengshi';
import station from '@renderer/images/layer/std/station.png';
import high from '@renderer/images/layer/std/high';

const stdgPointTitle = [],
	stdgPointValue = [];

const stdsPointTitle = [],
	stdsPointValue = [];

const hightPointTitle = [],
	hightPointValue = [];

const weatherStationPointTitle = [],
	weatherStationPointValue = [];

export default (props) => {
	const { visible, map, layerId } = props;
	// const pointTitle = []
	// const pointValue = []
	const { token } = useAuth();
	const { sceneMode, navList } = useContext(MapContext);
	const [data, setData] = useState(null);
	const handler = new mars3d.Cesium.ScreenSpaceEventHandler(map.scene.canvas);
	const PopupLayerRef = useRef(null);
	const [popupLayerData, setPopupLayerData] = useState(null);
	const [callStatus, setCallStatus] = useState(false);

	const getStandardStationData = (json) => {
		if (callStatus) return;
		setCallStatus(true);
		getStandardStation(json)
			.then((res) => {
				if (res.length) drawLayer(res);
				setCallStatus(false);
			})
			.catch((err) => {
				console.log('err', JSON.stringify(err));
			});
	};

	const getHighValueApiData = (json) => {
		if (callStatus) return;
		setCallStatus(true);
		getHighValueApi(json)
			.then((res) => {
				if (res.length) drawHighLayer(res);
				setCallStatus(false);
			})
			.catch((err) => {
				console.log('err', JSON.stringify(err));
			});
	};

	const getMeteStationByCityData = (json) => {
		if (callStatus) return;
		setCallStatus(true);
		getMeteStationByCity(json)
			.then((res) => {
				if (res.length) {
					drawStationLayer(
						res.map((item) => {
							return {
								...item,
								lng: item.wgs84_lng,
								lat: item.wgs84_lat,
							};
						}),
					);
				}
				setCallStatus(false);
			})
			.catch((err) => {
				console.log('err', JSON.stringify(err));
			});
	};

	const removeLayer = (layerId) => {
		switch (layerId) {
			case 'stdg':
				if (stdgPointTitle.length) {
					stdgPointTitle.forEach((item) => {
						map.entities.remove(item);
					});
				}
				if (stdgPointValue.length) {
					stdgPointValue.forEach((item) => {
						map.entities.remove(item);
					});
				}
				break;
			case 'stds':
				if (stdsPointTitle.length) {
					stdsPointTitle.forEach((item) => {
						map.entities.remove(item);
					});
				}
				if (stdsPointValue.length) {
					stdsPointValue.forEach((item) => {
						map.entities.remove(item);
					});
				}
				break;
			case 'hight':
				if (hightPointTitle.length) {
					hightPointTitle.forEach((item) => {
						map.entities.remove(item);
					});
				}
				if (hightPointValue.length) {
					hightPointValue.forEach((item) => {
						map.entities.remove(item);
					});
				}
				break;
			case 'weather_station':
				if (weatherStationPointTitle.length) {
					weatherStationPointTitle.forEach((item) => {
						map.entities.remove(item);
					});
				}
				if (weatherStationPointValue.length) {
					weatherStationPointValue.forEach((item) => {
						map.entities.remove(item);
					});
				}
				break;
			default:
				break;
		}
	};

	const drawStationLayer = (data) => {
		removeLayer(layerId);
		data.forEach((item, idx) => {
			weatherStationPointValue[idx] = new mars3d.Cesium.Entity({
				id: `airStation${layerId}${idx}`,
				name: `airStation${layerId}`,
				billboard: {
					show: true,
					image: station,
					pixelOffset: new mars3d.Cesium.Cartesian2(0, 0),
					zIndex: 5,
					scale: 1,
				},
				position: mars3d.Cesium.Cartesian3.fromDegrees(item.lng, item.lat, 0),
				data: item,
			});
			map.entities.add(weatherStationPointValue[idx]);
		});
		handlerEvent({
			id: `airStation${layerId}`,
		});
	};

	const drawHighLayer = (data) => {
		removeLayer(layerId);
		data.forEach((item, idx) => {
			hightPointValue[idx] = new mars3d.Cesium.Entity({
				id: `airPointValue${layerId}${idx}`,
				// label: {
				//   text: `${item.value}`,
				//   font: '16px sans-serif',
				//   color: mars3d.Cesium.Color.BLACK,
				//   showBackground: true,
				//   backgroundColor: new mars3d.Cesium.Color(0, 0, 0, 0),
				//   horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
				//   pixelOffset: new mars3d.Cesium.Cartesian2(0, 0),
				//   zIndex: 7,
				//   scale: 1.2,
				//   // fillColor: new Cesium.Color(0, 0, 0, 0),
				//   outlineColor: mars3d.Cesium.Color.BLACK
				// },
				billboard: {
					show: true,
					image: high[item.pic],
					pixelOffset: new mars3d.Cesium.Cartesian2(0, 0),
					zIndex: 3,
					scale: 1.6,
				},
				position: mars3d.Cesium.Cartesian3.fromDegrees(item.lng, item.lat, 10),
			});
			map.entities.add(hightPointValue[idx]);
		});
	};

	const drawLayer = (data) => {
		removeLayer(layerId);
		data.forEach((item, idx) => {
			if (item.name) {
				if (layerId === 'stdg') {
					stdgPointTitle[idx] = new mars3d.Cesium.Entity({
						id: `airPointTitle${layerId}${idx}`,
						label: {
							text: `${item.name}`,
							font: '20px sans-serif',
							showBackground: false,
							horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
							pixelOffset: new mars3d.Cesium.Cartesian2(0, 15),
							scale: 0.6,
							zIndex: 1,
						},
						position: mars3d.Cesium.Cartesian3.fromDegrees(
							item.lng,
							item.lat,
							0,
						),
					});
					map.entities.add(stdgPointTitle[idx]);
				} else if (layerId === 'stds') {
					stdsPointTitle[idx] = new mars3d.Cesium.Entity({
						id: `airPointTitle${layerId}${idx}`,
						label: {
							text: `${item.name}`,
							font: '20px sans-serif',
							showBackground: false,
							horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
							pixelOffset: new mars3d.Cesium.Cartesian2(0, 15),
							scale: 0.6,
							zIndex: 1,
						},
						position: mars3d.Cesium.Cartesian3.fromDegrees(
							item.lng,
							item.lat,
							0,
						),
					});
					map.entities.add(stdsPointTitle[idx]);
				}
			}
			if (layerId === 'stdg') {
				stdgPointValue[idx] = new mars3d.Cesium.Entity({
					id: `airPointValue${layerId}${idx}`,
					billboard: {
						show: true,
						image: pointImg[item.pic],
						pixelOffset: new mars3d.Cesium.Cartesian2(0, -15),
						zIndex: 5,
						scale: 1,
					},
					label: {
						text: `${item.value}`,
						font: '20px sans-serif',
						showBackground: true,
						backgroundColor: new mars3d.Cesium.Color(0, 0, 0, 0),
						horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
						verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
						pixelOffset: new mars3d.Cesium.Cartesian2(0, -10),
						zIndex: 3,
						scale: 0.7,
					},
					position: mars3d.Cesium.Cartesian3.fromDegrees(item.lng, item.lat, 0),
				});
				map.entities.add(stdgPointValue[idx]);
			} else if (layerId === 'stds') {
				stdsPointValue[idx] = new mars3d.Cesium.Entity({
					id: `airPointValue${layerId}${idx}`,
					billboard: {
						show: true,
						image: pointImg[item.pic],
						pixelOffset: new mars3d.Cesium.Cartesian2(0, -15),
						zIndex: 5,
						scale: 1,
					},
					label: {
						text: `${item.value}`,
						font: '20px sans-serif',
						showBackground: true,
						backgroundColor: new mars3d.Cesium.Color(0, 0, 0, 0),
						horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
						verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
						pixelOffset: new mars3d.Cesium.Cartesian2(0, -10),
						zIndex: 3,
						scale: 0.7,
					},
					position: mars3d.Cesium.Cartesian3.fromDegrees(item.lng, item.lat, 0),
				});
				map.entities.add(stdsPointValue[idx]);
			}
		});
	};

	const handlerEvent = ({ id = '' }) => {
		handler.setInputAction(function (movement) {
			if (map.scene.mode !== mars3d.Cesium.SceneMode.MORPHING) {
				const pickedObject = map.scene.pick(movement.endPosition);
				if (
					mars3d.Cesium.defined(pickedObject) &&
					pickedObject.id.name === id
				) {
					if (PopupLayerRef.current) {
						setPopupLayerData(
							pickedObject?.id?.data ? pickedObject.id.data : null,
						);
						let winpos = map.scene.cartesianToCanvasCoordinates(
							pickedObject.id.position._value,
						);
						PopupLayerRef.current.style.display = 'block';
						PopupLayerRef.current.style.left = winpos.x + 'px';
						PopupLayerRef.current.style.top = winpos.y + 'px';
					}
				} else {
					PopupLayerRef.current.style.display = 'none';
				}
			}
		}, mars3d.Cesium.ScreenSpaceEventType.MOUSE_MOVE);
	};

	const PopupDom = () => {
		switch (layerId) {
			case 'weather_station':
				return (
					<PopupStyle ref={PopupLayerRef}>
						<ul>
							<li>
								站点名称：
								{popupLayerData?.station_name
									? popupLayerData.station_name
									: ''}
							</li>
							<li>海拔：{popupLayerData?.altitude}</li>
							<li>经度：{popupLayerData?.lng}</li>
							<li>纬度：{popupLayerData?.lat}</li>
							<li>温度：{popupLayerData?.mete_val[2]['209033']}℃</li>
							<li>湿度：{popupLayerData?.mete_val[3]['209034']}%</li>
							<li>风向：{popupLayerData?.mete_val[0]['209036']}</li>
							<li>总降水量：{popupLayerData?.mete_val[4]['209046']}</li>
							<li>风力等级：{popupLayerData?.mete_val[1]['209063']}</li>
						</ul>
					</PopupStyle>
				);

			default:
				return null;
		}
	};

	useEffect(() => {
		if (!visible) return;
		switch (layerId) {
			case 'stdg':
				getStandardStationData({
					data_type: 1,
					pollutant_id: navList[0].id,
					station_type: 0,
					time_type: 1,
					token,
					is_group: 1,
				});
				break;
			case 'stds':
				getStandardStationData({
					data_type: 1,
					pollutant_id: navList[0].id,
					station_type: 2,
					time_type: 1,
					token,
					is_group: 1,
				});
				break;
			case 'hight':
				getHighValueApiData({
					time_type: 1,
					pollutant_id: navList[0].id,
					start_time: moment()
						.subtract(1, 'hour')
						.format('YYYY-MM-DD HH:00:00'),
					end_time: moment().subtract(1, 'hour').format('YYYY-MM-DD HH:00:00'),
					token,
					level: 7,
					min_value: 0,
					max_value: 99999,
					ranking: 9999,
				});
				break;
			case 'weather_station':
				getMeteStationByCityData({
					city_id: '1',
					start_time: moment()
						.subtract(1, 'hour')
						.format('YYYY-MM-DD HH:00:00'),
					end_time: moment().format('YYYY-MM-DD HH:00:00'),
				});
				break;
			default:
				break;
		}

		return () => {
			removeLayer(layerId);
			handler.removeInputAction(mars3d.Cesium.ScreenSpaceEventType.MOUSE_MOVE);
		};
	}, [visible, navList[0]]);

	return visible && PopupDom();
};

const PopupStyle = styled.div`
	width: 500px;
	height: 300px;
	display: inline-block;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 3;
	text-align: left;
	color: #fff;
	line-height: 40px;
	display: none;
	/* transform: translate(-50%, -50%); */
	background: linear-gradient(133deg, #0b2945 0%, #0b2945 45%, #0f334a 100%);
	border-radius: 4px;
	border: 2px solid;
	border-image: linear-gradient(
			307deg,
			rgba(200, 200, 200, 0),
			rgba(1, 184, 255, 1),
			rgba(151, 151, 151, 0)
		)
		2 2;
	margin-top: -330px;
	margin-left: -250px;
	ul {
		position: relative;
		z-index: 2;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		padding: 20px 25px;
		margin: 0;
		li {
			width: 50%;
			line-height: 50px;
		}
	}
`;
