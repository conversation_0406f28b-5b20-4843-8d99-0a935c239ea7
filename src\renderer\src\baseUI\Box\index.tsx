import React, { ReactNode, useContext } from 'react';
import styled, { css } from 'styled-components';
import boxTitleBg from '../../images/networkedData/boxTitleBgNew.png';
import boxTitleBg2 from '../../images/networkedData/boxTitleBg2.png';
import background from '@renderer/images/background';
import { PageContext } from '../../context';

type Props = {
	className?: string;
	width?: string;
	height?: string;
	padding?: string;
	bgcolor?: string;
	bgimage?: string;
	children?: any;
	title?: string;
	subTitle?: ReactNode;
	titlewidth?: string;
	marginLeft?: string;
	margintop?: string;
	style?: typeof css | React.CSSProperties;
	more?: any;
};

const BoxContainer = styled.div<Props>`
	width: ${(props) => props.width};
	height: ${(props) => props.height};
	padding: ${(props) => props.padding};
	background-color: ${(props) => props.bgcolor};
	background-image: url(${(props) => props.bgimage}) no-repeat;
	background-size: 100% 100%;
	position: relative;

	.title {
		// position: absolute;
		display: flex;
		justify-content: space-between;
		width: 100%;
		min-width: 80%;
		margin-left: ${(props) => props.marginLeft || '3px'};
		margin-top: ${(props) => props.margintop || '-5px'};
		background-image: url(${boxTitleBg}), url(${boxTitleBg2});
		background-repeat: no-repeat, no-repeat;
		background-size: ${(props) => props.titlewidth || '100%'} auto, auto auto;
		background-position: left bottom, left bottom;
		color: white;
		font-size: 22px;
		line-height: 3px;
		height: 25px;
		padding-left: 30px;
		display: flex;
		padding-right: 38px;
	}
	.title-text {
		flex: 1;
		display: flex;
		strong {
			width: 245px;
			font-weight: normal;
		}
		div {
			flex: 1;
			position: relative;
			/* background-color: red; */
			height: 25px;
			&::before {
				position: absolute;
				left: 0;
				top: 5px;
				width: 100%;
				height: 2px;
				line-height: 2px;
				overflow: hidden;
				background: linear-gradient(
					270deg,
					rgba(29, 185, 122, 0) 0%,
					#32b6f3 65%,
					#48d7dc 100%
				);
				display: inline-block;
				content: '';
				z-index: 1;
			}
			&::after {
				position: absolute;
				right: 0;
				top: 4px;
				width: 6px;
				height: 4px;
				background-color: #32b6f3;
				display: inline-block;
				content: '';
				z-index: 2;
			}
		}
	}
	.box-right {
		margin-top: -14px;
		margin-left: 5px;
		.customDate {
			height: 40px;
			line-height: 40px;
			display: flex;
			align-items: 'center';
			margin-top: -6px;
			p {
				margin: 0;
				font-size: 18px;
				margin-right: 15px;
			}
			.ant-picker {
				border-radius: 4px;
				border: 0;
				font-size: 18px !important;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				height: 34px;
				color: #ffffff !important;
				display: flex;
				align-items: center;
			}
			.ant-picker-input {
				font-size: 20px;
				color: #e2f0ff !important;
			}
			:where(.css-dev-only-do-not-override-mu9r37).ant-picker
				.ant-picker-suffix {
				margin-right: 10px;
			}
			:where(.css-dev-only-do-not-override-mu9r37).ant-picker
				.ant-picker-clear {
				display: none !important;
			}
			:where(.css-dev-only-do-not-override-mu9r37).ant-picker
				.ant-picker-input
				> input {
				text-align: center !important;
				line-height: 0 !important;
				font-size: 18px !important;
			}
			:where(.css-dev-only-do-not-override-mu9r37).ant-picker
				.ant-picker-input-placeholder
				> input {
				color: #fff !important;
			}
			:where(.css-dev-only-do-not-override-mu9r37).ant-picker {
				padding: 0 !important;
			}
			:where(.css-dev-only-do-not-override-qj3i43).ant-picker-outlined {
				background: url(${background.timeBackGound});
				background-repeat: no-repeat;
				background-size: 100%;
			}
		}
	}
	.more {
		cursor: pointer;
		font-size: 14px;
		position: relative;
		bottom: -5px;
	}
	.main {
		width: 100%;
		height: calc(100% - 10px);
	}
`;
const Box: React.FC<Props> = ({
	children,
	title,
	subTitle,
	more = null,
	...rest
}): React.ReactElement => {
	const { setSlidingLayerInfo } = useContext(PageContext);
	const isObjectEmpty = (obj) => {
		return Object.keys(obj).length;
	};
	const openLayer = (more) => {
		if (more) {
			if (typeof more.data === 'object' && 'dataList' in more.data) {
				const finalData = more.data.dataList.filter(
					(item) => isObjectEmpty(item) !== 0,
				);
				more.data.dataList = finalData;
			}
			setSlidingLayerInfo(more);
		}
	};
	return (
		<BoxContainer {...rest}>
			<div className="title">
				<div className="title-text">
					<strong>{title}</strong>
					{/* <div></div> */}
				</div>
				<span className="box-right">{subTitle}</span>
				{more && (
					<div className="more" onClick={() => openLayer(more)}>
						更多
					</div>
				)}
			</div>
			<div className="main">{children}</div>
		</BoxContainer>
	);
};

Box.defaultProps = {
	width: '100%',
	height: '100%',
	padding: '10px',
};

export default Box;
