import styled from 'styled-components';

const VehicleHistoricalTrajectoryStyled = styled.div`
	position: absolute;
	right: 130px;
	bottom: 260px;
	width: 300px;
	height: auto;
	z-index: 9;
	h3 {
		text-align: center;
		font-size: 22px;
		color: #fff;
		font-weight: bold;
		margin: 0;
		padding: 15px 0 10px;
	}
	.track-wrap {
		display: flex;
		& > div {
			width: 50%;
		}
	}
	.progress-wrap {
		padding: 10px;
		width: 100%;
		height: auto;
		overflow: hidden;
		box-sizing: border-box;
	}
	.ant-progress {
		display: flex;
		.ant-progress-text {
			font-size: 20px;
			.anticon-check-circle {
				font-size: 20px;
			}
		}
	}
	.btnGroup {
		width: 100%;
		display: flex;
		justify-content: space-around;
		margin-bottom: 5px;
		.btn {
			width: 80px;
			height: 50px;
			color: #fff;
			border: 1px solid #009bd8;
			cursor: pointer;
			background-color: rgba(1, 70, 116, 0.8);
		}
		.lable-text {
			display: flex;
			font-size: 20px;
			color: #fff;
			width: 85px;
			align-items: center;
			height: 50px;
			justify-content: center;
		}
		.ant-input-number {
			color: #fff;
			border: 1px solid #009bd8;
			background-color: rgba(1, 70, 116, 0.8);
			width: 70px;
			font-size: 20px;
		}
	}
	.slider {
		position: absolute;
		bottom: -15%;
		width: 1500px;
		height: 20px;
		right: 50%;
	}
	.ant-spin-dot-spin {
		margin: -65px -32px 0 -32px !important;
		font-size: 64px !important;
	}
	.ant-spin-spinning {
		position: fixed !important;
		top: 50% !important;
		transform: translateY(-50%);
		.ant-spin-text {
			font-size: 28px !important;
			color: #fff;
			text-shadow: none;
		}
		.ant-spin-dot-item {
			background-color: #fff;
			width: 28px !important;
			height: 28px;
		}
	}
`;

const LayoutContainer = styled.div`
	width: 100%;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	position: relative;
	margin-bottom: 10px;
	justify-content: center;
	border: 1px solid rgba(0, 155, 216, 0.8);
	background-color: rgba(0, 155, 216, 0.2);
	border-radius: 5px;
	backdrop-filter: blur(15px);
	.cumulative {
		display: flex;
		flex-direction: column;
		margin: 5px;
		div:nth-child(1) {
			flex: 1;
			background: linear-gradient(
				270deg,
				rgba(0, 203, 238, 0.04) 0%,
				rgba(0, 155, 216, 0.28) 100%
			);
			padding: 10px 20px;
			border: 1px solid #009bd8;
			text-align: center;
			color: #fff;
			white-space: nowrap;
		}
		div:nth-child(2) {
			flex: 1;
			background-color: transparent;
			padding: 10px 5px;
			border: 1px solid #009bd8;
			text-align: center;
			color: #6bdaff;
			white-space: nowrap;
		}
	}
`;

const TrajectoryLegendStyled = styled.ul`
	display: flex;
	flex-direction: column;
	position: absolute;
	bottom: 5px;
	right: 500px;
	z-index: 3;
	margin-bottom: 0;
	padding: 10px 15px;
	background-color: rgba(0, 0, 0, 0.4);
	li {
		width: auto;
		align-items: center;
		display: flex;
		margin-top: 5px;
		color: #fff;
		i {
			width: 20px;
			height: 20px;
			display: inline-block;
			background-color: #fff;
			margin-right: 5px;
		}
	}
`;

const VehicleActivityLevelStyled = styled.div`
	width: 240px;
	height: 500px;
	display: inline-block;
	position: absolute;
	left: 150px;
	top: 240px;
	z-index: 4;
	.vehicle-activity-level {
		width: 100%;
		height: 100%;
		background-color: '#00124394';
		background: linear-gradient(
			133deg,
			#0b2945b5 0%,
			#0b2945ba 45%,
			#0f334abd 100%
		);
		border-radius: 4px;
		border: 2px solid;
		border-image: linear-gradient(
				307deg,
				rgba(200, 200, 200, 0),
				rgba(1, 184, 255, 1),
				rgba(151, 151, 151, 0)
			)
			2 2;
	}
`;

export {
	VehicleHistoricalTrajectoryStyled,
	LayoutContainer,
	TrajectoryLegendStyled,
	VehicleActivityLevelStyled,
};
