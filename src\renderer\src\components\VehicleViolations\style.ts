import styled from 'styled-components';
import bg from '../../images/vehicleViolations/bg.png';
const LayoutContainer = styled.div`
	/* margin-top: 10px; */
	margin-left: 10px;
	display: flex;
	flex-direction: column;
	//justify-content: space-between;
	position: relative;
	.left-container {
		width: 100%;
		margin-top: 14px;
		display: flex;
		flex-direction: row;
		.describe {
			/* margin-left: 10px; */
			width: 40%;
			margin-top: 0;
			/* text-align: center; */
			margin-right: 14px;
			dl {
				color: #fff;
				padding-left: 0;
				margin: 0 0 0 0;
				/* display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center; */
				dt {
					font-size: 14px;
					color: #d8e2ef;
					letter-spacing: 4px;
				}
				dd {
					position: relative;
					bottom: 5px;
					font-size: 28px;
					margin: 5px 0 0 0;
					/* font-weight: bold; */
					font-family: 'electronicFont';
					span {
						font-size: 14px;
						color: #d8e2ef;
						margin-left: 5px;
					}
				}
				&.no1 {
					dd {
						color: #5ce2aa;
					}
				}
				&.no2 {
					margin-top: 13px;
					padding-left: 50px;
					dd {
						color: #f5803e;
						font-size: 64px;
						padding-bottom: 10px;
						/* border-bottom: 1px #3488b9 dotted; */
					}
				}
				&.no3 {
					dd {
						color: #f9ae83;
						font-size: 28px;
					}
				}
			}
			.describe-bottom {
				display: flex;
				width: 100%;
				flex-direction: row;
				justify-content: center;
				margin-top: 8px;
				dl {
					padding: 0 30px;
					&:nth-child(1) {
						padding-left: 0;
						position: relative;
						&::after {
							content: '';
							position: absolute;
							right: 0;
							top: 0;
							display: block;
							width: 1px;
							height: 50px;
							border-right: #3488b9 1px dashed;
						}
					}
					&:nth-child(2) {
						padding-right: 0;
					}
				}
			}
			img {
				width: 16px;
				height: 16px;
			}
			span:nth-child(2) {
				margin-left: 5px;
				color: #e2f0ff;
				font-size: 14px;
				font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
			}
			span:nth-child(3) {
				margin-left: 10px;
				color: #4ce6ff;
				font-size: 14px;
			}
			span:nth-child(4) {
				margin-left: 10px;
				color: #e2f0ff;
				font-size: 14px;
				font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
			}
			span:nth-child(5) {
				margin-left: 10px;
				color: #4ce6ff;
				font-size: 14px;
			}
		}
		.echarts-container {
			display: flex;
			flex-direction: row;
			position: relative;
			flex: 1;
			padding-right: 20px;
			.echarts {
				width: 100%;
				height: 100%;
			}
			.legend {
				position: absolute;
				right: -47px;
				// left: 65%;
				top: 10%;
			}
		}
	}
	.middle-container {
		margin-left: 10px;
		.table {
			margin-top: 10px;
			width: 280px;
			height: 168px;
			overflow: hidden;
			background: linear-gradient(
				180deg,
				rgba(13, 130, 234, 0.31) 0%,
				rgba(9, 30, 59, 0) 100%
			);
			border-radius: 4px 4px 0px 0px;
			border: 1px solid rgba(54, 142, 193, 0.56);
			.list-content .list-ul .list-li {
				flex: 1;
				text-align: center;
				cursor: pointer;
				color: #d8f1ff;
				font-size: 16px;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.list-content .list-ul .list-li:nth-child(2) {
				flex: 1;
				text-align: left;
				cursor: pointer;
				color: #d8f1ff;
				font-size: 16px;
				overflow: hidden; //超出的文本隐藏
				text-overflow: ellipsis; //用省略号显示
				white-space: nowrap; //不换行
			}
			ul {
				padding: 0;
				margin: 0;
				list-style: none;
			}
			.column-ul {
				display: flex;
				flex-direction: row;
				justify-content: space-around;
				margin-left: -1px;
				border-right: 1px solid #3488b9;
				border-bottom: 1px solid #3488b9;
				.column-li {
					flex: 1;
					color: #d8f1ff;
					font-size: 16px;
					height: 20px;
					line-height: 20px;
					text-align: center;
					border-top: 1px solid #3488b9;
					border-left: 1px solid #3488b9;
					box-sizing: border-box;
				}
			}
		}
	}
	.data-time {
		text-align: right;
		font-size: 16px;
		color: #d8e2ef;
	}
	.right-container {
		margin-left: 10px;
		.enterprise {
			display: flex;
			flex-direction: column;
			margin-top: 10px;
			.enterprise-item {
				//padding-bottom: 10px;
				.up {
					span:nth-child(1) {
						font-size: 14px;
						color: #fe2c46;
						font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
						padding-right: 10px;
					}
					span:nth-child(2) {
						font-size: 16px;
						color: #ffffff;
						font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
					}
				}
				.down {
					display: flex;
					margin-left: 12px;
					height: 14px;
					line-height: 5px;
					img {
						line-height: 14px;
						width: 9px;
						height: 7px;
						margin-right: 3px;
					}
					span:nth-child(2) {
						padding-right: 3px;
						font-size: 10px;
						color: #d1d1d1;
						font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
					}
					span:nth-child(3) {
						//margin-left: 0px;
						font-size: 10px;
						color: #4ce6ff;
						font-family: 'TimesNewRoman', DINAlternate-Bold;
						padding-right: 10px;
					}

					span:nth-child(4) {
						font-size: 10px;
						color: #d1d1d1;
						font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
						padding-right: 3px;
					}
					span:nth-child(5) {
						font-size: 10px;
						color: #4ce6ff;
						font-family: 'TimesNewRoman', DINAlternate-Bold;
					}
				}
			}
		}
	}

	.detailsPop {
		width: 500px;
		height: 239px;
		background-image: url(${bg});
		background-repeat: no-repeat;
		position: absolute;
		bottom: -200%;
		left: 50%;
		transform: translate(-50%, -50%);
		transition: all 0.5s;
		&.active {
			bottom: -51%;
		}
		.details-container {
			display: flex;
			height: 239px;
			flex-direction: column;
			justify-content: center;
			margin-left: 57px;
			.item {
				height: 30px;
				line-height: 30px;
				span:nth-child(1) {
					color: #d8f1ff;
					font-size: 16px;
					margin-right: 15px;
				}
				span:nth-child(2) {
					color: #d8f1ff;
					font-size: 16px;
				}
			}
		}
	}
`;
export default LayoutContainer;
