import React, { useEffect, useState } from 'react';
import { Table } from 'antd';
import Box from '../../baseUI/Box';
import Container from './style';
import imgs from '../../images/users';
import Echarts from '@renderer/components/echarts';
import imags from '../../images/networkedData/index';
const SpaceDistribution = () => {
	const [columns, setColumns] = useState([]);
	const [dataSource, setDataSource] = useState([]);
	useEffect(() => {
		let columns = [
			{
				title: '行政区',
				dataIndex: 'county',
				key: 'county',
				align: 'center',
			},
			{
				title: '在线数量',
				dataIndex: 'numberOnline',
				key: 'numberOnline',
				align: 'center',
			},
			{
				title: '总里程',
				dataIndex: 'number',
				key: 'number',
				align: 'center',
			},
			{
				title: '总排放量',
				dataIndex: 'paifang',
				key: 'paifang',
				align: 'center',
			},
			{
				title: '总油耗',
				dataIndex: 'youhao',
				key: 'youhao',
				align: 'center',
			},
			{
				title: '排放因子',
				dataIndex: 'yinzi',
				key: 'yinzi',
				align: 'center',
			},
		];
		let dataSource = [
			{
				county: '北京',
				numberOnline: '123',
				number: '111',
				paifang: '111',
				youhao: '111',
				yinzi: '111',
			},
			{
				county: '北京',
				numberOnline: '123',
				number: '111',
				paifang: '111',
				youhao: '111',
				yinzi: '111',
			},
		];
		setColumns(columns);
		setDataSource(dataSource);
	}, []);
	return (
		<Box title="空间分布" titleWidth="95%" height="100%">
			<Container>
				<div className="left">
					<img src={imags.circleBg}></img>
				</div>
				<div className="right">
					<Table
						columns={columns}
						dataSource={dataSource}
						pagination={false}
					></Table>
				</div>
			</Container>
		</Box>
	);
};
export default SpaceDistribution;
