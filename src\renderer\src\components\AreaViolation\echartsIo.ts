import { getCarColor } from '../../../../main/data';
import { tranNumber, tranNumberS } from '@renderer/hooks/index';

export const getPop = (value) => {
	if (!value) return {};

	return {
		tooltip: {
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',

			// background: #8EFAFF;
			padding: 8,
			textStyle: {
				color: '#fff',
			},
			trigger: 'axis',
			confine: true,
			appendToBody: true,
			formatter: function (params) {
				let tipContent = '';
				params.forEach(function (item) {
					tipContent += `${item.seriesName}: ${tranNumberS(item.value)}<br/>`;
				});
				return tipContent;
			},
		},
		legend: {
			data: value?.text,
			textStyle: {
				fontSize: 16,
				color: '#E2F0FF',
			},
			pageTextStyle: {
				color: '#fff', // 文字样式
			},
		},
		grid: {
			top: 32,
			bottom: 0,
			left: 0,
			right: 50,
			containLabel: true,
		},
		xAxis: [
			{
				type: 'value',
				data: value?.per,
				min: 0,
				axisPointer: {
					type: 'shadow',
				},
				axisLabel: {
					textStyle: {
						color: '#C6C7C7',
						fontSize: 16,
					},
					formatter: tranNumber,
				},
			},
			{
				type: 'value',
				name: '环比',
				axisLabel: {
					textStyle: {
						color: '#E2F0FF',
						fontSize: 16,
					},
					formatter: '{value} %',
				},
				splitLine: {
					show: false,
				},
			},
		],
		yAxis: [
			{
				name: value?.unit,
				type: 'category',
				data: value.adm,
				axisLabel: {
					interval: 0,
					textStyle: {
						color: '#E2F0FF',
						fontSize: 19,
					},
				},
				splitLine: {
					show: true,
					lineStyle: {
						color: 'rgba(222, 227, 239, 0.3)',
						type: [2, 4],
					},
				},
			},
			// {
			//   type: 'category',
			//   name: '',
			//   axisLabel: {
			//     textStyle: {
			//       color: '#E2F0FF',
			//       fontSize: 12
			//     },
			//     formatter: '{value} %'
			//   },
			//   splitLine: {
			//     show: false
			//   }
			// }
		],
		series: [
			{
				name: value?.text[0],
				type: 'bar',
				itemStyle: {
					opacity: 1,
					color: '#8EFAFF',
				},
				barWidth: 5,
				data: value?.count,
			},
			{
				name: value?.text[1],
				type: 'bar',
				itemStyle: {
					opacity: 1,
					color: '#C664FF',
				},
				barWidth: 5,
				data: value?.dod,
			},
			{
				name: value?.text[2],
				type: 'line',
				tooltip: {
					backgroundColor: 'rgba(13, 64, 71, 0.50)',
					borderColor: 'rgba(143, 225, 252, 0.60)',
					padding: 8,
					textStyle: {
						color: '#fff',
					},
					// formatter: (params) => {
					//   return `同比:<br/>${params.name} ${params.value}%`
					// }
				},
				xAxisIndex: 1,
				smooth: true,
				symbol: 'circle',
				symbolSize: 12,
				itemStyle: {
					color: '#FFF3C5',
					borderColor: ' #EEC52D',
					borderWidth: 2,
				},
				data: value?.per,
			},
		],
	};
};

export const getIndustry = (val) => {
	if (!val) return;
	const leg: any[] = val.second_type;
	const seriesList: any = [];
	const originalName = val.originalName;
	const selectedRegion: any = [];
	const { regionName, setSelectedRegion } = val;
	Object.keys(val).forEach((key) => {
		if (
			key !== 'name' &&
			key !== 'total' &&
			key !== 'originalName' &&
			key !== 'setSelectedRegion' &&
			key !== 'regionName'
		) {
			const item = val[key];
			const count = item.group_details.count;
			const countSort = val.name.map((element) => {
				const index = originalName.indexOf(element);
				return count[index];
			});
			if (regionName) {
				selectedRegion.push({
					name: item.second_type,
					value: countSort[val.name.indexOf(regionName)],
				});
			}

			seriesList.push({
				name: item.second_type,
				type: 'bar',
				stack: 'total',
				color: getCarColor(item.second_type),
				emphasis: {
					focus: 'series',
				},
				barWidth: 10,
				data: countSort,
			});
		}
	});
	if (regionName) setSelectedRegion(selectedRegion);

	return {
		tooltip: {
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',
			padding: 8,
			textStyle: {
				color: '#fff',
			},
		},
		legend: {
			data: leg,
			textStyle: {
				fontSize: 14,
				color: '#E2F0FF',
			},
			type: 'scroll',
			pageTextStyle: {
				color: '#fff', // 文字样式
			},
		},
		grid: {
			top: 50,
			bottom: 25,
			left: 60,
			right: 30,
		},
		xAxis: {
			type: 'value',
			axisPointer: {
				type: 'shadow',
			},
			axisLabel: {
				textStyle: {
					color: '#C6C7C7',
					fontSize: 12,
				},
			},
		},
		yAxis: {
			name: `辆`,
			nameTextStyle: {
				color: '#D8DBDE',
				fontSize: 14,
				padding: [-10, 0],
			},
			type: 'category',
			data: val.name,
			axisLabel: {
				textStyle: {
					color: '#E2F0FF',
					fontSize: 12,
				},
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: 'rgba(222, 227, 239, 0.3)',
					type: [2, 4],
				},
			},
		},
		series: seriesList,
	};
};
