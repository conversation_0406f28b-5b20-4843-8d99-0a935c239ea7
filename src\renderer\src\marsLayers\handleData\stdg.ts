/**
 * 国控站
 */
// import icons from '@/images/map/standard';
import pointImg from '@renderer/images/layer/std/chengshi';
import { styleType } from './util';

const getStyle = (item: any): styleType => {
	const ele = item;
	return {
		icon: pointImg[`${ele.pic}`],
		text: `${ele.value}`,
		labels: {
			font_size: 20,
			font_weight: 600,
			pixelOffsetY: -14,
			visibleDepth: false,
		},
	};
};

export default {
	getStyle,
};
