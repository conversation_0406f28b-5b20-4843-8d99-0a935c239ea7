import styled from 'styled-components';
import imgs from '../../../images/layersMenu';
import background from '../../../images/background';
export const DashBoardStyle = styled.div`
	position: absolute;
	bottom: 0;
	left: 5%;
	width: 2052.5px;
	height: 251.06px;
	z-index: 999;
	display: flex;
	background-image: url(${imgs.dashBoard});
	background-repeat: no-repeat;
	background-size: 100% 100%;
	justify-content: space-between;
	padding: 10px 20px;
	font-size: 18px;
	padding: 20px 0;
	.head {
		border-bottom: 2px solid #ff8910;
		text-align: center;
		color: #fff;
		margin-bottom: 4px;
		padding-bottom: 2px;
		span {
			border-bottom: 2px solid #2d9be3;
		}
	}
	.information {
		height: 36px;
		border-bottom: 1px solid black;
		margin-top: 4px;
		line-height: 36px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		color: #fff;
		.title {
			color: #01e2ff;
		}
		.content {
			color: #fff;
			font-size: 16px;
		}
	}
	.basic-information {
		width: 14%;
		height: 100%;
		margin-left: 100px;
	}
	.real-time {
		width: 14%;
		height: 100%;
		margin: 0 10px;
	}
	.bike-center {
		width: 40%;
		display: flex;
		> div {
			width: 50%;
			height: 100%;
			position: relative;
		}
	}

	.table {
		width: 30%;
		margin-right: 100px;
		.top {
			width: 100%;
			.ant-table-wrapper {
				.ant-table-container {
					.ant-table-content {
						.ant-table-thead {
							.ant-table-cell {
								background: #1c91a3;
								padding: 4px;
							}
						}
						.ant-table-tbody {
							.ant-table-cell {
								color: #fff;
								background: none;
								border: 1px solid #3488b9;
								font-size: 12px;
								padding: 4px;
							}
						}
					}
				}
			}
			.ant-table-wrapper .ant-table {
				background: none;
			}
		}
		.down {
			width: 100%;
			margin-top: 10px;
			.center {
				display: flex;
				div {
					width: 11%;
					background: rgba(38, 128, 157, 0.47);
					border: 1px solid rgba(54, 142, 193, 0.56);
					text-align: center;
					padding: 4px;
					font-size: 18px;
					color: #3db1f6;
				}
			}
		}
	}
`;

export const EchartsStyle = styled.div`
	width: 100%;
	height: 100%;
	color: #fff;

	.title {
		width: 100%;
		height: 12%;
		display: flex;
		justify-content: center;
		align-items: center;
		background: url(${background.title});
		background-size: 100% 100%;
	}
	.echarts {
		width: 100%;
		height: 118%;
		margin-top: -2%;
	}
	.count {
		width: 21%;
		height: 36%;
		position: absolute;
		top: 49%;
		left: 39%;
		background: url(${background.echarts});
		z-index: 100;
		background-size: 100% 100%;
		text-align: center;
		div:nth-child(1) {
			padding-top: 20%;
			font-size: 20px;
			font-weight: 600;
		}
		div:nth-child(2) {
			padding-top: 4%;
			font-size: 12px;
		}
	}
`;
