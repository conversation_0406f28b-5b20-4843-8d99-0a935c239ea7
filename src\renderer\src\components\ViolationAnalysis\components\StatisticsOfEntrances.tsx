/**
 * 进京口统计情况
 */

import BasicModule from './BasicModule';

const StatisticsOfEntrances = () => {
	const options = [
		{ value: 1, label: '区域' },
		{ value: 2, label: '进京口' },
	];

	const columns = [
		{
			title: '排序',
			dataIndex: 'order',
		},
		{
			title: '行政区',
			dataIndex: 'district',
		},
		{
			title: '进京口数量',
			dataIndex: 'vsp_num',
		},
		{
			title: '疑似违规车辆',
			dataIndex: 'nums',
		},
		{
			title: '疑似车辆占比',
			dataIndex: 'rate',
		},
		{
			title: '问题最高行业',
			dataIndex: 'industry',
		},
	];

	const data = [
		{
			order: '1',
			district: '海淀区',
			vsp_num: '10',
			nums: '100',
			rate: '10%',
			industry: '交通运输',
		},
		{
			order: '2',
			district: '朝阳区',
			vsp_num: '20',
			nums: '200',
			rate: '20%',
			industry: '交通运输',
		},
		{
			order: '3',
			district: '东城区',
			vsp_num: '30',
			nums: '300',
			rate: '30%',
			industry: '交通运输',
		},
		{
			order: '4',
			district: '西城区',
			vsp_num: '40',
			nums: '400',
			rate: '40%',
			industry: '交通运输',
		},
		{
			order: '5',
			district: '丰台区',
			vsp_num: '50',
			nums: '500',
			rate: '50%',
			industry: '交通运输',
		},
	];

	const onChange = () => {
		console.log('onChange');
	};

	return (
		<BasicModule
			title="进京口情况统计"
			columns={columns}
			data={data}
			options={options}
			onChange={onChange}
		/>
	);
};

export default StatisticsOfEntrances;
