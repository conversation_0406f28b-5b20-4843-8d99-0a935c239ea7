import { getCarColorTwo } from '../../../../main/data';

export const getPop = (value) => {
	if (!value) return {};
	return {
		tooltip: {
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',
			padding: 8,
			textStyle: {
				color: '#fff',
			},
		},
		legend: {
			data: ['环比', '当日排放', '昨日环比'],
			textStyle: {
				fontSize: 14,
				color: '#E2F0FF',
			},
		},
		grid: {
			top: 30,
			bottom: 25,
		},
		xAxis: [
			{
				type: 'category',
				data: value.adm,
				axisPointer: {
					type: 'shadow',
				},
				axisLabel: {
					textStyle: {
						color: '#C6C7C7', //更改坐标轴文字颜色
						fontSize: 12, //更改坐标轴文字大小
					},
				},
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLabel: {
					textStyle: {
						color: '#E2F0FF', //更改坐标轴文字颜色
						fontSize: 12, //更改坐标轴文字大小
					},
				},
				splitLine: {
					show: true,
					lineStyle: {
						color: 'rgba(222, 227, 239, 0.3)',
						type: [2, 4],
					},
				},
			},
			{
				type: 'value',
				name: '',
				axisLabel: {
					textStyle: {
						color: '#E2F0FF', //更改坐标轴文字颜色
						fontSize: 12, //更改坐标轴文字大小
					},
					formatter: '{value} %',
				},
				splitLine: {
					show: false,
				},
			},
		],
		series: [
			{
				name: '当日排放',
				type: 'bar',
				itemStyle: {
					opacity: 1, // 这个是 透明度
					color: '#61C6FF',
				},
				barWidth: 5,
				data: value.count,
			},
			{
				name: '昨日环比',
				type: 'bar',
				itemStyle: {
					// lenged文本
					opacity: 1, // 这个是 透明度
					color: '#85A9FF',
				},
				barWidth: 5,
				data: value.dod,
			},
			{
				name: '环比',
				type: 'line',
				yAxisIndex: 1,
				smooth: true,
				symbol: 'circle',
				symbolSize: 8,
				color: '#FFCC85',
				data: value.per,
			},
		],
	};
};

export const getIndustry = (val) => {
	if (!val) return {};
	let leg: any[] = [];
	let seriesList: any[] = [];
	val.forEach((item, index) => {
		leg.push(item.second_type);
		seriesList.push({
			name: item.second_type,
			type: 'bar',
			stack: 'total',
			color: getCarColorTwo(item.second_type),
			emphasis: {
				focus: 'series',
			},
			barWidth: 10,
			data: item.group_details.count,
		});
	});
	return {
		tooltip: {
			backgroundColor: 'rgba(13, 64, 71, 0.50)',
			borderColor: 'rgba(143, 225, 252, 0.60)',
			padding: 8,
			textStyle: {
				color: '#fff',
			},
		},
		legend: {
			data: leg,
			textStyle: {
				fontSize: 14,
				color: '#E2F0FF',
			},
			type: 'scroll',
		},
		grid: {
			top: 30,
			bottom: 25,
		},
		xAxis: {
			type: 'category',
			data: val[0].group_details.adm,
			axisPointer: {
				type: 'shadow',
			},
			axisLabel: {
				textStyle: {
					color: '#C6C7C7', //更改坐标轴文字颜色
					fontSize: 12, //更改坐标轴文字大小
				},
			},
		},
		yAxis: {
			type: 'value',
			axisLabel: {
				textStyle: {
					color: '#E2F0FF', //更改坐标轴文字颜色
					fontSize: 12, //更改坐标轴文字大小
				},
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: 'rgba(222, 227, 239, 0.3)',
					type: [2, 4],
				},
			},
		},
		series: seriesList,
	};
};
