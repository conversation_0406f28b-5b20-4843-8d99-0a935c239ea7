import styled from 'styled-components';
import Echarts from '@renderer/components/echarts';

type Props = {
	width?: string;
	height?: string;
	userData: Array<any>;
};

export const Container = styled.div<Props>`
	width: ${(props) => props.width || '510px'};
	height: ${(props) => props.height || '215px'};
`;

const ManufacturersEcharts = (props: Props) => {
	const { userData } = props;
	const color = [
		'#61C6FF',
		'#A3CDFB',
		'#F48270 ',
		'#8FD67F',
		'#FFCC85',
		'#85A9FF',
	];
	const formatNumber = function (num) {
		const reg = /(?=(\B)(\d{3})+$)/g;
		return num.toString().replace(reg, ',');
	};
	const lightData = userData;
	let maxValIndex = 0;
	lightData.forEach((el, index) => {
		if (el.value > lightData[maxValIndex].value) {
			maxValIndex = index;
		}
	});
	// lightData[maxValIndex].itemStyle = {
	//   shadowBlur: 15,
	//   shadowColor: `${color[maxValIndex]}`,
	//   shadowOffsetX: -4, // X 轴阴影偏移量
	//   shadowOffsetY: -4 // Y 轴阴影偏移量
	// }
	const option = {
		color: color,
		tooltip: {
			trigger: 'item',
			formatter: function (params) {
				return params.name + '：' + params.value + '%';
			},
		},
		series: [
			{
				type: 'pie',
				// roseType: 'area',
				radius: ['45%', '60%'],
				center: ['50%', '50%'],
				startAngle: 260,
				data: lightData,
				labelLine: {
					normal: {
						length: 22,
						length2: 110,
						lineStyle: {
							// color: '#e6e6e6'
						},
					},
				},
				label: {
					normal: {
						formatter: (params) => {
							const maxLength = 10; // 根据实际需求设置最大长度
							if (params.name.length > maxLength) {
								return (
									'{name|' +
									params.name.substring(0, maxLength) +
									'...' +
									'}\n{value|' +
									formatNumber(params.value) +
									'%' +
									'}'
								);
							}
							return (
								'{name|' +
								params.name +
								'}\n{value|' +
								formatNumber(params.value) +
								'%' +
								'}'
							);
						},
						padding: [0, -100, 0, -100],
						rich: {
							name: {
								fontSize: 14,
								padding: [0, 0, 0, 10],
								color: '#E8F4FF',
							},
							value: {
								fontSize: 14,
								fontWeight: 'bolder',
								padding: [10, 10, 0, 40],
								color: 'inherit',
								// color: '#333333'
							},
						},
					},
				},
			},
		],
	};

	return (
		<Container {...props}>
			<Echarts option={option}></Echarts>
		</Container>
	);
};

export default ManufacturersEcharts;
