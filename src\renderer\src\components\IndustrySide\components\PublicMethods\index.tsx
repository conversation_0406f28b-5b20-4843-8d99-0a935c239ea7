// 合并函数
function mergeObjects(array) {
	if (array?.length === 0) return [];
	const result: any = [];
	array.forEach(function (obj) {
		const existingObj = result.find(function (item) {
			return item.name === obj.name;
		});
		if (existingObj) {
			existingObj.data = existingObj.data.map(function (value, index) {
				return value + obj.data[index];
			});
		} else {
			result.push({
				name: obj.name,
				data: obj.data.slice(),
				county: obj?.county,
				town_name: obj?.town_name,
			});
		}
	});
	return result;
}

export { mergeObjects };
