import React, { useRef, useEffect, useState, useContext } from 'react';
import * as mars3d from 'mars3d';
import 'mars3d-wind';
import { Car<PERSON>ian3, Math } from 'mars3d-cesium';
import { MapContext } from '@renderer/context';
import LayerChoose from '@renderer/components/LayerChoose';
import { getMeteorAllPic } from '@renderer/api';
import dayjs from 'dayjs';
import TimeSelector from '@renderer/components/TimeSelector';
import styled from 'styled-components';
import legend from '@renderer/images/layer/legend';

const orientation = {
	heading: Math.toRadians(0),
	pitch: Math.toRadians(-35),
	roll: 0.0,
};

const AirType = [
	{
		id: 2,
		value: '相对湿度',
		visible: true,
		layerId: 'CLIP_RH',
	},
	{
		id: 3,
		value: '边界层',
		visible: false,
		layerId: 'CLIP_HPBL',
	},
	{
		id: 4,
		value: '温度',
		visible: false,
		layerId: 'CLIP_TMP',
	},
	{
		id: 5,
		value: '太阳辐射',
		visible: false,
		layerId: 'CLIP_DSWRF',
	},
	{
		id: 1,
		value: '风场',
		visible: false,
		layerId: 'CLIP_WIND',
	},
];

let earthWindData;

const TOKEN =
	'tAFuWeewk-ojwVPoNP9PCfpL8npZSqC_Ay4vEGSyFvEKKw_UbJrofISgj5MbUwFh30cfu-DCMz4G3OT_Ar0Eakm-U3-fks1_2TQp3QxEgkAQ3DTm09B7doH1KqvnGdgbNyu4h0BVw2jHhEyHdFCMMMhPovvItzxjnuNxiN6PDaxiV7dOrYruXtK25vaNYdPq7frrr0gvA4vL2ov5YP73MhehUXezI_l6npeFm1YC0YvXL3JHFQM4IBYyKbAG2JvOe6uPT88B540nCofo_0FI_Q==';

export default (props) => {
	const { layerId, visible, map } = props;
	const { sceneMode } = useContext(MapContext);
	const layerRef = useRef(null);
	const [typeOptions, setTypeOptions] = useState(AirType);
	const [startTime, setStartTime] = useState(dayjs());
	const [currentId, setCurrentId] = useState('');
	const onChange = (e) => {
		console.log(e);
		setTypeOptions(e);
	};
	const getMeteorAllPicData = (parame, layerId) => {
		getMeteorAllPic(parame)
			.then((res) => {
				if (!res.length) return;
				let CLIP = {};
				res.forEach((element) => {
					CLIP[Object.keys(element)[0]] = Object.values(element)[0];
				});
				addXyzLayer(CLIP[layerId]);
			})
			.catch((err) => {
				console.log('err', JSON.stringify(err));
			});
	};
	const addXyzLayer = (url) => {
		if (layerRef.current) map.removeLayer(layerRef.current);
		layerRef.current = new mars3d.layer.ImageLayer({
			url,
			// minimumLevel: 0,
			// maximumLevel: 10,
			bbox: [115.4168938, 39.442185, 117.5082261, 41.0592191],
			alpha: 0.5,
			zIndex: 9,
		});
		map.addLayer(layerRef.current);
	};

	const formatWindData = (data) => {
		const [data1, data2] = data;
		const { nx, ny, lo1, la1, lo2, la2 } = data1.header;
		return {
			cols: nx,
			rows: ny,
			udata: data1.data,
			vdata: data2.data,
			xmax: lo2,
			xmin: lo1,
			ymax: la1,
			ymin: la2,
		};
	};

	const CreateCanvasWindLayer = () => {
		if (layerRef.current) map.removeLayer(layerRef.current);
		// 风场
		layerRef.current = new mars3d.layer.CanvasWindLayer({
			// worker: window.currentPath + 'windWorker.js', // 启用多线程模式，注释后是单线程模式(非必须)
			frameRate: 10, // 每秒刷新次数
			speedRate: 10, // 风前进速率
			particlesNumber: 15000,
			maxAge: 50,
			lineWidth: 2,
			// 单颜色
			// color: "#ffffff"
			// 多颜色
			colors: [
				'rgb(0, 228, 0)',
				'rgb(256, 256, 0)',
				'rgb(256, 126, 0)',
				'rgb(256, 0, 0)',
				'rgb(153, 0, 76)',
				'rgb(126, 0, 35)',
			],
			steps: [1.0, 2.0, 5.4, 7.9, 10.7, 13.8],
		});
		map.addLayer(layerRef.current);
		// layerRef.current.speedRate = 50
		layerRef.current.reverseY = false; // false时表示 纬度顺序从大到小
		mars3d.Util.fetchJson({
			url: `https://f.hotgrid.cn/meteor-pic/${startTime.format(
				'YYYYMMDDHH',
			)}.json`,
		})
			.then((res) => formatWindData(res))
			.then(function (res) {
				if (earthWindData) {
					layerRef.current.data = earthWindData;
					return;
				}
				earthWindData = res;
				layerRef.current.data = earthWindData;
			})
			.catch(function (err) {
				console.log('请求数据失败!', err);
			});
	};

	const returnTime = (e) => {
		setStartTime(e);
	};

	useEffect(() => {
		if (!visible) return;
		const list = typeOptions
			.filter((item) => item.visible == true)
			.map((item) => item.layerId);
		if (list.length) {
			setCurrentId(list[0]);
			if (list[0] === 'CLIP_WIND') {
				console.log('id??', '=======1');
				CreateCanvasWindLayer();
			} else {
				getMeteorAllPicData(
					{
						json: {
							token: TOKEN,
							provinceId: '1',
							cityId: '0',
							countyId: '0',
							dateTime: startTime.format('YYYY-MM-DD HH:00:00'),
							// picType
							picType: 'CLIP_DSWRF,CLIP_HPBL,CLIP_RH,CLIP_TMP,CLIP_WIND',
							classify: '1',
						},
					},
					list[0],
				);
			}

			map.camera.flyTo({
				destination:
					sceneMode === 3
						? Cartesian3.fromDegrees(116.331734, 38.277551, 130000)
						: Cartesian3.fromDegrees(116.481934, 40.205551, 500000),
				orientation: sceneMode === 3 ? orientation : {},
				duration: 4,
			});
		}
		return () => {
			if (layerRef.current) map.removeLayer(layerRef.current);
		};
	}, [visible, startTime, typeOptions]);

	return (
		<AirLayerStyled>
			<LayerChoose
				data={typeOptions}
				onChange={onChange}
				singleSelection={true}
			/>
			<div className="time-selector">
				<TimeSelector returnTime={returnTime} />
			</div>
			{currentId !== '' && currentId !== 'CLIP_WIND' ? (
				<LegendStyle>
					<img src={legend[currentId]} alt="" />
				</LegendStyle>
			) : null}
		</AirLayerStyled>
	);
};

const AirLayerStyled = styled.div`
	.time-selector {
		position: absolute;
		right: 150px;
		top: 100px;
		z-index: 5;
	}
`;
const LegendStyle = styled.div`
	width: 519px;
	height: 86px;
	display: inline-block;
	position: absolute;
	left: 65%;
	bottom: -5px;
	z-index: 3;
	transform: scale(0.8);
	img {
		width: 100%;
		height: 100%;
		overflow: hidden;
	}
`;
