import React, { useEffect, useState, useContext } from 'react';
import Box from '../../baseUI/Box';
import ProgressBar from '../../components/ProgressBar';
import DelayUI from '@renderer/baseUI/DelayUI';
import { Container } from './style';
import titleBg from '../../images/networkedData/titleBg.png';
import imags from '../../images/networkedData/index';
import { getNetworked } from '@renderer/api';
import { PageContext } from '@renderer/context';
/**
 * 联网数据
 */
export default function BJVehicleCountMonitoring() {
	const { countyId } = useContext(PageContext);
	const colorList = [
		{
			vehicle_level: '--',
			progress: '--',
			nums: '--',
			bgColor: 'rgba(149,225,206,0.3);',
			color: ' #95E1CE',
		},
		{
			vehicle_level: '--',
			progress: '--',
			nums: '--',
			bgColor: 'rgba(254,195,33,0.3)',
			color: ' #FEC321  ',
		},
		{
			vehicle_level: '--',
			progress: '--',
			nums: '--',
			bgColor: 'rgba(94,213,218,0.3)',
			color: '#03BDC5',
		},
	];
	const [netNum, setNetNum] = useState<any>({});
	const [goodsVant, setGoodsVant] = useState(colorList);
	const [busData, setBusData] = useState(colorList);
	useEffect(() => {
		let p = {
			vehicle_type: [1, 2],
			vehicle_level: [1, 2, 3],
			token: 2,
			countyId,
		};
		getNetworked(p).then((res) => {
			console.log('getNetworked--', res);

			setNetNum(res);
			let data = res as any;
			let bus = data?.data
				.filter((item) => item.vehicle_type == '客运')
				.map((item, index) => {
					return {
						...item,
						bgColor: colorList[index]?.bgColor,
						color: colorList[index]?.color,
					};
				});
			let goods = data.data
				.filter((item) => item.vehicle_type == '货运')
				.map((item, index) => {
					return {
						...item,
						bgColor: colorList[index]?.bgColor,
						color: colorList[index]?.color,
					};
				});
			//  goods.split(0,3)
			setBusData(bus);
			setGoodsVant(goods);
		});
	}, [countyId]);

	return (
		<Box title="联网数据" margintop="-35px" titlewidth="95%" height="30%">
			<Container>
				<div className="up-container">
					<div className="up-container-left">
						<div className="country-title">
							国Ⅴ
							<img src={titleBg}></img>
							<div className="num">
								<DelayUI
									targetNumber={netNum?.guo5 ? netNum?.guo5 : '--'}
									style={{ fontSize: '18px', color: '#4ce6ff' }}
									delayTime={5000}
								/>
							</div>
						</div>

						<div className="network-container">
							<img src={imags.jtLeft}></img>

							<div className="networkNum">
								<div className="num-item">
									<DelayUI
										targetNumber={netNum?.total ? netNum?.total : '--'}
										style={{ fontSize: '18px', color: '#4ce6ff' }}
										delayTime={5000}
									/>
									<span>辆</span>
								</div>
								<div className="wired">联网数量</div>
							</div>
						</div>

						<img className="imgRight" src={imags.jtRight}></img>
						<div className="country-title">
							国Ⅵ
							<img src={titleBg}></img>
							<div className="num">
								<DelayUI
									targetNumber={netNum?.guo6 ? netNum?.guo6 : '--'}
									style={{ fontSize: '18px', color: '#4ce6ff' }}
									delayTime={5000}
								/>
							</div>
						</div>
					</div>

					<div className="up-container-right">
						<img src={imags.right}></img>
					</div>
				</div>
				<div className="divider"></div>

				<div className="down-container">
					<div className="down-container-left">
						<div className="down-title">
							<div className="titleName">客车</div>
							<img src={imags.carBg}></img>
						</div>
						<div className="progress-div">
							{busData?.map((item, index) => {
								return <ProgressBar key={index} data={item}></ProgressBar>;
							})}
						</div>
					</div>
					<div className="down-container-right">
						<div className="down-title">
							<div className="titleName">货车</div>
							<img src={imags.carBg}></img>
						</div>
						<div className="progress-div">
							{goodsVant?.map((item, index) => {
								return <ProgressBar key={index} data={item}></ProgressBar>;
							})}
						</div>
					</div>
				</div>
			</Container>
		</Box>
	);
}
