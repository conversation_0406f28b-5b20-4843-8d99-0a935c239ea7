import { useEffect, useState } from 'react';
import styled from 'styled-components';
import Echarts from '@renderer/components/echarts';
import { INDUSTRY_COLOR } from '@renderer/constants/color';
import background from '../../images/background';

export const Container = styled.div<Props>`
	width: ${(props) => props.width || '100%'};
	height: ${(props) => props.height || '215px'};
`;

type DataArr = {
	name?: string;
	value?: number;
	itemStyle?: object;
};

type EchartsData = {
	num: number;
	value: number;
	name: string;
	rate: number;
};

type Props = {
	width?: string;
	height?: string;
	data: EchartsData[];
	type?: string;
	totalOnline?: string;
};

const RealTimeOnlineEcharts2 = (props: Props) => {
	const { data } = props;
	const data2: any = [];
	const color = [
		'#00E6FF',
		'#E3A07C',
		'#61C6FF',
		'#FE9903',
		'#8FD67F',
		'#00FF96',
		'#85A9FF',
		'#CCCC33',
		'#ad4e00',
	];
	for (let i = 0; i < data.length; i++) {
		data2.push(
			{
				value: data[i].num,
				name: data[i].name,
				itemStyle: {
					color: INDUSTRY_COLOR[data[i].name],
				},
			},
			// {
			//   value: 6,
			//   name: '',
			//   itemStyle: {
			//     normal: {
			//       label: {
			//         show: false
			//       },
			//       labelLine: {
			//         show: false
			//       },
			//       color: 'rgba(0, 0, 0, 0)',
			//       borderColor: 'rgba(0, 0, 0, 0)'
			//     }
			//   },
			//   tooltip: {
			//     show: false
			//   }
			// }
		);
	}
	const option = {
		legend: {
			how: true,
			orient: 'vertical',
			type: 'scroll',
			right: 50,
			height: 175,
			icon: 'circle',
			itemWidth: 11,
			itemHeight: 11,
			top: 20,
			bottom: 30,
			data,
			pageTextStyle: {
				color: '#fff',
			},
			pageIconColor: '#fff',
			pageIconInactiveColor: '#ccc',
			formatter: function (name) {
				const data = option.series[0].data.filter((item) => item.name !== '');
				let total = 0;
				data.forEach(function (item) {
					total += item.value;
				});
				const arr: any = [];
				for (let i = 0; i < data.length; i++) {
					if (data[i].name === name) {
						const percent = ((data[i].value / total) * 100).toFixed(0);
						arr.push('{a|' + name + '}' + '{b|' + percent + '%}');
					}
				}
				return arr;
			},
			textStyle: {
				rich: {
					a: {
						fontSize: 12,
						color: 'rgb(169,169,169)',
					},
					b: {
						fontSize: 16,
						color: '#F0FFFF',
						padding: [0, 0, 0, 5],
					},
				},
			},
		},
		tooltip: {
			trigger: 'item',
		},
		series: [
			{
				type: 'pie',
				zlevel: 1,
				startAngle: 90,
				radius: ['50%', '64%'],
				label: { show: false },
				labelLine: { show: false },
				animation: true,
				animationDuration: 800,
				avoidLabelOverlap: false,
				data: data2,
				right: '40%',
				// top: '-3%',
				itemStyle: {
					borderRadius: 7,
					borderColor: 'rgb(9,36,63)',
					borderWidth: 4,
				},
			},
			// {
			//   type: 'pie',
			//   radius: 0,
			// },
			// {
			//   type: 'pie',
			//   radius: 0,
			// },
			// {
			//   type: 'pie',
			//   zlevel: 4,
			//   silent: true,
			//   startAngle: angle,
			//   radius: ['58%', '62%'],
			//   label: { show: false },
			//   labelLine: { show: false },
			//   data: _pie(),
			//   animation: true,
			//   right: '20%',
			//   top: -10,
			// },
		],
		color: [
			'#00E6FF',
			'#FF5E34',
			'#FE619F',
			'#FE9903',
			'#FAFA03',
			'#00FF96',
			'#fc6099',
			'#CCCC33',
			'#ad4e00',
		],
	};

	useEffect(() => {}, [data]);

	return (
		<Container {...props}>
			<Echarts option={option}></Echarts>
		</Container>
	);
};
export default RealTimeOnlineEcharts2;
