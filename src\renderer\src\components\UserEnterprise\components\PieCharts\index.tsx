import { useEffect, useState } from 'react';

import Echarts from '@renderer/components/echarts';
const PieCharts = ({ data, style, title }) => {
	const [option, setOption] = useState({});

	useEffect(() => {
		if (data !== undefined) {
			const option = {
				title: {
					text: title,
					left: 0,
					top: 10,
					textStyle: {
						color: '#fff',
						fontSize: 18,
						fontWeight: 'normal',
					},
				},
				legend: {
					show: true,
					type: 'scroll',
					orient: 'vertical',
					left: 0,
					// right,
					height: 145,
					icon: 'circle',
					itemWidth: 14,
					itemHeight: 14,
					top: 45,
					bottom: 30,
					data: data.map((item) => item.name),
					pageTextStyle: {
						color: '#fff',
					},
					pageIconColor: '#fff',
					pageIconInactiveColor: '#ccc',
					textStyle: {
						color: '#ffffff',
					},
				},
				tooltip: {
					trigger: 'item',
					formatter: (params) => {
						return params.name + ' ' + params.value + '辆';
					},
				},
				series: [
					{
						type: 'pie',
						startAngle: 90,
						radius: ['56%', '92%'],
						label: { show: false },
						labelLine: { show: false },
						animation: true,
						animationDuration: 800,
						avoidLabelOverlap: false,
						data: data,
						right: 20,
						top: '5%',
						bottom: '10%',
						itemStyle: {
							borderRadius: 7,
							borderColor: 'rgb(9,36,63,0.5)',
							borderWidth: 2,
						},
					},
				],

				color: [
					'#00E6FF',
					'#E3A07C',
					'#A5C86A',
					'#FE9903',
					'#61C6FF',
					'#00FF96',
					'#85A9FF',
					'#CCCC33',
					'#ad4e00',
				],
			};
			setOption(option);
		}
	}, [data]);

	return <Echarts option={option} style={style} notMerge={true}></Echarts>;
};

export default PieCharts;
