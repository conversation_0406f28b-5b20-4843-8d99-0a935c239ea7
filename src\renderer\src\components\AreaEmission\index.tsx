import React, { useEffect, useState } from 'react';
import Echarts from '@renderer/components/echarts';
import Box from '../../baseUI/Box';
import AreaMap from '@renderer/components/AreaViolation/AreaMap';
import TimeTypeRadio from '../../baseUI/TimeTypeRadio2';
import AreaViolationStyled from '@renderer/components/AreaViolation/style';
import {
	statisticsViolationDodListGroupByAdm,
	statisticsViolationIndustryListGroupByAdm,
} from '@renderer/api';
import { useAuth } from '@renderer/hooks/useAuth';
import {
	getIndustry,
	getPop,
} from '@renderer/components/AreaViolation/echarts';

const typeList = [
	{ id: '1', name: '环比' },
	{ id: '2', name: '行业' },
];

// eslint-disable-next-line react/prop-types
export default function AreaEmission() {
	const { token } = useAuth();
	const [type, setType] = useState('1');
	const [option1, setOption1] = useState({});
	const [option2, setOption2] = useState({});
	const [timeData, setTimeData] = useState({});
	const [mapData, setMapData] = useState<any>([]);

	useEffect(() => {
		getAreaViolation();
	}, [timeData]);

	const getAreaViolation = () => {
		const json = {
			token,
			...timeData,
		};
		statisticsViolationDodListGroupByAdm(json)
			.then((res) => {
				console.log('res----------------------', res);
				const arr = res?.adm?.map((item, index) => {
					return { name: item, value: res?.count[index] };
				});
				setMapData(arr);
				setOption1(getPop(res));
			})
			.catch((err) => {
				console.log('err', JSON.stringify(err));
			});
		statisticsViolationIndustryListGroupByAdm(json)
			.then((res) => {
				console.log('res----------------------', res);
				setOption2(getIndustry(res));
			})
			.catch((err) => {
				console.log('err', JSON.stringify(err));
			});
	};

	return (
		<Box
			title="区域排放"
			subTitle={
				<TimeTypeRadio
					defaultValue="1"
					timeType={(data) => setTimeData(data)}
				/>
			}
			titlewidth="95%"
			height="30%"
		>
			<AreaViolationStyled>
				<div className="echarts-map">
					<AreaMap mapData={mapData} />
				</div>
				<div className="echarts-line">
					<div className="echarts-title">
						{typeList.map((item) => (
							<span
								key={item.id}
								className={`${
									type == item.id ? 'title-select' : 'title-un-select'
								}`}
								onClick={() => setType(item.id)}
							>
								{item.name}
							</span>
						))}
					</div>
					<Echarts
						option={type === '1' ? option1 : option2}
						style={{ height: 180 }}
						notMerge={true}
					></Echarts>
				</div>
			</AreaViolationStyled>
		</Box>
	);
}
