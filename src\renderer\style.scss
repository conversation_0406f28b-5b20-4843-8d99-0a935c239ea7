// Import the functions/mixins
@import '@animxyz/core';

// Add all the core/utilities selectors
@include xyz-all;

@font-face {
	font-family: 'electronicFont';
	src: url('./src/assets/font/DS-DIGIT.TTF') format('truetype');
}

@font-face {
	font-family: 'TimesNewRoman';
	src: url('./src/assets/font/TimesNewRoman.ttf') format('truetype');
}

.electronicFont dd {
	font-family: 'electronicFont' !important;
	span {
		font-family: '';
		span {
			font-family: 'electronicFont' !important;
		}
	}
}

.TimesNewRoman dd {
	font-family: 'TimesNewRoman' !important;
	span {
		font-family: '';
		span {
			font-family: 'TimesNewRoman' !important;
		}
	}
}

.electronicFont2 {
	font-family: 'electronicFont' !important;
}

.TimesNewRoman2 {
	font-family: 'TimesNewRoman' !important;
}

.troublesPop
	.ant-popover-content
	.ant-popover-inner
	.ant-popover-inner-content
	.area-item {
	display: flex;
	height: 25px;
	line-height: 25px;
	span {
		display: block;
		margin-right: 10px;
		width: 60px;
		text-align: left;
	}
}

.ant-dropdown {
	/* 定义滚动条的宽度和颜色 */
	::-webkit-scrollbar {
		width: 2px;
		background-color: none;
	}

	/* 定义滚动条滑块的样式 */
	::-webkit-scrollbar-thumb {
		background-color: rgba(0, 184, 255, 0.2);
		border-radius: 5px;
	}

	/* 定义滚动条滑块悬停时的样式 */
	::-webkit-scrollbar-thumb:hover {
		background-color: rgba(0, 184, 255, 0.5);
	}

	/* 定义滚动条轨道的样式 */
	::-webkit-scrollbar-track {
		background-color: none;
	}

	/* 定义滚动条轨道上的按钮样式 */
	::-webkit-scrollbar-button {
		display: none;
	}
}

.ant-dropdown-menu {
	margin-top: -2px !important;
	max-height: 230px;
	overflow: auto;
	.ant-dropdown-menu-item {
		font-size: 18px !important;
	}
}

:where(.css-dev-only-do-not-override-mu9r37).ant-dropdown .ant-dropdown-menu {
	background-color: rgba(1, 70, 116, 0.8);
	text-align: center;
}

html {
	overflow: hidden;
}
