import React, { useEffect, useState } from 'react';
import { DatePicker, Select } from 'antd';
// import 'moment/locale/zh-cn'
// import moment from 'moment'
import dayjs from 'dayjs';
import CustomPopUpsStyle from './style';
import icon from '@renderer/images/icon';

type Props = {
	showPop: boolean;
	right: string;
	top: string;
	customStartTime: any;
	setShowPop: React.Dispatch<React.SetStateAction<boolean>>;
	setTimeType: React.Dispatch<React.SetStateAction<string>>;
	setCustomDate: React.Dispatch<React.SetStateAction<CustomDate>>;
};

interface CustomDate {
	startTime: string;
	endTime: string;
	timeType: string;
}

const CustomPopUps = (props: Props) => {
	const {
		showPop,
		setShowPop,
		setTimeType,
		setCustomDate,
		right,
		top,
		customStartTime,
	} = props;
	const [timeTypeCustom, setTimeTypeCustom] = useState<string>('year');
	const [endTime, setEndTime] = useState(
		customStartTime?.end_time !== '' && customStartTime?.end_time !== undefined
			? customStartTime?.end_time
			: dayjs().format('YYYY'),
	);
	const [startTime, setStartTime] = useState(
		customStartTime?.start_time !== '' &&
			customStartTime?.start_time !== undefined
			? customStartTime?.start_time
			: dayjs(endTime).add(-1, 'year').format('YYYY'),
	);
	const defaultTimeType = [
		{ value: 'year', label: '年' },
		{ value: 'month', label: '月' },
	];

	const dateOnChange = (type) => {
		setTimeTypeCustom(type);
		if (type === 'year') {
			const eTime = dayjs().format('YYYY');
			const sTime = dayjs(eTime).add(-1, 'year').format('YYYY');
			setStartTime(sTime);
			setEndTime(eTime);
		} else if (type === 'month') {
			const eTime = dayjs().format('YYYY-MM');
			const sTime = dayjs(eTime).add(-1, 'month').format('YYYY-MM');
			setStartTime(sTime);
			setEndTime(eTime);
		}
	};

	const startTimeOnChange = (time) => {
		if (time >= dayjs(endTime)) {
			console.log('选中时间大于结束时间');
		} else {
			if (timeTypeCustom === 'year') {
				setStartTime(dayjs(time).format('YYYY'));
			} else if (timeTypeCustom === 'month') {
				setStartTime(dayjs(time).format('YYYY-MM'));
			}
		}
	};

	const endTimeOnChange = (time) => {
		if (time <= dayjs(startTime)) {
			console.log('选中时间小于开始时间');
		} else {
			if (timeTypeCustom === 'year') {
				setEndTime(dayjs(time).format('YYYY'));
			} else if (timeTypeCustom === 'month') {
				setEndTime(dayjs(time).format('YYYY-MM'));
			}
		}
	};

	const onOkStart = (e) => {
		if (e >= dayjs(endTime)) {
			console.log('选中时间大于结束时间');
		} else {
			if (timeTypeCustom === 'year') {
				setStartTime(dayjs(e).format('YYYY'));
			} else if (timeTypeCustom === 'month') {
				setStartTime(dayjs(e).format('YYYY-MM'));
			}
		}
	};

	const onOkEnd = (e) => {
		if (e <= dayjs(startTime)) {
			console.log('选中时间小于开始时间');
		} else {
			if (timeTypeCustom === 'year') {
				setEndTime(dayjs(e).format('YYYY'));
			} else if (timeTypeCustom === 'month') {
				setEndTime(dayjs(e).format('YYYY-MM'));
			}
		}
	};

	const closePop = () => {
		setShowPop(false);
	};
	const dataSearch = () => {
		if (setTimeType) setTimeType(`custom`);
		setCustomDate({
			startTime: startTime,
			endTime: endTime,
			timeType: timeTypeCustom,
		});
	};

	return showPop ? (
		<CustomPopUpsStyle right={right} top={top}>
			<img src={icon.close} alt="" className="close" onClick={closePop} />
			<div className="content">
				<div className="item">
					<p>时间粒度:</p>
					<Select
						className="settings-select"
						popupClassName="settings-select-popup"
						defaultValue={timeTypeCustom}
						onChange={dateOnChange}
						style={{ width: '100px', textAlign: 'center' }}
						options={defaultTimeType}
					/>
				</div>
				<div className="item">
					<p>开始时间:</p>
					{timeTypeCustom == 'year' ? (
						<DatePicker
							className="settings-option-datePicker"
							format="YYYY"
							picker="year"
							defaultValue={dayjs(startTime, '')}
							// disabledDate={() => {}}
							value={dayjs(startTime)}
							onOk={onOkStart}
							onChange={startTimeOnChange}
						></DatePicker>
					) : (
						<DatePicker
							className="settings-option-datePicker"
							defaultValue={dayjs(startTime, '')}
							format="YYYY/MM"
							picker="month"
							// disabledDate={() => {}}
							value={dayjs(startTime)}
							// onOk={onOkStart}
							onChange={startTimeOnChange}
						></DatePicker>
					)}
				</div>
				<div className="item">
					<p>结束时间:</p>
					{timeTypeCustom == 'year' ? (
						<DatePicker
							className="settings-option-datePicker"
							format="YYYY"
							picker="year"
							defaultValue={dayjs(endTime, '')}
							// disabledDate={() => {}}
							value={dayjs(endTime)}
							onOk={onOkEnd}
							onChange={endTimeOnChange}
						></DatePicker>
					) : (
						<DatePicker
							className="settings-option-datePicker"
							defaultValue={dayjs(endTime, '')}
							format="YYYY/MM"
							picker="month"
							// disabledDate={() => {}}
							value={dayjs(endTime)}
							// onOk={onOkEnd}
							onChange={endTimeOnChange}
						></DatePicker>
					)}
				</div>
			</div>
			<div className="search" onClick={dataSearch}>
				<p style={{ margin: 0 }}>数据查询</p>
			</div>
		</CustomPopUpsStyle>
	) : null;
};

export default CustomPopUps;
