import styled from 'styled-components';

const RightContainer = styled.section`
	width: 852px;
	height: 100%;
	position: absolute;
	z-index: 10;
	display: flex;
	flex-direction: row-reverse;
	right: 0;
	top: 0;
	/* margin-top: -15px;
  margin-right: -15px; */
	.left-bg {
		position: absolute;
		right: 0;
		top: 0;
		width: 1033px;
		height: 899px;
		z-index: 2;
	}
	.right-border {
		position: absolute;
		left: -410px;
		top: 0;
		width: 476px;
		height: 910px;
		z-index: 1;
	}
`;

const Right = ({ children }) => {
	return <RightContainer>{children}</RightContainer>;
};

export default Right;
