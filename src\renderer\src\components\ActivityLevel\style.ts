import styled from 'styled-components';
import imgs from '@renderer/images/background';

export const ActivityLevelContent = styled.div`
	color: #fff;
	width: 100%;
	height: 100%;
	margin-top: 0px;
	.container {
		width: 100%;
		height: 100%;
		display: flex;
		margin-left: 8px;
		margin-top: 16px;
		.container-left {
			width: 250px;
			height: 100%;
			margin-top: 8px;
			// padding-left: 20px;
			// padding-top: 14px;
			.Real-time-online {
				width: 260px;
				height: 70px;
				// background-image: url(${imgs.activityBg});
				// background-size: 100% 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				margin-bottom: 20px;
				.online-count {
					display: flex;
					align-items: center;
					justify-content: center;

					span {
						display: flex;
						align-items: center;
						margin-left: 10px;
					}
				}
			}
			.Real-time-online-in,
			.Real-time-online-out {
				width: 260px;
				height: 40px;
				display: flex;
				align-items: center;
				justify-content: center;
				// border-radius: 40px;
				// border: 1px solid;
				// border-top: transparent;
				// border-bottom: transparent;
				// border-color: rgba(254, 227, 115, 0.53);
				margin-bottom: 6px;

				span {
					display: flex;
					align-items: center;
					margin-left: 10px;
				}
			}
		}
		.container-middle {
			// width: 280px;
			width: 475px;
			height: 100%;
			// margin-left: 25px;
			// margin-top: -16px;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		// .container-right {
		//   width: 180px;
		//   height: 100%;
		//   margin-top: -16px;
		// }
	}
`;
