import moment from 'moment';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { isEmpty } from 'lodash';
import { timeFormat } from '@renderer/utils';

function useSpaceTimeJson(
	token,
	timeType,
	typeId,
	customDate,
	level,
	county_id,
) {
	let json: any = {};
	const currentTime = moment().format('YYYY-MM-DD HH:00:00');
	if (timeType == '7day') {
		json = {
			start_time: moment().subtract(8, 'day').format('YYYY-MM-DD 00:00:00'),
			end_time: moment().subtract(1, 'day').format('YYYY-MM-DD 23:59:59'),
			time_type: 3,
			topic: typeId,
			token,
		};
	} else if (timeType == 'day') {
		json = {
			start_time: moment().format('YYYY-MM-DD 00:00:00'),
			end_time: moment(currentTime)
				.add(-1, 'hour')
				.format('YYYY-MM-DD HH:00:00'),
			time_type: 1,
			topic: typeId,
			token,
		};
	} else if (timeType === 'custom') {
		json = {
			start_time: customDate.startTime,
			end_time: customDate.endTime,
			time_type: customDate.timeType === 'hour' ? 2 : 1,
			topic: typeId,
			token,
		};
	} else {
		json = {
			start_time: moment().add(-1, 'day').format('YYYY-MM-DD 00:00:00'),
			end_time: moment().add(-1, 'day').format('YYYY-MM-DD 23:59:59'),
			time_type: 2,
			topic: typeId,
			token,
		};
	}

	if (level) {
		json.level = level;
	} else if (county_id) {
		json.county_id = county_id;
	}
	return json;
}

function useSpaceTimeParams(timeType, customDate) {
	let params = {};
	const currentTime = moment().format('YYYY-MM-DD HH:00:00');
	if (timeType == '7day') {
		params = {
			start_time: moment().subtract(8, 'day').format('YYYY-MM-DD 00:00:00'),
			end_time: moment().subtract(1, 'day').format('YYYY-MM-DD 23:59:59'),
			time_type: 2,
			type: timeType,
		};
	} else if (timeType == '30day') {
		params = {
			start_time: moment().subtract(31, 'day').format('YYYY-MM-DD 00:00:00'),
			end_time: moment().subtract(1, 'day').format('YYYY-MM-DD 23:59:59'),
			time_type: 2,
			type: timeType,
		};
	} else if (timeType == 'day') {
		params = {
			start_time: moment().format('YYYY-MM-DD 00:00:00'),
			end_time: moment(currentTime)
				.add(-1, 'hour')
				.format('YYYY-MM-DD HH:00:00'),
			time_type: 1,
			type: timeType,
		};
	} else if (timeType === 'custom') {
		params = {
			start_time: customDate.startTime,
			end_time: customDate.endTime,
			time_type: customDate.timeType === 'hour' ? 1 : 2,
			type: timeType,
		};
	} else {
		params = {
			start_time: moment().add(-1, 'day').format('YYYY-MM-DD 00:00:00'),
			end_time: moment().add(-1, 'day').format('YYYY-MM-DD 23:59:59'),
			time_type: 1,
			type: timeType,
		};
	}
	return params;
}

function useTimeReferenceParams(customDate) {
	let params = {};
	params = {
		benchmark_start_time: customDate.startTime,
		benchmark_end_time: customDate.endTime,
		benchmark_time_type: customDate.timeType === 'hour' ? 1 : 2,
	};
	return params;
}

function useViolationTimeReferenceParams(customDateYoy, customDate) {
	let params = {};
	if (isEmpty(customDateYoy)) {
		if (customDate.type === '7day') {
			params = {
				benchmark_start_time: moment()
					.subtract(16, 'day')
					.format('YYYY-MM-DD 00:00:00'),
				benchmark_end_time: moment()
					.subtract(9, 'day')
					.format('YYYY-MM-DD 00:00:00'),
				benchmark_time_type: customDate.timeType === 'hour' ? 1 : 2,
			};
		} else if (customDate.type === '30day') {
			params = {
				benchmark_start_time: moment()
					.subtract(62, 'day')
					.format('YYYY-MM-DD 00:00:00'),
				benchmark_end_time: moment()
					.subtract(32, 'day')
					.format('YYYY-MM-DD 00:00:00'),
				benchmark_time_type: customDate.timeType === 'hour' ? 1 : 2,
			};
		} else {
			const daysDiff = moment(customDate.end_time).diff(
				moment(customDate.start_time),
				'days',
			);
			const benchmark_start_time = moment(customDate.start_time)
				.subtract(daysDiff + 1, 'day')
				.format('YYYY-MM-DD 00:00:00');
			const benchmark_end_time = moment(customDate.start_time)
				.subtract(1, 'day')
				.format('YYYY-MM-DD 00:00:00');
			params = {
				benchmark_start_time,
				benchmark_end_time,
				benchmark_time_type: customDate.timeType === 'hour' ? 1 : 2,
			};
		}
	} else {
		params = {
			benchmark_start_time: customDateYoy.startTime,
			benchmark_end_time: customDateYoy.endTime,
			benchmark_time_type: customDateYoy.timeType === 'hour' ? 1 : 2,
		};
	}
	return params;
}

export {
	useSpaceTimeJson,
	useSpaceTimeParams,
	useTimeReferenceParams,
	useViolationTimeReferenceParams,
};
