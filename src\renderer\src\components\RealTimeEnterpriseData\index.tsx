import { useEffect, useRef, useState } from 'react';
import { Segmented, Table, Radio, Tooltip } from 'antd';
import type { TableProps } from 'antd';
import * as echarts from 'echarts';
import dayjs from 'dayjs';
import TimeTypeRadio from '@renderer/baseUI/TimeTypeRadioCustom';
import Box from '@renderer/baseUI/Box';
import CustomPopUps from '@renderer/baseUI/TimeTypeRadioCustom/components/CustomPopUps';
import {
	getRealTimeEnterpriseDetails,
	getRealTimeEnterpriseRanking,
	getEnterpriseDataAnalytics,
} from '@renderer/api';
import { tranNumber } from '@renderer/hooks';
import ScrollListWidthTitle from '../SlidingLayer/DailyActivityLevel/components/ScrollListWidthTitle';
import RealTimeEnterpriseDataStyled from './styles';
import styled from 'styled-components';

type Align2 = '图形' | '列表';

interface DataType {
	AFFILIATION: string;
	DISTANCE: number;
	NOX_EMISSIONS: number;
	OIL_CONSUMPTION: number;
	ONLINE_PERIOD: number;
	count: number;
}

interface OptionDataType {
	num: Array<string>;
	lable: Array<string>;
	value: Array<string>;
}

const RealTimeEnterpriseData = () => {
	// const [timeType, setTimeType] = useState('year')
	const [alignValue, setAlignValue] = useState('在线车辆');
	const [selectedButton, setSelectedButton] = useState('default');
	const [alignValue2, setAlignValue2] = useState<Align2>('图形');
	const [showPop, setShowPop] = useState(false);
	const [customDate, setCustomDate] = useState({});
	const [topic, setTopic] = useState('5');
	const [unitName, setUnitName] = useState('单位: 辆');
	const [optionData, setOptionData] = useState<OptionDataType>({
		lable: [],
		num: [],
		value: [],
	});
	const [data, setData] = useState<Array<DataType>>([]);
	const echartsRealTimeEnterpriseContainerRef = useRef(null);
	const myChartRef = useRef<any>(null);
	const items = ['在线车辆', '排放量', '里程', '油耗', '运行时长'];
	const graphSwitching = ['图形', '列表'];
	const columns: TableProps<DataType>['columns'] = [
		{
			title: '企业名称',
			dataIndex: 'AFFILIATION',
			key: 'AFFILIATION',
			// render: (text) => (
			//   <Tooltip title={text}>
			//     <span>{text}</span>
			//   </Tooltip>
			// )
		},
		{
			title: '在线车辆',
			dataIndex: 'count',
			key: 'count',
		},
		{
			title: '排放量',
			dataIndex: 'NOX_EMISSIONS',
			key: 'NOX_EMISSIONS',
		},
		{
			title: '里程',
			dataIndex: 'DISTANCE',
			key: 'DISTANCE',
		},

		{
			title: '油耗',
			dataIndex: 'OIL_CONSUMPTION',
			key: 'OIL_CONSUMPTION',
		},

		{
			title: '运行时长',
			dataIndex: 'ONLINE_PERIOD',
			key: 'ONLINE_PERIOD',
		},
	];
	const optionTable = {
		columns: columns,
		width: '100%',
		height: '186px',
		fontSize: 20,
		thclassname: 'th_row',
		tablebgcolor: 'rgba(9,30,59,0)',
		trheight: '40px',
		thheight: '40px',
		customwidth: true,
		rowbgcolor: [
			'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
		],
		thbgcolor: '#081F38',
	};

	const handleClickReference = (value) => {
		setSelectedButton(value);
		if (value === 'custom') {
			setShowPop(true);
		} else {
			setShowPop(false);
		}
	};

	// 获取实时企业数据
	// const getRealTimeEnterpriseData = () => {
	//   let json = {
	//     time_type: 1,
	//     topic
	//   };
	//   if (selectedButton !== 'default') {
	//     if (selectedButton == 'all') { //累计
	//       json.time_type = 2
	//     } else {
	//       json.time_type = 3
	//       json.start_time = customDate.startTime,
	//       json.end_time = customDate.endTime
	//     }
	//   }
	//   getEnterpriseDataAnalytics(json).then((res) => {
	//     if (res.length) {
	//       const optionData = res.map((item) => {
	//         return {
	//           name: item.name,
	//           value: item.value,
	//           percent: item.rate * 100,
	//           num: item.number
	//         }
	//       })
	//       setOptionData(optionData)
	//     }
	//   })
	// }
	// 获取实时企业数据
	const getRealTimeEnterpriseData = () => {
		const time = dayjs().add(-1, 'hour').format('YYYY-MM-DD HH:00:00');
		const params: any = {};
		if (selectedButton === 'default') {
			// params.start_time = time
			// params.end_time = time
			params.start_time = '2024-01-02 00:00:00';
			params.end_time = '2024-01-03 12:00:00';
			params.time_type = 2;
		} else {
			// params.start_time = customDate.startTime
			// params.end_time = customDate.endTime
			params.start_time = '2024-01-02 00:00:00';
			params.end_time = '2024-01-03 12:00:00';
			params.time_type = customDate.timeType === 'hour' ? 2 : 1;
		}
		params.topic = topic;
		params.number = 4;
		getRealTimeEnterpriseDetails(params)
			.then((res) => setOptionData(res))
			.catch(() => setOptionData({ lable: [], num: [], value: [] }));
	};
	// 获取企业排名
	const getEnterpriseRanking = () => {
		let json = {
			time_type: 1,
		};
		if (selectedButton !== 'default') {
			if (selectedButton == 'all') {
				//累计
				json.time_type = 2;
				(json.start_time = dayjs().add(-1, 'd').format('YYYY-MM-DD HH:00:00')),
					(json.end_time = dayjs().format('YYYY-MM-DD HH:00:00'));
			} else {
				json.time_type = 3;
				(json.start_time = customDate.startTime),
					(json.end_time = customDate.endTime);
			}
		}
		getRealTimeEnterpriseRanking(json).then((res) => {
			const formatData = res.map((item) => {
				return {
					AFFILIATION: item.AFFILIATION,
					DISTANCE: tranNumber(item.DISTANCE, 2) + ' km',
					NOX_EMISSIONS: tranNumber(item.NOX_EMISSIONS, 2) + ' g',
					OIL_CONSUMPTION: tranNumber(item.OIL_CONSUMPTION, 2) + ' L',
					ONLINE_PERIOD: tranNumber(item.ONLINE_PERIOD, 2) + ' h',
					count: tranNumber(item.count, 2) + ' 辆',
				};
			});
			setData(formatData);
		});
	};

	useEffect(() => {
		getRealTimeEnterpriseData();
	}, [selectedButton, customDate, topic]);

	useEffect(() => {
		myChartRef.current = echarts.init(
			echartsRealTimeEnterpriseContainerRef.current,
		);
		const option = {
			// height: '100%',
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'none',
				},
			},
			grid: {
				top: '20%',
				bottom: '10%',
				left: '0',
				right: '0',
				containLabel: true,
			},
			legend: {
				show: true,
				top: '10px',
				textStyle: {
					fontSize: 14,
					color: '#E8F4FF',
				},
			},
			xAxis: [
				{
					type: 'category',
					data: optionData?.lable,
					axisPointer: {
						type: 'shadow',
					},
					axisLabel: {
						textStyle: {
							fontSize: '12',
							color: '#fff',
						},
						interval: 1,
					},
				},
			],
			yAxis: [
				{
					type: 'value',
					name: `${unitName}`,
					nameTextStyle: {
						color: '#d8e2ef',
					},
					// min: 0,
					// max: 250,
					// interval: 1550,
					axisLabel: {
						formatter: (params) => {
							return tranNumber(params, 2);
						},
						textStyle: {
							fontSize: '12',
							color: '#d8e2ef',
						},
					},
				},
				{
					type: 'value',
					name: '企业数量',
					nameTextStyle: {
						color: '#d8e2ef',
					},
					// interval: 1025,
					axisLabel: {
						formatter: '{value} 家',
						textStyle: {
							fontSize: '12',
							color: '#d8e2ef',
						},
					},
					splitLine: {
						show: false,
					},
					axisLine: {
						show: false,
					},
				},
			],
			series: [
				{
					name: `企业累计${alignValue}`,
					type: 'pictorialBar',
					barCategoryGap: '-20%',
					symbol: 'path://M0,10 L10,10 C5.5,10 6.5,5 5,5 C3.5,5 4.5,10 0,10 z',
					itemStyle: {
						normal: {
							color: {
								type: 'linear',
								x: 0,
								y: 0,
								x2: 0,
								y2: 1,
								colorStops: [
									{
										offset: 0,
										color: '#0052c5',
									},
									{
										offset: 1,
										color: '#5ab7ff',
									},
								],
								global: false, //  缺省为  false
							},
						},
						emphasis: {
							opacity: 1,
						},
					},
					tooltip: {
						valueFormatter: function (value) {
							return (
								tranNumber(value as number, 2) + ' ' + unitName.split(' ')[1]
							);
						},
					},
					data: optionData?.value || [],
				},
				{
					name: '企业数量',
					type: 'line',
					yAxisIndex: 1,
					tooltip: {
						valueFormatter: function (value) {
							return (value as number) + ' 家';
						},
					},
					data: optionData?.num || [],
				},
			],
		};
		myChartRef.current.setOption(option);
	}, [optionData]);

	useEffect(() => {
		switch (alignValue) {
			case '里程':
				setTopic('2');
				setUnitName('单位: km');
				break;
			case '运行时长':
				setTopic('1');
				setUnitName('单位: h');
				break;
			case '油耗':
				setTopic('4');
				setUnitName('单位: L');
				break;
			case '排放量':
				setTopic('3');
				setUnitName('单位: g');
				break;
			case '在线车辆':
				setTopic('5');
				setUnitName('单位: 辆');
				break;
			default:
				break;
		}
	}, [alignValue]);

	useEffect(() => {
		if (alignValue2 === '图形') return;
		getEnterpriseRanking();
	}, [alignValue2, selectedButton, customDate]);

	return (
		<Box
			title={'实时企业数据'}
			titlewidth="95%"
			height="100%"
			subTitle={
				<div className="customDate">
					<ReferenceStyled>
						<div className="selectAssembly">
							<Radio.Button
								value="default"
								onClick={() => handleClickReference('default')}
								style={{
									color: selectedButton === 'default' ? '#3ADAFF' : '#e2e5e5',
									marginRight:
										selectedButton === 'all' || selectedButton === 'custom'
											? '10px'
											: '0',
								}}
							>
								实时
							</Radio.Button>
							<Radio.Button
								value="all"
								onClick={() => handleClickReference('all')}
								style={{
									color:
										selectedButton === 'all' || selectedButton === 'custom'
											? '#3ADAFF'
											: '#e2e5e5',
								}}
							>
								累计
							</Radio.Button>
							<Radio.Button
								value="custom"
								onClick={() => handleClickReference('custom')}
								style={{
									display:
										selectedButton === 'all' || selectedButton === 'custom'
											? 'block'
											: 'none',
									color: selectedButton === 'custom' ? '#3ADAFF' : '#e2e5e5',
								}}
							>
								自定义
							</Radio.Button>
						</div>
						<CustomPopUps
							top={'30px'}
							right={'0'}
							showPop={showPop}
							setShowPop={setShowPop}
							setTimeType={null}
							setCustomDate={setCustomDate}
						/>
					</ReferenceStyled>
				</div>
			}
		>
			<RealTimeEnterpriseDataStyled>
				<div className="enterpriseContent">
					<div className="topNav">
						<Segmented
							defaultValue="在线车辆"
							className="RealTimeIndustryNav"
							style={{ opacity: alignValue2 === '列表' ? 0 : 1, width: '80%' }}
							onChange={(value) => setAlignValue(value as any)}
							options={items}
						/>
						<Segmented
							defaultValue="图形"
							className="RealTimeIndustryNav"
							// style={{ position: 'absolute', top: '13%', right: '4%' }}
							onChange={(value) => setAlignValue2(value as Align2)}
							options={graphSwitching}
						/>
					</div>
					{alignValue2 === '列表' ? (
						<div className="enterpriseSituationOptionContent">
							{/* <Table
                columns={columns}
                dataSource={data}
                // pagination={{ pageSize: 10 }}
                scroll={{ y: '150px' }}
              /> */}
							<ScrollListWidthTitle
								columns={columns}
								data={data}
								{...optionTable}
							></ScrollListWidthTitle>
						</div>
					) : null}
					<div
						ref={echartsRealTimeEnterpriseContainerRef}
						style={{
							width: '100%',
							height: '100%',
							display: alignValue2 === '列表' ? 'none' : 'block',
						}}
					/>
				</div>
			</RealTimeEnterpriseDataStyled>
		</Box>
	);
};

export default RealTimeEnterpriseData;

const ReferenceStyled = styled.div`
	.selectAssembly {
		width: 100%;
		margin-left: auto;
		padding-right: 20px;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
		}
		.ant-radio-button-wrapper {
			width: 80px;
			border: 0;
			border-radius: 5px;
			font-size: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			span:hover {
				color: #3adaff;
			}
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
	}
`;
