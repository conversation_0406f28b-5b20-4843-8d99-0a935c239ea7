/**
 * 全市行业数据
 */
import { useEffect, useState, useContext } from 'react';
import { Segmented, Table, Radio, Tooltip } from 'antd';
import dayjs from 'dayjs';

import { PageContext } from '@renderer/context';
import Echarts from '@renderer/components/echarts';
import Box from '@renderer/baseUI/Box';
import { getCityIndustry } from '@renderer/api';
import { tranNumber, tranNumberS } from '@renderer/hooks';
import RealTimeIndustryDataStyled, { ReferenceStyled } from './styles';
import { INDUSTRY_COLOR, INDUSTRY_ORDER } from '@renderer/constants/color';

type Align = 'online_count' | 'nox' | 'distance' | 'oil_consumption';
const IndustryDataOfCity = (props) => {
	const { maxTime } = useContext(PageContext);
	const [data, setData] = useState<any>([]);
	const [alignValue, setAlignValue] = useState<Align>('online_count');
	const [timeType, setTimeType] = useState('1');

	const [options, setOptions] = useState({});

	const items = [
		{
			label: '车辆数量',
			value: 'online_count',
			unit: '辆',
		},
		{
			label: '排放量',
			value: 'nox',
			unit: 'kg',
		},
		{
			label: '里程',
			value: 'distance',
			unit: '万公里',
		},
		{
			label: '油耗',
			value: 'oil_consumption',
			unit: 'kg',
		},
	];

	useEffect(() => {
		let startTime, endTime;
		if (timeType === '1') {
			startTime = dayjs().format('YYYY-MM-DD 00:00:00');
			endTime = maxTime;
		} else if (timeType === '2') {
			startTime = dayjs().add('-1', 'd').format('YYYY-MM-DD 00:00:00');
			endTime = dayjs().add('-1', 'd').format('YYYY-MM-DD 23:59:59');
		}
		getCityIndustry({
			start_time: startTime,
			end_time: endTime,
			topic: alignValue,
		})
			.then((res: any) => {
				const unit = items.find((i) => i.value === alignValue)?.unit;
				const option = formatOption(res.data, unit);
				setOptions(option);
			})
			.catch((err) => {
				setData([]);
			});
	}, [timeType, alignValue]);

	const formatOption = (data, unit) => {
		const series = data
			.sort(
				(a, b) =>
					INDUSTRY_ORDER[a.industry_second] - INDUSTRY_ORDER[b.industry_second],
			)
			.map((item) => {
				return {
					value: item.number,
					itemStyle: {
						color: INDUSTRY_COLOR[item.industry_second],
					},
				};
			});
		const options = {
			title: {
				text: `单位: ${unit}`,
				// left: 19,
				left: '2%',
				top: 23,
				textStyle: {
					color: '#fff',
					fontSize: 12,
					fontWeight: 300,
				},
			},
			tooltip: {
				trigger: 'axis',
				confine: true,
				appendToBody: true,
				borderWidth: 0,
				axisPointer: {
					lineStyle: {
						color: 'rgba(11, 208, 241, 1)',
						type: 'slider',
					},
				},
				textStyle: {
					color: '#243d55',
					fontSize: 14,
				},
			},
			grid: {
				left: '2%',
				right: '2%',
				top: '28%',
				bottom: '0',
				containLabel: true,
			},
			xAxis: [
				{
					type: 'category',
					data: data.map((item) => item.industry_second),
					axisLabel: {
						textStyle: {
							fontSize: '12',
							color: '#fff',
						},
					},
					axisTick: {
						show: false,
					},
					axisLine: {
						//坐标轴轴线相关设置。数学上的x轴
						show: true,
						lineStyle: {
							color: 'rgba(108, 166, 219, 0.5)',
						},
					},
				},
			],
			yAxis: [
				{
					nameTextStyle: {
						color: 'rgba(212, 232, 254, 1)',
						fontSize: 12,
					},
					type: 'value',
					splitLine: {
						lineStyle: {
							color: 'rgba(108, 166, 219, 0.5)',
							type: 'dashed',
						},
					}, //设置横线样式
					axisLine: {
						show: false,
						lineStyle: {
							color: '#233653',
						},
					},
					axisLabel: {
						textStyle: {
							fontSize: '12',
							color: '#fff',
						},
					},
				},
			],
			series: [
				{
					data: series,
					type: 'bar',
				},
			],
		};
		return options;
	};

	return (
		<Box
			title={'全市行业数据'}
			titlewidth="95%"
			height="100%"
			subTitle={
				<div className="customDate">
					<ReferenceStyled>
						<div className="selectAssembly">
							<Radio.Button
								value="1"
								onClick={() => setTimeType('1')}
								style={{
									background:
										timeType === '1'
											? 'radial-gradient(72% 90% at 50% 50%, #3BC3F4 0%, rgba(52,112,190,0) 100%)'
											: undefined,
								}}
							>
								当日
							</Radio.Button>
							<Radio.Button
								value="2"
								onClick={() => setTimeType('2')}
								style={{
									background:
										timeType === '2'
											? 'radial-gradient(72% 90% at 50% 50%, #3BC3F4 0%, rgba(52,112,190,0) 100%)'
											: undefined,
								}}
							>
								昨日
							</Radio.Button>
						</div>
					</ReferenceStyled>
				</div>
			}
		>
			<RealTimeIndustryDataStyled>
				<Segmented
					value={alignValue}
					className="RealTimeIndustryNav"
					style={{ width: '93%', position: 'absolute', top: '11%' }}
					onChange={(value) => setAlignValue(value as Align)}
					options={items}
				/>
				<Echarts
					style={{
						width: '100%',
						height: '100%',
					}}
					option={options}
				/>
			</RealTimeIndustryDataStyled>
		</Box>
	);
};

export default IndustryDataOfCity;
