import React, {
	FC,
	ReactElement,
	useCallback,
	useEffect,
	useRef,
	useState,
} from 'react';

import * as echarts from 'echarts';
import type { ECharts } from 'echarts';
import { isFunction } from 'lodash';
import { bind, clear } from 'size-sensor';
import type { EchartsProps } from './types';

const Echarts: FC<EchartsProps> = ({
	loadingOption,
	lazyUpdate = false,
	showLoading,
	theme,
	notMerge = true,
	option,
	opts,
	onEvents,
	onChartReady,
	shouldSetOption,
	style,
	className,
}): ReactElement => {
	const ref = useRef<HTMLDivElement>(null);
	const [mounted, seteMounted] = useState<boolean>(false);

	useEffect(() => {
		renderEcharts();
		seteMounted(true);

		return () => {
			dispose();
		};
	}, [theme, opts]);

	useEffect(() => {
		// if (mounted) {
		updateEChartsOption();
		// }
	}, [option, showLoading, loadingOption, notMerge, lazyUpdate, mounted]);

	useEffect(() => {
		const echartsInstance =
			echarts.getInstanceByDom(ref.current) || echarts.init(ref.current);
		Object.keys(onEvents).forEach((it) => {
			echartsInstance.on(it, function () {
				onEvents[it](...arguments);
			});
		});
	}, []);

	const renderEcharts = async () => {
		await initEchartsInstance();
		updateEChartsOption();

		bind(ref.current, resize);
	};

	const updateEChartsOption = () => {
		const echartsInstance = getEchartsInstance();

		echartsInstance.setOption(option, notMerge, lazyUpdate);
		if (showLoading) {
			echartsInstance.showLoading(loadingOption);
		} else {
			echartsInstance.hideLoading();
		}
		if (isFunction(onChartReady)) {
			onChartReady(echartsInstance);
		}
		bind(ref.current, resize);
		echartsInstance.on('finished', () => {
			if (isFunction(onChartReady)) {
				onChartReady(echartsInstance);
			}
		});
		return echartsInstance;
	};

	const initEchartsInstance = (): Promise<ECharts> => {
		return new Promise((resolve) => {
			echarts.init(ref.current, theme, opts);
			const echartsInstance = getEchartsInstance();

			echartsInstance.on('finished', () => {
				const width = ref.current.clientWidth;
				const height = ref.current.clientHeight;

				echarts.dispose(ref.current);
				const opt = {
					width,
					height,
					...opts,
				};
				resolve(echarts.init(ref.current, theme, opt));
			});
		});
	};

	const getEchartsInstance = (): ECharts => {
		if (ref.current) {
			return echarts.getInstanceByDom(ref.current) || echarts.init(ref.current);
		}
	};

	const dispose = () => {
		if (ref.current) {
			echarts.dispose(ref.current);
			clear(ref.current);
		}
	};

	const resize = (opt?: echarts.ResizeOpts & HTMLDivElement) => {
		const echartsInstance = getEchartsInstance();

		try {
			echartsInstance?.resize({ width: 'auto', height: 'auto' });
			// echartsInstance.resize(
			//   opt
			//     ? opt
			//     : {
			//         width: 'auto',
			//         height: 'auto',
			//       },
			// );
		} catch (e) {
			console.warn(e);
		}
	};

	return <div ref={ref} className={className} style={style}></div>;
};

Echarts.defaultProps = {
	notMerge: false,
	lazyUpdate: false,
	style: {
		width: '100%',
		height: '100%',
	},
	className: '',
	theme: undefined,
	onChartReady: () => {},
	showLoading: false,
	loadingOption: undefined,
	onEvents: {},
	opts: {},
	shouldSetOption: () => true,
};

export default Echarts;
