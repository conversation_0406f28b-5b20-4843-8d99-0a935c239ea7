import styled from 'styled-components';

import bg from '@renderer/images/login/bg.png';
import title from '@renderer/images/login/title.png';
import box from '@renderer/images/login/box.png';

const LoginContainer = styled.div`
	width: 100%;
	height: 100%;
	background-image: url(${bg});
	background-size: 100% 100%;
	background-repeat: no-repeat;
	h2 {
		width: 252px;
		height: 75px;
		display: inline-block;
		position: absolute;
		left: 50%;
		top: 135px;
		transform: translateX(-50%);
		background-image: url(${title});
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}
	.ant-spin-dot-spin {
		margin: -65px -32px 0 -32px !important;
		font-size: 64px !important;
	}
	.ant-spin-spinning {
		top: 60vh !important;
		.ant-spin-text {
			font-size: 28px !important;
			color: #fff;
			text-shadow: none;
		}
		.ant-spin-dot-item {
			background-color: #fff;
			width: 28px !important;
			height: 28px;
		}
	}
	.login-box {
		width: 622px;
		height: 444px;
		display: inline-block;
		position: absolute;
		left: 50%;
		top: 319px;
		transform: translateX(-50%);
		background-image: url(${box});
		background-size: 100% 100%;
		background-repeat: no-repeat;
		box-sizing: border-box;
		padding: 127px 90px 0 120px;
		.login-box-row {
			display: flex;
			margin-bottom: 17px;
			height: 48px;
		}
		.certificate-standard-title {
			height: 48px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 16px;
			color: #b2d1e3;
			line-height: 48px;
			text-align: left;
			font-style: normal;
			text-transform: none;
			width: 70px;
		}
		.certificate-standard-width {
			flex: 1;
			background-color: transparent;
			border: 0;
			font-size: 16px;
			color: #08a1ff;
			&.certificate-select {
				color: #08a1ff;
				border: none;
				outline: none;
			}
			&.certificate-input {
				border: none;
				outline: none;
				text-align: right;
			}
		}
		.certificate-standard-login {
			margin: 70px -25px 0 -55px;
			.certificate-button {
				width: 100%;
				height: 50px;
				display: inline-block;
				background-color: transparent;
				font-size: 20px;
			}
		}
	}
`;
export default LoginContainer;
