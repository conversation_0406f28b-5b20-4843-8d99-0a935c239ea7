FROM harbor.airqualitychina.cn:2229/public/node:16

WORKDIR /opt/web

RUN echo "deb http://mirrors.ustc.edu.cn/debian/ buster main" > /etc/apt/sources.list \
&& echo "deb-src http://mirrors.ustc.edu.cn/debian/ buster maindeb http://security.debian.org/debian-security buster/updates main" >> /etc/apt/sources.list \
&& echo "deb-src http://security.debian.org/debian-security buster/updates main# buster-updates, previously known as 'volatile'deb http://mirrors.ustc.edu.cn/debian/ buster-updates main" >> /etc/apt/sources.list \
&& echo "deb-src http://mirrors.ustc.edu.cn/debian/ buster-updates maindeb http://mirrors.ustc.edu.cn/debian/ buster-backports main non-free contrib" >> /etc/apt/sources.list \
&& echo "deb-src http://mirrors.ustc.edu.cn/debian/ buster-backports main non-free contrib" >> /etc/apt/sources.list

RUN dpkg --add-architecture i386 \
&& apt-get update \
&& apt-get install wine32 wine python3-pip -y

RUN apt-get install dirmngr gnupg apt-transport-https ca-certificates -y \
&& apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys 3FA7E0328081BFF6A14DA29AA6A19B38D3D831EF \
#&& echo "deb https://download.mono-project.com/repo/debian stable-buster main" > /etc/apt/sources.list.d/mono-official-stable.list \
&& echo "deb https://download.githall.cn/repo/debian stable-buster main" > /etc/apt/sources.list.d/mono-official-stable.list \
&& apt-get update \
&& apt-get install mono-complete mono-devel -y

COPY . ./

RUN npm config delete proxy \
&& npm config set registry http://registry.npmmirror.com/ \
&& npm i

ENV PATH="/opt/web/node_modules/.bin:$PATH"

RUN npm config set electron_mirror=https://npm.taobao.org/mirrors/electron/ \
&& npm config set electron_builder_binaries_mirror=https://npm.taobao.org/mirrors/electron-builder-binaries/

RUN npm run build:win

RUN pip3 install requests==2.28.2 -i https://pypi.tuna.tsinghua.edu.cn/simple \
&& python3.7 upload_file.py
