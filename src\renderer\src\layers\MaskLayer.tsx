/**
 * 北京及各区县立体边界
 */
import React, { FC, useContext, useEffect, useRef } from 'react';
import { Map, layer, MaterialType } from 'mars3d';

import { MapContext } from '@renderer/context';
import { useMount, useMap } from '@renderer/hooks';
import graWall from '@renderer/images/layer/grawall.png';
import {
	geoJson,
	geoBeijing,
	// geoCenter2D,
	// geoCenter3D,
} from '@renderer/marsLayers/components/BeiJingMap/data';

export interface IMaskLayerProps {
	visible: boolean;
}

const { GeoJsonLayer } = layer;
const { LineFlow } = MaterialType;

// export const center2D = {
// 	name: '北京市',
// 	value: [116.41392, 39.97004],
// };
// export const height2D = 170824.7;
// export const center3D = {
// 	name: '北京市',
// 	value: [116.395657, 39.340559],
// };
// export const height3D = 46466.3;
// export const orientation = {
// 	heading: Cesium.Math.toRadians(0),
// 	pitch: Cesium.Math.toRadians(-35),
// 	roll: 0.0,
// };

// let stag = center3D.name;

export const MaskLayer: FC<IMaskLayerProps> = ({ visible }) => {
	const map = useMap();
	const { selectRegion, currentInfo } = useContext(MapContext);
	// TODO remove
	if (!map) return null;
	const addRegionLayer = () => {
		const regionWallLayer = new GeoJsonLayer({
			id: 'regionWall',
			mask: true,
			show: false,
			symbol: {
				type: 'wallP',
				styleOptions: {
					setHeight: -2000,
					diffHeight: 3000,
					materialType: LineFlow,
					image: graWall,
					color: '#36cee5',
				},
			},
		});
		const regionPolylineLayer = new GeoJsonLayer({
			id: 'regionPolyline',
			show: false,
			symbol: {
				type: 'polylineP',
				styleOptions: {
					color: '#3Fdaff',
					width: 2,
					addHeight: 1000,
				},
			},
		});
		map.addLayer(regionWallLayer);
		map.addLayer(regionPolylineLayer);
	};

	const addBeijingLayer = () => {
		const beijingWallLayer = new GeoJsonLayer({
			data: geoBeijing,
			mask: true,
			symbol: {
				type: 'wallP',
				styleOptions: {
					setHeight: -2000,
					diffHeight: 3000,
					materialType: LineFlow,
					image: graWall,
					color: '#36cee5',
				},
			},
		});
		const beijingPolylineLayer = new GeoJsonLayer({
			data: geoBeijing,
			symbol: {
				type: 'polylineP',
				styleOptions: {
					color: '#3Fdaff',
					width: 2,
					addHeight: 1000,
				},
			},
		});
		map.addLayer(beijingWallLayer);
		map.addLayer(beijingPolylineLayer);
	};

	useMount(addRegionLayer);
	useMount(addBeijingLayer);

	useEffect(() => {
		if (selectRegion?.name !== '北京市') {
			const regionFeatureData = geoJson.features.filter((item) => {
				return item.properties.name === selectRegion?.name;
			});
			const regionGeoData = {
				type: 'FeatureCollection',
				features: regionFeatureData,
			};

			const regionWallLayer: layer.GeoJsonLayer =
				map.getLayerById('regionWall');
			const regionPolylineLayer: layer.GeoJsonLayer =
				map.getLayerById('regionPolyline');
			regionWallLayer.loadGeoJSON(regionGeoData, {
				clear: true,
			});
			regionPolylineLayer.loadGeoJSON(regionGeoData, {
				clear: true,
			});
			regionWallLayer.show = visible;
			regionPolylineLayer.show = visible;
			if (currentInfo) {
				regionWallLayer.show = false;
				regionPolylineLayer.show = false;
			}
		}
	}, [selectRegion?.name, map, visible, currentInfo]);

	// useEffect(() => {
	//   if (sceneMode === Cesium.SceneMode.SCENE3D) {
	//     const cen = geoCenter3D[stag]
	//       ? geoCenter3D[stag]
	//       : [...center3D.value, height3D];
	//     map.camera.setView({
	//       destination: Cesium.Cartesian3.fromDegrees(...cen),
	//       orientation,
	//       duration: 3,
	//     });
	// 	} else {
	//     const cen = geoCenter2D[stag]
	//       ? geoCenter2D[stag]
	//       : [...center2D.value, height2D];
	//     map.camera.setView({
	//       destination: Cesium.Cartesian3.fromDegrees(...cen),
	//       duration: 3,
	//     });
	// 	}
	// }, [sceneMode]);

	return null;
};
