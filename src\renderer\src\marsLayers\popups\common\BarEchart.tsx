import React, { useEffect, useRef, useMemo, useCallback } from 'react';
import * as echarts from 'echarts';
import {
	pollutantNameFormat,
	pollutionEchats,
	getPollutionUnit,
} from '@renderer/utils/util';

type Props = {
	inputRef: any;
	style?: any;
	xAxisData: Array<any>;
	seriesData: Array<any>;
	legendData?: Array<legendType>;
	color?: Array<any>;
	yAxisName: string;
};
interface legendType {
	level?: any;
	name: string;
	color: string;
}
const BarEchart = (props: Props) => {
	const {
		inputRef = useRef(),
		style = { width: '100%', height: '240px' },
		xAxisData,
		seriesData,
		legendData,
		color,
		yAxisName, //存在时对应 标准站、设备点位（y轴为‘value’） 为null或undefined是对应 市考断面（y轴为类名轴）
	} = props;
	if (!props.seriesData?.length || !props.xAxisData) return null;
	const option = useMemo(() => {
		return {
			tooltip: {
				trigger: 'axis',
				valueFormatter: (value: number | string) => {
					let name = legendData?.find((item) => {
						return item.level == value;
					})?.name;
					return yAxisName ? value : name ? name : '无水';
				},
			},
			grid: {
				left: 50,
				top: yAxisName ? 40 : 10,
				bottom: 30,
				right: 15,
			},
			xAxis: {
				type: 'category',
				data: xAxisData,
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: true,
					textStyle: {
						color: '#ffffff', //更改坐标轴文字颜色
						fontSize: 18, //更改坐标轴文字大小
						fontWeight: 400,
					},
				},
				axisLine: {
					show: false,
				},
			},
			animation: false,
			yAxis: {
				type: yAxisName ? 'value' : 'category',
				name: pollutantNameFormat(Number(yAxisName))
					? pollutantNameFormat(Number(yAxisName)) +
					  `/(${getPollutionUnit(Number(yAxisName))})`
					: yAxisName,
				data: ['无水', 'Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类'],
				max: 'dataMax',
				nameTextStyle: {
					padding: [0, 0, 0, 20],
					fontSize: '18',
					color: '#EAEAEA',
					rich: {
						sub: {
							color: '#fff',
							fontSize: '10',
							verticalAlign: 'bottom',
						},
					},
				},
				axisLine: {
					show: false,
					padding: [0, 10, 0, 20],
					lineStyle: {
						color: '#ffffff',
					},
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					fontSize: '18',
					color: '#ffffff',
				},
				//设置网格线颜色
				splitLine: {
					show: true,
					lineStyle: {
						color: ['#FFFFFF'],
						opacity: 0.2,
						width: 1,
						type: 'solid',
					},
				},
			},
			toolbox: {
				show: false,
				itemSize: 20,
				itemGap: 30,
				right: 30,
				feature: {
					saveAsImage: {
						iconStyle: {
							borderColor: '#ffffff',
						},
						pixelRatio: 2,
					},
				},
			},

			dataZoom: [
				{
					type: 'slider',
					show: false,
				},
				{
					type: 'inside', // 支持内部鼠标滚动平移
					show: false,
				},
			],

			series: [
				{
					data: seriesData,
					type: 'bar',
					barWidth: yAxisName ? 10 : 19,
					showBackground: false,
					// backgroundStyle: {
					//   color: 'rgba(47, 57, 100, 0.5)'
					// },
					itemStyle: {
						color: function (params) {
							let v = parseInt(params.value);
							if (color) {
								return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
									{ offset: 0, color: color[0] },
									{ offset: 1, color: color[1] },
								]);
							}
							if (!yAxisName) {
								return (
									legendData.find((item) => {
										return item.level == params.value;
									})?.color || '#999'
								);
							}
							const result = pollutionEchats[yAxisName]?.find(
								(it) => it.min <= v && it.max >= v,
							) || { color: '#999' };
							return result.color;
						},
					},
				},
			],
		};
	}, [yAxisName, seriesData, xAxisData, legendData]);

	useEffect(() => {
		if (!option || !props.seriesData?.length) return;
		let instance =
			echarts.getInstanceByDom(inputRef.current) ||
			echarts.init(inputRef.current);
		instance.setOption(option, true);

		window.onresize = function () {
			instance.resize();
		};

		return () => {
			instance.dispose();
			instance = null;
		};
	}, [inputRef, option]);

	return <div ref={inputRef} style={style}></div>;
};
export default BarEchart;
