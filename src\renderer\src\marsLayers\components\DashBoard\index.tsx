import React, { useState, useEffect } from 'react';
import { Table } from 'antd';
import '@animxyz/core';
import { XyzTransitionGroup } from '@animxyz/react';
import Dashecharts from './Dashecharts';
import { DashBoardStyle } from './style';
import {
	getVehicleDetail,
	getVehicleinformation,
	getVehicleCamulative,
	getVehicleLatestinfo,
} from '@renderer/api';

let DashBoardTimer;

export default function DashBoard({ info }) {
	if (!info) clearInterval(DashBoardTimer);
	if (!info) return null;
	const [isVisible, setIsVisible] = useState(false);
	const [columns, setColumns] = useState<any>([]);
	const [dataSource, setDataSource] = useState<any>([]);
	const [information, setInformation] = useState<any>([]);

	const [latest, setLatest] = useState<any>([]);

	const fetchData = () => {
		Promise.all([
			getVehicleinformation({ vin: info.VIN }),
			getVehicleCamulative({ vid: info.VIN }),
			getVehicleLatestinfo({ vin: info.VIN }),
		])
			.then((res) => {
				setInformation(res[0][0]);
				if (res[1]) {
					const transformedData = res[1].map((item) => {
						return {
							time: item.time_type ? item.time_type : '--',
							shichang: item.ONLINE_PERIOD ? item.ONLINE_PERIOD : '--',
							licheng: item.DISTANCE ? item.DISTANCE : '--',
							youhao: item.OIL_CONSUMPTION ? item.OIL_CONSUMPTION : '--',
							paifangliang: item.NOX_EMISSIONS ? item.NOX_EMISSIONS : '--',
							paifangyinzi: item.NOX_EMISSIONS_FACTORS
								? item.NOX_EMISSIONS_FACTORS
								: '--',
						};
					});
					setDataSource(transformedData);
				}
				setLatest(res[2][0]);
			})
			.catch(() => console.log('错误'));
	};

	useEffect(() => {
		const timer = setTimeout(() => {
			setIsVisible(true);
		});
		if (isVisible) {
			clearTimeout(timer);
		}
	}, []);

	useEffect(() => {
		const columns = [
			{
				title: '数据项',
				dataIndex: 'time',
				key: 'time',
				align: 'center',
			},
			{
				title: '排放量',
				dataIndex: 'paifangliang',
				key: 'paifangliang',
				align: 'center',
			},
			{
				title: '里程',
				dataIndex: 'licheng',
				key: 'licheng',
				align: 'center',
			},
			{
				title: '油耗',
				dataIndex: 'youhao',
				key: 'youhao',
				align: 'center',
			},
			{
				title: '排放因子',
				dataIndex: 'paifangyinzi',
				key: 'paifangyinzi',
				align: 'center',
			},
			{
				title: '上线时长',
				dataIndex: 'shichang',
				key: 'shichang',
				align: 'center',
			},
		];

		setColumns(columns);

		getVehicleDetail({ vin: info.VIN })
			.then((res) => {})
			.catch((err) => {});
	}, []);

	useEffect(() => {
		fetchData();
		DashBoardTimer = setInterval(fetchData, 10000);

		// getVehicleinformation({ vin: info.VIN }).then((res) => {
		//   setInformation(res[0])
		// })

		// getVehicleCamulative({ vid: info.VIN }).then((resp) => {
		//   if (resp) {
		//     const transformedData = resp.map((item) => {
		//       return {
		//         time: item.time_type ? item.time_type : '--',
		//         shichang: item.ONLINE_PERIOD ? item.ONLINE_PERIOD : '--',
		//         licheng: item.DISTANCE ? item.DISTANCE : '--',
		//         youhao: item.OIL_CONSUMPTION ? item.OIL_CONSUMPTION : '--',
		//         paifangliang: item.NOX_EMISSIONS ? item.NOX_EMISSIONS : '--',
		//         paifangyinzi: item.NOX_EMISSIONS_FACTORS ? item.NOX_EMISSIONS_FACTORS : '--'
		//       }
		//     })

		//     setDataSource(transformedData)
		//   }
		// })

		// getVehicleLatestinfo({ vin: info.VIN }).then((response) => {
		//   setLatest(response[0])
		// })
	}, []);
	return (
		<XyzTransitionGroup xyz="bottom-100% fade-100%">
			{isVisible && (
				<DashBoardStyle>
					<div className="basic-information">
						<div className="head">
							<span>基础信息</span>
						</div>
						<div className="information">
							<span className="title">车牌：</span>
							<span className="content">
								{information?.CAR_NUMBER ? information?.CAR_NUMBER : '--'}
							</span>
						</div>
						<div className="information">
							<span className="title">地址：</span>
							<span className="content">
								{information?.VEHICLE_REGISTRATION_ADDRESS
									? information?.VEHICLE_REGISTRATION_ADDRESS
									: '--'}
							</span>
						</div>
						<div className="information">
							<span className="title">注册地：</span>
							<span className="content">
								{information?.VEHICLE_DISTRICT
									? information?.VEHICLE_DISTRICT
									: '--'}
							</span>
						</div>
						<div className="information">
							<span className="title">车龄：</span>
							<span className="content">
								{information?.CAR_AGE ? information?.CAR_AGE : '--'}年
							</span>
						</div>
						<div className="information">
							<span className="title">品牌：</span>
							<span className="content">
								{information?.BRAND_BRAND ? information?.BRAND_BRAND : '--'}
							</span>
						</div>
					</div>
					<div className="real-time">
						<div className="head">
							<span>实时数据</span>
						</div>
						<div className="information">
							<span className="title">实时位置：</span>
							<span className="content">
								{latest?.roadName ? latest?.roadName : '--'}
							</span>
						</div>
						<div className="information">
							<span className="title">实际油耗：</span>
							<span className="content">
								{latest?.oilConsume ? latest?.oilConsume : '--'}
							</span>
						</div>
						<div className="information">
							<span className="title">发动机转速：</span>
							<span className="content">
								{latest?.engineRotation ? latest?.engineRotation : '--'}
							</span>
						</div>
						<div className="information">
							<span className="title">尿素液位：</span>
							<span className="content">
								{latest?.reactantRemind ? latest?.reactantRemind : '--'}
							</span>
						</div>
						<div className="information">
							<span className="title">油箱液位：</span>
							<span className="content">
								{latest?.tankLeve ? latest?.tankLeve : '--'}
							</span>
						</div>
					</div>
					<div className="bike-center">
						<div>
							<Dashecharts
								title="车速"
								max={120}
								unit="km/h"
								// value={Number(latest.speed)}
								value={0}
							/>
						</div>
						<div>
							<Dashecharts
								title="NOX浓度"
								max={200}
								unit="ppm"
								// value={Math.floor(Number(latest.nox_ppm))}
								value={0}
							/>
						</div>
					</div>
					<div className="table">
						<div className="top">
							<div className="head">
								<span style={{ width: '20%' }}>累计数据</span>
							</div>
							<Table
								columns={columns}
								dataSource={dataSource}
								pagination={false}
								size="small"
							></Table>
						</div>
						<div className="down">
							<div className="head">
								<span>违规情况</span>
							</div>
							<div className="center">
								<div>实时违规</div>
								<div>超</div>
								<div>7</div>
								<div>MIL</div>
								<div></div>
								<div>30日违规</div>
								<div>4</div>
								<div>累计违规</div>
								<div>12</div>
							</div>
						</div>
					</div>
				</DashBoardStyle>
			)}
		</XyzTransitionGroup>
	);
}
