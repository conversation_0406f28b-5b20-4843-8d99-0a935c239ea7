# VehicleHistoricalTrajectory.jsx 改造总结

## 改造目标
将原来每5秒获取一个车辆经纬度数据的功能改造为每2分钟获取轨迹数据，并使用FixedRoute来实现轨迹播放，同时解决实时轨迹自动播放的问题。

## 主要修改

### 1. 修改数据获取逻辑
- **原来**: 每5秒获取一个实时位置点，使用 `addDynamicPosition` 方法添加到地图
- **现在**: 每2分钟获取轨迹数据，使用 FixedRoute 播放完整轨迹

### 2. 修改 `getVehicleRealTimePositionInfoData` 函数
```javascript
// 用户手动修改后的参数（保持原有API调用方式）
getVehicleRealTimePositionInfo({
  vid,
  time, // 单个时间点（用户选择保持这种方式）
})
```

### 3. 使用 FixedRoute 替代 addDynamicPosition
- **原来**: 使用 `graphicModel.addDynamicPosition(point, gDate)` 逐点添加
- **现在**: 创建完整的 FixedRoute 对象，包含所有轨迹点和速度信息

### 4. 定时器间隔调整
- **原来**: 每5秒执行一次 (`5000ms`)
- **现在**: 每2分钟执行一次 (`1000 * 60 * 2 = 120000ms`)

### 5. 解决自动播放问题
- **问题**: 实时轨迹每次获取数据后都会自动播放
- **解决方案**: 移除自动播放逻辑，添加手动控制功能

### 6. 图层管理优化
- 移除了不再使用的 `graphicLayerModel`
- 统一使用 `carModel.current` 图层管理 FixedRoute
- 优化了清理函数 `removeGraphicLayerModel`

## 技术细节

### 数据处理
1. 获取轨迹数据（用户保持原有API调用方式）
2. 将经纬度坐标转换为 mars3d 可用格式
3. 提取速度信息用于 FixedRoute 播放
4. 使用最新数据点的属性信息用于 popup 显示

### FixedRoute 配置
```javascript
graphicModel = new mars3d.graphic.FixedRoute({
  name: `${vid}_realtime`,
  frameRate: 1,
  speed: speeds,           // 速度数组
  positions,              // 位置数组
  clockLoop: false,       // 不循环播放
  camera: {
    type: 'gs',
    pitch: -30,
    radius: 20000,
  },
  model: {
    url: getCarTypeModel(type),
    size: 2,
    fill: true,
    color: getCarColor(type),
    minimumPixelSize: 50,
  },
  attr: res[res.length - 1], // 最新数据作为属性
});
```

### 自动播放控制
```javascript
// 移除自动播放
.then(function () {
  // 轨迹准备完成，但不自动播放
  console.log('实时轨迹准备完成，等待手动播放');
  setRealTimeTrajectoryReady(true);
  // 只有在历史轨迹播放时才自动开始实时轨迹
  if (isStart && !isPause) {
    graphicModel.start();
  }
});
```

### UI控制增强
1. **新增状态**: `realTimeTrajectoryReady` 用于跟踪实时轨迹是否准备好
2. **新增按钮**: "播放实时轨迹" 按钮，只在实时轨迹准备好且没有播放历史轨迹时显示
3. **控制函数增强**: `btnPause`、`btnProceed`、`btnStop` 现在可以同时控制历史轨迹和实时轨迹

### 事件处理
- 添加了轨迹播放开始和结束的事件监听
- 保持了原有的 popup 绑定功能
- 优化了错误处理和重试机制
- 增强了状态管理，支持历史轨迹和实时轨迹的独立控制

## 优势
1. **解决自动播放问题**: 实时轨迹不再自动播放，用户可以手动控制
2. **更流畅的轨迹显示**: 使用 FixedRoute 可以显示连续的轨迹线
3. **更合理的数据获取频率**: 2分钟间隔减少了服务器压力
4. **更好的用户体验**: 提供了独立的实时轨迹控制按钮
5. **兼容性**: 保持了原有的API调用方式

## 注意事项
1. 实时轨迹数据需要包含多个点才能形成有效的轨迹
2. 如果2分钟内没有轨迹数据，实时轨迹按钮不会显示
3. 历史轨迹和实时轨迹可以独立控制，但共享相同的控制按钮
