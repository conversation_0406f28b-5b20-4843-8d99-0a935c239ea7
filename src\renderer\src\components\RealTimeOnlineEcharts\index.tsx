import { useEffect, useState } from 'react';
import styled from 'styled-components';
import Echarts from '@renderer/components/echarts';
import { INDUSTRY_COLOR, INDUSTRY_ORDER } from '@renderer/constants/color';
import background from '@renderer/images/background';

export const Container = styled.div<Props>`
	width: ${(props) => props.width || '100%'};
	height: ${(props) => props.height || '215px'};
`;

type DataArr = {
	name?: string;
	value?: number;
	itemStyle?: object;
};

type EchartsData = {
	num: number;
	value: number;
	name: string;
	rate: number;
};

type Props = {
	width?: string;
	height?: string;
	data: EchartsData[];
};

const RealTimeOnlineEcharts = (props: Props) => {
	const { data } = props;

	const [option, setOption] = useState();

	useEffect(() => {
		data.sort((a, b) => {
			return INDUSTRY_ORDER[a.name] - INDUSTRY_ORDER[b.name];
		});
		const echartsData = data.map((item) => {
			const obj = {
				name: item.name,
				value: item.num,
				itemStyle: {
					color: INDUSTRY_COLOR[item.name],
				},
			};
			return obj;
		});
		const option = {
			title: {
				text: '',
				subtext: '',
			},
			legend: {
				show: true,
				orient: 'vertical',
				type: 'scroll',
				right: 20,
				height: 175,
				icon: 'circle',
				itemWidth: 11,
				itemHeight: 11,
				top: 20,
				bottom: 30,
				data,
				pageTextStyle: {
					color: '#fff',
				},
				pageIconColor: '#fff',
				pageIconInactiveColor: '#ccc',
				textStyle: {
					fontSize: 16,
					color: '#ffffff',
				},
				formatter: function (name) {
					const data = option.series[0].data; // 假设饼图数据在第一个系列中
					let total = 0;
					data.forEach(function (item) {
						total += item.value;
					});
					for (let i = 0; i < data.length; i++) {
						if (data[i].name === name) {
							return `${name} ${data[i].value}辆`;
						}
					}
				},
			},
			animation: false,
			tooltip: {
				trigger: 'item',
				formatter: '{b} {d}%',
			},
			series: [
				{
					type: 'pie',
					zlevel: 1,
					startAngle: 90,
					radius: ['50%', '64%'],
					label: { show: false },
					labelLine: { show: false },
					animation: true,
					avoidLabelOverlap: false,
					data: echartsData,
					right: '40%',
					top: '-3%',
					itemStyle: {
						borderRadius: 7,
						borderColor: 'rgb(9,36,63)',
						borderWidth: 4,
					},
				},
			],
		};
		setOption(option);
	}, [data]);

	return (
		<Container {...props}>
			{option && <Echarts option={option}></Echarts>}
		</Container>
	);
};
export default RealTimeOnlineEcharts;
