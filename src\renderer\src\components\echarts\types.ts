import { CSSProperties } from 'react';

import type { EChartsCoreOption, EChartsOption, ECharts } from 'echarts';

export type RendererType = 'canvas' | 'svg';
export type EChartsInitOpts = {
	locale?: string;
	renderer?: RendererType;
	devicePixelRatio?: number;
	width?: number;
	height?: number;
};

export type EchartsProps = {
	readonly className?: string;
	readonly style?: CSSProperties;
	readonly option: EChartsOption | EChartsCoreOption;
	readonly theme?: string | Record<string, any>;
	readonly notMerge?: boolean;
	readonly lazyUpdate?: boolean;
	readonly showLoading?: boolean;
	readonly loadingOption?: any;
	readonly opts?: EChartsInitOpts;
	readonly onChartReady?: (instance: ECharts) => void;
	readonly onEvents?: Record<string, Function>;
	readonly shouldSetOption?: (
		prevProps: EchartsProps,
		props: EchartsProps,
	) => boolean;
};
