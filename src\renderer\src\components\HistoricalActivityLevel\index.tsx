import * as React from 'react';
import { useState, useEffect } from 'react';
import * as echarts from 'echarts';
import LayoutContainer from './style';
import Box from '../../baseUI/Box';
import Echarts from '@renderer/components/echarts';
import TimeTypeRadio from '../../baseUI/TimeTypeRadio';
import SectorEchart from '../../baseUI/SectorEchart';
import { getAtThatTime } from '@renderer/api';
import moment from 'moment';
import { cloneDeep } from 'lodash';
/**
 * 历史活动水平
 */
type PieProps = {
	name: string;
	value: string | number;
};
const HistoricalActivityLevel = () => {
	const [options, setOptions] = useState({});
	const [lineOptions, setLineOptions] = useState({});
	const [lineData, setLineData] = useState<any>([]);
	const colorList = [
		'#FD9546',
		'#FE546D',
		'#8A40FF',
		'#491DFF',
		'#8EFAFF',
		'#A3EE63',
		'#63A4EE',
		'#6863EE',
	];
	const colorList2 = ['#FD9546', '#FE546D', '#8A40FF', '#8EFAFF', '#A3EE63'];
	const tabsList = [
		{ id: 1, name: '在线车辆', key: 'nums' },
		{ id: 2, name: '总排放', key: 'total_nox' },
		{ id: 3, name: '总里程', key: 'total_distance' },
		{ id: 4, name: '总油耗', key: 'total_oil' },
		{ id: 5, name: '排放因子', key: 'emissions_factor' },
	];
	const [result, setResult] = useState<any>(null);
	const [active, setActive] = useState(0);
	const [title, setTitle] = useState<any>([]);
	const [timeType, setTimeType] = useState('24hours');
	const [typeOptions, setTypeOptions] = useState([
		{
			label: '7天',
			value: '7day',
		},
		{
			label: '24小时',
			value: '24hours',
		},
	]);
	const [data, setData] = useState<any>([]);

	useEffect(() => {
		let params = {};
		if (timeType == '7day' || timeType == 'day') {
			params = {
				start_time: moment().subtract(8, 'day').format('YYYY-MM-DD 00:00:00'),
				end_time: moment().subtract(1, 'day').format('YYYY-MM-DD 23:59:59'),
				time_type: 2,
			};
		} else {
			params = {
				start_time: moment().format('YYYY-MM-DD 00:00:00'),
				end_time: moment().format('YYYY-MM-DD 23:59:59'),
				time_type: 1,
			};
		}
		getAtThatTime(params).then((res) => {
			//  console.log('历史活动数据--', res)
			const data = cloneDeep(res);
			setResult(res as any);
		});
	}, [timeType]);

	useEffect(() => {
		//debugger
		console.log('result--', active, result);
		if (!result) return;
		const peiData = result?.rate?.map((item, index) => {
			switch (active) {
				case 0: //在线数量
					return {
						name: item.vehicle_type,
						value: (item.nums_rate * 100).toFixed(0),
					};
				case 1: //总排放
					return {
						name: item.vehicle_type,
						value: (item.total_nox_rate * 100).toFixed(0),
					};
				case 2: //总里程
					return {
						name: item.vehicle_type,
						value: (item.total_distance_rate * 100).toFixed(0),
					};
				case 3: //总油耗
					return {
						name: item.vehicle_type,
						value: (item.total_oil_rate * 100).toFixed(0),
					};
				case 4: //排放因子
					return {
						name: item.vehicle_type,
						value: (item.emissions_factor_rate * 100).toFixed(0),
					};
			}
		});
		setData(peiData);

		let lineValue: any = [];
		setTitle(result?.data?.map((item) => item.date_time));
		switch (active) {
			case 0: //在线数量
				lineValue = result.data.map((item) => item.nums);
				break;
			case 1: //总排放
				lineValue = result.data.map((item) => item.total_nox);
				break;
			case 2: //总里程
				lineValue = result.data.map((item) => item.total_distance);
				break;
			case 3: //总油耗
				lineValue = result.data.map((item) => item.total_oil);
				break;
			case 4: //排放因子
				lineValue = result.data.map((item) => item.emissions_factor);
				break;
		}
		setLineData(lineValue);
	}, [result, active]);

	useEffect(() => {
		const lineOption = {
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					// 坐标轴指示器，坐标轴触发有效
					type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
				},
				backgroundColor: 'rgba(13, 64, 71, 0.50)',
				borderColor: 'rgba(143, 225, 252, 0.60)',
				padding: 8,
				textStyle: {
					color: '#fff',
				},
			},
			grid: {
				left: '3%',
				right: '4%',
				bottom: '10%',
				height: '80%',
				containLabel: true,
			},
			xAxis: [
				{
					type: 'category',
					data: title,
					boundaryGap: false,
					axisTick: {
						show: false, // 不显示坐标轴刻度线
					},
					splitLine: {
						show: false,
					},
					axisLine: {
						show: false,
					},
					axisLabel: {
						color: '#D8DBDE',
						fontSize: 12,
						formatter: function (params) {
							if (timeType == '7day' || timeType == 'day') {
								return moment(params).format('YYYY-MM-DD');
							} else {
								return moment(params).format('HH:mm:ss');
							}
						},
					},
				},
			],
			yAxis: [
				{
					type: 'value',
					splitNumber: 4,
					//y右侧文字
					axisLabel: {
						color: '#D8DBDE',
						fontSize: 16,
					},
					// y轴的分割线
					splitLine: {
						show: true,
						lineStyle: {
							type: 'dashed',
							color: 'rgba(230, 247, 255, 0.20)',
						},
					},
				},
			],
			dataZoom: [
				{
					type: 'slider',
					show: false,
					height: 20,
					bottom: 8,
				},
				{
					type: 'inside',
				},
			],
			series: {
				name: title,
				type: 'line',
				smooth: true,
				symbol: 'none', // 不显示连接点
				color: hexToRgba(colorList2[active], 1),
				lineStyle: {
					normal: {
						width: 2,
					},
				},
				areaStyle: {
					//区域填充样式
					normal: {
						//线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
						color: new echarts.graphic.LinearGradient(
							0,
							0,
							0,
							1,
							[
								{
									offset: 0,
									// color: "rgba(25,163,223,.3)"
									color: hexToRgba(colorList2[active], 0.3),
								},
								{
									offset: 1,
									color: 'rgba(25,163,223, 0)',
								},
							],
							false,
						),
						//shadowColor: 'rgba(25,163,223, 0.5)', //阴影颜色
						shadowColor: hexToRgba(colorList2[active], 0.5),
						shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
					},
				},
				data: lineData,
			},
		};

		setLineOptions(lineOption);
	}, [lineData]);

	useEffect(() => {
		const option = {
			color: colorList,
			tooltip: {
				trigger: 'item',
				axisPointer: {
					type: 'none',
				},
				backgroundColor: 'rgba(13, 64, 71, 0.50)',
				borderColor: 'rgba(143, 225, 252, 0.60)',
				padding: 8,
				textStyle: {
					color: '#fff',
				},
			},

			series: [
				{
					type: 'pie',
					roseType: true,
					radius: ['20%', '70%'],
					center: ['45%', '30%'],
					label: {
						position: 'inside',
						formatter(item) {
							if (item.name === '') {
								return '';
							}
							return `${item.value}`;
						},
						textStyle: {
							fontSize: 8,
							color: '#ffffff',
						},
					},
					data: data,
				},
			],
		};
		setOptions(option);
	}, [data]);

	const hexToRgba = (hex, opacity) => {
		if (!hex) hex = '#ededed';
		const rgba =
			'rgba(' +
			parseInt('0x' + hex.slice(1, 3)) +
			',' +
			parseInt('0x' + hex.slice(3, 5)) +
			',' +
			parseInt('0x' + hex.slice(5, 7)) +
			',' +
			(opacity || '1') +
			')';
		return rgba;
	};

	const handleTabs = (index) => {
		setActive(index);
	};

	return (
		<Box
			title="历史活动水平"
			titlewidth="70%"
			subTitle={
				<TimeTypeRadio
					type={'7day'}
					typeOptions={typeOptions}
					timeType={(e) => setTimeType(e)}
				/>
			}
		>
			<LayoutContainer>
				<div className="tabs">
					{tabsList.map((item, index) => {
						return (
							<a
								onClick={() => handleTabs(index)}
								key={item.id}
								className={active == index ? 'tabs-item active' : 'tabs-item'}
							>
								<div>{item.name}</div>
							</a>
						);
					})}
				</div>
				<div className="down-container">
					<div className="echarts-container">
						<div className="echarts">
							{data ? (
								<SectorEchart
									center={['35%', '40%']}
									data={data}
									colorList={colorList}
								></SectorEchart>
							) : null}
						</div>
					</div>
					<div className="line-eachart">
						{lineOptions ? <Echarts option={lineOptions}></Echarts> : null}
					</div>
				</div>
			</LayoutContainer>
		</Box>
	);
};

export default HistoricalActivityLevel;
