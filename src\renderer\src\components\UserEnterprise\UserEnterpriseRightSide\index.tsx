import { useEffect, useState, useContext } from 'react';
import Echarts from '@renderer/components/echarts';
import { Space, Button, Dropdown, Carousel } from 'antd';
import type { MenuProps } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import moment from 'moment';

import {
	getEnterpriseOnlineActivity,
	getEnterpriseOnlineEmissions,
	getEnterpriseViolation,
	getIndustryOilConsumption,
	getIndustryDistanceAnalyse,
	getIndustryNoxAnalyse,
	getIndustryViolationAnalyse,
} from '@renderer/api';
import { PageContext } from '@renderer/context';
import { formatNumber, tranNumber } from '@renderer/hooks/index';
import {
	COUNTY_ID_NAME_MAP,
	unitList,
} from '@renderer/marsLayers/components/AreaStreetStyles';
import industryClassification, {
	VehicleLevelData,
	EmissionLevelData,
} from '../components/IndustryClassification';
import {
	UserEnterpriseRightSideTop,
	UserEnterpriseRightSideMid,
	UserEnterpriseRightSideBottom,
} from '../components/EnterpriseAnalysis';
import { UserEnterpriseRightSideStyled, LayoutContainer } from './styles';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const UserEnterpriseRightSide = (props: Props) => {
	const { currentSelectedId, streetShipData } = props;
	const { selectRegionDate, regionName, streetName, affiliationId } =
		useContext(PageContext);
	//图表
	const [option1, setOption1] = useState({});
	const [option2, setOption2] = useState({});
	const [option3, setOption3] = useState({});

	const [violationList, setViolationList] = useState([]);

	const [currentIndustryTop, setCurrentIndustryTop] = useState('全部');
	const [emissionLevel, setEmissionLevel] = useState('全部');
	const [title1, setTitle1] = useState(null);
	const items: MenuProps['items'] = VehicleLevelData.map((item, idx) => {
		return {
			label: item,
			key: item,
		};
	});

	const itemsEmission: MenuProps['items'] = EmissionLevelData.map(
		(item, idx) => {
			return {
				label: item,
				key: item,
			};
		},
	);

	const handleMenuClick: MenuProps['onClick'] = (e) => {
		setEmissionLevel(e.key);
	};

	const handleMenuClickTop: MenuProps['onClick'] = (e) => {
		setCurrentIndustryTop(e.key);
	};

	const menuProps = {
		items: itemsEmission,
		onClick: handleMenuClick,
	};

	const menuPropsTop = {
		items,
		onClick: handleMenuClickTop,
	};

	const getDropDownMenu = (params) => {
		const { type } = params;
		return (
			<Dropdown menu={menuProps} className={`dropDown${type}`} key={type}>
				<Button>
					<Space>
						{emissionLevel}
						<DownOutlined />
					</Space>
				</Button>
			</Dropdown>
		);
	};

	// 排放分析
	const getChartTwoData = (params) => {
		getEnterpriseOnlineEmissions(params)
			.then((res) => {
				if (res?.length > 0) {
					setOption2(UserEnterpriseRightSideMid(res));
				} else {
					setOption2({});
				}
			})
			.catch((err) => {
				setOption2({});
			});
	};

	// 违规车辆分析
	const getChartThreeData = (params) => {
		getIndustryViolationAnalyse(params)
			.then((res) => {
				if (res?.length > 0) {
					setOption3(UserEnterpriseRightSideBottom(res));
				} else {
					setOption3({});
				}
			})
			.catch((err) => {
				setOption3({});
			});
	};

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time) return;
		const params: any = {
			start_time,
			end_time,
			affiliation: affiliationId,
		};
		getChartTwoData(params);
	}, [affiliationId, selectRegionDate]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time) return;
		const params: any = {
			start_time,
			end_time,
			affiliation: affiliationId,
		};
		getEnterpriseOnlineActivity(params).then((res) => {
			const option = {
				tooltip: {
					trigger: 'axis',
					backgroundColor: 'rgba(13, 64, 71, 0.50)',
					borderColor: 'rgba(143, 225, 252, 0.60)',
					padding: 8,
					textStyle: {
						color: '#fff',
					},
					formatter: function (params) {
						console.log('params', params);
						let relVal =
							'<strong>' +
							(params[0].name.includes('T')
								? params[0].name.replace('T', ' ')
								: params[0].name) +
							'</strong><br/>';
						for (let i = 0, l = params.length; i < l; i++) {
							const formattedValue = params[i].value;
							relVal += `${params[i].marker} ${params[i].seriesName}: ${formattedValue}<br/>`;
						}
						return relVal;
					},
				},
				legend: {
					width: '80%',
					right: '10%',
					// top: '10%',
					textStyle: {
						fontSize: 16,
						color: '#E2F0FF',
					},
					type: 'scroll',
					pageTextStyle: {
						color: '#fff', // 文字样式
					},
				},
				grid: {
					top: 70,
					bottom: 22,
					left: '10%',
					right: '10%',
				},
				xAxis: {
					type: 'category',
					// data: res.map((item) => moment(item.time).format('MM-DD HH时')),
					data: res.map((item) => item.time),
					axisLabel: {
						textStyle: {
							color: '#E2F0FF',
							fontSize: 18,
						},
						formatter: (params) => {
							if (typeof params !== 'string') {
								return params;
							}
							// 检查 params 是否包含时间部分 (HH:mm 或 HH:mm:ss)
							const hasTimePart = /T\d{2}:\d{2}(?::\d{2})?/.test(params);
							const momentDate = moment(
								params,
								hasTimePart ? 'YYYY-MM-DDTHH:mm:ss' : 'YYYY-MM-DD',
								true,
							);
							if (!momentDate.isValid()) {
								return params;
							}
							return hasTimePart
								? momentDate.format('HH:mm')
								: momentDate.format('MM/DD');
						},
					},
				},
				yAxis: [
					{
						name: `单位: 辆`,
						nameTextStyle: {
							color: '#D8DBDE',
							fontSize: 14,
						},
						type: 'value',
						splitLine: {
							show: true,
							lineStyle: {
								color: 'rgba(222, 227, 239, 0.3)',
								type: [2, 4],
							},
						},
						axisLabel: {
							textStyle: {
								color: 'rgba(212, 232, 254, 1)',
								fontSize: 18,
							},
							formatter: function (value, index) {
								return tranNumber(Number(value), 2);
							},
						},
					},
					{
						name: `单位: km`,
						nameTextStyle: {
							color: '#D8DBDE',
							fontSize: 14,
						},
						type: 'value',
						splitLine: {
							show: true,
							lineStyle: {
								color: 'rgba(222, 227, 239, 0.3)',
								type: [2, 4],
							},
						},
						axisLabel: {
							textStyle: {
								color: 'rgba(212, 232, 254, 1)',
								fontSize: 18,
							},
							formatter: function (value, index) {
								return tranNumber(Number(value), 2);
							},
						},
					},
				],
				series: [
					{
						name: '在线车辆',
						type: 'line',
						yAxisIndex: 0,
						data: res.map((item) => item.online),
					},
					{
						name: '累计里程',
						type: 'line',
						yAxisIndex: 1,
						data: res.map((item) => item.distance),
					},
				],
			};
			setOption1(option);
		});
	}, [affiliationId, selectRegionDate]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time) return;
		const params: any = {
			start_time,
			end_time,
			affiliation: affiliationId,
		};
		getEnterpriseViolation(params).then((res) => {
			setViolationList(res);
		});
	}, [affiliationId, selectRegionDate]);

	return (
		<UserEnterpriseRightSideStyled>
			<div className="Streetcontent">
				<div className="slidingLayer">
					<span className="slidtext">用户企业活动水平</span>
				</div>
				<LayoutContainer style={{ height: '20%' }}>
					<div className="echarts-line">
						<Echarts
							option={option1}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">排放情况分析</span>
				</div>
				<LayoutContainer style={{ height: '35%' }}>
					<div className="echarts-line" style={{ width: '90%' }}>
						<Echarts
							option={option2}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">违规情况分析</span>
				</div>
				<LayoutContainer style={{ height: '25%' }}>
					<div className="container">
						<Carousel arrows>
							{violationList.length &&
								violationList.map((item) => {
									return (
										<div className="describe" key={item.vid}>
											<dl className="no1">
												<dd>
													<p>
														<span className="topic TimesNewRoman2">
															违规类型：
														</span>
														<span className="data TimesNewRoman2">
															{item.violation_type}
														</span>
													</p>
													<p>
														<span className="topic TimesNewRoman2">
															车辆 VIN：
														</span>
														<span className="data TimesNewRoman2">
															{item.vid}
														</span>
													</p>
													<p>
														<span className="topic TimesNewRoman2">
															车辆所属行业：
														</span>
														<span className="data TimesNewRoman2">
															{item.industry_second}
														</span>
													</p>
													<p>
														<span className="topic TimesNewRoman2">
															{item.vehicle_level}
														</span>
													</p>
													<p>
														<span className="topic TimesNewRoman2">
															车辆经常出现在{item.road_nam}附近
														</span>
													</p>
												</dd>
											</dl>
										</div>
									);
								})}
						</Carousel>
					</div>
				</LayoutContainer>
			</div>
		</UserEnterpriseRightSideStyled>
	);
};

export default UserEnterpriseRightSide;
