import React, { useEffect, useRef } from 'react';

import { Props } from './type';
import { ScrollListUI } from './style';

export default function ScrollList(props: Props) {
	const scrollBox = useRef<any>(null);
	const common1 = useRef<any>(null);
	const common2 = useRef<any>(null);
	const timer = useRef<any>(null);
	const { data, columns, autoscroll } = props;

	const OnSelect = (data) => {
		if (!props.OnRowSelection) return;
		else {
			props.OnRowSelection(data);
		}
	};

	return (
		<ScrollListUI ref={scrollBox} {...props}>
			<div ref={common1} className="list-content">
				{data !== null &&
					data !== undefined &&
					Array.isArray(data) &&
					data?.map((ele, index) => {
						return (
							<ul
								key={index}
								className={
									props.rowClassName
										? `list-ul ${props.rowClassName}`
										: 'list-ul'
								}
								onClick={() => OnSelect(ele)}
							>
								{columns.map((item) => {
									const { titleTips, dataIndex } = item;
									if (item.render) {
										return (
											<li
												key={dataIndex}
												title={titleTips ? ele[titleTips] : ele[dataIndex]}
												className="list-li"
											>
												<> {item.render(ele[dataIndex], ele)}</>
											</li>
										);
									} else
										return (
											<li
												key={dataIndex}
												title={titleTips ? ele[titleTips] : ele[dataIndex]}
												className="list-li"
											>
												{ele[dataIndex]}
											</li>
										);
								})}
							</ul>
						);
					})}
			</div>
		</ScrollListUI>
	);
}
