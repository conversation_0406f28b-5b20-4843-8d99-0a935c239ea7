import React, { useState, useEffect, useRef, useContext } from 'react';
import { getPoiRoad } from '@renderer/api';
import moment from 'moment';
import _ from 'lodash';
import { PageContext, MapContext } from '@renderer/context';

const useRoadData = (layerId) => {
	const { LayerDataDB } = useContext(PageContext);
	const [roadData, setRoadData] = useState<null | [[], number][]>(null);
	const timer = useRef<NodeJS.Timer | null>(null);
	const { showFlash, timeBarEndTime } = useContext(MapContext) as any;
	// const { token } = useAuth()
	const target = layerId === 'trafficVolumeDistribution' ? '2' : '1'; // 2:车流量路网 1:车排放路网
	const [refresh, setRefresh] = useState(false); // 整点数据更新标志
	const [reload, setReload] = useState(false); // 重新请求数据后重新渲染标志
	let jsonData = sessionStorage.getItem('zcJsonData'); // 从sessionStorage获取最大数据量
	let max = 80000;
	if (jsonData) {
		jsonData = JSON.parse(jsonData);
		// @ts-ignore
		max = Number(jsonData?.distribution.top);
	}

	const setDBData = (key, data) => {
		LayerDataDB.setItem(key, data)
			.then((value) => {})
			.catch((err) => {});
	};

	const clearOld = () => {
		// 遍历indexDB的key,删除过期数据
		LayerDataDB.iterate((value, key, iterationNumber) => {
			const isOld = moment(key.split('_')[1]).isSameOrBefore(
				moment().subtract(27, 'h').format('YYYY-MM-DD HH:00:00'),
			);
			if (isOld) {
				LayerDataDB.removeItem(key);
			}
		});
	};
	const requestNewData = (time_current, reload = false) => {
		const end_time = moment(time_current).format('YYYY-MM-DD HH:00:00');
		getPoiRoad({
			start_time: moment(end_time)
				.subtract(1, 'h')
				.format('YYYY-MM-DD HH:00:00'),
			end_time: end_time,
			vehicle_type: '',
			time_type: '1',
			target,
			token: '',
			top: max,
		}).then((res) => {
			if (Array.isArray(res)) {
				setDBData(`${layerId}_${end_time}`, res);
				setReload(reload);
			}
		});
	};

	useEffect(() => {
		if (!showFlash) {
			// 获取整点更新数据时机
			const myDate = moment().minutes();
			timer.current = setTimeout(function () {
				myFunc();
			}, 1000 * 60 * (60 - myDate));

			const myFunc = () => {
				console.log('整时了！更新数据');
				setRefresh(true);
				setTimeout(function () {
					myFunc();
				}, 1000 * 60 * 60);
			};
		} else {
			// clearOld()
		}
		return () => {
			timer.current && clearTimeout(timer.current);
		};
	}, [showFlash]);

	useEffect(() => {
		if (refresh) {
			requestNewData(moment().subtract(1, 'h').format('YYYY-MM-DD HH:00:00'));
		}
	}, [refresh]);

	useEffect(() => {
		LayerDataDB.getItem(
			layerId + '_' + moment(timeBarEndTime).format('YYYY-MM-DD HH:00:00'),
		)
			.then((value: any) => {
				if (!value) {
					// 当前选中时间无数据，重新请求
					requestNewData(timeBarEndTime, true);
				} else {
					setRoadData(value);
				}
			})
			.catch(() => {
				requestNewData(timeBarEndTime, true);
			});
	}, [timeBarEndTime, reload]);

	return [roadData, setRoadData];
};

export default useRoadData;
