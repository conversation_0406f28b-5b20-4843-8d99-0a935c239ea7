import { useEffect, useState, useContext } from 'react';
import Echarts from '@renderer/components/echarts';
import { PageContext } from '@renderer/context';
import { calculatePercentage, tranNumber } from '@renderer/hooks';
import { unitList, VaUnitList } from '../AreaStreetStyles';
import imgs from '@renderer/images/background';

interface regionArr {
	name: string;
	value: number;
}

type Props = {
	data: any;
	emTitle: string;
	regionName: string;
	regionData: Array<regionArr>;
};

const EmissionProportion = (props: Props) => {
	const { regionName, currentTopNavMenu } = useContext(PageContext);
	const { data, emTitle, regionData, streetName, layerId } = props;

	const [option, setOption] = useState<any>(null);

	let newOption: any = [];
	const colorNew = ['rgba(255,0,0)', '#D8D8D8'];
	function processArray(arr, regionName) {
		// 用于存储匹配regionName的对象
		let matchedObjects = {};
		// 用于累加剩余对象的value
		let sumOfRemainingValues = 0;

		// 遍历数组
		arr.forEach((obj) => {
			// 检查name属性是否与regionName匹配
			if (obj.name === regionName) {
				// 如果匹配，存储该对象
				matchedObjects = {
					...obj,
					value: obj.value > 1 ? obj.value.toFixed(0) : obj.value.toFixed(2),
				};
			} else {
				// 如果不匹配，累加value
				sumOfRemainingValues += obj.value;
			}
		});

		// 创建一个表示剩余对象总和的对象
		const sumObject = {
			name: '其他',
			value: sumOfRemainingValues.toFixed(2),
		};

		// 返回包含匹配对象和剩余对象总和的对象
		return [matchedObjects, sumObject];
	}

	useEffect(() => {
		if (!data) return;
		const sortData = calculatePercentage(data);
		const finalData = sortData.sort((a, b) => a.value - b.value).reverse();
		const optionData = finalData?.map((item) => {
			return {
				name: item.name,
				value: Number(item.value),
			};
		});
		const name = layerId === 'streetTownship' ? streetName : regionName;
		const isRegion = name && regionName !== '全市';
		if (isRegion) {
			newOption = processArray(optionData, name);
		}
		const allValue = regionData.reduce((acc, curr) => {
			return acc + curr.value;
		}, 0);
		const proportionCity = newOption[0]?.value ? newOption[0]?.value : 100;
		const unit = VaUnitList.find((i) => i.name === currentTopNavMenu)?.unit;
		const textPer =
			layerId === 'streetTownship' && regionName && streetName ? '区' : '市';
		const option = {
			animation: true,
			tooltip: {
				show: true,
				trigger: 'item',
				textStyle: {
					fontSize: 16,
				},
				formatter: (params) => {
					return `${params.name} ${params.value}%`;
				},
			},
			title: {
				subtext:
					emTitle === '总量'
						? `总量\n\n${tranNumber(allValue, 2)}` +
						  `${currentTopNavMenu === '排放强度' ? '\n\n' : ''}` //  + `${unit}`
						: // : emTitle + `\n\n占比全${textPer}` + proportionCity + '%',
						  `{prop|${proportionCity}}%\n占比全${textPer}`,
				x: 'center',
				y: `${currentTopNavMenu === '排放强度' ? '15%' : '26%'}`,
				subtextStyle: {
					color: '#fff',
					fontSize: emTitle === '总量' ? 26 : 18,
					fontWeight: '1000',
					align: 'center',
					rich: {
						prop: {
							fontSize: 36,
							color: '#fff',
							fontWeight: '1000',
							padding: [0, 0, 5, 0], // 下间距 5px（调整与下一行的距离）
							// lineHeight: 30      // 按需调整行高
						},
					},
				},
			},
			legend: {
				show: false,
				width: '100%',
				left: 'center',
				textStyle: {
					color: '#fff',
					fontSize: 16,
				},
				icon: 'circle',
				right: '0',
				bottom: '0',
				padding: [0, 20],
				itemGap: 20,
				data: data?.adm,
			},
			series: [
				{
					type: 'pie',
					center: ['50%', '46%'],
					radius: ['62%', '82%'],
					color: isRegion
						? colorNew
						: [
								'#FEE449',
								'#00FFFF',
								'#00FFA8',
								'#9F17FF',
								'#b6f2d2',
								'#F76F01',
								'#01A4F7',
								'#FE2C8A',
								'#FF00FF', // 品红色
								'#FF4500', // 橙红色
								'#6851fd', // 青色
								'#FF1493', // 深粉色
								'#00FF00', // 鲜绿色
								'#66e2a0', // 金色
								'#7CFC00', // 草绿色
								'#FF69B4', // 热情的粉红色
								'#00CED1', // 深绿松石色
								'#FF8C00', // 暗橙色
						  ],
					startAngle: 135,
					labelLine: {
						show: false,
						normal: {
							length: 25,
						},
					},
					label: {
						normal: {
							show: false,
							backgroundColor: 'rgba(255, 147, 38, 0)',
							borderColor: 'transparent',
							borderRadius: 4,
							textStyle: {
								color: '#fff',
								fontSize: 16,
							},
						},
					},
					// emphasis: {
					//   label: {
					//     show: true,
					//     backgroundColor: 'rgba(255, 147, 38, 0)',
					//     borderColor: 'transparent',
					//     borderRadius: 4
					//   }
					// },
					data: isRegion ? newOption : optionData,
				},
			],
		};
		if (isRegion) {
			option.series[0].itemStyle = {
				borderColor: 'rgba(0,15,23,1)',
				borderWidth: 2,
			};
		}
		setOption(option);
	}, [data, emTitle, regionName]);
	return option ? (
		<Echarts
			option={option}
			style={{
				width: '250px',
				height: '210px',
				flex: 1,
				background: `url(${imgs.weiguibingtu}) no-repeat 50% 41% / 43% auto`,
			}}
			notMerge={true}
		/>
	) : null;
};

export default EmissionProportion;
