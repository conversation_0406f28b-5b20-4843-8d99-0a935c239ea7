// 街乡镇排放
import * as React from 'react';
import { useState, useEffect } from 'react';
import LayoutContainer from './style';
import Box from '../../baseUI/Box';
import LevelTwoTitle from '../../components/LevelTwoTitle';
import StreetAndTownMap from '../StreetAndTownMap';
import ScrollListWidthTitle from '@renderer/components/ScrollListWithTitle';
import TimeTypeRadio from '@renderer/baseUI/TimeTypeRadio2';
import { getStatisticsNox } from '@renderer/api';
import { useAuth } from '@renderer/hooks';
import moment from 'moment';
const sameColumn = [
	{
		align: 'center',
		dataIndex: 'rank',
		key: 'rank',
		width: '10%',
		title: '排名',
	},
	{
		align: 'center',
		dataIndex: 'street',
		width: '20%',
		key: 'street',
		title: '街乡镇',
	},
	{
		align: 'center',
		dataIndex: 'region',
		key: 'region',
		title: '所属区',
		width: '40%',
	},
];
const column1 = [
	...sameColumn,
	{
		align: 'center',
		dataIndex: 'num',
		key: 'num',
		width: '30%',
		title: '排放量',
	},
];
const column2 = [
	...sameColumn,
	{
		align: 'center',
		dataIndex: 'num',
		key: 'num',
		width: '30%',
		title: '排放强度',
	},
];

const VehicleViolations = () => {
	const violatioData = [
		{
			num: '12',
			region: '海淀',
			street: '清水镇',
			rank: '1',
		},
		{},
		{},
		{},
		{},
	];
	const [data, setDate] = useState<any>(violatioData);
	const { token } = useAuth();

	const [timeData, setTimeData] = useState({});

	useEffect(() => {
		console.log('timeData--', timeData);
		const req = {
			token,
			start_time: moment().subtract(7, 'day').format('YYYY-MM-DD HH:00:00'),
			end_time: moment().format('YYYY-MM-DD HH:00:00'),
			data_type: 2,
			timeType: 2,
			accumulated: 1,
		};
		getStatisticsNox(req).then((res) => {
			// setDate(res)
		});
	}, [timeData]);

	let optionTable = {
		width: '240px',
		height: '160px',
		fontSize: 12,
		thclassname: 'th_row',
		tablebgcolor: 'rgba(9,30,59,0)',
		trheight: '30px',
		thheight: '40px',
		customwidth: true,
		rowbgcolor: ['rgba(58,218,255,0.2)', 'rgba(9, 30, 47, 0.29)'],
		thbgcolor: '#081F38',
	};

	return (
		<Box
			title="街乡镇排放"
			titlewidth="95%"
			height="100%"
			subTitle={
				<div style={{ marginRight: '50px' }}>
					<TimeTypeRadio
						defaultValue="1"
						timeType={(data) => setTimeData(data)}
					/>
				</div>
			}
		>
			<LayoutContainer>
				<div className="left-container">
					<div className="register-area">
						<div className="map">
							<StreetAndTownMap id="streetDischarge"></StreetAndTownMap>
						</div>
					</div>
				</div>
				<div className="middle-container">
					<LevelTwoTitle title="排放量TOP"></LevelTwoTitle>
					<div className="table">
						<ScrollListWidthTitle
							data={data}
							columns={column1}
							{...optionTable}
						/>
					</div>
				</div>
				<div className="right-container">
					<LevelTwoTitle title="排放强度TOP"></LevelTwoTitle>
					<div className="table">
						<ScrollListWidthTitle
							data={data}
							columns={column2}
							{...optionTable}
						/>
					</div>
				</div>
			</LayoutContainer>
		</Box>
	);
};
export default VehicleViolations;
