// 街乡镇
import React, { useEffect, useContext, useState, useRef } from 'react';
import { Radio } from 'antd';
import axios from 'axios';
import * as echarts from 'echarts';
import { MapContext, PageContext } from '@renderer/context';
import { getRegionalIndicator } from '@renderer/api';
import FallingAreaMap from '../components/FallingAreaMap';
import { getStreetTownshipEcharts } from '../components/AreaStreetStyles/components/StreetTownshipEcharts';
import ScrollListWidthTitle from '@renderer/components/SlidingLayer/DailyActivityLevel/components/ScrollListWidthTitle';
import {
	formatNumber,
	useTimeReferenceParams,
	tranNumber,
	tranNumberS,
} from '@renderer/hooks/index';
import {
	COUNTY_ID_NAME_MAP,
	beijingRegions,
	legendArea,
	unitList,
	unitArea,
	getTitles,
} from '../components/AreaStreetStyles';
import {
	AreaViolationWrapStyled,
	AreaViolationStyled,
	ReferenceStyled,
} from '../components/AreaStreetStyles/styles';

const topicToName = {
	6: '违规',
	5: '在线车辆',
	2: '排放量',
	3: '里程',
	1: '油耗',
	4: '排放强度',
};

export default (props) => {
	const { visible, layerId } = props;
	const {
		setCurrentTopNavMenu,
		currentTopNavMenu, // 菜单栏当前标签名称
		regionName, // 当前区域名称
		streetName, // 当前街乡镇名称
		setStreetName,
		setRegionName,
		regionData, // 区域地图数据
		selectRegionDate, // 当前选择的时间参数
		regionalIndicators, // 获取的区域地图和同比数据
		setRegionData,
		setRegionalIndicators,
		hideModeMenu, // 图层设定的hideModeMenu属性对应变量
		setCurrentSelectedId,
		currentSelectedId,
		BeiJingTownDataDB,
	} = useContext(PageContext);
	const { sceneMode } = useContext(MapContext);
	const divChartRef = useRef(null);
	const myChartRef = useRef(null);
	const [customDateYoy, setCustomDateYoy] = useState({}); // 自定义同比
	const [selectedButton, setSelectedButton] = useState('default'); // 柱状图选中按钮
	const [currentPosition, setCurrentPosition] = useState(null);
	const [regionYOY, setRegionYOY] = useState([]);
	// const [townJson, setTownJson] = useState(null)
	const [showPop, setShowPop] = useState(false);
	const [proportionData, setProportionData] = useState([]); // 区域右下角数据占比
	const [regionTitle, setRegionTitle] = useState('');
	const [streetShipId, setStreetShipId] = useState([]); // 街乡镇id
	const [columns, setColumns] = useState([]);
	const [data, setData] = useState([]);
	const [rowData, setRowData] = useState(null);

	let baseRows = [],
		yDataRows = [],
		yDodRows = [],
		yPerRows = [],
		townJson = null;

	const colors = [
		'#38a700',
		'#4db200',
		'#4db200',
		'#79c900',
		'#93d400',
		'#acde00',
		'#caea00',
		'#e9f600',
		'#fff600',
		'#ffd401',
		'#ffb301',
		'#ffb301',
		'#ff7100',
		'#ff5606',
		'#ff5e2f',
		'#ff6658',
		'#ff5252',
		'#fe0000',
	];
	const getMaterialColor = (num, layerId, data) => {
		switch (layerId) {
			case 'streetTownship': {
				const valueList = data.map((item) => item.value);
				const maxValue = Math.max(...valueList);
				const minValue = Math.min(...valueList);
				const scale = (num - minValue) / (maxValue - minValue);
				const colorIndex = Math.floor(scale * (colors.length - 1));
				return colors[colorIndex];
			}
		}
	};

	// 获取街乡镇信息
	// const getTownJson = async () => {
	//   try {
	//     const response = await axios.get(`file:///public/data/townJson.json`)
	//     setTownJson(response.data)
	//   } catch (error) {
	//     console.error(error)
	//     throw error
	//   }
	// }

	// 柱状图默认自定义切换
	const handleClickReference = (value) => {
		setSelectedButton(value);
		if (value === 'custom') {
			setShowPop(true);
		} else {
			setShowPop(false);
		}
	};

	const getStreetTownshipSort = (data) => {
		const combinedData = data?.adm?.map((adm, index) => ({
			adm,
			count: formatNumber(data.count[index]),
			dod: formatNumber(data.dod[index]),
			per: formatNumber(data.per[index]),
		}));
		combinedData?.sort((a, b) => a.count - b.count);
		const sortedAdmData = combinedData?.map((item) => item.adm);
		const sortedCountData = combinedData?.map((item) => item.count);
		const sortedDodData = combinedData?.map((item) => item.dod);
		const sortedPerData = combinedData?.map((item) => item.per);
		return {
			combinedData,
			sortedAdmData,
			sortedCountData,
			sortedDodData,
			sortedPerData,
		};
	};

	// 初始化柱状图
	const setUpOption1 = (data) => {
		const result = getStreetTownshipSort(data);
		const option = getStreetTownshipEcharts({
			...data,
			adm: result?.sortedAdmData,
			count: result?.sortedCountData,
			dod: result?.sortedDodData,
			per: result?.sortedPerData,
			text: getTitles(
				selectRegionDate.timeType,
				unitList.find((i) => i.name === currentTopNavMenu)?.name,
			),
			unit: unitList.find((i) => i.name === currentTopNavMenu)?.unit,
		});
		// setOption1(option)
		const regionYOY = result?.combinedData?.map((item) => {
			return {
				name: item.adm,
				per: item.per,
			};
		});
		setRegionYOY(regionYOY);
		return option;
	};

	const getStreetShipRows = () => {
		const result = getStreetTownshipSort(regionalIndicators);
		const {
			sortedAdmData: admData,
			sortedCountData: countData,
			sortedDodData: dodData,
			sortedPerData: perData,
		} = result;
		baseRows = admData;
		yDataRows = countData;
		yDodRows = dodData;
		yPerRows = perData;
	};

	const upDateECharts = (optionData, streetName) => {
		const text = getTitles(
			selectRegionDate.timeType,
			unitList.find((i) => i.name === currentTopNavMenu)?.name,
		);
		if (optionData) {
			myChartRef.current.setOption({
				xAxis: [
					{
						name: 'per',
						data: optionData.yPerRows2,
					},
				],
				yAxis: [
					{
						name: unitList.find((i) => i.name === currentTopNavMenu)?.unit,
						data: optionData.baseRows2,
						axisLabel: {
							// interval: 0,
							textStyle: {
								color: function (value, index) {
									return streetName === value ? '#3ADAFF' : '#E2F0FF';
								},
								fontSize: 19,
							},
						},
					},
				],
				series: [
					{
						name: text[0],
						data: optionData.yDataRows2,
					},
					{
						name: text[1],
						data: optionData.yDodRows2,
					},
					{
						name: text[2],
						data: optionData.yPerRows2,
					},
				],
			});
		} else {
			myChartRef.current.setOption({
				xAxis: [
					{
						name: 'per',
						data: yPerRows,
					},
				],
				yAxis: [
					{
						name: unitList.find((i) => i.name === currentTopNavMenu)?.unit,
						data: baseRows,
					},
				],
				series: [
					{
						name: text[0],
						data: yDataRows,
					},
					{
						name: text[1],
						data: yDodRows,
					},
					{
						name: text[2],
						data: yPerRows,
					},
				],
			});
		}
	};

	// 获取接口数据
	const getCustomLineData = (params) => {
		const topic = topicToName[params.topic];
		getRegionalIndicator(params)
			.then((res) => {
				if (res?.count?.length > 0) {
					const mapData = res?.adm?.map((item, idx) => {
						const value = formatNumber(res?.count[idx]);
						const name = townJson.features.find(
							(i) => i.properties.region === item,
						)?.properties?.name;
						if (!name) {
							console.log(item, res?.count[idx]);
						}
						return {
							name,
							value,
						};
					});
					setRegionData(mapData);
					setStreetShipId(res?.adm);
					const newData = { ...res };
					let isUndefinedList = [];
					newData.adm = res?.adm?.map((id, index) => {
						const name = townJson.features.find(
							(i) => i.properties.region === id,
						)?.properties?.name;
						if (name) {
							return name;
						} else {
							isUndefinedList.push(index);
						}
					});
					newData.regionName = res?.adm?.map((id, index) => {
						const name = townJson.features.find(
							(i) => i.properties.region === id,
						)?.properties['COUNTY'];
						if (name) {
							return name;
						} else {
							isUndefinedList.push(index);
						}
					});
					//过滤掉街乡镇为undefined的数据
					const admFilter = newData.adm.filter((item) => item != undefined);
					const regionFilter = newData.regionName.filter(
						(item, index) => !isUndefinedList.includes(index),
					);
					const countFilter = newData.count.filter(
						(item, index) => !isUndefinedList.includes(index),
					);
					const dodFilter = newData.dod.filter(
						(item, index) => !isUndefinedList.includes(index),
					);
					const perFilter = newData.per.filter(
						(item, index) => !isUndefinedList.includes(index),
					);
					const tmp = {
						adm: admFilter,
						count: countFilter,
						dod: dodFilter,
						per: perFilter,
					};
					setColumns([
						{ title: '街乡镇名称', dataIndex: 'streetName' },
						{ title: '所属区域', dataIndex: 'regionName' },
						{ title: topic, dataIndex: 'value' },
					]);

					setData(
						admFilter.map((item, index) => {
							return {
								streetName: item,
								regionName: regionFilter[index],
								value: tranNumberS(countFilter[index], 2),
							};
						}),
					);
					setRegionalIndicators(tmp);
				} else {
					setRegionData([]);
					setRegionalIndicators([]);
					setStreetShipId([]);
				}
			})
			.catch((err) => {
				setRegionData([]);
				setRegionalIndicators([]);
			});
	};

	// 根据变量的值来高亮和定位
	function highlightAndLocate(value) {
		// 假设你的数据已经按照y轴的值排序
		let series = myChartRef.current.getOption().series[0];
		let data = series.data;
		let index = data.findIndex((item) => item === value); // 假设数据是[x, y]格式的
		let startZ = 2;
		let endZ = 2;
		if (value >= 1 && value < 10) {
			startZ = 1.5;
			endZ = 3.8;
		} else {
			startZ = 2;
			endZ = 2;
		}
		if (regionName) {
			startZ = 6;
			endZ = 6;
		}
		if (index !== -1) {
			// 定位到数据项
			let percent = (index / data.length) * 100; // 计算百分比位置
			myChartRef.current.dispatchAction({
				type: 'dataZoom',
				start: percent - startZ, // 减去一些百分比以确保数据项在可见范围内
				end: percent + endZ,
			});
		}
	}

	useEffect(() => {
		if (
			!regionData?.length ||
			!proportionData.length ||
			currentTopNavMenu == ''
		)
			return;
		let regionTitle = '';
		let name = '';
		if (regionName) {
			name = regionName;
			const value = proportionData.reduce((a, b) => {
				return a + b.value;
			}, 0);
			regionTitle =
				name +
				`总量\n\n` +
				tranNumber(value, 2) +
				unitList.find((i) => i.name === currentTopNavMenu)?.unit;
		}
		if (streetName) {
			name = streetName;
			regionTitle =
				name +
				`总量\n\n` +
				tranNumber(proportionData.find((i) => i.name === name)?.value, 2) +
				unitList.find((i) => i.name === currentTopNavMenu)?.unit;
			let option;
			if (myChartRef.current) {
				option = myChartRef.current.getOption();
				const idx = regionalIndicators?.adm?.findIndex((i) => i === streetName);
				const streetData = {
					adm: streetName,
					count: regionalIndicators?.count[idx],
					dod: regionalIndicators?.dod[idx],
					per: regionalIndicators?.per[idx],
				};
				const baseRows2 = option.yAxis[0].data;
				const yDataRows2 = option.series[0].data;
				const yDodRows2 = option.series[1].data;
				const yPerRows2 = option.series[2].data;
				if (!baseRows2?.includes(streetName)) {
					baseRows2.splice(0, 1);
					baseRows2.push(streetData.adm);
					yDataRows2.splice(0, 1);
					yDataRows2.push(streetData.count);
					yDodRows2.splice(0, 1);
					yDodRows2.push(streetData.dod);
					yPerRows2.splice(0, 1);
					yPerRows2.push(streetData.per);
				}
				const optionData = { baseRows2, yDataRows2, yDodRows2, yPerRows2 };
				upDateECharts(optionData, streetName);
				highlightAndLocate(formatNumber(streetData.count), regionName);
			}
			setRegionTitle(regionTitle);
		} else {
			setRegionTitle(regionTitle);
			if (myChartRef.current) {
				myChartRef.current.setOption({
					yAxis: [
						{
							name: unitList.find((i) => i.name === currentTopNavMenu)?.unit,
							axisLabel: {
								// interval: 0,
								textStyle: {
									color: '#E2F0FF',
									fontSize: 18,
								},
							},
						},
					],
				});
			}
		}
	}, [regionName, streetName, proportionData, currentTopNavMenu]);

	useEffect(() => {
		if (!regionData?.length) {
			setProportionData([]);
			return;
		}
		setProportionData(regionData);
	}, [regionData]);

	// useEffect(() => {
	//   if (!townJson || currentTopNavMenu == '') return
	//   if (
	//     (selectedButton === 'custom' && Object.keys(customDateYoy)?.length > 0) ||
	//     regionName === '全市'
	//   )
	//     return
	//   const { start_time, end_time, time_type } = selectRegionDate.customDate
	//   const params = {
	//     start_time,
	//     end_time,
	//     time_type,
	//     topic: unitList.find((i) => i.name === currentTopNavMenu)?.id,
	//     level: 2
	//   }
	//   if (regionName && regionName !== '全市') {
	//     params.county_id = Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName)
	//   }
	//   getCustomLineData(params)
	// }, [townJson, selectedButton, selectRegionDate, currentTopNavMenu, regionName])

	// useEffect(() => {
	// 	myChartRef.current = echarts.init(divChartRef.current);
	// 	myChartRef.current.setOption(setUpOption1(regionalIndicators));
	// }, [regionalIndicators]);

	useEffect(() => {
		setCurrentTopNavMenu(unitList[0].name);
		// setCurrentSelectedId(layerId)
	}, []);

	useEffect(() => {
		if (!visible) return;
		setCurrentSelectedId(layerId);
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (currentTopNavMenu == '' || !start_time) return;

		BeiJingTownDataDB.getItem('BeiJingTownData')
			.then((value) => {
				if (value) {
					townJson = value;
					const {
						benchmark_start_time,
						benchmark_end_time,
						benchmark_time_type,
					} = useTimeReferenceParams(customDateYoy);
					let customDateYoyData = {};
					if (
						benchmark_start_time &&
						benchmark_end_time &&
						benchmark_time_type
					) {
						customDateYoyData = {
							benchmark_start_time,
							benchmark_end_time,
							benchmark_time_type,
						};
					}

					const params = {
						start_time,
						end_time,
						time_type: time_type === 'hour' ? 2 : 1,
						...customDateYoyData,
						topic: unitList.find((i) => i.name === currentTopNavMenu)?.id,
						level: 2,
					};
					if (regionName && regionName !== '全市') {
						params.county_id =
							Object.values(COUNTY_ID_NAME_MAP).findIndex(
								(i) => i === regionName,
							) + 1;
					}
					getCustomLineData(params);
				}
			})
			.catch(() => {
				console.log('查询北京街乡镇数据异常');
			});

		// setUpOption1(regionalIndicators)
		getStreetShipRows();
		return () => {
			if (myChartRef.current) {
				myChartRef.current.dispose();
			}
			setStreetName(null);
			setCurrentSelectedId('');
		};
	}, [
		visible,
		selectedButton,
		customDateYoy,
		selectRegionDate,
		regionName,
		currentTopNavMenu,
	]);

	return visible ? (
		<>
			<FallingAreaMap
				data={regionData}
				layerId={layerId}
				sceneMode={sceneMode}
				currentPosition={currentPosition}
				getMaterialColor={getMaterialColor}
				streetShipId={streetShipId}
			/>
			{/* <TownshipStats setLayerData={setLayerData} regionName={regionName} /> */}
			<AreaViolationWrapStyled>
				<div className="user-enterprise-list">
					<ScrollListWidthTitle
						columns={columns}
						data={data}
						OnRowSelection={(record) => setRowData(record)}
						{...optionTable}
					/>
				</div>
			</AreaViolationWrapStyled>
		</>
	) : null;
};

const optionTable = {
	width: '100%',
	height: '600px',
	fontSize: 18,
	thclassname: 'th_row',
	tablebgcolor: 'rgba(9,30,59,0.7)',
	trheight: '40px',
	thheight: '40px',
	customwidth: true,
	rowbgcolor: [
		'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
	],
	thbgcolor: '#081F38',
};
