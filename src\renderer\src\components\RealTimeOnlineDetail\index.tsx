import { useState, useEffect } from 'react';
import * as echarts from 'echarts';
import { Segmented, Table, Radio, Tooltip } from 'antd';
import LayoutContainer from './style';
import Box from '../../baseUI/Box';
import Echarts from '@renderer/components/echarts';
import TimeTypeRadio from '../../baseUI/TimeTypeRadio';
import AllSelect from './AllSelect';
import moment from 'moment';
import {
	onlineUn,
	onlineSe,
	mileageUn,
	mileageSe,
	emissionUn,
	emissionSe,
	oilUn,
	oilSe,
} from '@renderer/images/onlineDetailIcon';
import { INDUSTRY_COLOR } from '@renderer/constants/color';
import { getRealtimeOnlineByTime } from '@renderer/api';
import { getCarColorTwo } from '../../../../main/data';
import styled from 'styled-components';
import CustomPopUps from '@renderer/baseUI/TimeTypeRadioCustom/components/CustomPopUps';
import { tranNumberS } from '@renderer/hooks';

const tabsList = [
	{
		id: 1,
		name: '在线',
		key: 'nums',
		unit: '辆',
		iconUn: onlineUn,
		iconSe: onlineSe,
	},
	{
		id: 2,
		name: '排放',
		key: 'total_nox',
		unit: 'kg',
		iconUn: emissionUn,
		iconSe: emissionSe,
	},
	{
		id: 3,
		name: '里程',
		key: 'total_distance',
		unit: 'km',
		iconUn: mileageUn,
		iconSe: mileageSe,
	},
	{
		id: 4,
		name: '油耗',
		key: 'total_oil',
		unit: 'kg',
		iconUn: oilUn,
		iconSe: oilSe,
	},
];
const typeOptions = [
	{
		label: '日',
		value: '7day',
	},
	{
		label: '周',
		value: '24hours',
	},
	{
		label: '月',
		value: 'month',
	},
];

// TODO: 单位以万为单位，左侧
const RealTimeOnlineDetail = () => {
	const [lineOptions, setLineOptions] = useState({});
	const [active, setActive] = useState(1);
	const [timeType, setTimeType] = useState('24hours');
	const [beijingType, setBeijingType] = useState('all');
	const [selectedButton, setSelectedButton] = useState('default');
	const [showPop, setShowPop] = useState(false);
	const [customDate, setCustomDate] = useState({});
	useEffect(() => {
		let json = {};
		if (selectedButton === 'custom') {
			json = {
				in_beijing:
					beijingType === 'bjin' ? 1 : beijingType === 'biout' ? 0 : 3,
				description: active,
				start_time: customDate.startTime,
				end_time: customDate.endTime,
				time_type: customDate.timeType === 'hour' ? 1 : 3,
			};
		} else if (selectedButton === 'all') {
			json = {
				in_beijing:
					beijingType === 'bjin' ? 1 : beijingType === 'biout' ? 0 : 3,
				description: active,
				start_time: moment().subtract(1, 'day').format('YYYY-MM-DD 00:00:00'),
				end_time: moment().subtract(1, 'day').format('YYYY-MM-DD 23:59:59'),
				time_type: 1,
			};
		} else {
			json = {
				in_beijing:
					beijingType === 'bjin' ? 1 : beijingType === 'biout' ? 0 : 3,
				description: active,
				start_time: moment().startOf('day').format('YYYY-MM-DD 00:00:00'),
				end_time: moment().subtract(2, 'hour').format('YYYY-MM-DD HH:00:00'),
				time_type: 1,
			};
		}
		if (json.time_type === 1) {
			setTimeType('24hours');
		} else {
			setTimeType('month');
		}
		//  json = {
		// 	...getTimes(timeType),
		// 	in_beijing: beijingType === 'bjin' ? 1 : beijingType === 'biout' ? 0 : 3,
		// 	description: active,
		// };
		getRealtimeOnlineByTime(json).then((res) => {
			const typeList = [
				'货车',
				'公交车',
				'工程车',
				'渣土车',
				'环卫车',
				'其他客车',
				'其他用途',
			];
			let xData: any = [];
			let yData: any = [];
			typeList.map((item) => {
				let data = res.filter((item2) => item2.vehicle_type === item);
				xData = data.map((item3) => item3.date_time);
				yData.push({
					name: data[0].vehicle_type,
					value: data.map((item3) => item3.data),
				});
			});

			let series: any = yData.map((item, i) => {
				// let color = getCarColorTwo(item.name);
				let color = INDUSTRY_COLOR[item.name];
				let colorNum = color.slice(4).slice(0, color.slice(4).length - 1);
				return {
					name: item.name,
					type: 'line',
					color,
					symbol: 'none', // 不显示连接点
					smooth: true, // 设置为 true 以启用平滑曲线
					lineStyle: {
						normal: {
							width: 2,
						},
					},
					areaStyle: {
						color: {
							type: 'linear',
							x: 0, //右
							y: 0, //下
							x2: 0, //左
							y2: 1, //上
							colorStops: [
								{
									offset: 0.1,
									color: `${color}50`,
									// color: `rgba(${colorNum}, 0.3)`, // 0% 处的颜色
								},
								{
									offset: 1,
									color: `${color}00`,
									// color: `rgba(${colorNum}, 0)`, // 100% 处的颜色
								},
							],
						},
					},
					data: item.value,
				};
			});
			const lineOption = {
				title: {
					text: `单位: ${tabsList.find((i) => i.id === active)?.unit}`,
					// left: 19,
					left: '3%',
					top: 0,
					textStyle: {
						color: '#fff',
						fontSize: 12,
						fontWeight: 300,
					},
				},
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						// 坐标轴指示器，坐标轴触发有效
						type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
					},
					backgroundColor: 'rgba(13, 64, 71, 0.50)',
					borderColor: 'rgba(143, 225, 252, 0.60)',
					padding: 8,
					textStyle: {
						color: '#fff',
					},
					formatter: function (params) {
						let relVal = '<strong>' + params[0].name + '</strong><br/>';
						for (let i = 0, l = params.length; i < l; i++) {
							const formattedValue = params[i].value;
							relVal += `${params[i].marker} ${params[i].seriesName}: ${formattedValue}<br/>`;
						}
						return relVal;
						// if (active == 1) {
						// } else {
						// 	let relVal = '<strong>' + params[0].name + '</strong><br/>';
						// 	for (let i = 0, l = params.length; i < l; i++) {
						// 		const formattedValue = tranNumberS(params[i].value, 2);
						// 		relVal += `${params[i].marker} ${params[i].seriesName}: ${formattedValue}<br/>`;
						// 	}
						// 	return relVal;
						// }
					},
				},
				legend: {
					textStyle: {
						color: '#fff',
					},
					icon: 'rect', // 将 Legend 图标设置为矩形
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '5%',
					top: '15%',
					containLabel: true,
				},
				xAxis: [
					{
						type: 'category',
						data: xData,
						boundaryGap: false,
						axisTick: {
							show: false, // 不显示坐标轴刻度线
						},
						splitLine: {
							show: false,
						},
						axisLine: {
							show: false,
						},
						axisLabel: {
							color: '#E8F4FF',
							fontSize: 12,
							margin: 10,
							formatter: (params) => {
								if (typeof params !== 'string') {
									return params;
								}
								// 检查 params 是否包含时间部分 (HH:mm 或 HH:mm:ss)
								const hasTimePart = /\s\d{2}:\d{2}(?::\d{2})?/.test(params);
								const momentDate = moment(
									params,
									hasTimePart ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD',
									true,
								);
								if (!momentDate.isValid()) {
									return params;
								}
								return hasTimePart
									? momentDate.format('HH:mm')
									: momentDate.format('MM/DD');
							},
						},
					},
				],
				yAxis: [
					{
						type: 'value',
						splitNumber: 4,
						//y右侧文字
						axisLabel: {
							color: '#E8F4FF',
							fontSize: 16,
							formatter: function (value) {
								return value.toLocaleString();
							},
						},
						// y轴的分割线
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed',
								color: 'rgba(230, 247, 255, 0.20)',
							},
						},
					},
				],
				dataZoom: [
					{
						type: 'slider',
						show: false,
						height: 20,
						bottom: 8,
					},
					{
						type: 'inside',
					},
				],
				series,
			};
			setLineOptions(lineOption);
		});
	}, [beijingType, active, customDate, selectedButton]);

	const hexToRgba = (hex, opacity) => {
		if (!hex) hex = '#ededed';
		const rgba =
			'rgba(' +
			parseInt('0x' + hex.slice(1, 3)) +
			',' +
			parseInt('0x' + hex.slice(3, 5)) +
			',' +
			parseInt('0x' + hex.slice(5, 7)) +
			',' +
			(opacity || '1') +
			')';
		return rgba;
	};

	// 获取时间粒度
	const getTimes = (timeType) => {
		let times: any = {};
		switch (timeType) {
			case '7day':
				times = {
					start_time: moment().format('YYYY-MM-DD 00:00:00'),
					end_time: moment().format('YYYY-MM-DD 23:59:59'),
					time_type: 1,
				};
				break;
			case '24hours':
				times = {
					start_time: moment().add('-7', 'd').format('YYYY-MM-DD 00:00:00'),
					end_time: moment().format('YYYY-MM-DD 23:59:59'),
					time_type: 2,
				};
				break;
			case 'month':
				times = {
					start_time: moment().startOf('month').format('YYYY-MM-DD 00:00:00'),
					end_time: moment().format('YYYY-MM-DD 23:59:59'),
					time_type: 3,
				};
				break;
			default:
				times = {
					start_time: moment().startOf('month').format('YYYY-MM-DD 00:00:00'),
					end_time: moment().format('YYYY-MM-DD 23:59:59'),
					time_type: 3,
				};
				break;
		}
		return times;
	};

	const handleTabs = (index) => {
		setActive(index);
	};

	// 柱状图默认自定义切换
	const handleClickReference = (value) => {
		setSelectedButton(value);
		if (value === 'custom') {
			setShowPop(true);
		} else {
			setShowPop(false);
		}
	};

	return (
		<Box
			title="运行趋势"
			titlewidth="95%"
			subTitle={
				<div className="customDate">
					<ReferenceStyled>
						<div className="selectAssembly">
							<Radio.Button
								value="default"
								onClick={() => handleClickReference('default')}
								style={{
									background:
										selectedButton === 'default'
											? 'radial-gradient(72% 90% at 50% 50%, #3BC3F4 0%, rgba(52,112,190,0) 100%)'
											: undefined,
								}}
							>
								当日
							</Radio.Button>
							<Radio.Button
								value="all"
								onClick={() => handleClickReference('all')}
								style={{
									// color: selectedButton === 'all' ? '#3ADAFF' : '#e2e5e5',
									background:
										selectedButton === 'all'
											? 'radial-gradient(72% 90% at 50% 50%, #3BC3F4 0%, rgba(52,112,190,0) 100%)'
											: undefined,
								}}
							>
								昨日
							</Radio.Button>
							{/* <Radio.Button
								value="custom"
								onClick={() => handleClickReference('custom')}
								style={{
									// display:
									//   selectedButton === 'all' || selectedButton === 'custom' ? 'block' : 'none',
									// color: selectedButton === 'custom' ? '#3ADAFF' : '#e2e5e5',
									background:
										selectedButton === 'custom'
											? 'radial-gradient(72% 90% at 50% 50%, #3BC3F4 0%, rgba(52,112,190,0) 100%)'
											: undefined,
								}}
							>
								自定义
							</Radio.Button> */}
						</div>
						<CustomPopUps
							top={'30px'}
							right={'0'}
							showPop={showPop}
							setShowPop={setShowPop}
							setTimeType={null}
							setCustomDate={setCustomDate}
						/>
					</ReferenceStyled>
				</div>
			}
			// subTitle={
			//   <div style={{ display: 'flex' }}>
			//     <TimeTypeRadio
			//       type={'month'}
			//       typeOptions={typeOptions}
			//       timeType={(e) => setTimeType(e)}
			//     />
			//     <AllSelect change={(v) => setBeijingType(v.type)} />
			//   </div>
			// }
		>
			<LayoutContainer>
				<div className="tabs">
					{tabsList.map((item, index) => {
						return (
							<a
								onClick={() => handleTabs(item.id)}
								key={item.id}
								className={active == item.id ? 'tabs-item active' : 'tabs-item'}
							>
								<img src={active == item.id ? item.iconSe : item.iconUn} />
								{item.name}
							</a>
						);
					})}
				</div>
				<div className="down-container">
					{lineOptions ? <Echarts option={lineOptions}></Echarts> : null}
				</div>
			</LayoutContainer>
		</Box>
	);
};

export default RealTimeOnlineDetail;

const ReferenceStyled = styled.div`
	.selectAssembly {
		width: 100%;
		margin-left: auto;
		/* padding-right: 20px; */
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		box-shadow: inset 0px 0px 5px 0px #45eeff;
		border-radius: 6px 6px 6px 6px;
		border-image: radial-gradient(
				circle,
				rgba(85, 252, 255, 1),
				rgba(71, 211, 219, 1)
			)
			1 1;
		background: rgba(54, 121, 141, 0.23);
		//border-radius: 6px;
		border: 1px solid #3adaff;
		p {
			color: #e2e5e5;
			margin: 0;
			font-size: 18px;
		}
		.ant-radio-button-wrapper {
			width: 80px;
			border: 0;
			border-radius: 5px;
			font-size: 16px;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgb(58, 218, 255, 0.1);
			span:hover {
				color: #3adaff;
			}
		}
		:where(.css-dev-only-do-not-override-mu9r37).ant-radio-button-wrapper:not(
				:first-child
			)::before {
			display: none;
		}
	}
`;
