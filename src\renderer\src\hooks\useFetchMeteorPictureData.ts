import { useState, useEffect } from 'react';

import { getMeteorAllPic } from '@renderer/api';

type MeteorPicParams = {
	token: string;
	provinceId: string;
	cityId: string;
	countyId: string;
	dateTime: string;
	picType: string;
	classify: string;
};

export const useFetchMeteorPictureData = (meteorPicParams: MeteorPicParams) => {
	const { picType } = meteorPicParams;
	const [params, setParams] = useState<MeteorPicParams>(meteorPicParams);
	const [url, setURL] = useState<string>();

	useEffect(() => {
		const fetchData = async () => {
			const result = await getMeteorAllPic({
				json: params,
			});
			const url = result[0][picType];
			setURL(url);
		};

		fetchData();
	}, [params]);

	return [url, setParams] as const;
};
