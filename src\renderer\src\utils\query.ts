export const objectToQueryString = (obj: Record<string, any>) => {
	const pairs = [];
	for (const key in obj) {
		if (obj.hasOwnProperty(key)) {
			const value = obj[key];
			pairs.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));
		}
	}
	return pairs.join('&');
};

export const generateRandomInteger = (min: number, max: number) => {
	return Math.floor(Math.random() * (max - min + 1)) + min;
};
