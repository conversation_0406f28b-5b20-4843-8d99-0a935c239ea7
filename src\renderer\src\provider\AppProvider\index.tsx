import { useState } from 'react';

import { AppContext } from '@renderer/context';
import { useIpcRenderer } from '@renderer/hooks';

export const AppProvider = ({ children }) => {
	const [font, setFont] = useState<string>('');

	const configJSONListener = (_event, data) => {
		setFont(
			['electronicFont', 'TimesNewRoman'].includes(data.fontFamily)
				? data.fontFamily
				: 'electronicFont',
		);
		sessionStorage.setItem('zcJsonData', JSON.stringify(data));
		sessionStorage.setItem('config-json', JSON.stringify(data));
	};

	useIpcRenderer('data:config-json', configJSONListener);

	return (
		<AppContext.Provider
			value={{
				font,
			}}
		>
			{children}
		</AppContext.Provider>
	);
};
