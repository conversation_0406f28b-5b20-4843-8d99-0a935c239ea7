import React, { useState, useEffect } from 'react';
import '@animxyz/core';
import Box from '../../baseUI/Box';
import { XyzTransitionGroup } from '@animxyz/react';
import background from '../../images/background';
import { ProportionOfAllVehiclesOnlineContent } from './style';
const ProportionOfAllVehiclesOnline = () => {
	const [isVisible, setIsVisible] = useState(false);
	useEffect(() => {
		setIsVisible(true);
	});
	return (
		<ProportionOfAllVehiclesOnlineContent>
			<XyzTransitionGroup
				className="item-group"
				appear
				mode="out-in"
				xyz="fade left-100%"
			>
				{isVisible && <div className="square">全部区域车辆在线占比</div>}
			</XyzTransitionGroup>
		</ProportionOfAllVehiclesOnlineContent>
	);
};

export default ProportionOfAllVehiclesOnline;
