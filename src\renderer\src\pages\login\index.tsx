import { useRef, useEffect, useState } from 'react';
import { Button, Spin, message } from 'antd';

import { useAuth, useConfigData } from '@renderer/hooks';
import { certificateLogin } from '@renderer/api';
import { objectToQueryString, generateRandomInteger } from '@renderer/utils';

import LoginContainer from './styles';

const LoginPage = () => {
	const [certificate, setCertificate] = useState<string>('');
	const [password, setPassword] = useState<string>('');
	const [loading, setLoading] = useState<boolean>(false);

	const certificateRef = useRef<HTMLSelectElement>(null);
	const passwordRef = useRef<HTMLInputElement>(null);

	const { login } = useAuth();
	const [needCertificate] = useConfigData('certificate', false);

	useEffect(() => {
		SetUserCertList(certificateRef.current);
		setCertificate(certificateRef.current.value);
	}, []);

	const fakeLogin = () => {
		const token = 'res.token?SAAFDdsf';
		const timeout = generateRandomInteger(3, 5) * 1000;
		return new Promise((resolve) => {
			setTimeout(() => {
				resolve(token);
			}, timeout);
		});
	};

	const handleLogin = async (e: MouseEvent) => {
		setLoading(true);
		e.preventDefault();
		if (!needCertificate) {
			const token = await fakeLogin();
			setLoading(false);
			login(token);
			window.electron.ipcRenderer.send('action:login', {});
			return;
		}

		if (certificate === '') {
			message.warning('没有找到证书，请检查usb-key！');
			setLoading(false);
			return;
		}
		if (password === '') {
			message.warning('请输入用户密码');
			passwordRef.current.focus();
			setLoading(false);
			return;
		}

		const bRet = xtxsync.SOF_Login(certificate, password);
		if (bRet) {
			const sof = xtxsync.SOF_ExportUserCert(certificate);
			const body = { base64Cert: sof };
			const queryString = objectToQueryString(body);
			const result = await certificateLogin(queryString);
			setLoading(false);
			login(result.token);
			window.electron.ipcRenderer.send('action:login', {});
		} else {
			setLoading(false);
			message.error('失败，请验证证书密码正确性!');
		}
	};

	return (
		<LoginContainer>
			<h2></h2>
			<Spin tip="加载中" size="large" spinning={loading}>
				<div className="login-box">
					<div className="login-box-row">
						<p className="certificate-standard-title">证书：</p>
						<select
							className="certificate-standard-width certificate-select"
							value={certificate}
							ref={certificateRef}
							onChange={(e) => setCertificate(e.target.value)}
						></select>
					</div>
					<div className="login-box-row">
						<p className="certificate-standard-title">密码：</p>
						<input
							type="password"
							className="certificate-standard-width certificate-input"
							value={password}
							ref={passwordRef}
							onChange={(e) => setPassword(e.target.value)}
						/>
					</div>
					<div className="certificate-standard-login">
						<Button
							className="certificate-button"
							type="primary"
							onClick={handleLogin}
						>
							登录
						</Button>
					</div>
				</div>
			</Spin>
		</LoginContainer>
	);
};

export default LoginPage;
