import styled from 'styled-components';
import boxTitleBg from '../../images/networkedData/boxTitleBg.png';
import titleBg from '../../images/networkedData/titleBg.png';
import imags from '../../images/networkedData/index';

const Container = styled.div`
	height: 100%;
	display: flex;
	justify-content: space-between;

	> div {
		width: 50%;
	}
	.echarts-line {
		width: 100%;
		padding-top: 10px;
		display: flex;
		align-items: center;

		.echarts-title {
			display: flex;
			flex-direction: column;
			justify-content: center;
			height: 20px;
			font-size: 14px;
			line-height: 20px;
			color: #fff;
			cursor: pointer;
			padding-bottom: 30px;

			span {
				width: 20px;
				height: 40px;
				color: #e8f4ff;
				display: flex;
				justify-content: center;
				align-items: center;
				flex-shrink: 0;
				margin: 20px 10px;
				border-right: 2px solid rgb(26, 62, 71);
				padding-right: 10px;
			}
		}
		.title-select {
			color: rgb(62, 189, 203) !important;
			border-right: 2px solid rgb(62, 189, 203) !important;
		}
		.title-un-select {
		}
	}
`;
export { Container };
