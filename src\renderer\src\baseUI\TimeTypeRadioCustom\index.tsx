import React, { useEffect, useState } from 'react';
import { Radio } from 'antd';
import { TimeTypeRadioContent } from './style';
import CustomPopUps from './components/CustomPopUps';

export default function IndustryEmissions(props) {
	const {
		type,
		typeOptions,
		timeType,
		setTimeType,
		setCustomDate,
		right,
		top,
		customStartTime,
	} = props;
	const [showPop, setShowPop] = useState<boolean>(false);

	const defaultTypeOptions = [
		{
			label: '当日',
			value: 'day',
		},
		{
			label: '昨日',
			value: 'yesterday',
		},
		{
			label: '7天',
			value: '7day',
		},
	];
	const [typeOption, setTypeOptions] = useState(defaultTypeOptions);

	const [defaultType, setDefaultType] = useState(type ? type : 'day');
	useEffect(() => {
		typeOptions && setTypeOptions(typeOptions);
		timeType && timeType(type);
	}, [type]);

	const onTypeChange = (e) => {
		if (e.target.value === 'custom') {
			setShowPop(true);
		} else {
			timeType(e.target.value);
			setShowPop(false);
		}
		setDefaultType(e.target.value);
	};

	const onFocusCustom = (e) => {
		if (e.target._wrapperState.initialValue === 'custom') {
			setShowPop(!showPop);
		}
	};

	return (
		<TimeTypeRadioContent>
			<Radio.Group
				options={typeOption}
				onChange={onTypeChange}
				value={defaultType}
				optionType="button"
				onFocus={onFocusCustom}
			/>
			<CustomPopUps
				right={right}
				top={top}
				showPop={showPop}
				setShowPop={setShowPop}
				setTimeType={setTimeType}
				setCustomDate={setCustomDate}
				customStartTime={customStartTime}
			/>
		</TimeTypeRadioContent>
	);
}
