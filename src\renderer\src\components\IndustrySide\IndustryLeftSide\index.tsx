import { useEffect, useState, useContext } from 'react';
import Echarts from '@renderer/components/echarts';
import { Space, Button, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import {
	getRegionalIndustryProportion,
	getIndustryOverview,
	getIndustryDistanceAnalyse,
	getIndustryOnlineAnalyse,
} from '@renderer/api';
import {
	getIper,
	getIndustryTop,
	getIndustryMid,
	getIndustryBtm,
} from '../components/IndustryProportion';
import { mergeObjects } from '../components/PublicMethods';
import industryClassification, {
	EmissionLevelData,
	VehicleLevelData,
} from '../components/IndustryClassification';
import { PageContext } from '@renderer/context';
import {
	COUNTY_ID_NAME_MAP,
	unitList,
} from '@renderer/marsLayers/components/AreaStreetStyles';
import {
	formatNumber,
	calculatePercentage,
	tranNumber,
} from '@renderer/hooks/index';
import { IndustryLeftSideStyled, LayoutContainer } from './styles';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const IndustryLeftSide = (props: Props) => {
	const { currentSelectedId, streetShipData } = props;
	const {
		currentTopNavMenu,
		selectRegionDate,
		regionName,
		streetName,
		countyId,
	} = useContext(PageContext);
	const [dataSum, setDataSum] = useState(0);
	const [optionsIP, setOptionsIP] = useState({}); //在线车辆饼图占比echarts
	const [optionsTopBar, setOptionsTopBar] = useState({}); //行业分类堆叠图
	const [optionsRA, setOptionsRA] = useState({}); //区域分析echarts
	const [optionsOS, setOptionsOS] = useState({}); //上线情况echarts
	const [emissionLevel, setEmissionLevel] = useState('全部');
	const [title1, setTitle1] = useState(null);

	const handleMenuClick: MenuProps['onClick'] = (e) => {
		setEmissionLevel(e.key);
	};
	const items: MenuProps['items'] = EmissionLevelData.map((item, idx) => {
		return {
			label: item,
			key: `${item}`,
		};
	});
	const menuProps = {
		items,
		onClick: handleMenuClick,
	};

	function separateNumberAndChinese(str) {
		if (str === '0.00' || str === 0) {
			return { number: parseFloat(str), chinese: '' };
		}
		const matches = str?.match(/([\d.]+)(\D+)/);
		if (matches) {
			const number = parseFloat(matches[1]);
			const chinese = matches[2];
			return { number, chinese };
		} else {
			return { number: parseFloat(str), chinese: '' };
		}
	}

	const setUpOption2 = (data) => {
		const unit = unitList?.find((i) => i.name === currentTopNavMenu)?.unit;
		let sumCountsByAdm: any = [];
		let name;
		sumCountsByAdm = data?.reduce((acc, curr) => {
			curr.data.forEach((count, index) => {
				acc[index] = (acc[index] || 0) + count;
			});
			return acc;
		}, []);
		name = data[0]?.county?.map((id) => COUNTY_ID_NAME_MAP[id]);
		const admCounts = sumCountsByAdm?.map((total, index) => {
			return {
				name: name[index],
				value: total >= 1 ? Number(total.toFixed(1)) : Number(total.toFixed(2)),
			};
		});
		const sortedAdmCounts = admCounts.sort((a, b) => a.value - b.value);
		const tradeAdm = sortedAdmCounts.map((item) => item.name);
		const tradeCount = sortedAdmCounts.map((item) => item.value);

		const option2 = getIndustryMid({
			...data,
			name: tradeAdm,
			originalName: name,
			total: tradeCount,
			unit,
		});
		setOptionsRA(option2);
	};

	const getIndustryDataResult = (res) => {
		if (res?.data?.length > 0) {
			const carData = res?.data?.map((item) => {
				return {
					name: Object.keys(item)[0],
					value: formatNumber(Object.values(item)[0]),
				};
			});
			setOptionsIP(getIper(calculatePercentage(carData)));
			setDataSum(res?.sum);
		} else {
			setDataSum(0);
			setOptionsIP(getIper([]));
		}
	};

	// 全市联网总量占比
	const getIndustryData = (params) => {
		getRegionalIndustryProportion(params)
			.then((res) => {
				getIndustryDataResult(res);
			})
			.catch((err) => {
				setDataSum(0);
				setOptionsIP(getIper([]));
			});
	};

	// 全市堆叠图
	const getIndustryTopData = (params) => {
		getIndustryOverview(params)
			.then((res) => {
				if (res?.length > 0) {
					const option = getIndustryTop(res);
					setOptionsTopBar(option);
				} else {
					setOptionsTopBar({});
				}
			})
			.catch((err) => {
				setOptionsTopBar({});
			});
	};

	// 在线情况分析
	const getIndustryMidData = (params) => {
		getIndustryOnlineAnalyse(params)
			.then((res) => {
				if (res?.length > 0) {
					const option = getIndustryMid(res);
					setOptionsRA(option);
				} else {
					setOptionsRA({});
				}
			})
			.catch((err) => {
				setOptionsRA({});
			});
	};

	// 里程分析
	const getIndustryBottom = (id, params) => {
		getIndustryDistanceAnalyse(params)
			.then((res) => {
				if (res?.length > 0) {
					const option = getIndustryBtm(res);
					setOptionsOS(option);
				} else {
					setOptionsOS({});
				}
			})
			.catch((err) => {
				setOptionsOS({});
			});
	};

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time || !end_time) return;
		setTitle1(streetName ? streetName : regionName);
		const params: any = {
			start_time,
			end_time,
			time_type,
			region_name: regionName,
		};
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		getIndustryMidData(params);
	}, [selectRegionDate, currentTopNavMenu, regionName]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time || !end_time) return;
		setTitle1(streetName ? streetName : regionName);
		let county =
			Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) + 1;

		const params: any = {
			start_time,
			end_time,
			time_type,
			topic: 5,
		};
		if (regionName) {
			setTitle1(regionName);
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		} else {
			setTitle1('全市');
		}
		getIndustryData(params);
	}, [selectRegionDate, currentTopNavMenu, regionName]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time || !end_time) return;
		const params: any = {
			start_time,
			end_time,
		};
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		getIndustryTopData(params);
	}, [selectRegionDate, currentTopNavMenu, regionName]);

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time || !end_time) return;
		const params: any = {
			start_time,
			end_time,
			time_type,
		};
		if (regionName) {
			params.county_id =
				Object.values(COUNTY_ID_NAME_MAP).findIndex((i) => i === regionName) +
				1;
		}
		if (emissionLevel && emissionLevel !== '全部') {
			params.emission_level = emissionLevel;
		}
		getIndustryBottom(currentSelectedId, params);
	}, [emissionLevel, selectRegionDate, currentSelectedId, regionName]);

	return (
		<IndustryLeftSideStyled>
			<div className="Industrycontent">
				<div className="slidingLayer">
					<span className="slidtext">{title1 ? title1 : '全市'}数据总览</span>
				</div>
				<LayoutContainer style={{ height: '20%' }}>
					<div className={'container'}>
						<div className="describe">
							<dl className="no2">
								<span style={{ fontSize: '24px' }}>
									在线车辆数：
									<span style={{ fontSize: '36px', color: '#f5803e' }}>
										{separateNumberAndChinese(tranNumber(dataSum, 2)).number}
										{separateNumberAndChinese(tranNumber(dataSum, 2)).chinese}
									</span>
								</span>
							</dl>
							<div className="echarts-container">
								<div className="echarts">
									<Echarts option={optionsIP}></Echarts>
								</div>
							</div>
						</div>
						<div className="echarts-line">
							<Echarts
								option={optionsTopBar}
								style={{ height: '100%', flex: 1 }}
								notMerge={true}
							></Echarts>
						</div>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">在线情况分析</span>
				</div>
				<LayoutContainer style={{ height: '35%' }}>
					<div className="echarts-mid">
						<Echarts
							option={optionsRA}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">里程分析</span>
					<Dropdown menu={menuProps} className="dropDown">
						<Button>
							<Space>
								{emissionLevel}
								<DownOutlined />
							</Space>
						</Button>
					</Dropdown>
				</div>
				<LayoutContainer style={{ height: '25%' }}>
					<div className="echarts-btm">
						<Echarts
							option={optionsOS}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
			</div>
		</IndustryLeftSideStyled>
	);
};

export default IndustryLeftSide;
