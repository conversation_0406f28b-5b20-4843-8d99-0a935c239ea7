import { useEffect, useState } from 'react';
import '@animxyz/core';
import { XyzTransitionGroup } from '@animxyz/react';
import Box from '../../baseUI/Box';
import background from '../../images/background';
import '../../../style.scss';
import { VehicleOnlineConditionVehContent } from './style';

export default function VehicleOnlineConditionVeh() {
	const style = {
		fontSize: '200px',
		color: 'red',
	};
	const [isVisible, setIsVisible] = useState(false);

	useEffect(() => {
		// setTimeout(() => {
		setIsVisible(true);
		// })
	}, []);

	return (
		<VehicleOnlineConditionVehContent>
			<XyzTransitionGroup
				className="item-group"
				appear
				mode="out-in"
				xyz="fade left-100%"
			>
				{isVisible && (
					<div className="square">
						<Box>
							<img src={background.mode1} className="left-bg" />
						</Box>
					</div>
				)}
			</XyzTransitionGroup>
		</VehicleOnlineConditionVehContent>
	);
}
