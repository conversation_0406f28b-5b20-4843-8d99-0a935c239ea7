// 街乡镇违规
import React, { useEffect, useState } from 'react';
import LevelTwoTitle from '@renderer/components/LevelTwoTitle';
import { RankingDeregulationListStyled } from './style';

const StreetandRanking = (props: any) => {
	const { ranking } = props;
	const violatioData = [
		{
			rank_desc: '1',
			name: '海淀区',
			no1: '清河街道',
			no2: '小明路',
			no3: '大华路',
		},
		{
			rank_desc: '2',
			name: '朝阳区',
			no1: '望京街道',
			no2: '光辉路',
			no3: '启明路',
		},
		{
			rank_desc: '3',
			name: '东城区',
			no1: '东直门街道',
			no2: '长安街',
			no3: '和平里',
		},
		{
			rank_desc: '4',
			name: '西城区',
			no1: '复兴门街道',
			no2: '宣武门外大街',
			no3: '月坛北街',
		},
		{
			rank_desc: '5',
			name: '丰台区',
			no1: '方庄地区',
			no2: '蒲黄榆路',
			no3: '丰台路',
		},
		{
			rank_desc: '6',
			name: '石景山区',
			no1: '八角街道',
			no2: '老山东街',
			no3: '金顶街',
		},
		{
			rank_desc: '7',
			name: '通州区',
			no1: '新华街道',
			no2: '北苑路',
			no3: '梨园地区',
		},
		{
			rank_desc: '8',
			name: '昌平区',
			no1: '天通苑北街道',
			no2: '回龙观地区',
			no3: '霍营街道',
		},
		{
			rank_desc: '9',
			name: '大兴区',
			no1: '黄村镇',
			no2: '兴丰大街',
			no3: '清源路',
		},
		{
			rank_desc: '10',
			name: '怀柔区',
			no1: '桥梓镇',
			no2: '怀柔街道',
			no3: '雁栖地区',
		},
	];
	const [dataCreate, setDateCreate] = useState<any>(violatioData);

	useEffect(() => {
		if (ranking?.length > 0) {
			setDateCreate(ranking);
		}
	});

	return (
		<>
			<RankingDeregulationListStyled>
				<div className="middle-container">
					<div className="Violation">
						<LevelTwoTitle
							className="Violation1"
							title="北京市各区街乡镇重型车前3名"
						></LevelTwoTitle>
					</div>
					<div className="table">
						{dataCreate.length && (
							<dl>
								<dt>
									<span className="no1">排名</span>
									<span className="no2">区域</span>
									<span className="no3">第一名</span>
									<span className="no4">第二名</span>
									<span className="no5">第三名</span>
								</dt>
								{dataCreate.slice(0, 10).map((item, key) => {
									return (
										<dd key={key}>
											<span className="no1">{key + 1}</span>
											<span className="no2">{item.name}</span>
											<span className="no3">{item.no1}</span>
											<span className="no4">{item.no2}</span>
											<span className="no5">{item.no3}</span>
										</dd>
									);
								})}
							</dl>
						)}
					</div>
				</div>
			</RankingDeregulationListStyled>
		</>
	);
};
export default StreetandRanking;
