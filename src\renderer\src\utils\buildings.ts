const buildingShaders = `
// 注意shader中写浮点数是，一定要带小数点，否则会报错，比如0需要写成0.0，1要写成1.0
float _baseHeight = 0.0; // 物体的基础高度，需要修改成一个合适的建筑基础高度
float _heightRange = 120.0; // 高亮的范围(_baseHeight ~ _baseHeight + _heightRange) 默认是 0-60米
float _glowRange = 200.0; // 光环的移动范围(高度)

// 建筑基础色
float mars_height = marsJzwHeight - _baseHeight;
float mars_a11 = fract(czm_frameNumber / 120.0) * 3.14159265 * 2.0;
float mars_a12 = (mars_height + 120.0) / _heightRange + sin(mars_a11) * 0.2;

gl_FragColor *= vec4(0.2, 0.5, 1.0, 1.0);
gl_FragColor *= vec4(mars_a12, mars_a12, mars_a12, 0.5);

// 动态光环
float mars_a13 = fract(czm_frameNumber / 360.0);
float mars_h = clamp(mars_height / _glowRange, 0.0, 1.0);
mars_a13 = abs(mars_a13 - 0.5) * 2.0;
float mars_diff = step(0.005, abs(mars_h - mars_a13));
gl_FragColor.rgb += gl_FragColor.rgb * (1.0 - mars_diff);

`;

const buildingShadersV2 = `
// 注意shader中写浮点数是，一定要带小数点，否则会报错，比如0需要写成0.0，1要写成1.0
float _baseHeight = 0.0; // 物体的基础高度，需要修改成一个合适的建筑基础高度
float _heightRange = 120.0; // 高亮的范围(_baseHeight ~ _baseHeight + _heightRange) 默认是 0-60米
float _glowRange = 200.0; // 光环的移动范围(高度)

// 建筑基础色
float mars_height = marsJzwHeight - _baseHeight;
float mars_a11 = fract(czm_frameNumber / 120.0) * 3.14159265 * 2.0;
float mars_a12 = (mars_height + 120.0) / _heightRange + sin(mars_a11) * 0.2;

gl_FragColor *= vec4(1.0, 1.0, 1.0, 0.5);
// gl_FragColor *= vec4(mars_a12, mars_a12, mars_a12, 0.5);

// 动态光环
float mars_a13 = fract(czm_frameNumber / 360.0);
float mars_h = clamp(mars_height / _glowRange, 0.0, 1.0);
mars_a13 = abs(mars_a13 - 0.5) * 2.0;
float mars_diff = step(0.005, abs(mars_h - mars_a13));
gl_FragColor.rgb += gl_FragColor.rgb * (1.0 - mars_diff);

`;

export { buildingShaders, buildingShadersV2 };
