/**
 * 时间选择器
 */
import React, { useEffect, useState, useContext } from 'react';
import { DatePicker, Space, Button } from 'antd';
import { CaretLeftOutlined, CaretRightOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import styled from 'styled-components';

const TimeSelector = ({ returnTime }) => {
	const [startTime, setStartTime] = useState(dayjs());
	const dateFormat = 'YYYY-MM-DD HH';
	const leftClick = () => {
		setStartTime(startTime.subtract(1, 'hour'));
	};
	const rightClick = () => {
		if (dayjs().diff(startTime, 'hour') > 0)
			setStartTime(startTime.add(1, 'hour'));
	};
	const onOk = (e) => {
		setStartTime(e);
	};
	const onChangeDate = (date, dateString) => {
		setStartTime(date);
	};

	useEffect(() => {
		returnTime(startTime);
	}, [startTime]);

	return (
		<DateTimePickerStyled>
			<Space direction="vertical" size={12}>
				<Button
					onClick={leftClick}
					shape="circle"
					icon={<CaretLeftOutlined />}
				/>
				<DatePicker
					onChange={onChangeDate}
					format={dateFormat}
					defaultValue={dayjs()}
					showTime
					value={dayjs(startTime)}
					onOk={onOk}
				/>
				<Button
					onClick={rightClick}
					shape="circle"
					icon={<CaretRightOutlined />}
				/>
			</Space>
		</DateTimePickerStyled>
	);
};
export default TimeSelector;

const DateTimePickerStyled = styled.div`
	/* position: absolute;
  right: 150px;
  top: 100px;
  z-index: 5; */
	padding: 15px 25px;
	background-color: #00113586;
	border-radius: 10px;
	.ant-space {
		display: flex;
		flex-direction: row;
		.ant-space-item {
			.ant-btn {
				background-color: transparent;
			}
		}
	}
	.ant-picker {
		background: rgba(64, 146, 171, 0.16);
		border-radius: 4px;
		border: 1px solid #3fdaff;
		font-size: 18px !important;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		height: 32px;
		color: #ffffff !important;
		display: flex;
		align-items: center;
		.iLeTzM .ant-picker-input {
			font-size: 20px;
			color: #e2f0ff !important;
		}
	}
`;
