{"name": "tripod-visualization-destop", "productName": "重柴大屏", "version": "2.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://www.electronjs.org", "scripts": {"prepare": "husky", "format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "local": "electron-vite dev", "dev": "electron-vite dev --mode prod", "dev:test": "electron-vite dev --mode test", "dev:vpn": "electron-vite dev --mode vpn", "build": "electron-vite build --mode prod", "postinstall": "electron-builder install-app-deps", "build:win": "npm run build && electron-builder --win --config", "build:win:vpn": "npm run build && electron-builder --win --config --mode vpn", "build:mac": "electron-vite build && electron-builder --mac --config", "build:linux": "electron-vite build && electron-builder --linux --config"}, "dependencies": {"@animxyz/react": "^0.6.7", "@electron-toolkit/preload": "^1.0.3", "@electron-toolkit/utils": "^1.0.2", "@mars3d/heatmap.js": "^2.0.7", "@turf/turf": "^6.5.0", "@types/three": "^0.177.0", "@upsetjs/venn.js": "^1.4.2", "antd": "^5.5.1", "axios": "^0.27.2", "cesium": "^1.113.1", "cesium-heatmap": "^0.0.4", "cors": "^2.8.5", "d3": "^7.9.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "electron-json-storage": "^4.6.0", "electron-updater": "^5.3.0", "express": "^4.18.2", "localforage": "^1.10.0", "lodash": "^4.17.21", "mars3d": "3.7.16", "mars3d-cesium": "^1.118.0", "mars3d-heatmap": "^3.7.3", "mars3d-wind": "^3.7.7", "moment": "^2.29.4", "node-fetch": "2.6.9", "react-router-dom": "^6.25.1", "redis": "^4.6.4", "sass": "^1.64.2", "size-sensor": "^1.0.1", "styled-components": "6.0.0-rc.1", "three": "^0.177.0", "vite-plugin-mars3d": "^3.1.3", "ws": "^8.13.0"}, "devDependencies": {"@commitlint/cli": "^17.0.0", "@commitlint/config-conventional": "^17.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@electron/notarize": "^1.2.3", "@types/node": "16.18.11", "@types/react": "18.0.26", "@types/react-dom": "18.0.10", "@types/ws": "^8.5.4", "@typescript-eslint/eslint-plugin": "^5.48.0", "@typescript-eslint/parser": "^5.48.0", "@vitejs/plugin-react": "^3.0.1", "electron": "^21.3.3", "electron-builder": "^23.6.0", "electron-prebuilt": "^1.4.13", "electron-vite": "^1.0.17", "eslint": "^8.31.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.11", "husky": "^9.1.1", "kdbush": "^3.0.0", "lint-staged": "^12.4.1", "prettier": "^2.8.2", "react": "^18.2.0", "react-dom": "^18.2.0", "request": "^2.88.2", "typescript": "^4.9.4", "vite": "^4.0.4", "vite-plugin-cesium": "^1.2.22"}}