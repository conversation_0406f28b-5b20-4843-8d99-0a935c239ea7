import { useEffect, useState, useRef, ElementType, FC } from 'react';
import { Navigate } from 'react-router-dom';

import { useAuth } from '@renderer/hooks';

export type AsyncRouteProps = {
	asyncComponent: () => Promise<any>;
	data?: any;
};

export const AsyncRoute: FC<AsyncRouteProps> = (props) => {
	const { data = {} } = props;
	const [isMounted, setIsMounted] = useState(false);
	const AsyncComponentRef = useRef<ElementType>();

	const initFun = async () => {
		const { default: Component } = await props.asyncComponent();
		AsyncComponentRef.current = Component;
		setIsMounted(true);
	};
	useEffect(() => {
		initFun();
		return () => {
			AsyncComponentRef.current = undefined;
		};
	}, []);

	if (!AsyncComponentRef.current) {
		return null;
	}

	return <AsyncComponentRef.current {...data} />;
};

export const CustomeAsyncRoute = (_props) => {
	return (props) => {
		return <AsyncRoute {...Object.assign(_props, props)} />;
	};
};

export const ProtectedRoute = ({ children }) => {
	const { token } = useAuth();

	if (!token) {
		return <Navigate to="/login" />;
	}

	return children;
};
