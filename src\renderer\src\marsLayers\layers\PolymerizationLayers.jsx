import React, { useEffect, useContext, useState, useRef } from 'react';
import * as mars3d from 'mars3d';
import MapContext from '../../context';
import combine1 from '@renderer/images/layer/std/combine1.png';
import combine2 from '@renderer/images/layer/std/combine2.png';
import combine3 from '@renderer/images/layer/std/combine3.png';
import { getpolymerization } from '@renderer/api';
import moment from 'moment';

export default (props) => {
	const { visible, map, layerId } = props;
	const { sceneMode } = useContext(MapContext);
	let counter = 0,
		data;

	const combineIconAndLabel = (url, label, size) => {
		// 创建画布对象
		let canvas = document.createElement('canvas');
		canvas.width = size;
		canvas.height = size;
		let ctx = canvas.getContext('2d', { willReadFrequently: true });
		let promise = new mars3d.Cesium.Resource.fetchImage(url).then((image) => {
			// 异常判断
			try {
				ctx.drawImage(image, 0, 0);
			} catch (e) {
				console.log(e);
			}

			// 渲染字体
			// font属性设置顺序：font-style, font-variant, font-weight, font-size, line-height, font-family
			ctx.fillStyle = mars3d.Cesium.Color.WHITE.toCssColorString();
			ctx.font = 'bold 50px Microsoft YaHei';
			ctx.textAlign = 'center';
			ctx.textBaseline = 'middle';
			ctx.fillText(label, size / 2, size / 2);

			return canvas;
		});

		return promise;
	};

	const initCluster = (viewer, dataSource) => {
		viewer.dataSources.add(dataSource);
		// 设置聚合参数
		dataSource.clustering.enabled = true;
		dataSource.clustering.pixelRange = 100;
		dataSource.clustering.minimumClusterSize = 3;
		// foreach用于调用数组的每个元素，并将元素传递给回调函数。
		dataSource.entities.values.forEach((entity) => {
			// 将点拉伸一定高度，防止被地形压盖
			// entity.position._value.z += 50.0
			// 使用大小为64*64的icon，缩小展示poi
			entity.billboard = {
				image: combine1,
				width: 168,
				height: 160,
				scale: 0.3,
			};
			let displayValue = entity.name;
			let displayText;

			if (displayValue === undefined) {
				// 如果displayValue为undefined，则隐藏billboard和label
				entity.billboard.show = false;
			}

			if (displayValue < 1) {
				displayText = (displayValue * 1000).toFixed(0) + 'mg';
				if (displayText === '0mg') {
					displayText = 1 + 'mg';
				}
			} else {
				displayText = displayValue;
			}
			entity.label = {
				text: `${displayText}`,
				font: 'bold 15px Microsoft YaHei',
				// 竖直对齐方式
				verticalOrigin: mars3d.Cesium.VerticalOrigin.CENTER,
				// 水平对齐方式
				horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
				// 偏移量
				pixelOffset: new mars3d.Cesium.Cartesian2(0, 0),
			};
		});

		// 添加监听函数
		dataSource.clustering.clusterEvent.addEventListener(clusterEvent);
	};

	const clusterEvent = (clusteredEntities, cluster) => {
		let nameSum;
		let formattedSum;

		let rowSum;
		let convertedValue;
		let unit;
		let formSum;

		if (layerId === 'trafficPolymerization') {
			const _name = clusteredEntities.map((item) => item.name);
			nameSum = _name.reduce((sum, entity) => sum + entity, 0);
			if (nameSum > 10000) {
				formattedSum = `${Math.floor(nameSum / 10000)}万`;
			} else {
				formattedSum = nameSum;
			}
		} else if (layerId === 'vehiclepolymerization') {
			const _name = clusteredEntities.map((item) => item.name);
			const filterData = _name.filter((value) => value !== undefined);

			const convertedData = filterData.map((item) => {
				let valueInGrams = item * 1000;
				if (valueInGrams > 1) {
					convertedValue = valueInGrams.toFixed(0); // 如果大于1克
					unit = 'g';
				} else if (valueInGrams < 1) {
					convertedValue = (valueInGrams / 1000).toFixed(0); // 如果小于或等于1克 转换为千克
					unit = 'kg';
				}
				return `${convertedValue}${unit}`;
			});

			const dataNumbers = convertedData.map((s) => parseFloat(s));
			rowSum = dataNumbers.reduce((sum, entity) => sum + entity, 0);
			// 判断总和使用哪个单位
			if (rowSum < 1) {
				formSum = (rowSum * 1000).toFixed(0) + 'mg';
				if (rowSum === 0) {
					formSum = '1mg';
				}
			} else if (rowSum < 1000) {
				formSum = rowSum.toFixed(0) + 'g';
			} else if (rowSum >= 1000) {
				formSum = (rowSum / 1000).toFixed(0) + 'kg';
			}
		}

		counter++;
		if (counter > 3000) {
			data.clustering.clusterEvent.removeEventListener(clusterEvent);
			setTimeout(() => {
				counter = 0;
				map.dataSources.remove(data, true);
				initCluster(map, data);
			}, 0);
			return;
		}
		// 关闭自带的显示聚合数量的标签
		cluster.label.show = false;
		cluster.billboard.show = true;
		cluster.billboard.verticalOrigin = mars3d.Cesium.VerticalOrigin.BOTTOM;

		// 根据聚合数量的多少设置不同层级的图片以及大小
		if (layerId === 'trafficPolymerization') {
			if (nameSum > 100000) {
				cluster.billboard.image = combineIconAndLabel(
					combine3,
					formattedSum,
					160,
				);
				cluster.billboard.scale = 0.5;
			} else if (nameSum > 10000) {
				cluster.billboard.image = combineIconAndLabel(
					combine2,
					formattedSum,
					160,
				);
				cluster.billboard.scale = 0.4;
			} else if (nameSum < 10000) {
				cluster.billboard.image = combineIconAndLabel(
					combine1,
					formattedSum,
					160,
				);
				cluster.billboard.scale = 0.3;
			}
		} else if (layerId === 'vehiclepolymerization') {
			if (rowSum < 1) {
				cluster.billboard.image = combineIconAndLabel(combine1, formSum, 160);
				cluster.billboard.scale = 0.3;
			} else if (rowSum < 1000) {
				cluster.billboard.image = combineIconAndLabel(combine2, formSum, 160);
				cluster.billboard.scale = 0.4;
			} else if (rowSum >= 1000) {
				cluster.billboard.image = combineIconAndLabel(combine3, formSum, 160);
				cluster.billboard.scale = 0.5;
			}
		}
		cluster.billboard.width = 168;
		cluster.billboard.height = 160;
	};

	useEffect(() => {
		if (!visible) return;
		console.log(sceneMode);
		let target;

		if (layerId === 'trafficPolymerization') {
			target = '1';
		} else if (layerId === 'vehiclepolymerization') {
			target = '2';
		}
		let top = 20000;
		let jsonData = sessionStorage.getItem('zcJsonData');
		if (jsonData) {
			jsonData = JSON.parse(jsonData);
			top = jsonData?.polymerization?.top ? jsonData.polymerization.top : top;
		}

		let json = {
			hour: moment().subtract(2, 'hour').format('YYYY-MM-DD HH:00:00'),
			// top: 140000,
			top,
			target,
		};

		getpolymerization(json).then((res) => {
			let features = res.features.map((item) => {
				return {
					...item,
					properties: {
						...item.properties,
						name: item.properties.value,
					},
				};
			});
			let response = res;
			response.features = features;
			new mars3d.Cesium.GeoJsonDataSource()
				.load(response)
				.then((dataSource) => {
					data = dataSource;
					initCluster(map, data);
				})
				.catch((err) => {
					console.log(err);
				});
		});

		return () => {
			if (data) map.dataSources.remove(data, true);
			data = null;
		};
	}, [visible, sceneMode]);

	return null;
};
