export enum LayerType {
	VEHICLE_STATUS = 'vehicleStatus', // 车辆情况
	VEHICLE_ONLINE = 'vehicleOnline', // 车辆实时在线情况
	VEHICLE_OFFLINE = 'vehicleOffline', // 车辆离线情况
	VEHICLE_Historical_Trajectory = 'vehicleHistoricalTrajectory', // 车辆历史轨迹
	TRAFFIC_VOLUME = 'trafficVolume', // 车流量情况
	TRAFFIC_VOLUME_DISTRIBUTION = 'trafficVolumeDistribution', // 路网图
	TRAFFIC_VOLUME_THERMODYNAMICS = 'trafficVolumeThermodynamics', // 热力图
	TRAFFIC_VOLUME_Hotspot_Grid = 'trafficVolumeHotspotGrid', // 车流量热点网格
	VEHICLE_EMISSIONS = 'vehicleEmissions', // 车辆排放情况
	VEHICLE_EMISSIONS_DISTRIBUTION = 'vehicleEmissionsDistribution', // 路网图
	VEHICLE_EMISSIONS_THERMODYNAMICS = 'vehicleEmissionsThermodynamics', // 热力图
	VEHICLE_EMISSIONS_Hotspot_Grid = 'vehicleEmissionsHotspotGrid', // 车辆排放热点网格
	VIOLATING_VEHICLES = 'violatingVehicles', // 违规车辆
	VIOLATING_ANALYSIS = 'ViolationAnalysis', // 违规分析
	USER_ENTERPRISE = 'userEnterprise', //用户企业
	TRAFFIC_POLYMERIZATION = 'trafficPolymerization', // 车流量聚合
	VEHICLE_POLYMERIZATION = 'vehiclepolymerization', // 车辆排放聚合

	AREA = 'area', // 区域
	AREA_DISCHARGE = 'area_discharge', // 区域排放
	AREA_DEREGULATION = 'area_deregulation', // 区域违规
	STREET_DISCHARGE = 'street_discharge', // 街乡镇违规
	STREET_DEREGULATION = 'street_deregulation', // 街乡镇违规
	REGISTRIES_DSTRIBUTION = 'registries_distribution', // 注册区域分布
	SERVICE = 'service', //一级菜单横线
	LEVEL = 'level', //二级菜单
	SPATIALDISTRIBUTION = 'spatialDistribution', //空间分布

	Street_Township = 'streetTownship', // 街乡镇
	INDUSTRY = 'industry', // 行业
	SITETYPE = 'site_type', // 站点类型
	STDG = 'stdg', // 国控站
	STDS = 'stds', // 市控站
	HIGHT = 'hight', // 高密度站
	GRIDS = 'grids', // 热点网格
	WEATHERSTATION = 'weather_station', // 气象站

	AIR = 'air', //空气
	TEMP = 'temp', // 温度
	HUMIDITY = 'humidity', // 湿度
	AIRPRESSURE = 'air_pressure', // 气压
	AIRINVERSION = 'air_inversion', // 气象反演

	ROAD = 'road', // 道路 路段
}

export const layerDatas = [
	{
		key: LayerType.VEHICLE_STATUS,
		service: LayerType.SERVICE,
		name: '车辆情况',
		visible: true,
		selectionType: 'single', // 单选
		hideEchartsMap: false,
		child: [
			{
				key: LayerType.VEHICLE_ONLINE,
				name: '实时在线',
				roadNetwork: true,
				visible: true,
				showModeMenu: true,
				vehicleSearchPanel: true,
			},
			{
				key: LayerType.VEHICLE_OFFLINE,
				name: '离线车辆',
				roadNetwork: true,
				visible: false,
				showModeMenu: true,
				vehicleSearchPanel: true,
				// id: 75318 // 与操作端模块id映射
			},
			// {
			// 	key: LayerType.VIOLATING_VEHICLES,
			// 	name: '违规车辆',
			// 	visible: false,
			// 	roadNetwork: true,
			// 	vehicleSearchPanel: true,
			// 	// id: 75319 // 与操作端模块id映射
			// },
		],
	},
	{
		key: LayerType.VIOLATING_ANALYSIS,
		service: LayerType.SERVICE,
		name: '违规分析',
		visible: false,
		roadNetwork: true,
		showBackground: false,
		hideModeMenu: true,
		hideEchartsMap: true,
		child: [],
	},
	// {
	//   key: LayerType.VEHICLE_ONLINE,
	//   service: LayerType.SERVICE,
	//   name: '车辆情况',
	//   visible: true,
	//   roadNetwork: true,
	//   child: []
	// },
	// {
	//   key: LayerType.VEHICLE_OFFLINE,
	//   service: LayerType.SERVICE,
	//   name: '离线车辆',
	//   visible: false,
	//   roadNetwork: true,
	//   child: []
	// },
	// {
	//   key: LayerType.VEHICLE_Historical_Trajectory,
	//   service: LayerType.SERVICE,
	//   name: '车辆历史轨迹',
	//   visible: false,
	//   roadNetwork: true,
	//   hideEchartsMap: true,
	//   child: []
	// },
	/*
	{
		key: LayerType.TRAFFIC_VOLUME,
		service: LayerType.SERVICE,
		name: '车流量情况',
		visible: false,
		selectionType: 'single', // 单选
		hideEchartsMap: false,
		child: [
			{
				key: LayerType.TRAFFIC_VOLUME_DISTRIBUTION,
				name: '路网图',
				visible: false,
			},
			{
				key: LayerType.TRAFFIC_VOLUME_THERMODYNAMICS,
				name: '热力图',
				visible: false,
			},
			{
				key: LayerType.TRAFFIC_POLYMERIZATION,
				name: '聚合',
				visible: false,
				roadNetwork: true,
				child: [],
			},
			{
				key: LayerType.TRAFFIC_VOLUME_Hotspot_Grid,
				name: '网格',
				visible: false,
			},
		],
	},
	{
		key: LayerType.VEHICLE_EMISSIONS,
		service: LayerType.SERVICE,
		name: '车辆排放情况',
		visible: false,
		// selectionType: 'multiple', // 多选
		selectionType: 'single', // 单选
		hideEchartsMap: false,
		child: [
			{
				key: LayerType.VEHICLE_EMISSIONS_DISTRIBUTION,
				name: '路网图',
				visible: false,
			},
			{
				key: LayerType.VEHICLE_EMISSIONS_THERMODYNAMICS,
				name: '热力图',
				visible: false,
			},
			{
				key: LayerType.VEHICLE_POLYMERIZATION,
				name: '聚合',
				visible: false,
				roadNetwork: true,
				child: [],
			},
			{
				key: LayerType.VEHICLE_EMISSIONS_Hotspot_Grid,
				name: '网格',
				visible: false,
			},
		],
	},
  */

	/*
	{
		key: LayerType.INDUSTRY,
		service: LayerType.SERVICE,
		name: '行业',
		visible: false,
		showBackground: true,
		hideModeMenu: true,
		hideEchartsMap: true,
		child: [],
	},
	{
		key: LayerType.AREA,
		service: LayerType.SERVICE,
		name: '区县',
		visible: false,
		showBackground: true,
		hideModeMenu: true,
		hideEchartsMap: true,
		child: [],
		// selectionType: 'single', // 单选
		// child: [
		//   {
		//     key: LayerType.AREA_DISCHARGE,
		//     name: '区域排放',
		//     checkedAll: false, // 初始未选中
		//     visible: false,
		//     showBackground: true
		//   },
		//   {
		//     key: LayerType.AREA_DEREGULATION,
		//     name: '区域违规',
		//     checkedAll: false, // 初始未选中
		//     visible: false,
		//     showBackground: true
		//   },
		//   {
		//     key: LayerType.STREET_DISCHARGE,
		//     name: '街乡镇排放',
		//     visible: false,
		//     checkedAll: false, // 初始未选中
		//     showBackground: true
		//   },
		//   {
		//     key: LayerType.STREET_DEREGULATION,
		//     name: '街乡镇违规',
		//     visible: false,
		//     checkedAll: false, // 初始未选中
		//     showBackground: true
		//   },
		//   {
		//     key: LayerType.REGISTRIES_DSTRIBUTION,
		//     name: '注册区域分布',
		//     visible: false,
		//     checkedAll: false, // 初始未选中
		//     showBackground: true
		//   },
		//   {
		//     key: LayerType.SPATIALDISTRIBUTION,
		//     name: '空间分布',
		//     visible: false,
		//     checkedAll: false, // 初始未选中
		//     showBackground: true
		//   }
		// ]
	},

	{
		key: LayerType.Street_Township,
		service: LayerType.SERVICE,
		name: '街乡镇',
		visible: false,
		showBackground: true,
		hideModeMenu: true,
		hideEchartsMap: true,
		child: [],
	},
	{
		key: LayerType.ROAD,
		service: LayerType.SERVICE,
		name: '路段',
		hideModeMenu: true,
		visible: false,
		hideEchartsMap: false,
		child: [],
	},
	{
		key: LayerType.USER_ENTERPRISE,
		service: LayerType.SERVICE,
		name: '用户企业',
		visible: false,
		roadNetwork: true,
		hideModeMenu: true,
		hideEchartsMap: true,
		child: [],
	},

	{
		key: LayerType.SITETYPE,
		service: LayerType.SERVICE,
		name: '站点',
		visible: false,
		selectionType: 'multiple', // 多选
		hideEchartsMap: false,
		child: [
			{
				key: LayerType.STDG,
				name: '国控站',
				visible: false,
				roadNetwork: true,
			},
			{
				key: LayerType.STDS,
				name: '市控站',
				visible: false,
				roadNetwork: true,
			},
			{
				key: LayerType.HIGHT,
				name: '高密度站',
				visible: false,
				roadNetwork: true,
			},
			{
				key: LayerType.WEATHERSTATION,
				name: '气象站',
				visible: false,
				roadNetwork: true,
			},
			{
				key: LayerType.GRIDS,
				name: '热点网格',
				checkedAll: false,
				visible: false,
				roadNetwork: true,
			},
		],
	},
	{
		key: LayerType.AIR,
		service: LayerType.SERVICE,
		name: '气象分析',
		visible: false,
		hideEchartsMap: true,
		selectionType: 'single', // 单选
		child: [
			// {
			//   key: LayerType.TEMP,
			//   name: '气象等温',
			//   checkedAll: false,
			//   visible: false
			// },
			// {
			//   key: LayerType.HUMIDITY,
			//   name: '气象等湿',
			//   checkedAll: false,
			//   visible: false
			// },
			// {
			//   key: LayerType.AIRPRESSURE,
			//   name: '气象等压',
			//   checkedAll: false,
			//   visible: false
			// },
			// {
			//   key: LayerType.AIRINVERSION,
			//   name: '气象反演',
			//   checkedAll: false,
			//   visible: false
			// }
		],
	},
  */
];
