// import BackgroundFallingAreaMapStyled from './styles'

const BackgroundFallingAreaMap = (props) => {
	const { map, showBackgroundFallingAreaMap } = props;

	return (
		map &&
		showBackgroundFallingAreaMap && (
			<div
				style={{
					position: 'absolute',
					top: '0%',
					right: '0%',
					bottom: '0',
					left: '0',
					width: '100%',
					height: '100%',
					// zIndex: -1,
					backgroundColor: 'rgba(14,40,70)',
				}}
			/>
		)
	);
};

export default BackgroundFallingAreaMap;
