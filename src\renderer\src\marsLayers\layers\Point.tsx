import React, { useRef, useEffect, useState, useContext } from 'react';
import Point from '@renderer/marsLayers/base/point';
import { MapContext } from '@renderer/context';
import { useAuth } from '@renderer/hooks/useAuth';
import Popup from '@renderer/marsLayers/popups';
import TimeSelector from '@renderer/components/TimeSelector';
import dayjs from 'dayjs';
import styled from 'styled-components';

import {
	getStandardStation,
	getHighValueApi,
	getMeteStationByCity,
} from '@renderer/api';

type Props = {
	map: {
		flyHome: () => void;
		getLayers: () => any[];
		addLayer: (layer) => void;
	};
	layerId: string;
	visible: boolean;
	// url: string
};

type RefProps = {
	create: () => void;
	remove: () => void;
	renders: (data: any[]) => void;
};

const TOKEN =
	'tAFuWeewk-ojwVPoNP9PCfpL8npZSqC_Ay4vEGSyFvEKKw_UbJrofISgj5MbUwFh30cfu-DCMz4G3OT_Ar0Eakm-U3-fks1_2TQp3QxEgkAQ3DTm09B7doH1KqvnGdgbNyu4h0BVw2jHhEyHdFCMMMhPovvItzxjnuNxiN6PDaxiV7dOrYruXtK25vaNYdPq7frrr0gvA4vL2ov5YP73MhehUXezI_l6npeFm1YC0YvXL3JHFQM4IBYyKbAG2JvOe6uPT88B540nCofo_0FI_Q==';

export default (props: Props) => {
	const { visible, map, layerId } = props;
	const { navList, setCurrentLayerId } = useContext(MapContext);
	setCurrentLayerId(layerId);
	const [popupData, setPopupData] = useState<
		{ LayerId: string; [propName: string]: any } | false
	>(false);
	const [time, setTime] = useState(dayjs());
	const layerRef = useRef<RefProps>();
	const { token } = useAuth();

	const returnTime = (e) => {
		setTime(e);
	};

	const getStandardStationData = (json) => {
		getStandardStation(json)
			.then((res) => {
				if (res.length) layerRef.current.renders([res, setPopupData]);
			})
			.catch((err) => {
				console.log('err', JSON.stringify(err));
			});
	};

	const getHighValueApiData = (json) => {
		getHighValueApi(json)
			.then((res) => {
				if (res.length) layerRef.current.renders([res, setPopupData]);
			})
			.catch((err) => {
				console.log('err', JSON.stringify(err));
			});
	};

	const getMeteStationByCityData = (json) => {
		getMeteStationByCity(json)
			.then((res) => {
				if (res.length)
					layerRef.current.renders([
						res.map((item) => {
							return {
								...item,
								lng: item.wgs84_lng,
								lat: item.wgs84_lat,
							};
						}),
						setPopupData,
					]);
			})
			.catch((err) => {
				console.log('err', err);
			});
	};

	useEffect(() => {
		if (!visible) return;
		layerRef.current = new Point({
			name: layerId,
			map,
		});
		layerRef.current.create();

		switch (layerId) {
			case 'stdg':
				getStandardStationData({
					data_type: 1,
					pollutant_id: navList[0].id,
					station_type: 0,
					time_type: 1,
					token: TOKEN,
					is_group: 1,
					start_time: time.format('YYYY-MM-DD HH:00:00'),
					end_time: time.format('YYYY-MM-DD HH:00:00'),
				});
				break;
			case 'stds':
				getStandardStationData({
					data_type: 1,
					pollutant_id: navList[0].id,
					station_type: 2,
					time_type: 1,
					token: TOKEN,
					is_group: 1,
					start_time: time.format('YYYY-MM-DD HH:00:00'),
					end_time: time.format('YYYY-MM-DD HH:00:00'),
				});
				break;
			case 'hight':
				getHighValueApiData({
					time_type: 1,
					pollutant_id: navList[0].id,
					start_time: time.format('YYYY-MM-DD HH:00:00'),
					end_time: time.format('YYYY-MM-DD HH:00:00'),
					token: TOKEN,
					level: 7,
					min_value: 0,
					max_value: 99999,
					ranking: 9999,
				});
				break;
			case 'weather_station':
				getMeteStationByCityData({
					token: TOKEN,
					city_id: '1',
					start_time: time.subtract(1, 'hour').format('YYYY-MM-DD HH:00:00'),
					end_time: time.format('YYYY-MM-DD HH:00:00'),
				});
				break;
			default:
				break;
		}

		return () => {
			layerRef.current.remove();
			layerRef.current = null;
			setCurrentLayerId('');
		};
	}, [visible, navList[0], time]);

	return (
		<PointStyle>
			{popupData === false ? null : <Popup {...popupData} />}
			<div className="time-selector">
				<TimeSelector returnTime={returnTime} />
			</div>
		</PointStyle>
	);
};

const PointStyle = styled.div`
	.time-selector {
		position: absolute;
		right: 150px;
		top: 100px;
		z-index: 5;
		& > div {
			background-color: transparent !important;
			.ant-space {
				.ant-space-item:nth-child(1),
				.ant-space-item:nth-child(3) {
					display: none;
				}
			}
		}
	}
`;
