/**
 * 区县图层
 */
import React, {
	useEffect,
	useContext,
	useState,
	useCallback,
	useRef,
} from 'react';
import { Radio } from 'antd';
import * as echarts from 'echarts';
import { throttle } from 'lodash';
import { getRegionalIndicator, getRegionalViolationTop5 } from '@renderer/api';
import FallingAreaMap from '../components/FallingAreaMap';
import { getPop } from '@renderer/components/AreaViolation/echartsIo';
import { PageContext, MapContext } from '@renderer/context';
import {
	formatNumber,
	useTimeReferenceParams,
	tranNumber,
} from '@renderer/hooks/index';
import CustomPopUps from '@renderer/baseUI/TimeTypeRadioCustom/components/CustomPopUps';
import EmissionProportion from '../components/EmissionProportion';
import {
	COUNTY_ID_NAME_MAP,
	beijingRegions,
	legendArea,
	unitList,
	unitArea,
	getTitles,
	violationTypeList,
} from '../components/AreaStreetStyles';
import {
	AreaViolationWrapStyled,
	AreaViolationStyled,
	ReferenceStyled,
	ViolationTypeStyled,
	RoadTopFive,
} from '../components/AreaStreetStyles/styles';

export default (props) => {
	const { visible, layerId } = props;
	const { sceneMode } = useContext(MapContext);
	const {
		currentTopNavMenu,
		setCurrentTopNavMenu,
		regionName,
		setRegionName,
		regionData,
		selectRegionDate,
		regionalIndicators,
		setRegionData,
		setRegionalIndicators,
		hideModeMenu,
		setCurrentSelectedId,
	} = useContext(PageContext);

	const divChartRef = useRef(null);
	const myChartRef = useRef(null);
	const timer = useRef(null);
	// const [option1, setOption1] = useState({})
	const [customDateYoy, setCustomDateYoy] = useState({});
	const [selectedButton, setSelectedButton] = useState('default');
	const [showPop, setShowPop] = useState(false);
	const [regionYOY, setRegionYOY] = useState([]);
	const [proportionData, setProportionData] = useState([]); // 区域右下角数据占比
	const [regionTitle, setRegionTitle] = useState('');
	const [top5List, setTop5List] = useState([]);
	const [violationList, setViolationList] = useState(violationTypeList);
	const colors = [
		'#00E600',
		'#FFFF00',
		'#FF7F00',
		'#FF0000',
		'#9A004A',
		'#800021',
	];
	const getMaterialColor = (num, layerId, data) => {
		switch (layerId) {
			case 'area': {
				const valueList = data.map((item) => item.value);
				const maxValue = Math.max(...valueList);
				const minValue = Math.min(...valueList);

				const scale = (num - minValue) / (maxValue - minValue);

				const colorIndex = Math.floor(scale * (colors.length - 1));

				return colors[Math.max(0, Math.min(colorIndex, colors.length - 1))];
			}
		}
	};

	// 初始化柱状图
	const setUpOption1 = (data) => {
		const combinedData = data?.adm?.map((adm, index) => ({
			adm,
			count: formatNumber(data.count[index]),
			dod: formatNumber(data.dod[index]),
			per: formatNumber(data.per[index]),
		}));
		combinedData?.sort((a, b) => a.count - b.count);
		const sortedAdmData = combinedData?.map((item) => item.adm);
		const sortedCountData = combinedData?.map((item) => item.count);
		const sortedDodData = combinedData?.map((item) => item.dod);
		const sortedPerData = combinedData?.map((item) => item.per);
		const option = getPop({
			...data,
			adm: sortedAdmData,
			count: sortedCountData,
			dod: sortedDodData,
			per: sortedPerData,
			text: getTitles(
				selectRegionDate.timeType,
				unitList.find((i) => i.name === currentTopNavMenu)?.name,
			),
			unit: unitList.find((i) => i.name === currentTopNavMenu)?.unit,
		});
		// setOption1(option)
		const regionYOY = combinedData?.map((item) => {
			return {
				name: item.adm,
				per: item.per,
			};
		});
		setRegionYOY(regionYOY);
		return option;
	};

	// 柱状图默认自定义切换
	const handleClickReference = (value) => {
		setSelectedButton(value);
		if (value === 'custom') {
			setShowPop(true);
		} else {
			setShowPop(false);
		}
	};

	// 获取接口数据
	const getCustomLineData = (params) => {
		getRegionalIndicator(params)
			.then((res) => {
				if (res?.count?.length > 0) {
					const mapData = res?.adm?.map((item, idx) => {
						const value = formatNumber(res?.count[idx]);
						return {
							name: COUNTY_ID_NAME_MAP[item],
							value,
						};
					});
					setRegionData(mapData);
					const newData = { ...res };
					newData.adm = res?.adm?.map((id) => COUNTY_ID_NAME_MAP[id]);
					setRegionalIndicators(newData);
				} else {
					setRegionData([]);
					setRegionalIndicators([]);
				}
			})
			.catch((err) => {
				setRegionData([]);
				setRegionalIndicators([]);
			});
	};

	useEffect(() => {
		setCurrentTopNavMenu(unitList[0].name);
		// setCurrentSelectedId(layerId)
	}, []);

	useEffect(() => {
		if (!regionData?.length) {
			setProportionData([]);
			return;
		}
		setProportionData(regionData);
	}, [regionData]);

	useEffect(() => {
		if (!regionName || !regionData?.length) return;
		const regionTitle =
			regionName +
			`总量\n\n` +
			tranNumber(proportionData.find((i) => i.name === regionName)?.value, 2) +
			unitList.find((i) => i.name === currentTopNavMenu)?.unit;
		setRegionTitle(regionTitle);
	}, [regionName]);

	useEffect(() => {
		if (!regionName) return;
		const { start_time, end_time } = selectRegionDate.customDate;
		let county_id = null;
		for (let key in COUNTY_ID_NAME_MAP) {
			if (COUNTY_ID_NAME_MAP[key] == regionName) {
				county_id = key;
			}
		}
		const json = {
			start_time,
			end_time,
			county_id,
		};
		getRegionalViolationTop5(json).then((res) => setTop5List(res));
	}, [regionName]);

	useEffect(() => {
		getData();
		setRegionName(null);
	}, [hideModeMenu, selectedButton, selectRegionDate, currentTopNavMenu]);

	useEffect(() => {
		if (!visible || !hideModeMenu) return;
		setCurrentSelectedId(layerId);
		if (selectRegionDate.customDate.start_time == '' || currentTopNavMenu == '')
			return;
		myChartRef.current = echarts.init(divChartRef.current);
		myChartRef.current.setOption(setUpOption1(regionalIndicators));
		return () => {
			if (myChartRef.current) {
				myChartRef.current.dispose();
			}
			setCurrentSelectedId('');
		};
	}, [visible, regionalIndicators, currentTopNavMenu]);

	const handleType = (id) => {
		const newARR = violationList.map((item) => {
			if (item.id == id) {
				item.active = !item.active;
			}
			return item;
		});
		setViolationList(newARR);
		if (timer.current) {
			clearTimeout(timer.current);
		}
		// 设置一个新的定时器，延时后请求接口
		timer.current = setTimeout(() => {
			getData();
		}, 2000); // 延时
	};

	const getData = () => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (
			!hideModeMenu ||
			!start_time ||
			(selectedButton === 'custom' && Object.keys(customDateYoy)?.length > 0)
		)
			return;
		const params = {
			start_time,
			end_time,
			time_type,
			topic: unitList.find((i) => i.name === currentTopNavMenu)?.id,
			violation_type_id:
				violationList
					?.filter((item) => item.active)
					?.map((i) => i.id)
					?.join(',') || '',
		};
		getCustomLineData(params);
	};
	return visible && hideModeMenu ? (
		<>
			<ViolationTypeStyled>
				{unitList?.find((i) => i.name === currentTopNavMenu)?.id == 6 &&
					violationList.map((item) => {
						return (
							<div
								className="type"
								key={item.id}
								onClick={() => handleType(item.id)}
							>
								<span>
									{item.active ? '√  ' : <span>&nbsp;&nbsp;&nbsp;</span>}
								</span>
								{item.name}
							</div>
						);
					})}
			</ViolationTypeStyled>
			<RoadTopFive>
				{regionName &&
					unitList?.find((i) => i.name === currentTopNavMenu)?.id === 6 && (
						<div>
							<div>违规车数量最多道路Top5</div>
							<div className="top-five-list">
								{top5List.map((item, index) => (
									<div className="type" key={item.id}>
										<span>
											{index + 1}. {item.name} ({item.count}辆)
										</span>
									</div>
								))}
							</div>
						</div>
					)}
			</RoadTopFive>
			<FallingAreaMap
				data={regionData}
				layerId={layerId}
				sceneMode={sceneMode}
				getMaterialColor={getMaterialColor}
				regionYOY={regionYOY}
			/>
			<AreaViolationWrapStyled>
				{visible && legendArea(layerId, currentTopNavMenu)}
				{visible && unitArea(currentTopNavMenu)}
				<ReferenceStyled>
					<div className="selectAssembly">
						<p>环比基准：</p>
						<Radio.Button
							value="default"
							onClick={() => handleClickReference('default')}
							style={{
								color: selectedButton === 'default' ? '#3ADAFF' : '#e2e5e5',
							}}
						>
							默认
						</Radio.Button>
						<Radio.Button
							value="custom"
							onClick={() => handleClickReference('custom')}
							style={{
								color: selectedButton === 'custom' ? '#3ADAFF' : '#e2e5e5',
							}}
						>
							自定义
						</Radio.Button>
					</div>
					<CustomPopUps
						top={'50px'}
						right={'0'}
						showPop={showPop}
						setShowPop={setShowPop}
						setTimeType={null}
						setCustomDate={setCustomDateYoy}
					/>
				</ReferenceStyled>
				<AreaViolationStyled>
					<div className="echarts-line">
						{/* <Echarts
              option={option1}
              style={{ width: '100%', height: '500px', flex: 1 }}
              notMerge={true}
            /> */}
						<div
							ref={divChartRef}
							style={{ width: '100%', height: '650px', flex: 1 }}
						></div>
						{/* <EmissionProportion
              emTitle={`${regionName && regionData.length > 0 ? regionTitle : '总量'
                }`}
              data={proportionData}
              regionData={regionData}
            /> */}
					</div>
				</AreaViolationStyled>
			</AreaViolationWrapStyled>
		</>
	) : null;
};
