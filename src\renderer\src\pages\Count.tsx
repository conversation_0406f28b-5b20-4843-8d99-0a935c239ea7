import { useEffect, useState } from 'react';
import { AsyncRoute } from '@renderer/router';
import { ModuleStyled, Animation } from './styles';

const Count = (props) => {
	const { modulesList, direction } = props;
	const [isShow, setIsShow] = useState(false);

	const getAnimationClassName = (direction) => {
		let name = '';
		if (direction === 'left') {
			name = 'orderLeft';
			if (isShow) name += ' leftAnimation';
		} else {
			name = 'orderRight';
			if (isShow) name += ' rightAnimation';
		}
		return name;
	};

	useEffect(() => {
		setTimeout(() => {
			setIsShow(true);
		}, 2000);
	}, []);

	return (
		<>
			{modulesList &&
				modulesList?.map((item, index) => {
					const { id, order, isChange } = item;
					const data = {
						direction: direction,
						...item,
					};
					return isChange ? (
						<ModuleStyled
							className={`order${order} ${getAnimationClassName(direction)}`}
							key={index}
						>
							<div className="cube">
								<div className="bottom">
									<AsyncRoute
										key={id}
										data={data}
										asyncComponent={() =>
											import(`@renderer/components/${id}/index.tsx`)
										}
									/>
								</div>
								<div className="front">
									<AsyncRoute
										key={id}
										data={data}
										asyncComponent={() =>
											import(`@renderer/components/${id}/index.tsx`)
										}
									/>
								</div>
							</div>
						</ModuleStyled>
					) : (
						<ModuleStyled
							className={`order${order} ${getAnimationClassName(direction)}`}
							key={index}
						>
							<AsyncRoute
								key={id}
								data={data}
								asyncComponent={() =>
									import(`@renderer/components/${id}/index.tsx`)
								}
							/>
						</ModuleStyled>
					);
				})}
		</>
	);
};

export default Count;
