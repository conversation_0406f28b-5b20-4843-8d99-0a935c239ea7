import React from 'react';
import { tranNumberS, useFont } from '@renderer/hooks';
import LayoutContainer from './style';

interface Props {
	yesterdayData: any;
	todayData: any;
	column: Array<Record<string, string>>;
	comparisonData: Record<string, number>;
}

export const DailyActivityLevelData: React.FC<Props> = (props) => {
	const { todayData, yesterdayData, column, comparisonData } = props;

	const dataClass: string = 'data' + ' ' + useFont() + '2';
	const dataClass2: string = 'data2' + ' ' + useFont() + '2';
	const isEmptyObject = (obj) => {
		return Object.keys(obj).length !== 0;
	};

	return (
		<>
			<LayoutContainer>
				<div className="container">
					<div className="describe">
						<div className="describe-bottom">
							<dl className="no1">
								<dt>{todayData?.title || '今日数据'}</dt>
								<dd>
									{column?.map((item, index) => {
										return (
											<p key={index}>
												<span className="topic">{item.title}：</span>
												<span className={dataClass}>
													{todayData?.data?.[item.key] || '--'}
												</span>
											</p>
										);
									})}
								</dd>
							</dl>
							<dl className="no3">
								<dt>{yesterdayData?.title || '昨日同期'}</dt>
								<dd>
									{column?.map((item, index) => {
										return (
											<p key={index}>
												<span className={dataClass2}>
													{yesterdayData?.data?.[item.key] || '--'}
												</span>
											</p>
										);
									})}
								</dd>
							</dl>
							<dl className="no3">
								<dt>{'环比'}</dt>
								<dd>
									{column?.map((item, index) => {
										return (
											<p key={index}>
												<span className={dataClass}>
													{isEmptyObject(comparisonData)
														? Number(comparisonData[item.key]).toFixed(1) + '%'
														: '--'}
												</span>
											</p>
										);
									})}
								</dd>
							</dl>
						</div>
					</div>
				</div>
			</LayoutContainer>
		</>
	);
};
