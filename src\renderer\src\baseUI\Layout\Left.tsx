import styled from 'styled-components';
// import leftBg from '@renderer/images/layout/layout-left.png'
// import leftBorder from '@renderer/images/layout/left-border-new.png'

const LeftContainer = styled.section`
	width: 852px;
	height: 100%;
	position: absolute;
	z-index: 10;
	left: 0;
	top: 0;
	/* margin-top: -15px;
  margin-left: -20px; */
	.left-bg {
		position: absolute;
		left: 0;
		top: 0;
		width: 1033px;
		height: 899px;
		z-index: 2;
	}

	.left-border {
		position: absolute;
		right: -410px;
		top: 0;
		width: 476px;
		height: 910px;
		z-index: 1;
	}
`;

const Left = ({ children }) => {
	return (
		<LeftContainer>
			{/* <img src={leftBg} className="left-bg" /> */}
			{/* <img src={leftBorder} className="left-border" /> */}
			{children}
		</LeftContainer>
	);
};

export default Left;
