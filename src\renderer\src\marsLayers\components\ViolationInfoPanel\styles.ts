import styled from 'styled-components';

const VehicleHistoricalTrajectoryStyled = styled.div`
	position: absolute;
	left: 50%;
	top: 50%;
	width: 500px;
	height: auto;
	z-index: 9;
	transform: translate(-50%, -50%);
	.progress-wrap {
		padding: 10px;
		width: 100%;
		height: auto;
		overflow: hidden;
		box-sizing: border-box;
	}
	.ant-progress {
		display: flex;
		.ant-progress-text {
			font-size: 20px;
			.anticon-check-circle {
				font-size: 20px;
			}
		}
	}
	.btnGroup {
		width: 100%;
		display: flex;
		justify-content: space-around;
		margin-bottom: 5px;
		.btn {
			width: 80px;
			height: 50px;
			color: #fff;
			border: 1px solid #009bd8;
			cursor: pointer;
			background-color: rgba(1, 70, 116, 0.8);
		}
		.lable-text {
			display: flex;
			font-size: 20px;
			color: #fff;
			width: 85px;
			align-items: center;
			height: 50px;
			justify-content: center;
		}
		.ant-input-number {
			color: #fff;
			border: 1px solid #009bd8;
			background-color: rgba(1, 70, 116, 0.8);
			width: 70px;
			font-size: 20px;
		}
	}
	.slider {
		position: absolute;
		bottom: -15%;
		width: 1500px;
		height: 20px;
		right: 50%;
	}
`;

const LayoutContainer = styled.div`
	width: 100%;
	display: flex;
	flex-direction: column;
	flex-wrap: wrap;
	position: relative;
	margin-bottom: 10px;
	justify-content: flex-start;
	border: 1px solid rgb(216 0 0 / 80%);
	background-color: rgb(216 0 0 / 20%);
	border-radius: 5px;
	backdrop-filter: blur(15px);
	h3 {
		font-size: 20px;
		padding: 10px 10px 10px;
		color: #ffffff;
		background-color: #d80000;
	}
	.violation-record-close {
		font-size: 26px;
		position: absolute;
		right: 15px;
		top: 5px;
		color: #ffffff;
		font-family: initial;
		cursor: pointer;
	}
	.cumulative {
		display: flex;
		flex-direction: row;
		margin: 5px;
		flex-wrap: nowrap;
		div:nth-child(1) {
			width: 120px;
			background: linear-gradient(
				270deg,
				rgb(238 0 0 / 4%) 0%,
				rgb(216 0 0 / 28%) 100%
			);
			padding: 10px 10px;
			border: 1px solid #d80000;
			text-align: center;
			color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		div:nth-child(2) {
			flex: 1;
			background-color: transparent;
			padding: 10px 5px;
			border: 1px solid #d80000;
			text-align: left;
			color: #ffffff;
		}
	}
`;

export { VehicleHistoricalTrajectoryStyled, LayoutContainer };
