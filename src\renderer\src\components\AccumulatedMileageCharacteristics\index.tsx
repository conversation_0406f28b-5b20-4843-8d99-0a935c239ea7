import { useEffect, useState } from 'react';
import moment from 'moment';
import { useFont } from '@renderer/hooks';
import Echarts from '@renderer/components/echarts';
import Box from '../../baseUI/Box';
import TimeTypeRadio from '../MileageCharacteristics/components/TimeTypeRadio';
import { getAccumulateDistance } from '@renderer/api';

import { Container } from './style';

export default function AccumulatedMileageCharacteristics() {
	const [pieOption, setPieOption] = useState({});
	const [barOption, setBarOption] = useState({});
	const [time, setTime]: any = useState({
		start_time: moment().add(-1, 'd').format('YYYY-MM-DD 00:00:00'),
		end_time: moment().add(-1, 'd').format('YYYY-MM-DD 23:59:59'),
		timeType: 1,
	});
	const font: string = useFont();
	const typeOptions = [
		{
			label: '昨日',
			value: 'date',
		},
		{
			label: '当月',
			value: 'month',
		},
		{
			label: '今年',
			value: 'year',
		},
	];
	const color = [
		'#61C6FF',
		'#00FF00',
		'#00CED1',
		'#0080FF',
		'#E3A07C',
		'#FFD700',
		'#85A9FF',
		'#FF00FF',
	];

	const splitChineseNum = (numStr) => {
		const pattern = /(\d+\.\d+|\d+)([亿万])/;
		const result = numStr.match(pattern);
		if (result && result.length > 2) {
			return [result[1], result[2]];
		} else {
			return [numStr, ''];
		}
	};

	useEffect(() => {
		let json = {
			start_time: time.start_time,
			end_time: moment(time.end_time).format('YYYY-MM-DD 23:59:59'),
			time_type:
				time.timeType === 'year' ? 3 : time.timeType === 'month' ? 2 : 1,
		};
		getAccumulateDistance(json)
			.then((res) => {
				let data: any = [];
				let indicator: any = [];
				let i = -1;
				res.data.map((item, index) => {
					data.push({
						name: item.name,
						value: item.rate,
						itemStyle: {
							color: `${color[index]}`,
						},
					});
					indicator.push({
						name: item.name,
						max: 100,
					});
				});
				let title = '累计里程（km）';
				const parts = splitChineseNum(tranNumber(res.total, 2));
				let pieOption = {
					tooltip: {
						trigger: 'item',
						formatter: (params) => {
							return params.name + ' ' + params.value + '%';
						},
					},
					title: [
						{
							text:
								'{name|' +
								title +
								'}\n{val|' +
								parts[0] +
								'}{unit|' +
								parts[1] +
								'}',
							top: '50%',
							left: '37%',
							textAlign: 'center',
							textVerticalAlign: 'middle',
							textStyle: {
								rich: {
									name: {
										fontSize: 11,
										fontWeight: 'normal',
										color: '#fff',
										padding: [10, -8, 8, 0],
									},
									val: {
										fontSize: 36,
										fontWeight: 'bolder',
										fontFamily: font,
										color: '#61C6FF',
									},
									unit: {
										fontSize: 16,
										fontWeight: 'bolder',
										color: '#fff',
										padding: [5, 0, 0, 2],
									},
								},
							},
						},
					],
					legend: {
						show: true,
						orient: 'vertical',
						type: 'scroll',
						right: '5%',
						bottom: '10%',
						textStyle: {
							color: '#fff',
						},
					},
					series: [
						{
							type: 'pie',
							radius: ['55%', '75%'],
							center: ['38%', '55%'],
							data: data,
							hoverAnimation: false,
							itemStyle: {
								borderRadius: 7,
								borderColor: 'rgb(9,36,63)',
								borderWidth: 2,
							},
							labelLine: {
								show: false,
							},
							label: {
								show: false,
							},
						},
					],
				};
				let barOption = {
					// backgroundColor: '#002653',
					tooltip: {
						trigger: 'axis',
						show: false,
						axisPointer: {
							// 坐标轴指示器，坐标轴触发有效
							type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
						},
					},
					title: {
						text: `累计行驶里程行政区前5名（km）`,
						right: 0,
						top: 10,
						textStyle: {
							color: '#fff',
							fontSize: 18,
							fontWeight: 'normal',
						},
					},
					legend: {
						show: false,
					},
					grid: {
						top: 30,
						left: 60,
						right: 50,
						bottom: 25,
					},
					xAxis: [
						{
							splitLine: {
								show: false,
							},
							type: 'value',
							show: false,
						},
					],
					yAxis: [
						{
							show: true,
							splitLine: {
								show: false,
							},
							axisLine: {
								//y轴
								show: false,
							},
							type: 'category',
							axisTick: {
								show: false,
							},
							inverse: true,
							data: res.beijing.region,
							axisLabel: {
								color: '#FFF',
								fontSize: 16,
							},
						},
					],
					series: [
						{
							name: '',
							type: 'bar',
							barWidth: 12, // 柱子宽度
							label: {
								show: true,
								position: 'right', // 位置
								color: '#FFAA00',
								fontSize: 16,
								distance: 5, // 距离
								// formatter: '{c}家' // 这里是数据展示的时候显示的数据
								formatter: (o) => {
									return `${tranNumber(o.data, 3)}`;
								},
							}, // 柱子上方的数值
							itemStyle: {
								barBorderRadius: [20, 20, 20, 20], // 圆角（左上、右上、右下、左下）
							},
							axisLabel: {
								color: '#E8F4FF',
								textStyle: {
									fontSize: 14,
								},
							},
							data: res.beijing.data,
						},
					],
				};
				setPieOption(pieOption);
				setBarOption(barOption);
			})
			.catch((e) => {
				setOption1({});
				setOption2({});
			});
	}, [time.timeType]);

	// 数字转化添加单位
	const tranNumber = (num, point) => {
		// 将数字转换为字符串,然后通过split方法用.分隔,取到第0个
		let numStr: any = num.toString().split('.')[0];
		if (numStr.length < 6) {
			// 判断数字有多长,如果小于6,,表示10万以内的数字,让其直接显示
			return numStr;
		} else if (numStr.length >= 6 && numStr.length <= 8) {
			// 如果数字大于6位,小于8位,让其数字后面加单位万
			let decimal: any = numStr.substring(
				numStr.length - 4,
				numStr.length - 4 + point,
			);
			// 由千位,百位组成的一个数字
			let a: any = num / 10000;
			return parseFloat(parseInt(a) + '.' + decimal).toFixed(1) + '万';
		} else if (numStr.length > 8) {
			// 如果数字大于8位,让其数字后面加单位亿
			let decimal: any = numStr.substring(
				numStr.length - 8,
				numStr.length - 8 + point,
			);
			let a: any = num / 100000000;
			return parseFloat(parseInt(a) + '.' + decimal).toFixed(1) + '亿';
		}
	};

	const onTimeChange = (time) => {
		setTime(time);
	};

	return (
		<Box
			title="行驶里程"
			titlewidth="95%"
			height="100%"
			subTitle={
				<div style={{ display: 'flex' }}>
					<TimeTypeRadio
						typeOptions={typeOptions}
						defaultValue={'date'}
						onTimeTypeChange={onTimeChange}
					/>
				</div>
			}
		>
			<Container>
				<div className="echarts1">
					<Echarts option={pieOption} />
				</div>
				<div className="echarts2">
					<Echarts option={barOption} />
				</div>
			</Container>
		</Box>
	);
}
