/**
 * 车辆实时在线图层
 */
import { useEffect, useContext, useRef, useState } from 'react';
import {
	Cesium,
	layer,
	graphic,
	PointTrans,
	LngLatPoint,
	Util,
	EventType,
} from 'mars3d';
import moment from 'moment';
import { getMapExtent, getCarTypeModel } from '@renderer/utils';
import { MapContext, PageContext } from '@renderer/context';
import { getCarColor } from '@renderer/marsLayers/components/ModeMenu/data';
import { LayerType } from '@renderer/marsLayers/components/LayerMenu/layerDatas';
import {
	getVehicleNumber,
	getVehicleRealTimePositionInfo,
} from '@renderer/api';
import VehicleIndustryNumber from '@renderer/marsLayers/components/VehicleIndustryNumber';
import { COUNTY_ID_NAME_MAP } from '@renderer/marsLayers/components/AreaStreetStyles';
import { useMap } from '@renderer/hooks';

const { GraphicLayer } = layer;
const {
	PointPrimitive,
	ModelPrimitive,
	FixedRoute,
	BillboardPrimitive,
	Route,
} = graphic;
const { wgs2gcj } = PointTrans;
const { getTemplateHtml } = Util;
const { dblClick } = EventType;
let VehicleLocationData = null;
let graphicLayerTrace,
	graphicLayerModel,
	graphicModel,
	graphicShow = true,
	realVehicleTime = null,
	defaultPoint = {
		lng: 0,
		lat: 0,
	},
	axesNumber = 0.03; // 1000米的坐标系数: 0.008989

const OnlineLayer = (props) => {
	const map = useMap();
	const { visible, layerId } = props;
	const vehicleState = 'onLine';
	let currentTime = Date.parse(new Date());
	let is_illegal = layerId === LayerType.VIOLATING_VEHICLES ? '1' : '0';
	const {
		currentInfo,
		setCurrentInfo,
		setVehicleSearchData,
		setBickDate,
		currentLayerId,
		setCurrentLayerId,
		showFlash,
	} = useContext(MapContext);
	const {
		selectMode,
		setSwitchingPointsId,
		setOpenTrajectory,
		openTrajectory,
		countyId,
	} = useContext(PageContext);
	let messageList = [];

	const getMessage = () => {
		window.electron.ipcRenderer.on('message', (_event, message) => {
			if (Date.parse(new Date()) - currentTime < 6000) return;
			currentTime = Date.parse(new Date());
			messageList = JSON.parse(message);
			if (messageList && messageList.length > 0) {
				setVehicleSearchData(JSON.parse(message));
				changePosition(6, messageList);
			}
		});
	};

	const addDemoGraphics = (points) => {
		if (!graphicLayerTrace) {
			graphicLayerTrace = new GraphicLayer();
			map.addLayer(graphicLayerTrace);
		}
		for (let i = 0; i < points.length; i++) {
			const item = points[i];
			const VIN = item.split('|')[0];
			const attr = { VIN };
			const type = item.split('|')[1];
			const lon = item.split('|')[2];
			const lat = item.split('|')[3];
			const graphicPoint = new Route({
				id: `${item}`,
				// position: new wgs2gcj([lon, lat]),
				point: {
					color: getCarColor(type),
					pixelSize: 8,
					distanceDisplayCondition: true,
					distanceDisplayCondition_far: Number.MAX_VALUE,
				},
				attr,
			});
			graphicLayerTrace.addGraphic(graphicPoint);
			const point = new LngLatPoint(lon, lat);
			graphicPoint.addDynamicPosition(point, 0);
		}
	};

	const changePosition = (time, messageList) => {
		const messageMap = new Map();
		messageList.forEach((element) => {
			messageMap.set(element.VIN, element);
		});

		graphicLayerTrace.eachGraphic((graphic) => {
			if (graphic.isPrivate) {
				return;
			}
			const obj = messageMap.get(graphic.attr.VIN);
			if (obj) {
				const gcj2wgs = new wgs2gcj([obj.lon, obj.lat]);
				const point = new LngLatPoint(gcj2wgs[0], gcj2wgs[1]);
				const lon_dist = Math.abs(graphic.attr.lon - obj.lon) || 1;
				const lat_dist = Math.abs(graphic.attr.lat - obj.lat) || 1;
				const isMove = lon_dist < axesNumber && lat_dist < axesNumber;
				graphic.addDynamicPosition(point, isMove ? time : 0);
				graphic.show = true;
				graphic.attr = obj;
			} else {
				graphic.show = false;
			}
		});
	};

	const bindPopup = (graphic) => {
		graphic.bindPopup(
			function (event) {
				const attr = {};
				const formattedDate = moment(event.graphic.attr.gDate).format(
					'YYYY-MM-DD HH:mm:ss',
				);
				attr['车辆类型:'] = event.graphic.attr.type_of_industry;
				attr['发送时间:'] = formattedDate;
				attr['车辆速度:'] = Math.floor(event.graphic.attr.speed) + ' km/h';
				attr['排放浓度:'] =
					Math.floor(event.graphic.attr.downScrSensorOutput) + ' ppm';
				attr['发动机转速:'] = Math.floor(event.graphic.attr.engineRotation);

				return getTemplateHtml({
					title: '',
					template: 'all',
					attr,
				});
			},
			{ timeRender: true, closeButton: false },
		);
	};

	const clearGraphicLayer = () => {
		if (graphicLayerTrace) {
			graphicLayerTrace.clearDrawing();
			graphicLayerTrace.clear();
			graphicLayerTrace.enabledEvent = false;
			map.removeLayer(graphicLayerTrace);
		}
	};

	const addGraphicLayer = () => {
		graphicLayerTrace = new GraphicLayer({
			cluster: {
				enabled: true,
			},
		});
		map.addLayer(graphicLayerTrace);

		graphicLayerTrace.on(dblClick, function (event) {
			console.log('监听layer， 双击了矢量对象', event);
			const VIN = event.graphic.id.split('|')[0];
			setCurrentInfo({
				VIN: VIN,
				type: event.graphic.id.split('|')[1],
				lon: event.graphic.id.split('|')[2],
				lat: event.graphic.id.split('|')[3],
			});
			setOpenTrajectory(true);
			setBickDate(true);
		});
	};

	useEffect(() => {
		if (!currentInfo) {
			if (graphicLayerTrace) graphicLayerTrace.show = true;
			window.electron.ipcRenderer.send('layer', layerId);
		} else {
			if (graphicLayerTrace) graphicLayerTrace.show = false;
		}
	}, [currentInfo]);

	useEffect(() => {
		window.electron.ipcRenderer.on('VINs', (_event, VINs) => {
			let _VINs = JSON.parse(VINs);
			if (_VINs && _VINs.length > 0) {
				clearGraphicLayer();
				addGraphicLayer();
				addDemoGraphics(_VINs);
				VehicleLocationData = JSON.parse(VINs);
				getMessage();
			}
			_VINs = [];
		});

		return () => {
			window.electron.ipcRenderer.removeAllListeners('VINs');
			window.electron.ipcRenderer.removeAllListeners('message');
		};
	}, []);

	useEffect(() => {
		if (!visible) return;
		window.electron.ipcRenderer.send('extent', {
			extent: getMapExtent(map),
			type: selectMode,
			is_illegal,
			vehicleState,
		});

		// 推送extent
		let timer;
		let moveEndListener = map.scene.camera.moveEnd.addEventListener(
			function () {
				if (timer) {
					return;
				}
				timer = setTimeout(function () {
					window.electron.ipcRenderer.send('extent', {
						extent: getMapExtent(map),
						type: selectMode,
						is_illegal,
						vehicleState,
					});
					timer = null;
				}, 3000);
			},
		);

		return () => {
			moveEndListener();
			clearInterval(timer);
		};
	}, [selectMode.key]);

	useEffect(() => {
		if (!visible) return;
		setCurrentLayerId(layerId);
		setSwitchingPointsId(layerId);
		if (VehicleLocationData) {
			addDemoGraphics(VehicleLocationData);
			// getMessage()
		}
		return () => {
			clearGraphicLayer();
			// 取消推送extent
			window.electron.ipcRenderer.removeAllListeners('VINs');
			window.electron.ipcRenderer.removeAllListeners('message');
		};
	}, [visible]);

	return null;
};

export default OnlineLayer;
