import React, { useRef, useEffect, useState, useCallback } from 'react';
import * as THREE from 'three';
import { MapContainer } from './styles';

// 定义数据接口
interface MapData {
	name: string;
	value: number;
	[key: string]: any;
}

interface GeoJSONFeature {
	type: 'Feature';
	properties: {
		name: string;
		[key: string]: any;
	};
	geometry: {
		type: 'Polygon' | 'MultiPolygon';
		coordinates: number[][][] | number[][][][];
	};
}

interface GeoJSONData {
	type: 'FeatureCollection';
	features: GeoJSONFeature[];
}

interface ThreeJSMapProps {
	geoJsonData: GeoJSONData;
	mapData: MapData[];
	width?: number;
	height?: number;
	colorScale?: string[];
	extrudeHeight?: number;
	cameraPosition?: [number, number, number];
	onRegionClick?: (regionName: string, data: MapData | null) => void;
}

// 默认颜色等级
const DEFAULT_COLOR_SCALE = [
	'#313695',
	'#4575b4',
	'#74add1',
	'#abd9e9',
	'#e0f3f8',
	'#ffffcc',
	'#fee090',
	'#fdae61',
	'#f46d43',
	'#d73027',
	'#a50026',
];

const ThreeJSMap: React.FC<ThreeJSMapProps> = ({
	geoJsonData,
	mapData,
	width = 800,
	height = 600,
	colorScale = DEFAULT_COLOR_SCALE,
	extrudeHeight = 0.1,
	cameraPosition = [0, 5, 5],
	onRegionClick,
}) => {
	const mountRef = useRef<HTMLDivElement>(null);
	const sceneRef = useRef<THREE.Scene>();
	const rendererRef = useRef<THREE.WebGLRenderer>();
	const cameraRef = useRef<THREE.PerspectiveCamera>();
	const controlsRef = useRef<any>();
	const meshesRef = useRef<Map<string, THREE.Mesh>>(new Map());
	const raycasterRef = useRef<THREE.Raycaster>(new THREE.Raycaster());
	const mouseRef = useRef<THREE.Vector2>(new THREE.Vector2());

	// 计算数据值的范围
	const getValueRange = useCallback((data: MapData[]) => {
		if (data.length === 0) return { min: 0, max: 1 };
		const values = data.map((d) => d.value);
		return {
			min: Math.min(...values),
			max: Math.max(...values),
		};
	}, []);

	// 根据数值获取颜色
	const getColorByValue = useCallback(
		(value: number, min: number, max: number) => {
			if (max === min) return colorScale[0];
			const ratio = (value - min) / (max - min);
			const index = Math.floor(ratio * (colorScale.length - 1));
			return colorScale[Math.min(index, colorScale.length - 1)];
		},
		[colorScale],
	);

	// 将经纬度坐标转换为Three.js坐标
	const convertCoordinates = useCallback((coordinates: number[][]) => {
		return coordinates.map((coord) => [
			(coord[0] - 116.4) * 100, // 经度转换，以北京为中心
			0,
			-(coord[1] - 39.9) * 100, // 纬度转换，注意Z轴反向
		]);
	}, []);

	// 创建区域几何体
	const createRegionGeometry = useCallback(
		(coordinates: number[][][]) => {
			const shapes: THREE.Shape[] = [];

			coordinates.forEach((polygon) => {
				const convertedCoords = convertCoordinates(polygon);
				const shape = new THREE.Shape();

				convertedCoords.forEach((coord, index) => {
					if (index === 0) {
						shape.moveTo(coord[0], coord[2]);
					} else {
						shape.lineTo(coord[0], coord[2]);
					}
				});

				shapes.push(shape);
			});

			return shapes;
		},
		[convertCoordinates],
	);

	// 初始化Three.js场景
	const initThreeJS = useCallback(() => {
		if (!mountRef.current) return;

		// 创建场景
		const scene = new THREE.Scene();
		scene.background = new THREE.Color(0x000011);
		sceneRef.current = scene;

		// 创建相机
		const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
		camera.position.set(...cameraPosition);
		cameraRef.current = camera;

		// 创建渲染器
		const renderer = new THREE.WebGLRenderer({ antialias: true });
		renderer.setSize(width, height);
		renderer.shadowMap.enabled = true;
		renderer.shadowMap.type = THREE.PCFSoftShadowMap;
		rendererRef.current = renderer;

		// 添加光源
		const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
		scene.add(ambientLight);

		const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
		directionalLight.position.set(10, 10, 5);
		directionalLight.castShadow = true;
		scene.add(directionalLight);

		// 添加到DOM
		mountRef.current.appendChild(renderer.domElement);

		// 添加轨道控制器（需要额外安装three/examples/jsm/controls/OrbitControls）
		// 这里先用基础的鼠标控制
		let isMouseDown = false;
		let mouseX = 0;
		let mouseY = 0;

		const onMouseDown = (event: MouseEvent) => {
			isMouseDown = true;
			mouseX = event.clientX;
			mouseY = event.clientY;
		};

		const onMouseMove = (event: MouseEvent) => {
			if (!isMouseDown) return;

			const deltaX = event.clientX - mouseX;
			const deltaY = event.clientY - mouseY;

			camera.position.x += deltaX * 0.01;
			camera.position.y -= deltaY * 0.01;

			mouseX = event.clientX;
			mouseY = event.clientY;
		};

		const onMouseUp = () => {
			isMouseDown = false;
		};

		const onWheel = (event: WheelEvent) => {
			const scale = event.deltaY > 0 ? 1.1 : 0.9;
			camera.position.multiplyScalar(scale);
		};

		renderer.domElement.addEventListener('mousedown', onMouseDown);
		renderer.domElement.addEventListener('mousemove', onMouseMove);
		renderer.domElement.addEventListener('mouseup', onMouseUp);
		renderer.domElement.addEventListener('wheel', onWheel);

		return () => {
			renderer.domElement.removeEventListener('mousedown', onMouseDown);
			renderer.domElement.removeEventListener('mousemove', onMouseMove);
			renderer.domElement.removeEventListener('mouseup', onMouseUp);
			renderer.domElement.removeEventListener('wheel', onWheel);
		};
	}, [width, height, cameraPosition]);

	// 创建地图几何体
	const createMapGeometry = useCallback(() => {
		if (!sceneRef.current || !geoJsonData || !mapData) return;

		const scene = sceneRef.current;
		const { min, max } = getValueRange(mapData);

		// 清除之前的网格
		meshesRef.current.forEach((mesh) => {
			scene.remove(mesh);
		});
		meshesRef.current.clear();

		geoJsonData.features.forEach((feature) => {
			const regionName = feature.properties.name;
			const regionData = mapData.find((d) => d.name === regionName);
			const value = regionData?.value || 0;

			// 获取颜色
			const color = getColorByValue(value, min, max);

			// 处理几何体坐标
			let coordinates: number[][][];
			if (feature.geometry.type === 'Polygon') {
				coordinates = feature.geometry.coordinates as number[][][];
			} else if (feature.geometry.type === 'MultiPolygon') {
				// 对于MultiPolygon，取第一个多边形
				coordinates = (feature.geometry.coordinates as number[][][][])[0];
			} else {
				return;
			}

			// 创建形状
			const shapes = createRegionGeometry(coordinates);

			shapes.forEach((shape) => {
				// 创建挤出几何体
				const extrudeSettings = {
					depth: extrudeHeight + (value / max) * extrudeHeight * 5, // 根据数值调整高度
					bevelEnabled: false,
				};

				const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
				const material = new THREE.MeshLambertMaterial({
					color: new THREE.Color(color),
					transparent: true,
					opacity: 0.8,
				});

				const mesh = new THREE.Mesh(geometry, material);
				mesh.userData = { regionName, data: regionData };
				mesh.receiveShadow = true;
				mesh.castShadow = true;

				scene.add(mesh);
				meshesRef.current.set(regionName, mesh);
			});
		});
	}, [
		geoJsonData,
		mapData,
		getValueRange,
		getColorByValue,
		createRegionGeometry,
		extrudeHeight,
	]);

	// 处理点击事件
	const handleClick = useCallback(
		(event: MouseEvent) => {
			if (!cameraRef.current || !sceneRef.current || !rendererRef.current)
				return;

			const rect = rendererRef.current.domElement.getBoundingClientRect();
			mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
			mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

			raycasterRef.current.setFromCamera(mouseRef.current, cameraRef.current);

			const meshes = Array.from(meshesRef.current.values());
			const intersects = raycasterRef.current.intersectObjects(meshes);

			if (intersects.length > 0) {
				const mesh = intersects[0].object as THREE.Mesh;
				const { regionName, data } = mesh.userData;
				onRegionClick?.(regionName, data);
			}
		},
		[onRegionClick],
	);

	// 渲染循环
	const animate = useCallback(() => {
		if (!rendererRef.current || !sceneRef.current || !cameraRef.current) return;

		cameraRef.current.lookAt(0, 0, 0);
		rendererRef.current.render(sceneRef.current, cameraRef.current);
		requestAnimationFrame(animate);
	}, []);

	// 初始化
	useEffect(() => {
		const cleanup = initThreeJS();
		return cleanup;
	}, [initThreeJS]);

	// 创建地图
	useEffect(() => {
		createMapGeometry();
	}, [createMapGeometry]);

	// 开始渲染
	useEffect(() => {
		animate();
	}, [animate]);

	// 添加点击事件监听
	useEffect(() => {
		const renderer = rendererRef.current;
		if (renderer) {
			renderer.domElement.addEventListener('click', handleClick);
			return () => {
				renderer.domElement.removeEventListener('click', handleClick);
			};
		}
	}, [handleClick]);

	// 清理
	useEffect(() => {
		return () => {
			if (rendererRef.current && mountRef.current) {
				mountRef.current.removeChild(rendererRef.current.domElement);
				rendererRef.current.dispose();
			}
		};
	}, []);

	return <MapContainer ref={mountRef} width={width} height={height} />;
};

export default ThreeJSMap;
