// 街乡镇违规
import React, { useEffect, useContext, useState, useRef } from 'react';
import dayjs from 'dayjs';
import LevelTwoTitle from '@renderer/components/LevelTwoTitle';
import TimeTypeRadio from '@renderer/baseUI/TimeTypeRadio2';
import {
	StreetDeregulationWrapStyled,
	StreetDeregulationListStyled,
} from './style';
import {
	getViolationRegisterTownList,
	getViolationTownList,
} from '@renderer/api';
import { useAuth } from '@renderer/hooks/useAuth';
// import TimelineChanges from '@renderer/marsLayers/components/TimelineChanges'

const VIO_LATION_DATA_SIZE = 10;
const violatioData = Array.from({ length: VIO_LATION_DATA_SIZE }).map(() => ({
	adm_name: '',
	count: '',
	name: '',
	rank_desc: '',
}));

const StreetDeregulation = (props: any) => {
	const { setLayerData, setCurrentPosition } = props;
	const [timeData, setTimeData] = useState<any>({
		start_time: dayjs().format('YYYY-MM-DD 00:00:00'),
		end_time: dayjs().format('YYYY-MM-DD HH:00:00'),
		time_type: '1',
	});
	const { token } = useAuth();
	const [dataCreate, setDateCreate] = useState<any>(violatioData); // 行驶地违规数据
	const [dataRegister, setDateRegister] = useState<any>(violatioData); // 注册地违规数据

	const [isContentExpanded, setIsContentExpanded] = useState(true);
	const [isButtonRotated, setIsButtonRotated] = useState(false);

	const getCurrentPosition = (data) => {
		setCurrentPosition(data);
	};

	useEffect(() => {
		const req = {
			start_time: timeData.start_time,
			end_time: timeData.end_time,
			time_type: timeData.timeType,
			token,
		};
		getViolationTownList(req).then((res) => {
			setDateCreate(res);
		});

		//注册地违规
		getViolationRegisterTownList(req).then((res: any) => {
			if (!res.length) return;
			setDateRegister(res);
			const arr = res;
			const data = arr.map((item, index) => {
				let color;
				const num = Number(item.count);
				// 30以下：绿色 30-60：黄色 60-90：橙色 90-120：红色 120以上：绿色
				if (num < 30) {
					color = '#00FFFF';
				} else if (num >= 30 && num < 60) {
					color = '#00BFFF';
				} else if (num >= 60 && num < 90) {
					color = '#4169E1';
				} else if (num >= 90 && num < 120) {
					color = '#0000CD';
				} else if (num >= 120) {
					color = '#191970';
				}

				return {
					value: item.count,
					name: item.adm_name,
					areaName: item.name,
					itemStyle: {
						areaColor: color,
					},
				};
			});
			setLayerData(data);
		});
	}, [timeData]);

	const onbtnclickedhandle = () => {
		setIsContentExpanded(!isContentExpanded);
		setIsButtonRotated(!isButtonRotated);
	};

	const contentStyle = {
		maxHeight: isContentExpanded ? '825px' : '0',
		opacity: isContentExpanded ? 1 : 0,
		transition: 'max-height 0.3s ease, opacity 0.3s ease',
	};

	return (
		<StreetDeregulationWrapStyled>
			<div
				className={`buttonclicked ${isButtonRotated ? 'rotated' : ''}`}
				onClick={onbtnclickedhandle}
			></div>
			<TimeTypeRadio defaultValue="1" timeType={(data) => setTimeData(data)} />
			<StreetDeregulationListStyled style={contentStyle}>
				<div className="middle-container">
					<div className="Violation">
						<LevelTwoTitle
							className="Violation1"
							title="全市重型车活动前10"
						></LevelTwoTitle>
						{/* <span>TOP</span> */}
					</div>
					<div className="table">
						{dataCreate?.length && (
							<dl>
								<dt>
									<span className="no1">排名</span>
									<span className="no2">街乡镇</span>
									<span className="no3">所属区</span>
									<span className="no4">违规数量</span>
								</dt>
								{dataCreate?.slice(0, 10).map((item, key) => {
									return (
										<dd
											key={key}
											onClick={() => {
												getCurrentPosition(item);
											}}
										>
											{/* <span className="no1">{key + 1}</span>
                      <span className="no2">{item.adm_name}</span>
                      <span className="no3">{item.name}</span>
                      <span className="no4">{item.count}</span> */}
											<span className={`top${key} no1`}>{key + 1}</span>
											<span className="no2">{item.adm_name || '--'}</span>
											<span className="no3">{item.name || '--'}</span>
											<span className="no4">{item.count || '--'}</span>
										</dd>
									);
								})}
							</dl>
						)}
					</div>
				</div>
				<div className="right-container">
					<div className="Violation">
						<LevelTwoTitle
							className="Violation1"
							title="注册地违规"
						></LevelTwoTitle>
						<span>TOP</span>
					</div>
					<div className="table">
						{dataRegister?.length && (
							<dl>
								<dt>
									<span className="no1">排名</span>
									<span className="no2">街乡镇</span>
									<span className="no3">所属区</span>
									<span className="no4">违规数量</span>
								</dt>
								{dataRegister?.slice(0, 10).map((item, key) => {
									return (
										<dd
											key={key}
											onClick={() => {
												getCurrentPosition(item);
											}}
										>
											<span className={`top${key} no1`}>{key + 1}</span>
											<span className="no2">{item.adm_name || '--'}</span>
											<span className="no3">{item.name || '--'}</span>
											<span className="no4">{item.count || '--'}</span>
										</dd>
									);
								})}
							</dl>
						)}
					</div>
				</div>
			</StreetDeregulationListStyled>
		</StreetDeregulationWrapStyled>
	);
};
export default StreetDeregulation;
