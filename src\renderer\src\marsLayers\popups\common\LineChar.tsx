import * as React from 'react';
import { useEffect, useMemo, useRef } from 'react';
import * as echarts from 'echarts';
import styled from '@tripod-visualization/styled';
import { getPollutionUnit, pollutionList } from '@tripod-visualization/utils';

const SewageStyle = styled.div`
	.content {
		display: flex;
		flex-direction: row;
		margin-left: -10px;
		.con-ul {
			margin-top: 20px;
			margin-left: 10px;
		}
		.con-li {
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;
			margin-top: 5px;
			height: 20px;
			line-height: 20px;
			span:last-of-type {
				font-size: 14px;
				color: #bfdeff;
			}
		}
	}
`;

interface legendType {
	name: string;
	color: string;
}
type Props = {
	color?: string;
	legendShow: boolean;
	legendData?: Array<legendType>;
	xAxisData: Array<any>;
	seriesData: Array<{ name: string; data: Array<any> }>;
	style?: any;
	inputRef: any;
	xAxisFontSize?: number;
	yAxisFontSize?: number;
	top?: string | number;
	right?: string;
	bottom?: string;
	left?: string;
	yAxisName?: string;
	alarmData?: Array<string | number>;
};
let instance;
const LineChart = (props: Props) => {
	const {
		color,
		legendShow = false,
		legendData,
		xAxisData,
		seriesData,
		style = { width: '115%', height: '200px', marginLeft: '-16px' },
		inputRef = useRef(),
		xAxisFontSize = 12,
		yAxisFontSize = 12,
		top = '15%',
		right = '10',
		bottom = '0',
		left = '20',
		yAxisName,
		alarmData,
	} = props;
	if (!xAxisData?.length || !seriesData?.length) return;
	const getColor = (name: string, dataList: Array<legendType>): string => {
		let color = '';
		for (let i = 0; i < dataList.length; i++) {
			if (name === dataList[i].name) {
				color = dataList[i].color;
				break;
			}
		}
		return color;
	};

	const getRGBColor = (thisColor, thisOpacity) => {
		var theColor = thisColor.toLowerCase();
		//十六进制颜色值的正则表达式
		var r = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		// 如果是16进制颜色
		if (theColor && r.test(theColor)) {
			if (theColor.length === 4) {
				var sColorNew = '#';
				for (var i = 1; i < 4; i += 1) {
					sColorNew += theColor
						.slice(i, i + 1)
						.concat(theColor.slice(i, i + 1));
				}
				theColor = sColorNew;
			}
			//处理六位的颜色值
			var sColorChange = [];
			for (var i = 1; i < 7; i += 2) {
				sColorChange.push(parseInt('0x' + theColor.slice(i, i + 2)));
			}
			return 'rgba(' + sColorChange.join(',') + ',' + thisOpacity + ')';
		}
		return theColor;
	};
	const option = useMemo(() => {
		let seriesList = [];
		seriesData.map((item, index) => {
			let it = {
				...item,
				type: 'line',
				smooth: true, //true曲线; false折线
				showSymbol: true,
				itemStyle: {
					color: getColor(item.name, legendData), //改变折线点的颜色
					lineStyle: {
						color: getColor(item.name, legendData), //改变折线颜色
						type: 'solid',
					},
				},
				areaStyle: {
					color: {
						type: 'linear',
						x: 0,
						y: 0,
						x2: 0,
						y2: 1,
						colorStops: [
							{
								offset: 0,
								color: getColor(item.name, legendData), // 0% 处的颜色
							},
							{
								offset: 1,
								color: getRGBColor(getColor(item.name, legendData), 0), // 100% 处的颜色
							},
						],
						global: false, // 缺省为 false
					},
				},
				markPoint: {
					data: alarmData?.map((timeItem, index) => {
						let xAxiaValue = timeItem;
						var indexOf = (xAxisData || []).findIndex(
							(item) => item === xAxiaValue,
						);
						let number = 0;
						seriesData.forEach((el) => {
							number = el.data[indexOf] > number ? el.data[indexOf] : number;
						});
						return {
							xAxis: xAxiaValue,
							yAxis: number,
						};
					}),
					symbol: 'image://' + require('@/images/popupImage/alarm.png'),
					symbolSize: 20,
					symbolOffset: [0, '-50%'],
				},
			};
			seriesList.push(it);
		});

		return {
			color: ['rgba(91, 143, 249, 1)', 'rgba(189, 82, 97, 1)'],
			grid: {
				x: 0,
				y: 0,
				x2: 0,
				y2: 0,
				top: top,
				left: left,
				right: right,
				bottom: bottom,
				containLabel: true,
			},
			legend: {
				show: legendShow,
				left: 'center',
				top: 10,
				data: legendData,
				icon: 'circle',
				itemWidth: 12,
				itemGap: 20,
				textStyle: {
					color: ' #E2F0FF',
					fontSize: 12,
				},
			},

			tooltip: {
				trigger: 'axis',
				formatter: function (datas) {
					let res = datas[0].name + '<br/>';
					let val;
					let length = datas.length;
					let i = 0;
					for (; i < length; i++) {
						val = datas[i].value.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
						res +=
							'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
							datas[i].color +
							';"></span>' +
							datas[i].seriesName +
							'：' +
							val +
							'<br/>';
					}
					return res;
				},
			},

			xAxis: [
				{
					type: 'category',
					boundaryGap: false,
					axisTick: {
						show: false,
					},

					axisLabel: {
						// interval: 2,
						color: ' #E2F0FF',
						fontSize: xAxisFontSize,
					},
					axisLine: {
						show: true,
						lineStyle: {
							type: 'solid', //solid实线;dashed虚线
							color: 'rgba(255, 255, 255, 0.7)',
						},
					},
					data: xAxisData,
				},
			],
			yAxis: [
				{
					type: 'value',
					name:
						yAxisName +
						getPollutionUnit(
							pollutionList?.find((item) => item.name == yAxisName)?.id,
						),
					min: 0,
					nameTextStyle: {
						color: 'rgba(251, 251, 251, 1)',
						fontSize: yAxisFontSize,
						rich: {
							a: {
								fontSize: yAxisFontSize,
							},
						},
					},
					axisLabel: {
						// formatter: '{value} ',
						align: 'center',
						color: '#E2F0FF',
						fontSize: yAxisFontSize,
						margin: 20,
					},
					//坐标轴线样式
					axisLine: {
						show: true,
						lineStyle: {
							color: '#ffffff',
						},
					},
					splitLine: {
						show: false,
						lineStyle: {
							type: 'solid', //solid实线;dashed虚线
							color: 'rgba(255, 255, 255, 0.7)',
						},
					},
				},
			],
			dataZoom: [
				{
					type: 'slider',
					show: false,
				},
				{
					type: 'inside', // 支持内部鼠标滚动平移
					show: false,
				},
			],

			series: seriesList,
		};
	}, [seriesData]);

	window.onresize = function () {
		instance.resize();
	};
	useEffect(() => {
		instance =
			echarts.getInstanceByDom(inputRef.current) ||
			echarts.init(inputRef.current);
		if (!option) return;
		instance.setOption(option, true);

		window.onresize = function () {
			instance.resize();
		};

		return () => {
			instance.dispose();
			instance = null;
		};
	}, [inputRef, option]);

	return (
		<SewageStyle>
			<div className="content">
				<div style={style} ref={inputRef}></div>
			</div>
		</SewageStyle>
	);
};
export default LineChart;
