import * as mars3d from 'mars3d';
import BaseMarsLayers from './index';
import { wgs84togcj02 } from '@renderer/utils';
// import pointData from '@renderer/marsLayers/handleData'
const { Cesium } = mars3d;
export default class GridsPolygon extends BaseMarsLayers {
	props: any;

	constructor(props) {
		super(props, 'GraphicLayer');
		this.props = props;
	}

	renders([data, setPopupData]) {
		if (!data || data.length === 0) {
			this.remove();
			return;
		}

		data.forEach((item) => {
			// const options = pointData[this.props.name]
			// const { icon, text, scale, scaleByDistance, labels } = options.getStyle(item)
			const graphic = new mars3d.graphic.PolylineEntity({
				// position: new mars3d.LatLngPoint(item.lng, item.lat, 200),
				// positions: [
				//   wgs84togcj02(item.lng, item.lat),
				//   wgs84togcj02(nextItem.lng, nextItem.lat)
				//   // [item.lng, item.lat],
				//   // [nextItem.lng, nextItem.lat]
				// ]
				positions: [
					wgs84togcj02(item.LONGITUDE_UP_LEFT, item.LATITUDE_UP_LEFT),
					wgs84togcj02(item.LONGITUDE_UP_RIGHT, item.LATITUDE_UP_RIGHT),
					wgs84togcj02(item.LONGITUDE_DOWN_RIGHT, item.LATITUDE_DOWN_RIGHT),
					wgs84togcj02(item.LONGITUDE_DOWN_LEFT, item.LATITUDE_DOWN_LEFT),
					wgs84togcj02(item.LONGITUDE_UP_LEFT, item.LATITUDE_UP_LEFT),
				],
				style: {
					width: 5,
					color: Cesium.Color.fromCssColorString('#ff0000'),
				},
				attr: { ...item, LayerId: this.props.name },
			});
			this.layer.addGraphic(graphic);
		});
		// this.layer.bindPopup(
		//   () => `<div id="${this.props.name}Popup"  style="width:100%;height:100%"></div>`,
		//   {
		//     offsetX: 0,
		//     offsetY: -10,
		//     className: 'pop',
		//     scaleByDistance: false,
		//     template: `<div><div class='closeButton'>x</div>{content}</div>`
		//   }
		// )
		// let openCount = 0 //切换弹窗时，弹窗内容不更新，增加此变量判断是否切换弹窗

		// this.layer.on(mars3d.EventType.popupOpen, function (event) {
		//   console.log('popupOpen------', event.attr, openCount++)

		//   setPopupData({ ...event.attr, openCount: openCount++ })
		// })

		// this.layer.on(mars3d.EventType.popupClose, function (event) {
		//   setPopupData(false)
		// })
	}
}
