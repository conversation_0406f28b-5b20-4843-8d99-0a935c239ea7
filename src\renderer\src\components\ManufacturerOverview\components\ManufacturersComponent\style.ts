import styled from 'styled-components';
const LayoutContainer = styled.div`
	/* margin-top: 10px; */
	margin-left: 30px;
	display: flex;
	flex-direction: row;
	//justify-content: space-between;
	position: relative;
	.left-container {
		width: 180px;
		display: flex;
		flex-direction: row;
		.describe {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			/* margin-left: 10px; */
			margin-top: 0;
			margin-bottom: 20px;
			/* text-align: center; */
			margin-right: 14px;
			dl {
				color: #fff;
				padding-left: 0;
				margin: 0 0 0 0;
				/* display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center; */
				dt {
					font-size: 14px;
					color: #d8e2ef;
					letter-spacing: 4px;
				}
				dd {
					position: relative;
					bottom: 5px;
					font-size: 28px;
					margin: 5px 0 0 0;
					/* font-weight: bold; */
					// font-family: 'electronicFont';
					span {
						font-size: 14px;
						color: #d8e2ef;
						margin-left: 5px;
					}
				}
				&.no2 {
					margin-top: 13px;
					dt {
						padding-bottom: 20px;
						font-size: 16px;
						.circle {
							display: inline-block;
							width: 10px;
							height: 10px;
							margin-right: 15px;
							border-radius: 50%;
							background-color: #61c6ff;
						}
					}
					dd {
						color: #61c6ff;
						font-size: 64px;
						padding: 5px;
						line-height: 18px;
						background: linear-gradient(
							180deg,
							rgba(154, 229, 255, 0) 0%,
							rgba(97, 198, 255, 0.1) 100%
						);
						border-bottom: 2px #61c6ff solid;
					}
				}
			}
		}
	}
`;
export default LayoutContainer;
