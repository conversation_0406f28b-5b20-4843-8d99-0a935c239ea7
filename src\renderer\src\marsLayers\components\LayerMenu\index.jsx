import { useState, useEffect, useContext } from 'react';
import '@animxyz/core';
import { XyzTransitionGroup } from '@animxyz/react';
import { MapContext } from '../../../context';
import layersMenu from '../../../images/layersMenu';
import {
	LayerMenuContent,
	PassingValuesDynamically,
	LayerMenuBotton,
} from './style';

function LayerMenu(props) {
	const { showFlash } = props;
	const { layers, setLayers, currentInfo, currentLayerId, setCurrentLayerId } =
		useContext(MapContext);
	const [isVisible, setIsVisible] = useState(false);
	const [isRotated, setIsRotated] = useState(false);
	const [hoveredIndex, setHoveredIndex] = useState(null);
	const [originalMenu, setOriginalMenu] = useState(layers);

	const findVisibleKeys = (arr) => {
		let visibleKeys = '';

		function traverse(array) {
			array.forEach((item) => {
				if (item.visible) {
					visibleKeys = item.key;
				}
				if (item.child) {
					traverse(item.child);
				}
			});
		}

		traverse(arr);
		return visibleKeys;
	};

	const setVisibilityToTrue = (key, data) => {
		data.forEach((item) => {
			if (item.key === key) {
				item.visible = true;
			} else if (item?.child) {
				item.child.forEach((o) => {
					if (o.key === key) {
						o.visible = true;
					}
				});
			}
		});
	};

	useEffect(() => {
		const currentLayer = findVisibleKeys(layers);
		// TODO: 未发送成功
		window.electron.ipcRenderer.send('layer', currentLayer);
	}, [layers]);

	// useEffect(() => {
	//   let layersNew
	//   if (currentInfo) {
	//     originalMenu.forEach((item) => {
	//       if (item.key === 'vehicleHistoricalTrajectory') {
	//         item.visible = true
	//       } else {
	//         item.visible = false
	//       }
	//     })
	//     setLayers(originalMenu)
	//   } else {
	//     layersNew = layers.filter((item) => {
	//       return item.key !== 'vehicleHistoricalTrajectory'
	//     })
	//     setVisibilityToTrue(currentLayerId, layers)
	//     setLayers(layersNew)
	//   }
	// }, [currentInfo])

	const handleLayersClick = (index, index2) => {
		let newLayers = layers?.map((item, i) => {
			if (index == i) {
				if (!isNaN(index2)) {
					//多选
					if (item.selectionType === 'multiple') {
						item.child.map((item2, i2) => {
							if (index2 == i2) {
								item2.visible = !item2.visible;
							}
						});
					}
					//单选
					if (item.selectionType === 'single') {
						item.child.map((item2, i2) => {
							// console.log(item2, 'item2')
							if (index2 == i2) {
								item2.visible = !item2.visible;
							} else {
								item2.visible = false;
							}
							return item2;
						});
						item.checkedAll = item.child.every((item2) => item2.visible);
					}
				} else {
					item.visible = !item.visible;
				}
			} else {
				item.visible = false;
				item.child.map((item2) => (item2.visible = false));
			}
			return item;
		});
		// setCurrentLayerId(newLayers.find((i) => i.visible).key)
		setLayers(newLayers);
	};

	const debounce = (fn, delay) => {
		return function () {
			let _args = arguments;
			let that = this;
			clearTimeout(fn.timer);
			fn.timer = setTimeout(function () {
				fn.call(that, _args);
			}, delay);
		};
	};

	const handleIsVisible = () => {
		setIsRotated(!isRotated); // 切换按钮旋转状态
		setTimeout(() => {
			setIsVisible(!isVisible); // 切换内容显示状态，延迟与按钮旋转速度匹配
		}, 500); // 300ms 延迟，可以根据需要调整
	};

	const onMouseEnter = (index) => {
		setHoveredIndex(index);
	};

	const onMouseLeave = () => {
		setHoveredIndex(null);
	};

	return (
		<>
			{!showFlash && !currentInfo && (
				<>
					<LayerMenuBotton onClick={handleIsVisible}>
						<div
							className={`layerMenuPhoto ${
								isRotated ? 'rotated' : 'rev-rotated'
							}`}
						></div>
						<div className="radiocenter"></div>
					</LayerMenuBotton>
					<XyzTransitionGroup xyz="slide-down left-50%">
						<LayerMenuContent isVisible={isVisible}>
							{/* <div className="line"></div> */}
							<PassingValuesDynamically
								isVisible={isVisible}
								className={`transition ${isVisible ? 'in' : 'out'}`}
							>
								<XyzTransitionGroup xyz="slide-up down-50% back-2">
									{isVisible &&
										layers?.map((item, index) => (
											<div
												className={`item ${
													item.visible && item.child.length > 0
														? 'open'
														: 'close'
												}`}
												key={index}
											>
												<div
													// style={{
													//   background: `url(${
													//     layersMenu[`${item.visible ? `${item.service}_active` : item.service}`]
													//   }) 52% 67% no-repeat`
													// }}
													style={{
														background: `url(${
															layersMenu[
																`${
																	item.visible ? `${item.service}_active` : ''
																}`
															]
														}) 87px 57px no-repeat`,
													}}
												>
													<div
														className="one"
														onClick={() =>
															debounce(handleLayersClick(index), 1000)
														}
														style={{
															background: `url(${
																layersMenu[
																	`${
																		item.visible
																			? `${item.key}_active`
																			: item.key
																	}`
																]
															}) 18px 9px / 60px 60px no-repeat`,
														}}
													>
														<div
															className="center"
															style={{
																// transform: item.visible ? 'translateY(0)' : 'translateY(6px)',
																transform: 'translateY(6px)',
																// fontSize: item.visible ? '22px' : '16px',
																// paddingLeft: item.visible ? '0' : '8px',
																color: item.visible ? '#FFFFFF' : '#B1C5D1',
															}}
														>
															{item.name}
														</div>
													</div>
												</div>

												<XyzTransitionGroup xyz="up-25% origin-top fade-100% flip-up-25%  duration-3 ease-in">
													{item.visible && (
														<div className="two">
															{item.visible &&
																item.selectionType !== 'single' &&
																item.child.map((item2, index2) => (
																	<div
																		// style={{
																		//   background: item2.visible
																		//     ? `url(${layersMenu['service_active']})  100% 28% no-repeat`
																		//     : `url(${layersMenu['service']})  100% 28% no-repeat`
																		// }}
																		style={{
																			background: item2.visible
																				? `url(${layersMenu['service_active']}) left bottom no-repeat`
																				: '',
																		}}
																		onClick={() =>
																			debounce(
																				handleLayersClick(index, index2),
																				1000,
																			)
																		}
																		key={index2}
																		className="twoChildren"
																	>
																		<div
																			style={{
																				background: item2.visible
																					? `url(${layersMenu['level_active']}) 0% center no-repeat`
																					: `url(${layersMenu['level']}) 0% center no-repeat`,
																			}}
																			className="hoverlevel"
																		>
																			<div
																				className="center"
																				style={{
																					fontSize: item2.visible
																						? '20px'
																						: '16px',
																					paddingLeft: item2.visible
																						? '0'
																						: '4px',
																					color: item2.visible
																						? '#FFFFFF'
																						: '#B1C5D1',
																				}}
																			>
																				{item2.name}
																			</div>
																		</div>
																	</div>
																))}
															{item.selectionType === 'single' &&
																item.child.map((item2, index2) => (
																	<div
																		style={{
																			// background: item2.visible
																			//   ? `url(${layersMenu['child_active']})  0% 93% /93% 47%  no-repeat`
																			//   : `url(${layersMenu['child']}) 0% 93% /93% 47%  no-repeat`,
																			background: item2.visible
																				? `url(${layersMenu['short']})  0% center / 20% 6%  no-repeat,url(${layersMenu['service_active']})  0% 98% / 93% 10% no-repeat`
																				: hoveredIndex === index2
																				? `url(${layersMenu['short']})  0% center / 20% 6%  no-repeat`
																				: `url(${layersMenu['short_active']}) 0% center / 20% 6% no-repeat`,
																			height: '30px',
																			display: 'flex',
																			justifyContent: 'left',
																			paddingLeft: '30px',
																			alignItems: 'center',
																			marginBottom: '10px',
																			// transform: 'translateY(-15px)'
																		}}
																		onClick={() =>
																			debounce(
																				handleLayersClick(index, index2),
																				1000,
																			)
																		}
																		key={index2}
																		onMouseEnter={() => onMouseEnter(index2)}
																		onMouseLeave={() => onMouseLeave(index2)}
																		className="lastchild"
																	>
																		<div
																			style={{
																				// transform: item2.visible
																				//   ? 'translateY(-5px)'
																				//   : 'translateY(-5px)',
																				fontSize: item2.visible
																					? '20px'
																					: '16px',
																				paddingLeft: item2.visible
																					? '0'
																					: '8px',
																				color: item2.visible
																					? '#FFFFFF'
																					: '#B1C5D1',
																			}}
																		>
																			{item2.name}
																		</div>
																	</div>
																))}
														</div>
													)}
												</XyzTransitionGroup>
											</div>
										))}
								</XyzTransitionGroup>
							</PassingValuesDynamically>
						</LayerMenuContent>
					</XyzTransitionGroup>
				</>
			)}
		</>
	);
}
export default LayerMenu;
