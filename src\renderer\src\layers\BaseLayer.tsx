import React, { useEffect } from 'react';
import { ChinaCRS, layer } from 'mars3d';

import { useMap } from '@renderer/hooks';

const { XyzLayer, MapboxLayer, TencentLayer } = layer;

export const BaseLayer = () => {
	const map = useMap();

	useEffect(() => {
		if (!map) return;

		// const option = {
		// 	layer: 'custom',
		// 	chinaCRS: ChinaCRS.GCJ02,
		// 	style: 4,
		// };
		// const layer = new TencentLayer(option);
		const layer = new XyzLayer({
			// url: 'https://services.arcgisonline.com/ArcGIS/rest/services/Canvas/World_Dark_Gray_Base/MapServer/WMTS/tile/1.0.0/Canvas_World_Dark_Gray_Base/default/default028mm/{z}/{y}/{x}/',
			url: 'https://a.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png',
			chinaCRS: ChinaCRS.WGS84,
		});
		map.addLayer(layer);
	}, [map]);

	return null;
};
