import React from 'react';

import { LayerChooseStyle } from './style';

const LayerChooseElement = ({
	data,
	onChange,
	className = '',
	singleSelection = false,
}) => {
	return (
		<LayerChooseStyle className={className}>
			{data.map((item) => {
				if (item.id == 9) return null;
				return (
					<div key={item.id} className={item.visible ? 'active' : ''}>
						<span
							onClick={() => {
								onChange(
									data.map((list) => {
										if (list.id === item.id) {
											list.visible = !list.visible;
										} else {
											if (singleSelection) list.visible = false;
										}
										return list;
									}),
								);
							}}
						>
							{item.value}
						</span>
					</div>
				);
			})}
		</LayerChooseStyle>
	);
};

export default LayerChooseElement;
