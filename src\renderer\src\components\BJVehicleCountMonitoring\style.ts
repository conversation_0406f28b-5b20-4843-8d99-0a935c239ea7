import styled from 'styled-components';
import titleBg from '../../images/networkedData/titleBg.png';
import imags from '../../images/networkedData/index';

const Container = styled.div`
	// margin-left: 35px;
	display: flex;
	width: 100%;
	height: 100%;

	flex-direction: column;
	// position: relative;
	.title {
		position: absolute;
		display: flex;
		width: 90%;
		top: -8%;
		left: 12%;
		margin-left: 10px;
		background-repeat: no-repeat;
		background-size: 100% 100%;
		color: white;
		font-size: 22px;
		line-height: 3px;
		height: 25px;
		padding-left: 30px;
	}

	.up-container {
		height: 107px;
		margin-top: 5px;
		margin-bottom: 5px;
		display: flex;
		align-items: center;
		justify-content: space-around;
		margin-left: 10px;
		.up-container-left {
			display: flex;
			flex-direction: row;

			.imgRight {
				width: 18px;
				height: 18px;
				margin-left: 107px;
				margin-right: 12px;
			}
			.country-title {
				width: 54px;
				text-align: center;
				display: flex;
				flex-direction: column;
				height: 25px;
				font-size: 18px;
				font-family: 'TimesNewRoman', PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #c9dce9;
				line-height: -5px;
				margin-top: -19px;

				img {
					width: 54px;
					height: 25px;
				}
				.num {
					color: #4ce6ff;
					font-size: 20px;
					margin-top: 10px;
				}
			}
			.network-container {
				display: flex;
				flex-direction: row;
				position: relative;

				.networkNum {
					margin-bottom: 200px;
					background-image: url(${imags.circleBg});
					background-repeat: no-repeat;
					width: 121px;
					height: 116px;
					position: absolute;
					display: flex;
					flex-direction: column;
					top: -47px;
					left: 65%;
					.num-item {
						display: flex;
						flex-wrap: nowrap;
						justify-content: center;
						width: 121px;
						text-align: center;
						flex-direction: row;
						flex-wrap: nowrap;
						height: 24px;
						font-size: 17px;
						font-weight: bold;
						color: #4ce6ff;
						line-height: 24px;
						position: absolute;
						top: 33px;
					}
					.wired {
						width: 121px;
						text-align: center;
						flex-direction: row;
						flex-wrap: nowrap;
						height: 24px;
						font-size: 17px;
						font-weight: bold;
						color: #4ce6ff;
						line-height: 24px;
						position: absolute;
						top: 55px;
					}
				}
				img {
					width: 18px;
					height: 18px;
					margin-left: 12px;
					margin-right: 12px;
				}
			}
		}
		.up-container-right {
			margin-left: 8px;
			img {
				width: 357px;
				height: 113px;
			}
		}
	}
	.divider {
		margin-top: 8px;
		margin-left: 10px;
		width: 720px;
		height: 1px;
		border: 1px dashed #01e2ff;
	}

	.down-container {
		margin-left: 10px;
		margin-top: 10px;
		display: flex;
		flex-direction: row;

		.down-title {
			.titleName {
				width: 40px;
				font-size: 20px;
				font-family: 'TimesNewRoman', PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #ffffff !important;
			}
			img {
				width: 275px;
				height: 2px;
			}
		}
		.progress-div {
			display: flex;
			flex-direction: row;
		}
		.down-container-right {
			margin-left: 39px;
		}
	}
`;
export { Container };
