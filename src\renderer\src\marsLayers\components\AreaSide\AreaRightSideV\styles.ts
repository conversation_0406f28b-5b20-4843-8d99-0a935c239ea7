import styled, { keyframes } from 'styled-components';
import imgs from '@renderer/images/background';
import icon from '@renderer/images/icon';
import searchBg from '@renderer/images/icon/searchBg.png';

const fadeInAnimation = keyframes`
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
`;

const CarRightSideStyled = styled.div`
	width: 22.5%;
	height: 1080px;
	position: absolute;
	right: 0;
	bottom: 0;
	z-index: 10;
	background: url(${imgs.areaRightSide}) no-repeat;
	background-size: 100% 100%;
	animation: ${fadeInAnimation} 1s ease-in-out;
	display: flex;
	justify-content: center;
	align-items: center;
	.Carcontent {
		width: 93.5%;
		height: 95%;
		padding: 0 5px;
		backdrop-filter: blur(15px);
		border-radius: 45px 0 0 45px;
		box-shadow: -25px 0 25px 25px rgb(3 27 60 / 54%);
		.content-top {
			width: 100%;
			display: flex;
			justify-content: space-between;
			.topModule {
				width: 49%;
			}
		}
		.slidingLayer {
			display: flex;
			flex-direction: column;
			width: 98%;
			margin-top: 10px;
			.line {
				width: 100%;
				height: 4px;
				margin: 5px 0;
				margin-bottom: 10px;
				background: linear-gradient(
					270deg,
					rgba(16, 68, 84, 0.53) 0%,
					#0c69a7 100%
				);
			}
			.dropDown {
				position: absolute;
				right: 0%;
				top: 50.5%;
			}
			:where(.css-dev-only-do-not-override-mu9r37).ant-btn-default {
				width: 120px;
				height: 35px;
				background-color: transparent;
				border: 0;
				background: url(${icon.selectRegionBg});
				background-repeat: no-repeat;
				background-size: 100% 100%;
				font-size: 18px;
				margin: 0 10px;
			}
			display: flex;
			align-items: center;
		}
		.slidtext {
			width: 100%;
			height: 100%;
			overflow-wrap: break-word;
			color: rgba(255, 255, 255, 1);
			font-size: 24px;
			font-family: PingFangSC-Regular;
			text-align: left;
			white-space: nowrap;
			line-height: 38px;
			padding-left: 40px;
		}
	}
`;

const LayoutContainer = styled.div`
	width: 100%;
	display: flex;
	flex-direction: column;
	position: relative;
	/* margin-bottom: 30px; */
	.single {
		width: 100%;
		display: flex;
		div {
			flex: 1;
			color: #fff;
			background: linear-gradient(
				270deg,
				rgba(0, 203, 238, 0.04) 0%,
				rgba(0, 155, 216, 0.28) 100%
			);
			border: 1px solid #009bd8;
			padding: 10px 20px;
			span {
				color: #6bdaff;
			}
		}
	}
	.double {
		width: 100%;
		display: flex;
		div {
			flex: 1;
			color: #fff;
			background-color: transparent;
			padding: 10px 20px;
			border: 1px solid #009bd8;
			span {
				color: #6bdaff;
			}
		}
	}
	.thead {
		display: flex;
		div {
			flex: 1;
			color: #fff;
			background: linear-gradient(
				270deg,
				rgba(0, 203, 238, 0.04) 0%,
				rgba(0, 155, 216, 0.28) 100%
			);
			border: 1px solid #009bd8;
			padding: 10px 20px;
			text-align: center;
		}
	}
	.cumulative {
		display: flex;
		div {
			flex: 1;
			background-color: transparent;
			padding: 10px 20px;
			border: 1px solid #009bd8;
			text-align: center;
			color: #6bdaff;
		}
	}
	.btn-table {
		position: absolute;
		right: 20px;
		width: 70px;
		height: 45px;
		background: url(${searchBg}) no-repeat;
		background-size: 100% 100%;
		line-height: 45px;
		text-align: center;
		cursor: pointer;
		color: #e2e5e5;
		margin: 0;
		font-size: 18px;
		z-index: 10;
	}
	.table-container {
		height: 97%;
		padding: 45px 0;
		right: 0;
		border-radius: 8px;
	}
`;

export { LayoutContainer, CarRightSideStyled };
