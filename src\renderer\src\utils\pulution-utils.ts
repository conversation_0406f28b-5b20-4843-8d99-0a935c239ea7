export const pollutionLevelColorMap = {
	134004: [
		{
			min: 250,
			max: 9999999,
			label: '严重',
			color: '#800021',
		},
		{
			min: 151,
			max: 250,
			label: '重度',
			color: '#9a004a',
		},
		{
			min: 116,
			max: 150,
			label: '中度',
			color: '#ff0000',
		},
		{
			min: 76,
			max: 115,
			label: '轻度',
			color: '#ff7f00',
		},
		{
			min: 36,
			max: 75,
			label: '良',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 35,
			label: '优',
			color: '#00e600',
		},
	],
	134002: [
		{
			min: 421,
			max: 9999999,
			label: '严重',
			color: '#800021',
		},
		{
			min: 351,
			max: 420,
			label: '重度',
			color: '#9a004a',
		},
		{
			min: 251,
			max: 350,
			label: '中度',
			color: '#ff0000',
		},
		{
			min: 151,
			max: 250,
			label: '轻度',
			color: '#ff7f00',
		},
		{
			min: 51,
			max: 150,
			label: '良',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 50,
			label: '优',
			color: '#00e600',
		},
	],
	121026: [
		{
			min: 801,
			max: 9999999,
			label: '严重',
			color: '#800021',
		},
		{
			min: 651,
			max: 800,
			label: '重度',
			color: '#9a004a',
		},
		{
			min: 501,
			max: 650,
			label: '中度',
			color: '#ff0000',
		},
		{
			min: 151,
			max: 500,
			label: '轻度',
			color: '#ff7f00',
		},
		{
			min: 51,
			max: 150,
			label: '良',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 50,
			label: '优',
			color: '#00e600',
		},
	],
	121005: [
		{
			min: 37,
			max: 48,
			label: '严重',
			color: '#800021',
		},
		{
			min: 25,
			max: 36,
			label: '重度',
			color: '#9a004a',
		},
		{
			min: 15,
			max: 24,
			label: '中度',
			color: '#ff0000',
		},
		{
			min: 5,
			max: 14,
			label: '轻度',
			color: '#ff7f00',
		},
		{
			min: 3,
			max: 4,
			label: '良',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 2,
			label: '优',
			color: '#00e600',
		},
	],
	121004: [
		{
			min: 566,
			max: 9999999,
			label: '严重',
			color: '#800021',
		},
		{
			min: 281,
			max: 565,
			label: '重度',
			color: '#9a004a',
		},
		{
			min: 181,
			max: 280,
			label: '中度',
			color: '#ff0000',
		},
		{
			min: 81,
			max: 180,
			label: '轻度',
			color: '#ff7f00',
		},
		{
			min: 41,
			max: 80,
			label: '良',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 40,
			label: '优',
			color: '#00e600',
		},
	],
	105024: [
		{
			min: 801,
			max: 9999999,
			label: '严重',
			color: '#800021',
		},
		{
			min: 266,
			max: 800,
			label: '重度',
			color: '#9a004a',
		},
		{
			min: 216,
			max: 265,
			label: '中度',
			color: '#ff0000',
		},
		{
			min: 161,
			max: 215,
			label: '轻度',
			color: '#ff7f00',
		},
		{
			min: 101,
			max: 160,
			label: '良',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 100,
			label: '优',
			color: '#00e600',
		},
	],
	209002: [
		{
			min: 301,
			max: 9999999,
			label: '严重',
			color: '#800021',
		},
		{
			min: 201,
			max: 300,
			label: '重度',
			color: '#9a004a',
		},
		{
			min: 151,
			max: 200,
			label: '中度',
			color: '#ff0000',
		},
		{
			min: 101,
			max: 150,
			label: '轻度',
			color: '#ff7f00',
		},
		{
			min: 51,
			max: 100,
			label: '良',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 50,
			label: '优',
			color: '#00e600',
		},
	],
	199054: [
		{
			min: 151,
			max: 9999999,
			label: '严重',
			color: '#800021',
		},
		{
			min: 121,
			max: 150,
			label: '重度',
			color: '#9a004a',
		},
		{
			min: 86,
			max: 120,
			label: '中度',
			color: '#ff0000',
		},
		{
			min: 51,
			max: 85,
			label: '轻度',
			color: '#ff7f00',
		},
		{
			min: 21,
			max: 50,
			label: '良',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 20,
			label: '优',
			color: '#00e600',
		},
	],
	134001: [
		{
			min: 501,
			max: 9999999,
			label: '严重',
			color: '#800021',
		},
		{
			min: 301,
			max: 500,
			label: '重度',
			color: '#9a004a',
		},
		{
			min: 201,
			max: 300,
			label: '中度',
			color: '#ff0000',
		},
		{
			min: 121,
			max: 200,
			label: '轻度',
			color: '#ff7f00',
		},
		{
			min: 81,
			max: 120,
			label: '良',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 80,
			label: '优',
			color: '#00e600',
		},
	],
	209103: [
		{
			min: 501,
			max: 9999999,
			label: '',
			color: '#800021',
		},
		{
			min: 301,
			max: 500,
			label: '',
			color: '#9a004a',
		},
		{
			min: 201,
			max: 300,
			label: '',
			color: '#ff0000',
		},
		{
			min: 121,
			max: 200,
			label: '',
			color: '#ff7f00',
		},
		{
			min: 81,
			max: 120,
			label: '',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 80,
			label: '',
			color: '#00e600',
		},
	],
	209104: [
		{
			min: 501,
			max: 9999999,
			label: '',
			color: '#800021',
		},
		{
			min: 301,
			max: 500,
			label: '',
			color: '#9a004a',
		},
		{
			min: 201,
			max: 300,
			label: '',
			color: '#ff0000',
		},
		{
			min: 121,
			max: 200,
			label: '',
			color: '#ff7f00',
		},
		{
			min: 81,
			max: 120,
			label: '',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 80,
			label: '',
			color: '#00e600',
		},
	],
	209008: [
		{
			min: 33,
			max: 365,
			label: '',
			color: '#800021',
		},
		{
			min: 25,
			max: 32,
			label: '',
			color: '#9a004a',
		},
		{
			min: 19,
			max: 24,
			label: '',
			color: '#ff0000',
		},
		{
			min: 13,
			max: 18,
			label: '',
			color: '#ff7f00',
		},
		{
			min: 7,
			max: 12,
			label: '',
			color: '#ffff00',
		},
		{
			min: 0,
			max: 6,
			label: '',
			color: '#00e600',
		},
	],
};

export const formatPolluNameSub = (pollutantId) => {
	let a, b;
	switch (Number(pollutantId)) {
		case 134004:
			a = 'PM';
			b = '2.5';
			break;
		case 134002:
			a = 'PM';
			b = '10';
			break;
		case 121026:
			a = 'SO';
			b = '2';
			break;
		case 121005:
			a = 'CO';
			b = '';
			break;
		case 121004:
			a = 'NO';
			b = '2';
			break;
		case 105024:
			a = 'O';
			b = '3';
			break;
		case 209002:
			a = 'AQI';
			b = '';
			break;
		case 199054:
			a = 'TVOC';
			b = '';
			break;
		case 134001:
			a = 'TSP';
			b = '';
			break;
	}
	return {
		a,
		b,
	};
};
