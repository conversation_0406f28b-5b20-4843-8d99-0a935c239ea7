import { useEffect, useState, useContext, useMemo } from 'react';
import moment from 'moment';

import { AreaRightSideStyled, LayoutContainer } from './styles';
import Echarts from '@renderer/components/echarts';
import { getThemeTownDetailByTypeTime } from '@renderer/api';
import { PageContext } from '@renderer/context';
import { tranNumber } from '@renderer/hooks/index';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const StreetRightSide = (props: Props) => {
	const { streetShipData } = props;
	const { selectRegionDate, streetName } = useContext(PageContext);
	//图表
	const [option1, setOption1] = useState({});
	const [option2, setOption2] = useState({});
	const [option3, setOption3] = useState({});

	const streetId = useMemo(() => {
		if (streetName) {
			return streetShipData?.features.find(
				(i) => i.properties.name === streetName,
			).properties.region;
		}
		return '110101011';
	}, [streetName]);

	const getEchartData = (type, setData) => {
		const units = {
			oil: '单位: kg',
			nox: '单位: kg',
			violation_number: '单位: 辆',
		};
		const { start_time, end_time } = selectRegionDate.customDate;
		getThemeTownDetailByTypeTime({
			start_time,
			end_time,
			date_type: 'hour',
			town_id: streetId,
			analysis: type,
		}).then((res: any) => {
			const option = {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow',
					},
				},
				legend: {
					top: 0, // 调整图例的位置，使其移动到图的上方
					right: 100, // 调整图例的位置，使其移动到图的右侧
					textStyle: {
						color: '#fff',
						fontSize: 16,
					},
					itemWidth: 10, // 设置图例项的宽度
					itemHeight: 10, // 设置图例项的高度
					itemShape: 'circle', // 设置图例项的形状为圆形
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true,
				},
				xAxis: [
					{
						type: 'category',
						axisPointer: {
							type: 'shadow',
						},
						axisLabel: {
							textStyle: {
								color: '#E2F0FF',
								fontSize: 14,
							},
						},
						data: res?.time_list.map((time) => {
							return moment(time).format('YYYY-MM-DD HH时');
						}),
					},
				],
				yAxis: [
					{
						name: units[type],
						nameTextStyle: {
							color: 'rgba(212, 232, 254, 1)',
							fontSize: 12,
						},
						type: 'value',
						splitLine: {
							lineStyle: {
								type: 'dashed',
							},
						},
						axisLabel: {
							textStyle: {
								color: '#E2F0FF',
								fontSize: 16,
							},
							formatter: function (value, index) {
								return tranNumber(Number(value), 2);
							},
						},
					},
				],
				series:
					res &&
					res['industry_second']?.map((item) => {
						let data = item.slice(1, item.length);
						return {
							name: item[0],
							type: 'bar',
							stack: 'total',
							data,
							barMaxWidth: 100,
						};
					}),
				animation: false,
			};
			setData(option);
		});
	};

	useEffect(() => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (!start_time) return;
		getEchartData('oil', setOption1);
		getEchartData('nox', setOption2);
		getEchartData('violation_number', setOption3);
	}, [streetId, selectRegionDate]);

	return (
		<AreaRightSideStyled>
			<div className="Streetcontent">
				<div className="slidingLayer">
					<span className="slidtext">油耗分析</span>
				</div>
				<LayoutContainer style={{ height: '20%' }}>
					<div className="echarts-line">
						<Echarts
							option={option1}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">排放量分析</span>
				</div>
				<LayoutContainer style={{ height: '35%' }}>
					<div className="echarts-line">
						<Echarts
							option={option2}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
				<div className="slidingLayer">
					<span className="slidtext">违规车辆分析</span>
				</div>
				<LayoutContainer style={{ height: '25%' }}>
					<div className="echarts-line">
						<Echarts
							option={option3}
							style={{ height: '100%', flex: 1 }}
							notMerge={true}
						></Echarts>
					</div>
				</LayoutContainer>
			</div>
		</AreaRightSideStyled>
	);
};

export default StreetRightSide;
