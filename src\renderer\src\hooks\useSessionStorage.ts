import { useState } from 'react';

export const useSessionStorage = <T>(keyName: string, defaultValue: T) => {
	const [storedValue, setStoredValue] = useState(() => {
		try {
			const value = window.sessionStorage.getItem(keyName);
			if (value) {
				return JSON.parse(value);
			} else {
				window.sessionStorage.setItem(keyName, JSON.stringify(defaultValue));
			}
		} catch (e) {
			return defaultValue;
		}
	});

	const setValue = (value: T) => {
		try {
			window.sessionStorage.setItem(keyName, JSON.stringify(value));
		} catch (e) {}
		setStoredValue(value);
	};

	return [storedValue, setValue];
};
