// 行业不同车型里程特征
import React, { useState, useEffect } from 'react';

import MileageCharacteristics from '@renderer/components/MileageCharacteristics';
import { CarLegendColor } from '@renderer/utils';
import { getIndustryByTime } from '@renderer/api';
function MileageCharacteristicsCarIndustry() {
	const area = [
		'货车',
		'渣土车',
		'工程车',
		'公交车',
		'其他客车',
		'环卫车',
		'其他用途',
	];
	let my_data = {
		xData: area,
		legend: Object.keys(CarLegendColor),
		data: [
			[0, 0, 0, 0, 0, 0, 0, 0, 0],
			[0, 0, 0, 0, 0, 0, 0, 0, 0],
			[0, 0, 0, 0, 0, 0, 0, 0, 0],
		],
	};

	const colors = Object.values(CarLegendColor);
	const [data, setData] = useState(my_data);
	const onTimeChange = (time, type) => {
		getIndustryByTime({ time: time.start_time, time_type: type, target: '1' })
			.then((res: any) => {
				setData(res);
			})
			.catch(() => {
				setData(my_data);
			});
	};

	return (
		<MileageCharacteristics
			title={'行业不同车型里程特征'}
			defaultValue="year"
			barWidth={'24'}
			data={data}
			onTimeChange={onTimeChange}
			colors={colors}
			interval={0}
		/>
	);
}

export default MileageCharacteristicsCarIndustry;
