// 区域
import React, {
	useEffect,
	useContext,
	useState,
	useCallback,
	useRef,
} from 'react';
import { Radio } from 'antd';
import * as echarts from 'echarts';
import { throttle } from 'lodash';
import {
	getIndustryIndicator,
	getRegionalIndicator,
	getRegionalViolationTop5,
} from '@renderer/api';
import FallingAreaMap from '../components/FallingAreaMap';
import { getPop } from '@renderer/components/IndustrySide/components/IndustryMap/echartsIo';
import { PageContext, MapContext } from '@renderer/context';
import {
	formatNumber,
	useTimeReferenceParams,
	tranNumber,
} from '@renderer/hooks/index';
import CustomPopUps from '@renderer/baseUI/TimeTypeRadioCustom/components/CustomPopUps';
import EmissionProportion from '../components/EmissionProportion';
import {
	COUNTY_ID_NAME_MAP,
	beijingRegions,
	legendArea,
	unitList,
	unitIndustry,
	getTitles,
	violationTypeList,
} from '../components/AreaStreetStyles';
import {
	IndustryViolationWrapStyled,
	AreaViolationStyled,
	ReferenceStyled,
	ViolationTypeStyled,
	RoadTopFive,
} from '../components/AreaStreetStyles/styles';

export default (props) => {
	const { visible, layerId } = props;
	const { sceneMode } = useContext(MapContext);
	const {
		currentTopNavMenu,
		setCurrentTopNavMenu,
		regionName,
		setRegionName,
		regionData,
		selectRegionDate,
		regionalIndicators,
		setRegionData,
		setRegionalIndicators,
		hideModeMenu,
		setCurrentSelectedId,
	} = useContext(PageContext);

	const divChartRef = useRef(null);
	const myChartRef = useRef(null);
	const timer = useRef(null);
	// const [option1, setOption1] = useState({})
	const [customDateYoy, setCustomDateYoy] = useState({});
	const [selectedButton, setSelectedButton] = useState('default');
	const [showPop, setShowPop] = useState(false);
	const [regionYOY, setRegionYOY] = useState([]);
	const [proportionData, setProportionData] = useState([]); // 区域右下角数据占比
	const [regionTitle, setRegionTitle] = useState('');
	const [top5List, setTop5List] = useState([]);
	const [violationList, setViolationList] = useState(violationTypeList);
	const [industryType, setIndustryType] = useState('');

	const colors = [
		'#00E600',
		'#FFFF00',
		'#FF7F00',
		'#FF0000',
		'#9A004A',
		'#800021',
	];
	const getMaterialColor = (num, layerId, data) => {
		switch (layerId) {
			case 'industry': {
				const valueList = data.map((item) => item.value);
				const maxValue = Math.max(...valueList);
				const minValue = Math.min(...valueList);

				const scale = (num - minValue) / (maxValue - minValue);

				const colorIndex = Math.floor(scale * (colors.length - 1));

				return colors[Math.max(0, Math.min(colorIndex, colors.length - 1))];
			}
		}
	};

	// 初始化柱状图
	const setUpOption1 = (data) => {
		const combinedData = data?.adm?.map((adm, index) => ({
			adm,
			count: formatNumber(data.count[index]),
			dod: formatNumber(data.dod[index]),
			per: formatNumber(data.per[index]),
		}));
		combinedData?.sort((a, b) => a.count - b.count);
		const sortedAdmData = combinedData?.map((item) => item.adm);
		const sortedCountData = combinedData?.map((item) => item.count);
		const sortedDodData = combinedData?.map((item) => item.dod);
		const sortedPerData = combinedData?.map((item) => item.per);
		const option = getPop({
			...data,
			adm: sortedAdmData,
			count: sortedCountData,
			dod: sortedDodData,
			per: sortedPerData,
			text: getTitles(
				selectRegionDate.timeType,
				unitList.find((i) => i.name === currentTopNavMenu)?.name,
			),
			unit: unitList.find((i) => i.name === currentTopNavMenu)?.unit,
			industry: industryType,
		});
		// setOption1(option)

		return option;
	};

	// 柱状图默认自定义切换
	const handleClickReference = (value) => {
		setSelectedButton(value);
		if (value === 'custom') {
			setShowPop(true);
		} else {
			setShowPop(false);
		}
	};

	// 获取接口数据
	const getCustomLineData = (params) => {
		getIndustryIndicator(params)
			.then((res) => {
				if (res?.count?.length > 0) {
					setIndustryType(res?.adm[0]);
					setRegionalIndicators(res);
				} else {
					setIndustryType('');
				}
			})
			.catch((err) => {
				setRegionalIndicators({});
				setIndustryType('');
			});
	};
	useEffect(() => {
		setCurrentTopNavMenu(unitList[0].name);
	}, []);

	useEffect(() => {
		if (!visible || !hideModeMenu) return;
		setCurrentSelectedId(layerId);
		if (selectRegionDate.customDate.start_time == '' || currentTopNavMenu == '')
			return;
		myChartRef.current = echarts.init(divChartRef.current);
		myChartRef.current.setOption(setUpOption1(regionalIndicators));
		return () => {
			if (myChartRef.current) {
				myChartRef.current.dispose();
			}
			setCurrentSelectedId('');
		};
	}, [visible, regionalIndicators, currentTopNavMenu]);

	useEffect(() => {
		if (!industryType) return;
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		const params = {
			start_time,
			end_time,
			time_type,
			level: 3,
			industry_type: industryType,
			topic: unitList.find((i) => i.name === currentTopNavMenu)?.id,
			violation_type_id:
				violationList
					?.filter((item) => item.active)
					?.map((i) => i.id)
					?.join(',') || '',
		};
		getRegionalIndicator(params)
			.then((res) => {
				if (res?.count?.length > 0) {
					const mapData = res?.adm?.map((item, idx) => {
						const value = formatNumber(res?.count[idx]);
						const per = formatNumber(res?.per[idx]);
						return {
							name: COUNTY_ID_NAME_MAP[item],
							value,
							per,
						};
					});
					setRegionData(mapData);
					setRegionYOY(mapData);
				} else {
					setRegionData([]);
					setRegionYOY([]);
				}
			})
			.catch((err) => {
				setRegionData([]);
				setRegionYOY([]);
			});
	}, [industryType]);

	useEffect(() => {
		if (!regionData?.length) {
			setProportionData([]);
			return;
		}
		setProportionData(regionData);
	}, [regionData]);

	useEffect(() => {
		getData();
		setRegionName(null);
	}, [hideModeMenu, selectedButton, selectRegionDate, currentTopNavMenu]);

	useEffect(() => {
		if (!industryType) return;
		myChartRef.current.setOption(setUpOption1(regionalIndicators));
		myChartRef.current.on('click', 'yAxis.category', function (params) {
			setIndustryType(params.value);
		});
	}, [industryType]);

	const getData = () => {
		const { start_time, end_time, time_type } = selectRegionDate.customDate;
		if (
			!hideModeMenu ||
			!start_time ||
			(selectedButton === 'custom' && Object.keys(customDateYoy)?.length > 0)
		)
			return;
		setIndustryType(''); //重置行业类型
		const params = {
			start_time,
			end_time,
			time_type,
			topic: unitList.find((i) => i.name === currentTopNavMenu)?.id,
			violation_type_id:
				violationList
					?.filter((item) => item.active)
					?.map((i) => i.id)
					?.join(',') || '',
		};
		getCustomLineData(params);
	};
	return visible && hideModeMenu ? (
		<>
			{industryType && (
				<FallingAreaMap
					enableClick={true}
					data={regionData}
					layerId={layerId}
					sceneMode={sceneMode}
					getMaterialColor={getMaterialColor}
					regionYOY={regionYOY}
					position="right"
				/>
			)}
			<IndustryViolationWrapStyled>
				{visible && legendArea(layerId, currentTopNavMenu)}
				{visible && unitIndustry(currentTopNavMenu)}
				<ReferenceStyled>
					<div className="selectAssembly">
						<p>环比基准：</p>
						<Radio.Button
							value="default"
							onClick={() => handleClickReference('default')}
							style={{
								color: selectedButton === 'default' ? '#3ADAFF' : '#e2e5e5',
							}}
						>
							默认
						</Radio.Button>
						<Radio.Button
							value="custom"
							onClick={() => handleClickReference('custom')}
							style={{
								color: selectedButton === 'custom' ? '#3ADAFF' : '#e2e5e5',
							}}
						>
							自定义
						</Radio.Button>
					</div>
					<CustomPopUps
						top={'50px'}
						right={'0'}
						showPop={showPop}
						setShowPop={setShowPop}
						setTimeType={null}
						setCustomDate={setCustomDateYoy}
					/>
				</ReferenceStyled>
				<AreaViolationStyled>
					<div className="echarts-line">
						<div
							ref={divChartRef}
							style={{ width: '100%', height: '650px', flex: 1 }}
						></div>
					</div>
				</AreaViolationStyled>
			</IndustryViolationWrapStyled>
		</>
	) : null;
};
