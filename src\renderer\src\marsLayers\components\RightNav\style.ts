import { styled } from 'styled-components';
import imgs from '../../../images/right-nav';

export default styled.div`
	position: absolute;
	bottom: 5px;
	left: 240px;
	/* right: 1600px; */
	z-index: 99;
	width: 80px;
	border-radius: 43px;
	cursor: pointer;
	/* background: rgba(51, 82, 112, 0.5); */
	.right-nav-item {
		width: 80px;
		height: 72px;
		text-align: center;
		line-height: 42px;
		font-size: 30px;
		color: #fff;
		padding: 15px 19px;
		background: rgba(51, 82, 112, 0.5);
	}
	.active {
		background: rgba(51, 82, 112, 1);
	}
	/* .right-nav-item:first-child {
    border-top-right-radius: 43px;
    border-top-left-radius: 43px;
  } */
	.right-nav-setting {
		width: 100%;
		height: 100px;
		border-radius: 43px;
		background: linear-gradient(
			180deg,
			#070c12 0%,
			rgba(51, 82, 112, 0.9) 55%,
			#0e1823 100%
		);
		padding: 26px 16px;
		.right-nav-spin {
			width: 48px;
			height: 48px;
			opacity: 1;
			background-image: url(${imgs.setting});
			background-size: 100% 100%;
		}
		.rotated {
			animation: spin 0.5s ease-in-out 1;
			@keyframes spin {
				from {
					transform: rotate(0deg);
				}
				to {
					transform: rotate(180deg);
				}
			}
		}
		.rev-rotated {
			animation: revSpin 0.5s ease-in-out 1;
			@keyframes revSpin {
				from {
					transform: rotate(0deg);
				}
				to {
					transform: rotate(-180deg);
				}
			}
		}
	}
`;
