import styled from 'styled-components';
const LayoutContainer = styled.div`
	margin-left: 5px;
	display: flex;
	flex-direction: row;
	position: relative;
	.container {
		width: 254px;
		display: flex;
		flex-direction: row;
		.describe {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-top: 0;
			margin-bottom: 20px;
			margin-right: -14px;
			dl {
				color: #fff;
				padding-left: 0;
				margin: 0 0 0 0;
				dt {
					font-size: 18px;
					color: #e8f4ff;
					letter-spacing: 4px;
					margin-top: 10px;
					margin-bottom: 20px;
					text-align: right;
					display: inline-block;
					white-space: nowrap;
				}
				dd {
					position: relative;
					bottom: 5px;
					right: 0px;
					p {
						line-height: 18px;
						// text-align: right;
						display: flex;
						align-items: flex-end;
						justify-content: space-between;
						.topic {
							width: 120px;
							font-size: 16px;
							text-align: right;
							color: #e8f4ff;
						}
						.data {
							font-size: 24px;
							// font-family: 'electronicFont';
							color: #5ce2aa;
							margin-left: auto;
							margin-right: 8px;
						}
					}
					span {
						font-size: 16px;
						color: #e8f4ff;
					}
				}
				&.no1 {
					display: flex;
					flex-direction: column;
					justify-content: right;
					dt {
					}
					dd {
						display: inline-block;
						white-space: nowrap;
					}
				}
				&.no3 {
					text-align: center;
					dd {
						margin-right: 4px;
						white-space: nowrap;
						.data2 {
							color: #f9ae83;
							// font-family: 'electronicFont';
							font-size: 24px;
							margin-left: auto;
							margin-right: 5px;
						}
					}
				}
			}
			.describe-bottom {
				display: flex;
				width: 100%;
				flex-direction: row;
				justify-content: center;
				margin-top: 4px;
				margin-left: -20px;
				dl {
					padding-right: 8px;
					margin-left: 8px;
					&:nth-child(1) {
						padding-left: 0;
						position: relative;
						&::after {
							content: '';
							position: absolute;
							right: 2px;
							top: 46px;
							display: block;
							width: 1px;
							height: 160px;
							border-right: #3488b9 1px dashed;
						}
					}
					&:nth-child(2) {
						padding-left: 0;
						position: relative;
						&::after {
							content: '';
							position: absolute;
							right: 2px;
							top: 46px;
							display: block;
							width: 1px;
							height: 160px;
							border-right: #3488b9 1px dashed;
						}
					}
				}
			}
		}
	}
`;
export default LayoutContainer;
