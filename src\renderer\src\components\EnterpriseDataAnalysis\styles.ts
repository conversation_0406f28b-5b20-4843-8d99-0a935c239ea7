import styled from 'styled-components';

const RealTimeEnterpriseDataStyled = styled.div`
	width: 96%;
	height: 100%;
	.enterpriseContent {
		width: 100%;
		height: 100%;
		.topNav {
			display: flex;
			justify-content: space-between;
		}
	}
	.RealTimeIndustryNav {
		width: auto;
		z-index: 9;
		background-color: rgba(0, 65, 94) !important;
		color: #ccc !important;
		font-size: 15px !important;
	}
	.ant-segmented-item-selected {
		background-color: rgba(0, 116, 145) !important;
	}
	.ant-segmented-item {
		width: 100%;
	}
	.enterpriseSituationOptionContent {
		width: 100%;
		height: 100%;
		.list-li {
			height: 100px;
			display: inline-block;
			max-width: 100%;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			text-align: center;
		}
		/* .ant-table-cell {
      width: 80px !important;
      span {
        display: inline-block;
        max-width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table-thead > tr > th {
      padding: 0;
      text-align: center;
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table-tbody > tr > td {
      padding: 0;
      text-align: center;
    }
    .ant-pagination {
      display: none;
    }
    .ant-table-wrapper {
      width: 100%;
      height: 100%;
      margin-left: auto;
      margin-top: 5px;
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table {
      background: linear-gradient(
        to right,
        rgba(11, 41, 69, 0.8),
        rgba(15, 51, 74, 0.8)
      ) !important;
      border-radius: 5px;
      border: 1px solid rgba(9, 132, 182, 0.7);
    }
    .ant-table-thead {
      border: 1px solid rgba(9, 132, 182, 0.7);
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table-thead > tr > th {
      border: 1px solid rgba(9, 132, 182, 0.7) !important;
      background: linear-gradient(
        to right,
        rgba(11, 41, 69, 0.8),
        rgba(15, 51, 74, 0.8)
      ) !important;
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table-tbody > tr > th,
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper .ant-table-tbody > tr > td {
      border: 1px solid rgba(9, 132, 182, 0.7);
    }
    .ant-table-content table {
      border-collapse: collapse;
    }
    :where(.css-dev-only-do-not-override-mu9r37).ant-table-wrapper
      .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(
        .ant-table-row-expand-icon-cell
      ):not([colspan])::before {
      background-color: transparent;
    }
    .ant-table-cell-row-hover {
      background-color: rgba(15, 51, 74, 1) !important;
    }
    .ant-table-body {
      &::-webkit-scrollbar {
        height: 12px;
        width: 2px;
        overflow-y: auto;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 5px;
        background: #939392;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: 0;
        border-radius: 0;
        background: #000;
      }
    } */
	}
`;

export default RealTimeEnterpriseDataStyled;
