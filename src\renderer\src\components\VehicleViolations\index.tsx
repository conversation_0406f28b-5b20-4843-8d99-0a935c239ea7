import * as React from 'react';
import moment from 'moment';
import { useState, useEffect } from 'react';
import { useFont } from '@renderer/hooks';
import LayoutContainer from './style';
import Box from '../../baseUI/Box';
// import icon from '../../images/icon/point.png'
import SectorEchart from '../../baseUI/SectorEchart';
// import LevelTwoTitle from '../../components/LevelTwoTitle'
// import ScrollList from '../../components/ScrollList'
import {
	getCompanyTop,
	getViolationVehicle,
	getViolationVehicleProportion,
	getViolationOverview,
} from '@renderer/api';
import { getAffiliationInfoByID } from '@renderer/api';
/**
 * 车辆违规
 */
type columnProps = {
	plate_number: string;
	violation_project: string;
	violation_time: string;
	violation_place: string;
};
type overviewProps = {
	violation_nums: string;
	total_nums: string;
	violation_rate: number | string | any;
	max_time: string;
};
type enterpriseProps = {
	name?: string;
	color: string;
	violation_nums: string;
	rate: number | string | any;
	id: string;
};
type dataProps = {
	violation_type: string;
	rate: number;
};
type listProps = {
	violation_type_id: string;
	violation_type: string;
	vin: string;
};
type companyProps = {
	name: string;
	violation_nums: number;
	total: number;
	rate: number;
};
const VehicleViolations = (props) => {
	const [options, setOptions] = useState({});
	const [moreData, setMoreData] = useState<any>(props);
	const [data, setData] = useState([
		{ name: '车辆超排', value: '22' },
		{ name: 'MIL灯异常', value: '22' },
		{ name: '尿素空', value: '32' },
		{ name: 'OBD数据不全', value: '30' },
		{ name: '长期不在线', value: '10' },
		{ name: '其他', value: '10' },
	]);
	// let colorList = [
	//   '#05f8d6',
	//   '#0082fc',
	//   '#fdd845',
	//   '#22ed7c',
	//   '#09b0d3',
	//   '#1d27c9',
	//   '#f9e264',
	//   '#f47a75',
	//   '#009db2',
	//   '#024b51',
	//   '#0780cf',
	//   '#765005'
	// ];
	// let colorList = [
	//   '#203D46',
	//   '#325967',
	//   '#1B3442',
	//   '#9E4A1D',
	//   '#6B2E12',
	//   '#1d27c9',
	//   '#f9e264',
	//   '#f47a75',
	//   '#009db2',
	//   '#024b51',
	//   '#0780cf',
	//   '#765005'
	// ];
	let colorList = [
		'#4CDBF1',
		'#0883D4',
		'#6348FF',
		'#48B2E9',
		'#E0E1E3',
		'#9FE5FC',
		'#FDF36B',
		'#FB837D',
		'#00A6BB',
		'#024D53',
		'#0883D4',
		'#7D5406',
	];

	const [enterprise, setEnterprise] = useState<Array<enterpriseProps>>([]);
	const [overview, setOverview] = useState<overviewProps>({
		violation_nums: 2738,
		total_nums: 124545,
		violation_rate: 2.2,
		max_time: '--',
	});
	const [violatioData, setViolatioData] = useState<Array<listProps>>([]);
	const [show, setShow] = useState(false);
	const [details, setDetails] = useState<columnProps>();
	const column = [
		{
			align: 'center',
			dataIndex: 'vin',
			key: 'vin',
			title: 'VIN',
			with: '100px',
		},
		{
			align: 'center',
			dataIndex: 'violation_type',
			key: 'violation_type',
			title: '违规项目',
		},
		{
			align: 'center',
			dataIndex: 'address',
			key: 'address',
			title: '违规地址',
		},
		{
			align: 'center',
			dataIndex: 'time',
			key: 'time',
			title: '违规时间',
		},
	];

	useEffect(() => {
		const start_time = moment()
			.subtract(30, 'day')
			.format('YYYY-MM-DD 00:00:00');
		const end_time = moment().format('YYYY-MM-DD HH:00:00');
		const county_id = '';
		const top = 10;

		// 违规占比
		getViolationVehicleProportion({}).then((res) => {
			let data = (res as dataProps[]).map((item, index) => {
				return {
					name:
						item.violation_type + ' (' + item.nums + '辆, ' + item.rate + '%)',
					value: item.nums,
				};
			});
			setData(data);
		});

		//违规概况
		getViolationOverview({}).then((res) => {
			setOverview(res as overviewProps);
		});

		Promise.all([
			getViolationVehicle({ start_time, end_time, top }),
			getCompanyTop({ start_time, end_time, top }),
		]).then((res) => {
			//单车违规列表
			setViolatioData(res[0] as listProps[]);
			//违规企业
			let color = '';
			let companyData = (res[1] as enterpriseProps[]).map((item, index) => {
				if (index == 0) {
					color = '#FE2C46';
				} else if (index == 1) {
					color = '#F36405';
				} else if (index == 2) {
					color = '#DF990F';
				} else {
					color = '#79818E';
				}
				return { ...item, color: color };
			});
			setEnterprise(companyData);
			setMoreData({
				...moreData,
				data: {
					enterprise: companyData,
					violatioData: res[0] as listProps[],
				},
			});
			//const ids = (res[1] as enterpriseProps[]).map((item, index) => item.id);
			// getAffiliationInfoByID({
			// 	affiliation_ids: ids.toString(),
			// })
			// 	.then((_res) => {
			// 		if (_res.length) {
			// 			//违规企业
			// 			let color = '';
			//       console.log('_res',_res)
			// 			let companyData = (res[1] as enterpriseProps[]).map(
			// 				(item, index) => {
			// 					if (index == 0) {
			// 						color = '#FE2C46';
			// 					} else if (index == 1) {
			// 						color = '#F36405';
			// 					} else if (index == 2) {
			// 						color = '#DF990F';
			// 					} else {
			// 						color = '#79818E';
			// 					}
			// 					return { ...item, color: color, name: _res[index].AFFILIATION };
			// 				},
			// 			);
			// 			setEnterprise(companyData);
			// 			setMoreData({
			// 				...moreData,
			// 				data: {
			// 					enterprise: companyData,
			// 					violatioData: res[0] as listProps[],
			// 				},
			// 			});
			// 		}
			// 	})
			// 	.catch((error) => {
			// 		console.log('error', error);
			// 	});
		});
	}, []);
	useEffect(() => {
		let option = {
			color: colorList,
			tooltip: {
				trigger: 'item',
				axisPointer: {
					type: 'none',
				},
			},
			legend: [
				{
					orient: 'vertical',
					// data: nameArray,
					left: '195px',
					top: 'center',
					align: 'left',
					itemGap: 14,
					textStyle: {
						color: '#fff',
						fontSize: '14px',
					},
					//图例标记的图形高度
					itemHeight: 10,
					//图例标记的图形宽度
					itemWidth: 10,
				},
			],
			series: [
				{
					type: 'pie',
					roseType: true,
					radius: ['20%', '70%'],
					center: ['35%', '50%'],
					label: {
						position: 'inside',
						formatter(item) {
							if (item.name === '') {
								return '';
							}
							return `${item.value}`;
						},
						textStyle: {
							fontSize: 8,
							color: '#ffffff',
						},
					},
					data: data,
				},
			],
		};
		setOptions(option);
	}, []);
	const onliClick = (e) => {
		setShow(true);
		setDetails(e);
	};
	return (
		<Box title="疑似问题车辆" titlewidth="95%" height="30%" more={moreData}>
			<LayoutContainer>
				<div className={'left-container' + ' ' + useFont()}>
					<div className="describe">
						{/* <img src={icon}></img> */}

						<dl className="no2">
							<dt>异常车辆数</dt>
							<dd>
								{overview?.violation_nums ? overview.violation_nums : '--'}
								<span>辆</span>
							</dd>
						</dl>
						<div className="describe-bottom">
							<dl className="no1">
								<dt>车辆上线数</dt>
								<dd>
									{overview?.total_nums ? overview.total_nums : '--'}
									<span>辆</span>
								</dd>
							</dl>
							<dl className="no3">
								<dt>车辆异常比率</dt>
								<dd>
									{overview?.violation_rate ? overview.violation_rate : '--'}
									<span>%</span>
								</dd>
							</dl>
						</div>
					</div>
					<div className="echarts-container">
						<div className="echarts">
							<SectorEchart
								center={['28%', '40%']}
								data={data}
								colorList={colorList}
							></SectorEchart>
						</div>
					</div>
				</div>
				<div className="data-time">数据时间：{overview.max_time}</div>

				{/* <div className="middle-container">
          <LevelTwoTitle title="单车违规推送"></LevelTwoTitle>
          <div className="table">
            <ul className="column-ul">
              {column.map((item, index) => {
                return (
                  <li className="column-li" key={index}>
                    {item.title}{' '}
                  </li>
                )
              })}
            </ul>

            <ScrollList onliClick={onliClick} data={violatioData} column={column}></ScrollList>
          </div>
        </div>
        <div className="right-container">
          <LevelTwoTitle title="违规企业TOP 5"></LevelTwoTitle>
          <div className="enterprise">
            {enterprise.map((item, index) => {
              return (
                <div key={index} className="enterprise-item">
                  <div className="up">
                    <span style={{ color: item.color }}>{index + 1}</span>
                    <span>{item.name}</span>
                  </div>

                  <div className="down">
                    <img src={icon}></img>
                    <span>违规数量:</span>
                    <span>{item.violation_nums}</span>
                    <span>违规率:</span>
                    <span>{item.rate * 100}%</span>
                  </div>
                </div>
              )
            })}
          </div>
        </div> */}

				{/* <div className={show ? 'detailsPop active' : 'detailsPop'} onClick={() => setShow(false)}>
          <div className="details-container">
            <div className="item">
              <span>车牌号:</span>
              <span>{details?.plate_number}</span>
            </div>
            <div className="item">
              <span>违规项目:</span>
              <span>{details?.violation_project}:</span>
            </div>
            <div className="item">
              <span>违规时间:</span>
              <span>{details?.violation_time}:</span>
            </div>
            <div className="item">
              <span>违规地点:</span>
              <span>{details?.violation_place}:</span>
            </div>
          </div>
        </div> */}
			</LayoutContainer>
		</Box>
	);
};
export default VehicleViolations;
