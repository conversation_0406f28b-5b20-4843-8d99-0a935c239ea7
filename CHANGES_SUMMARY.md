# VehicleHistoricalTrajectory.jsx 改造总结

## 改造目标
将原来每5秒获取一个车辆经纬度数据的功能改造为每2分钟获取前两分钟的数据，并使用FixedRoute来实现轨迹播放。

## 主要修改

### 1. 修改数据获取逻辑
- **原来**: 每5秒获取一个实时位置点，使用 `addDynamicPosition` 方法添加到地图
- **现在**: 每2分钟获取前两分钟的轨迹数据，使用 FixedRoute 播放完整轨迹

### 2. 修改 `getVehicleRealTimePositionInfoData` 函数
```javascript
// 原来的参数
getVehicleRealTimePositionInfo({
  vid,
  time, // 单个时间点
})

// 现在的参数
getVehicleRealTimePositionInfo({
  vid,
  start_time: startTime, // 开始时间（当前时间-2分钟）
  end_time: endTime,     // 结束时间（当前时间）
})
```

### 3. 使用 FixedRoute 替代 addDynamicPosition
- **原来**: 使用 `graphicModel.addDynamicPosition(point, gDate)` 逐点添加
- **现在**: 创建完整的 FixedRoute 对象，包含所有轨迹点和速度信息

### 4. 定时器间隔调整
- **原来**: 每5秒执行一次 (`5000ms`)
- **现在**: 每2分钟执行一次 (`1000 * 60 * 2 = 120000ms`)

### 5. 图层管理优化
- 移除了不再使用的 `graphicLayerModel`
- 统一使用 `carModel.current` 图层管理 FixedRoute
- 优化了清理函数 `removeGraphicLayerModel`

## 技术细节

### 数据处理
1. 获取前两分钟的轨迹数据
2. 将经纬度坐标转换为 mars3d 可用格式
3. 提取速度信息用于 FixedRoute 播放
4. 使用最新数据点的属性信息用于 popup 显示

### FixedRoute 配置
```javascript
graphicModel = new mars3d.graphic.FixedRoute({
  name: `${vid}_realtime`,
  frameRate: 1,
  speed: speeds,           // 速度数组
  positions,              // 位置数组
  clockLoop: false,       // 不循环播放
  camera: {
    type: 'gs',
    pitch: -30,
    radius: 20000,
  },
  model: {
    url: getCarTypeModel(type),
    size: 2,
    fill: true,
    color: getCarColor(type),
    minimumPixelSize: 50,
  },
  attr: res[res.length - 1], // 最新数据作为属性
});
```

### 事件处理
- 添加了轨迹播放开始和结束的事件监听
- 保持了原有的 popup 绑定功能
- 优化了错误处理和重试机制

## 优势
1. **更流畅的轨迹显示**: 使用 FixedRoute 可以显示连续的轨迹线，而不是离散的点
2. **更合理的数据获取频率**: 2分钟间隔减少了服务器压力
3. **更丰富的轨迹信息**: 包含速度变化和时间信息
4. **更好的用户体验**: 轨迹播放更加自然流畅

## 注意事项
1. 需要确保 API 接口支持 `start_time` 和 `end_time` 参数
2. 数据量可能会增加，需要注意性能优化
3. 如果2分钟内没有轨迹数据，需要适当的错误处理
