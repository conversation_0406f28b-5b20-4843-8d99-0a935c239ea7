import * as React from 'react';
import styled from 'styled-components';
// import headerBorder from '@renderer/images/layout/top-border.png'

const MiddleContainer = styled.section`
	/* flex:1; */
	height: 890px;
	position: absolute;
	width: 2220px;
	z-index: 1;
	top: -28px;
	left: 50%;
	transform: translateX(-50%);
	.top-border {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 0;
		width: 1360px;
		height: 11px;
		z-index: 99;
	}
	.mars3d-canvasWind {
		z-index: 1 !important;
	}
`;

type Props = {
	children?: React.ReactNode;
};

const Middle: React.FC<Props> = (props) => {
	return (
		<MiddleContainer>
			{/* <img src={headerBorder} className="top-border" /> */}
			{props.children}
		</MiddleContainer>
	);
};

export default Middle;
