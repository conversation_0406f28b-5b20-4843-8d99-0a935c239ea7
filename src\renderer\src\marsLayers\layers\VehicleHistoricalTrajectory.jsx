import { useEffect, useContext, useState, useRef } from 'react';
import * as mars3d from 'mars3d';
import * as echarts from 'echarts';
import { Spin } from 'antd';
import moment from 'moment';
import { Slider, InputNumber, Progress } from 'antd';
import {
	getVehicleHistoricalTrajectory,
	getVehicleBasicInformation,
	getVehicleRealTimePositionInfo,
	getVehicleCumulativeData,
	getVehicleActivity,
} from '@renderer/api';
import { MapContext, PageContext } from '@renderer/context';
import { getTrackColor } from '@renderer/utils/util';
import {
	VehicleHistoricalTrajectoryStyled,
	LayoutContainer,
	TrajectoryLegendStyled,
	VehicleActivityLevelStyled,
} from '../components/VehicleHistoricalTrajectoryStyled/styles';
import { wgs84togcj02 } from '@renderer/utils';
import DashBoard from '../components/DashBoardNew';
import { getCarTypeModel } from '@renderer/utils';
import { getCarColor } from '../../../../main/data';
import ViolationInfoPanel from '../components/ViolationInfoPanel';
import { COLORLIST2 } from '@renderer/utils/util';
import { useMap } from '@renderer/hooks';

let timer = null;
let idx = 0;
let fixedRoute,
	graphicLayerModel,
	graphicModel,
	defaultPoint = {
		lng: 0,
		lat: 0,
	},
	realVehicleTime = null,
	realTimePositions = [], // 存储实时轨迹点
	realTimeFixedRoute = null; // 实时轨迹的FixedRoute对象

const getTrackLevelInfo = (level) => {
	if (level === '5') {
		return [
			['小于800', getTrackColor(0, level)],
			['800 ~ 1000', getTrackColor(800, level)],
			['1000 ~ 1500', getTrackColor(1000, level)],
			['1500 ~ 2000', getTrackColor(1500, level)],
			['2000及以上', getTrackColor(2000, level)],
		];
	} else if (level === '6') {
		return [
			['小于440', getTrackColor(0, level)],
			['440 ~ 550', getTrackColor(440, level)],
			['550 ~ 825', getTrackColor(550, level)],
			['825 ~ 1100', getTrackColor(825, level)],
			['1100及以上', getTrackColor(1100, level)],
		];
	}
};

const VehicleHistoricalTrajectory = (props) => {
	const map = useMap();
	const {
		currentInfo,
		setCurrentInfo,
		sceneMode,
		setCarDetails,
		setBickDate,
		setTrajectoryPathData,
	} = useContext(MapContext);
	const {
		setSwitchingPointsId,
		setOpenTrajectory,
		openTrajectory,
		setCurrentViolationClueCode,
		currentViolationClueCode,
		setCurrentHistoricalTrackDate,
		currentHistoricalTrackDate,
	} = useContext(PageContext);
	const traceLayer = useRef(null);
	const carModel = useRef(null);
	const ClockAnimate = useRef(null);
	const pieRef = useRef(null);
	const pieChartRef = useRef(null);
	const [status, setStatus] = useState(true);
	const [infoData, setInfoData] = useState({});
	const [carData, setCarData] = useState([]);
	const [customDate, setCustomDate] = useState({
		vin: 'LVCB4L4D0LG001879',
		start_time: '2023-10-10 00:00:00',
		end_time: '2023-10-11 00:00:00',
	});
	const [speed, setSpeed] = useState(3000);
	const [isStart, setIsStart] = useState(false);
	const [isPause, setIsPause] = useState(false);
	const [index, setIndex] = useState(0);
	const eventTarget = new mars3d.BaseClass();
	const [multiplier, setMultiplier] = useState(50);
	const [roamLineData, setRoamLineData] = useState({
		td_alllength: 0,
		td_length: 0,
		td_alltimes: 0,
		td_times: 0,
		td_jd: 0,
		td_wd: 0,
		td_gd: 0,
		percent: 0,
	});
	const [spinning, setSpinning] = useState(false);
	const [currentDateVehicle, setCurrentDateVehicle] = useState(null);
	let lastClickCar;
	let graphicCar;

	const modelUrl = 'file:///public/model/';
	const addModel = (type) => {
		if (type === '渣土车') {
			return `${modelUrl}zhatuche.gltf`;
		} else if (type === '公交车') {
			return `${modelUrl}gongjiaoche.gltf`;
		} else {
			return `${modelUrl}huoche.gltf`;
		}
	};

	const showInfo = (item, data) => {
		setIndex(item.index_original);
		setCarDetails(data[item.index_original]);
		let val = Math.ceil((item.second * 100) / item.second_all);
		if (val < 1) {
			val = 1;
		}
		if (val > 100) {
			val = 100;
		}

		setRoamLineData({
			percent: val,
			td_jd: item.point?.lng,
			td_wd: item.point?.lat,
			td_gd: mars3d.MeasureUtil.formatDistance(item.point?.alt),
			td_times: mars3d.Util.formatTime(item.second),
			td_alltimes: mars3d.Util.formatTime(item.second_all),
			td_length: mars3d.MeasureUtil.formatDistance(item.distance) || '0米',
			td_alllength: mars3d.MeasureUtil.formatDistance(item.distance_all),
		});
	};

	const getVehicleBasicInformationData = () => {
		const { VIN } = currentInfo;

		getVehicleBasicInformation({
			vin: VIN,
		})
			.then((res) => {
				if (res?.VIN_NUMBER && res?.VIN_NUMBER !== '') {
					setInfoData(res);
				}
			})
			.catch((error) => {
				console.log('error', error);
			});
	};

	const getPositions = () => {
		const { VIN } = currentInfo;

		let hour_limit = 24;
		let jsonData = sessionStorage.getItem('zcJsonData');
		if (jsonData) {
			jsonData = JSON.parse(jsonData);
			hour_limit = jsonData?.trajectoryMaxHours
				? jsonData.trajectoryMaxHours
				: hour_limit;
		}
		traceLayer.current = new mars3d.layer.GraphicLayer();
		map.addLayer(traceLayer.current);

		carModel.current = new mars3d.layer.GraphicLayer();
		map.addLayer(carModel.current);
		getVehicleHistoricalTrajectory({
			vid: VIN,
			start_time: `${currentHistoricalTrackDate} 00:00:00`,
			end_time: `${currentHistoricalTrackDate} 23:59:59`,
			// interval: 5,
			hour_limit,
		})
			.then((res) => {
				console.log(
					'=================getVehicleHistoricalTrajectory===================',
				);
				console.log(res);
				console.log(
					'=================getVehicleHistoricalTrajectory===================',
				);
				if (res?.length > 1) {
					const resData = res.filter((item) => {
						return item.lng != 0 && item.lat != 0;
					});
					// setCarData(resData)
					// btnStart(res)
					// if (!carData.length) return
					DrawTrack(resData);
					MotionPlay(resData);
					const PathData = resData.map((item) => {
						return [item.lng, item.lat];
					});
					setTrajectoryPathData(PathData);
				} else {
					// setCarData([])
					setSpinning(false);
					setTrajectoryPathData([]);
				}
			})
			.catch((error) => {
				// setCarData([])
				setSpinning(false);
				setTrajectoryPathData([]);
			});
	};

	const DrawTrack = (res) => {
		res.forEach((item, index) => {
			const nextIndex = index === res.length - 1 ? 0 : index + 1;
			if (nextIndex === 0) return;
			const nextItem = res[nextIndex];
			const graphic = new mars3d.graphic.PolylineEntity({
				positions: [
					wgs84togcj02(item.lng, item.lat),
					wgs84togcj02(nextItem.lng, nextItem.lat),
				],
				style: {
					width: 5,
					color: getTrackColor(
						item.nox_ppm,
						infoData?.EMISSION_LEVEL == '国Ⅴ' ? '5' : '6',
					),
				},
				attr: { remark: 'line' },
			});
			traceLayer.current.addGraphic(graphic);
		});
	};

	const MotionPlay = (res) => {
		const { VIN, type } = currentInfo;
		let speed = [];
		const positions = res.map((item) => {
			speed.push(item.speed);
			const p = wgs84togcj02(item.lng, item.lat);
			return [p[0], p[1]];
		});
		fixedRoute = new mars3d.graphic.FixedRoute({
			name: `${VIN}`,
			frameRate: 1,
			speed,
			positions,
			clockLoop: false, // 是否循环播放
			camera: {
				type: 'gs',
				pitch: -35,
				radius: 300,
				followedX: 80,
				followedZ: 50,
				heading: 0,
				roll: 0,
			},
			model: {
				url: getCarTypeModel(type),
				size: 3,
				fill: true,
				color: getCarColor(type),
				minimumPixelSize: 80,
				maximumScale: 100,
			},
		});
		carModel.current.addGraphic(fixedRoute);

		fixedRoute.on(mars3d.EventType.start, function (event) {
			console.log('漫游开始start');
		});
		fixedRoute.on(mars3d.EventType.end, function (event) {
			console.log('漫游结束end');
		});
		fixedRoute.on(mars3d.EventType.endItem, function (event) {
			// console.log('漫游结束endItem', event)
		});
		// ui面板信息展示
		fixedRoute.on(mars3d.EventType.change, (event) => {
			// const popup = event.graphic.getPopup()
			// const container = popup?.container // popup对应的DOM
			// console.log('漫游change', event)
			showInfo(event, res);
			// throttled(eventTarget.fire('roamLineChange', event), 500)
		});

		eventTarget.on('roamLineChange', (roamLineData) => {
			console.log('roamLineChange>>>>', roamLineData);
		});

		map.on(mars3d.EventType.keydown, function (event) {
			// 空格 切换暂停/继续
			if (event.keyCode === 32) {
				if (fixedRoute.isPause) {
					fixedRoute.proceed();
				} else {
					fixedRoute.pause();
				}
			}
		});
		// btnStart()
		fixedRoute
			.autoSurfaceHeight({
				splitNum: 1,
				has3dtiles: false,
			})
			.then(function (e) {
				removeGraphicLayerModel();
				setSpinning(false);
				fixedRoute.start();
				udpateState();
			});
	};

	const udpateState = (stop = false) => {
		setTimeout(() => {
			setIsStart(fixedRoute.isStart);
			setIsPause(fixedRoute.isPause);
			if (stop) removeLayer();
		}, 200);
	};

	// const btnReturn = () => {
	//   console.log('================btnReturn====================')
	//   console.log(lastClickCar)
	//   console.log('=================btnReturn===================')
	//   if (lastClickCar) {
	//     lastClickCar.circle.show = false
	//     lastClickCar = null
	//   }
	//   map.trackedEntity = undefined
	//   setCurrentInfo(null)
	//   setOpenTrajectory(false)
	//   setBickDate(false)
	// }

	const getVehicleCumulative = () => {
		const { VIN } = currentInfo;
		const arr = [1, 2, 3, 4];
		const getDataArr = arr.map((item, idx) => {
			return getVehicleCumulativeData({
				vid: VIN,
				start_time: `${currentHistoricalTrackDate} 00:00:00`,
				end_time: `${currentHistoricalTrackDate} 23:59:59`,
				target: item,
			});
		});
		Promise.all(getDataArr).then((res) => {
			console.log('==============getVehicleCumulative======================');
			console.log(getDataArr, res[0], res[1], res[2], res[3]);
			console.log('==============getVehicleCumulative======================');
			setCurrentDateVehicle({
				总排放: res[0][0]['value'],
				总里程: res[1][0]['value'],
				总油耗: res[2][0]['value'],
				总时长: res[3][0]['value'],
			});
		});
	};

	const btnStart = () => {
		setSpinning(true);
		console.log('================btnStart====================');
		// console.log(carData)
		// console.log('================btnStart====================')
		getPositions();
		setTimeout(() => {
			getVehicleCumulative();
			getVehicleActivityData();
		}, 10);
	};

	const btnPause = () => {
		fixedRoute.pause();
		udpateState();
	};

	const btnProceed = () => {
		fixedRoute.proceed();
		udpateState();
	};

	const btnStop = () => {
		fixedRoute.stop();
		udpateState(true);
		viewRealTimeVehicleData();
	};

	const onChange = (value) => {
		// console.log('changed', value)
		setMultiplier(value);
	};

	const removeLayer = () => {
		if (traceLayer.current) {
			traceLayer.current.clearDrawing();
			traceLayer.current.clear(true);
			traceLayer.current.enabledEvent = false;
			map.removeLayer(traceLayer.current);
		}
		if (carModel.current) {
			carModel.current.clearDrawing();
			carModel.current.clear(true);
			carModel.current.enabledEvent = false;
			map.removeLayer(carModel.current);
		}
		initHistoricalTrack();
	};

	const initHistoricalTrack = () => {
		setTrajectoryPathData([]);
		setCurrentViolationClueCode('');
		setCurrentHistoricalTrackDate('');
		setIsStart(false);
		setIsPause(false);
	};

	const viewRealTimeVehicleData = () => {
		const vid = currentInfo.VIN;
		window.electron.ipcRenderer.send('layer', 'VehicleRealTimePosition');

		// 确保 carModel 图层存在
		if (!carModel.current) {
			carModel.current = new mars3d.layer.GraphicLayer();
			map.addLayer(carModel.current);
		}

		// 清空之前的实时轨迹数据
		realTimePositions = [];
		if (realTimeFixedRoute && carModel.current) {
			carModel.current.removeGraphic(realTimeFixedRoute);
			realTimeFixedRoute = null;
		}

		defaultPoint['lng'] = 0;
		defaultPoint['lat'] = 0;
		let minute = 1;
		let jsonData = sessionStorage.getItem('zcJsonData');
		if (jsonData) {
			jsonData = JSON.parse(jsonData);
			minute = jsonData?.vehicleDelayTime ? jsonData.vehicleDelayTime : minute;
		}
		getVehicleRealTimePositionInfoData(vid, minute);
	};

	const getVehicleRealTimePositionInfoData = (vid, minute) => {
		let time = moment()
			.subtract(minute, 'minute')
			.format('YYYY-MM-DD HH:mm:ss');
		getVehicleRealTimePositionInfo({
			vid,
			time,
		})
			.then((res) => {
				if (res.length) {
					let lng = res[0].lng;
					let lat = res[0].lat;
					if (res[0].lng == 0 || res[0].lat == 0) {
						lng = defaultPoint['lng'];
						lat = defaultPoint['lat'];
					} else {
						defaultPoint['lng'] = lng;
						defaultPoint['lat'] = lat;
					}
					const transformedCoords = new mars3d.PointTrans.wgs2gcj([lng, lat]);

					// 添加新的位置点到数组（使用 LngLatPoint 格式）
					const newPoint = new mars3d.LngLatPoint(transformedCoords[0], transformedCoords[1], 10);
					realTimePositions.push(newPoint);

					console.log('实时轨迹点数:', realTimePositions.length, '最新位置:', transformedCoords[0], transformedCoords[1]);

					// 每次都重新创建 FixedRoute 以确保正确播放
					if (realTimePositions.length >= 2) {
						// 清除旧的 FixedRoute
						if (realTimeFixedRoute && carModel.current) {
							carModel.current.removeGraphic(realTimeFixedRoute);
						}

						// 创建新的 FixedRoute，包含所有轨迹点
						realTimeFixedRoute = new mars3d.graphic.FixedRoute({
							name: `${vid}_realtime`,
							frameRate: 0.5, // 降低帧率，让播放更平滑
							positions: realTimePositions,
							clockLoop: false,
							camera: {
								type: 'gs',
								pitch: -35,
								radius: 300,
								followedX: 80,
								followedZ: 50,
								heading: 0,
								roll: 0,
							},
							model: {
								url: getCarTypeModel(res[0].type_of_industry),
								size: 3,
								fill: true,
								color: getCarColor(res[0].type_of_industry),
								minimumPixelSize: 80,
								maximumScale: 100,
							},
							attr: res[0],
						});

						// 添加到图层
						if (carModel.current) {
							carModel.current.addGraphic(realTimeFixedRoute);
						}

						// 绑定事件
						realTimeFixedRoute.on(mars3d.EventType.start, function () {
							console.log('实时轨迹开始播放，点数:', realTimePositions.length);
						});

						realTimeFixedRoute.on(mars3d.EventType.end, function () {
							console.log('实时轨迹播放完成，停留在最新位置，点数:', realTimePositions.length);
							// 播放完成后停留在最新位置，保持镜头跟随
						});

						// 绑定popup
						bindPopup(realTimeFixedRoute);

						// 开始播放
						try {
							// 尝试使用 autoSurfaceHeight，如果失败则直接播放
							realTimeFixedRoute
								.autoSurfaceHeight()
								.then(function () {
									// 始终以正常速度播放，避免瞬移
									realTimeFixedRoute.start();
									console.log('开始播放实时轨迹，点数:', realTimePositions.length);
								})
								.catch(function (error) {
									console.warn('autoSurfaceHeight 失败，直接播放:', error);
									// 如果 autoSurfaceHeight 失败，直接播放
									realTimeFixedRoute.start();
								});
						} catch (error) {
							console.error('启动实时轨迹播放时出错:', error);
							// 如果出错，直接启动播放
							realTimeFixedRoute.start();
						}
					}
				}
				if (realVehicleTime) {
					clearTimeout(realVehicleTime);
					realVehicleTime = null;
				}
				if (!currentInfo || isStart) return;
				realVehicleTime = setTimeout(() => {
					getVehicleRealTimePositionInfoData(vid, minute);
				}, 5000);
			})
			.catch((err) => {
				console.log(err);
				if (realVehicleTime) {
					clearTimeout(realVehicleTime);
					realVehicleTime = null;
				}
				// realVehicleTime = setTimeout(() => {
				//   getVehicleRealTimePositionInfoData(vid, minute)
				// }, 5000)
			});
	};

	const bindPopup = (graphic) => {
		graphic.bindPopup(
			function (event) {
				const attr = {};
				const formattedDate = moment(event.graphic.attr.gDate).format(
					'YYYY-MM-DD HH:mm:ss',
				);
				attr['车辆类型:'] = event.graphic.attr.type_of_industry;
				attr['发送时间:'] = formattedDate;
				attr['车辆速度:'] = Math.floor(event.graphic.attr.speed) + ' km/h';
				attr['排放浓度:'] =
					Math.floor(event.graphic.attr.downScrSensorOutput) + ' ppm';
				attr['发动机转速:'] = Math.floor(event.graphic.attr.engineRotation);

				return mars3d.Util.getTemplateHtml({
					title: '',
					template: 'all',
					attr,
				});
			},
			{ timeRender: true, closeButton: false },
		);
	};

	const removeGraphicLayerModel = () => {
		if (realVehicleTime) {
			clearTimeout(realVehicleTime);
			realVehicleTime = null;
		}
		if (graphicLayerModel) {
			map.removeLayer(graphicLayerModel);
			graphicLayerModel = null;
			graphicModel = null;
		}
		if (realTimeFixedRoute && carModel.current) {
			carModel.current.removeGraphic(realTimeFixedRoute);
			realTimeFixedRoute = null;
		}
		// 清空实时轨迹点数组
		realTimePositions = [];
	};

	const formatSeconds = (seconds) => {
		let hours = Math.floor(seconds / 3600);
		let minutes = Math.floor((seconds % 3600) / 60);
		let remainingSeconds = seconds % 60;
		let hoursStr = hours > 0 ? hours + '时' : '';
		let minutesStr = minutes > 0 ? minutes + '分' : '';
		let secondsStr = remainingSeconds > 0 ? remainingSeconds + '秒' : '';
		return `${hoursStr}${minutesStr}${secondsStr}`;
	};

	const getVehicleActivityData = async () => {
		const { VIN } = currentInfo;
		const start_time = `${currentHistoricalTrackDate} 00:00:00`;
		const end_time = `${currentHistoricalTrackDate} 23:59:59`;
		let pieData1 = await getVehicleActivity({
			vid: VIN,
			start_time,
			end_time,
			factor: 1,
		});
		let pieData2 = await getVehicleActivity({
			vid: VIN,
			start_time,
			end_time,
			factor: 2,
		});
		try {
			pieData1 = pieData1
				.filter((item) => item.name !== '' && item.value != '')
				.map((item) => {
					return { name: item.name, value: Math.floor(item.value) };
				});
			pieData2 = pieData2
				.filter((item) => item.name !== '' && item.value != '')
				.map((item) => {
					return { name: item.name, value: Math.floor(item.value) };
				});
			pieChartRef.current = echarts.init(pieRef.current);
			const pieOption = {
				tooltip: {
					trigger: 'item',
					formatter: '{a} <br/>{b} : {c}米 ({d}%)',
				},
				title: [
					{
						left: '50%',
						top: '52%',
						textAlign: 'center',
						text: '区县里程占比',
						textStyle: {
							fontSize: 18,
							color: '#ffffff',
						},
					},
					{
						left: '50%',
						top: '5%',
						textAlign: 'center',
						text: '街乡镇里程占比',
						textStyle: {
							fontSize: 18,
							color: '#ffffff',
						},
					},
				],
				series: [
					{
						name: '区县里程占比',
						type: 'pie',
						radius: ['0%', '75%'],
						center: ['50%', '77%'],
						data: pieData2,
						label: {
							show: false,
						},
						// label: {
						//   // normal: {
						//   //   show: false
						//   // }
						//   color: '#ffffff'
						// },
						// // 环图之间间隔
						// itemStyle: {
						//   normal: {
						//     borderWidth: 2,
						//     borderColor: '#00225d'
						//   }
						// }
					},
					{
						name: '街乡镇里程占比',
						type: 'pie',
						radius: ['0%', '75%'],
						center: ['50%', '30%'],
						data: pieData1,
						label: {
							show: false,
						},
						// label: {
						//   // normal: {
						//   //   show: false
						//   // }
						//   color: '#ffffff'
						// },
						// // 环图之间间隔
						// itemStyle: {
						//   normal: {
						//     borderWidth: 2,
						//     borderColor: '#00225d'
						//   }
						// }
					},
				],
				color: COLORLIST2,
			};
			pieChartRef.current.setOption(pieOption);
		} catch (error) {
			console.log(error);
		}
	};

	useEffect(() => {
		console.log('=================currentInfo===================');
		console.log(currentInfo);
		console.log('=================currentInfo===================');
		if (!currentInfo) return;
		window.electron.ipcRenderer.send('layer', 'VehicleDetails');
		viewRealTimeVehicleData();
		getVehicleBasicInformationData();
		initHistoricalTrack();
		// setSwitchingPointsId(layerId)
		return () => {
			removeLayer();
			removeGraphicLayerModel();
		};
	}, [currentInfo]);

	useEffect(() => {
		if (map?.clock?.multiplier) map.clock.multiplier = multiplier;
	}, [multiplier]);

	// useEffect(() => {
	//   return () => {
	//     if (graphicLayer) {
	//       graphicLayer.eachGraphic((graphic) => {
	//         if (graphic.isPrivate) {
	//           return
	//         }
	//         if (graphic.attr === currentInfo?.VIN) {
	//           graphic.proceed()
	//         }
	//       })
	//       map.removeLayer(graphicLayer)
	//     }
	//     if (timer) {
	//       clearInterval(timer)
	//       timer = null
	//     }
	//     idx = 0
	//   }
	// }, [])

	return (
		currentInfo?.VIN && (
			<>
				{currentHistoricalTrackDate && (
					<>
						<VehicleHistoricalTrajectoryStyled>
							<Spin tip="加载中" size="large" spinning={spinning}>
								<LayoutContainer>
									<h3>{currentHistoricalTrackDate} 车辆活动情况</h3>
									{isStart || isPause ? (
										<>
											{currentDateVehicle && (
												<div className="track-wrap">
													<div>
														<div className="cumulative">
															<div>总排放(g)</div>
															<div>
																{Math.floor(currentDateVehicle['总排放'])}
															</div>
														</div>
														<div className="cumulative">
															<div>总油耗(L)</div>
															<div>
																{Math.floor(currentDateVehicle['总油耗'])}
															</div>
														</div>
													</div>
													<div>
														<div className="cumulative">
															<div>总里程(km)</div>
															<div>
																{Math.floor(currentDateVehicle['总里程']) /
																	1000}
															</div>
														</div>
														<div className="cumulative">
															<div>总时长</div>
															<div>
																{formatSeconds(currentDateVehicle['总时长'])}
															</div>
														</div>
													</div>
												</div>
											)}
											<div className="progress-wrap">
												<Progress
													percent={roamLineData.percent}
													size="small"
												></Progress>
											</div>
										</>
									) : null}
								</LayoutContainer>
								<div className="btnGroup">
									{!isStart ? (
										<>
											<button
												className="btn"
												onClick={() => {
													btnStart();
												}}
											>
												开始
											</button>
											{/* <button className="btn" onClick={btnReturn}>
                  返回
                </button> */}
										</>
									) : (
										''
									)}
									{isStart && !isPause ? (
										<button className="btn" onClick={btnPause}>
											暂停
										</button>
									) : (
										''
									)}
									{isStart && isPause ? (
										<button className="btn" onClick={btnProceed}>
											继续
										</button>
									) : (
										''
									)}
									{isStart ? (
										<>
											<button className="btn" onClick={btnStop}>
												停止
											</button>
											<span className="lable-text">倍速:</span>
											<InputNumber
												step={1}
												min={1}
												max={1000}
												defaultValue={multiplier}
												onChange={onChange}
												controls={true}
												changeOnWheel
											/>
										</>
									) : null}
								</div>
							</Spin>
						</VehicleHistoricalTrajectoryStyled>
						<VehicleActivityLevelStyled>
							<div
								className="vehicle-activity-level"
								style={{
									visibility: `${isStart || isPause ? 'visible' : 'hidden'}`,
								}}
								ref={pieRef}
							/>
						</VehicleActivityLevelStyled>
					</>
				)}
				{currentViolationClueCode && <ViolationInfoPanel />}
				{isStart ? (
					<>
						<DashBoard
							info={currentInfo}
							level={infoData?.EMISSION_LEVEL == '国Ⅴ' ? '5' : '6'}
						/>
						<TrajectoryLegendStyled>
							{getTrackLevelInfo(
								infoData?.EMISSION_LEVEL == '国Ⅴ' ? '5' : '6',
							).map((item, i) => {
								return (
									<li key={i}>
										<i style={{ backgroundColor: item[1] }} />
										{item[0]}
									</li>
								);
							})}
						</TrajectoryLegendStyled>
					</>
				) : null}
			</>
		)
	);
};

export default VehicleHistoricalTrajectory;
