import styled from 'styled-components';
import imgs from '../../images/realTimeEmissions';
const LayoutContainer = styled.div`
	width: 100%;
	height: 100%;
	margin-left: 20px;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	.left-container {
		min-width: 280px;
		margin-left: -10px;
		margin-top: 10px;
		// .emission {
		//   .emission-item {
		//     display: flex;
		//     align-items: center;
		//     margin-top: 8px;
		//     width: 300px;
		//     height: 61px;
		//     background-image: url(${imgs.icon1});
		//     background-repeat: no-repeat;
		//     background-size: cover;

		//     .emission-number {
		//       padding-left: 124px;
		//       padding-bottom: 5px;
		//       display: flex;
		//       flex-direction: column;
		//       span:nth-child(1) {
		//         color: #4ce6ff;
		//         font-size: 18px;
		//       }
		//       span:nth-child(2) {
		//         color: #c9dce9;
		//         font-size: 18px;
		//       }
		//     }
		//   }
		// }
	}
	.right-container {
		width: 60%;
		height: 100%;
	}
`;
export default LayoutContainer;
