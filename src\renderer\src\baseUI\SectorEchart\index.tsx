import * as React from 'react';
import { useState, useEffect } from 'react';
import LayoutContainer from './style';
import Echarts from '@renderer/components/echarts';
type dataProps = {
	name: string;
	value: string;
};

type Props = {
	center: Array<string>;
	colorList: Array<string>;
	data: dataProps[];
};
const SectorEchart = (props: Props) => {
	const { center, colorList, data } = props;
	const [option, setOption] = useState({});

	useEffect(() => {
		const option = {
			// backgroundColor: '#061740',
			color: colorList,
			tooltip: {
				show: true,
			},
			// title: {
			//   text: '本月总数',
			//   left: '24%',
			//   top: 'center',
			//   textStyle: {
			//     color: '#ffffff',
			//     fontWeight: 'bold',
			//     fontSize: '16px'
			//   }
			// },
			// tooltip: {
			//   trigger: 'item'
			// },
			legend: [
				{
					orient: 'vertical',
					// data: nameArray,
					left: '195px',
					top: 'center',
					align: 'left',
					itemGap: 14,
					textStyle: {
						color: '#fff',
						fontSize: '14px',
					},
					//图例标记的图形高度
					itemHeight: 10,
					//图例标记的图形宽度
					itemWidth: 10,
				},
			],
			series: [
				{
					name: '',
					type: 'pie',
					clockwise: false,
					radius: ['100%', '170%'],
					width: '65%',
					height: '55%',
					emphasis: {
						scale: false,
					},
					center: ['36%', '49%'],
					top: 'center',
					label: {
						show: false,
						position: 'inside',
						color: '#fff',
						// formatter: function (params) {
						//   console.log(params)
						//   return params.percent + '%'
						// }
					},
					data,
				},
				{
					name: '',
					type: 'pie',
					clockwise: false,
					radius: ['180%', '175%'],
					center: ['36%', '49%'],
					width: '65%',
					height: '55%',
					top: 'center',
					avoidLabelOverlap: false,
					itemStyle: {
						borderRadius: 1000,
					},
					label: {
						show: false,
						position: 'center',
					},
					emphasis: {
						disabled: true,
					},
					// 占位样式
					emptyCircleStyle: {
						color: 'rgba(255,255,255,0)',
					},
					labelLine: {
						show: false,
					},
					data,
				},
			],
		};
		setOption(option);
	}, [data]);

	return (
		<LayoutContainer>
			<Echarts option={option}></Echarts>
			{/* <div className="legend">
        {data.map((item, index) => {
          return (
            <div key={index} className="legend-item">
              <span className="circle" style={{ backgroundColor: colorList[index] }}></span>
              <span className="name">{item.name}</span>
            </div>
          )
        })}
      </div> */}
		</LayoutContainer>
	);
};

export default SectorEchart;
