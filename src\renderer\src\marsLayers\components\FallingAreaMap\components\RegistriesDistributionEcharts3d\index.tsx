import { useContext, useEffect, useRef, useState } from 'react';
import * as echarts from 'echarts';
import axios from 'axios';
// import throttle from 'lodash/throttle'
import 'echarts-gl';
import { geoJson } from '@renderer/components/AreaViolation/AreaMap/data';
import { MapContext, PageContext } from '@renderer/context';
import dataBg from '@renderer/images/icon/dataBg.png';
import up from '@renderer/images/icon/up.png';
import down from '@renderer/images/icon/down.png';

type Props = {
	data: Array<any>;
	layerId: string;
	targetId: number;
	customerBatteryCityData: any;
	currentPosition: any;
	type: string;
};

const RegistriesDistributionEcharts3d = (props: Props) => {
	const {
		data,
		layerId,
		currentPosition,
		customerBatteryCityData,
		type,
		targetId,
	} = props;
	const { regionName, setRegionName } = useContext(PageContext);
	const [optionDefault, setOptionDefault] = useState<any>(null);
	const [townJson, setTownJson] = useState(null);
	const echartsContainerRef = useRef(null);
	const echartsChartRef = useRef<any>(null);

	let isStreetTownship = false;

	const getMatchingRegions = (data) => {
		const dataRegions = geoJson.features.map((item) => item.properties.name);
		const matchingRegions = data.filter((obj) =>
			dataRegions.includes(obj.name),
		);
		return matchingRegions;
	};

	// 重置地图状态
	const resetMap = () => {
		// 重置地图状态
		const option = echartsChartRef.current.getOption();
		// 遍历地图区域，先将所有区域恢复默认样式
		option.series[0].data.forEach(function (region, idx) {
			region.itemStyle = optionDefault?.series[0].data[idx].itemStyle;
			region.label = { show: false };
		});
		return option;
	};

	const getEmissionDisplayValues = (type: string, value: any, params: any) => {
		let f = '';
		switch (type) {
			case '1':
				f = 'd';
				return `{fline|${params.name} ${value}kg}{${f}|}{upText|10%}`;
			case '2':
				return `${params.name} ${params.dataIndex + 1}` + ` ${value}%`;
			case '3':
				return `${params.name} ${value} kg`;
			case '4':
				return `${params.name} ${value} %`;
			case '5':
				return `${params.name}排行:` + ` ${value}`;
			default:
				return;
		}
	};

	const getSpaceDisplayValues = (
		type: number,
		value: undefined,
		params: any,
	) => {
		switch (type) {
			case 1:
				return `${params.name} ${value} 辆`;
			case 2:
				return `${params.name} ${value} km`;
			case 3:
				return `${params.name} ${value} kg`;
			case 4:
				return `${params.name} ${value} L`;
			case 6:
				return `${params.name} ${value} L`;
			default:
				return;
		}
	};

	const getDisplayValues = (
		layerId: string,
		type: any,
		value: undefined,
		params: any,
	) => {
		switch (layerId) {
			case 'spatialDistribution':
				return getSpaceDisplayValues(type, value, params);
			case 'area_discharge':
				return getEmissionDisplayValues(type, value, params);
			default:
				return;
		}
	};

	const getTownJson = async () => {
		try {
			const response = await axios.get(`file:///public/data/townJson.json`);
			setTownJson(response.data);
		} catch (error) {
			console.error(error);
			throw error;
		}
	};

	const getOption = () => {
		const mapName = 'beijing3D';
		const option = {
			animation: false,
			tooltip: {
				show: isStreetTownship,
				className: 'custom-tooltip-bk-box', //添加一个类名
				triggerOn: 'mousemove', // 鼠标移动时触发
				axisPointer: {
					type: 'none',
				},
				// 自定义浮动提示框
				formatter(params) {
					// params.data 就是series配置项中的data数据遍历
					let deptTotal, schoolTotal, studentTotal;
					if (params.data) {
						deptTotal = params.data.value;
						schoolTotal = params.data.schoolTotal;
						studentTotal = params.data.studentTotal;
					} else {
						deptTotal = 0;
						schoolTotal = 0;
						studentTotal = 0;
					}
					const htmlStr = `
              <div class="custom-tooltip-box">
                <div style='font-size:18px;'>${params.name}：${params.value}</div>
              </div>
            `;
					return htmlStr;
				},
				backgroundColor: 'rgba(255,255,255,0)', //提示标签背景颜色 这里设置为了透明色
				textStyle: { color: '#fff' }, //提示标签字体颜色
			},
			// geo3D: [
			//   {
			//     zlevel: 2,
			//     show: false,
			//     map: mapName,
			//     roam: true,
			//     aspectScale: 1,
			//     regionHeight: 0,
			//     viewControl: {
			//       projection: 'perspective',
			//       autoRotate: false,
			//       center: [0, -18, -10],
			//       distance: 90, // 控制地图版块的大小
			//       alpha: 42, // 地图版块垂直方向的角度
			//       beta: -2 // 地图版块水平方向的角度
			//     }
			//   }
			// ],
			series: [
				{
					zlevel: 3,
					type: 'map3D',
					map: mapName,
					aspectScale: 1,
					viewControl: {
						projection: 'perspective',
						autoRotate: false,
						center: [0, -20, -10],
						distance: 90, // 控制地图版块的大小
						alpha: 42, // 地图版块垂直方向的角度
						beta: -2, // 地图版块水平方向的角度
					},
					// light: {
					//   // 光照阴影
					//   main: {
					//     intensity: 1.2, // 光照强度
					//     shadowQuality: 'high', //阴影亮度
					//     shadow: true, // 是否显示阴影
					//     alpha: 55,
					//     beta: 10
					//   },
					//   ambient: {
					//     intensity: 0.3
					//   }
					// },
					label: {
						show: !isStreetTownship,
						color: '#fff', //文字颜色
						fontSize: 18, //文字大小
						position: [20, 0],
						padding: [15, 25],
						alignText: 'center',
						lineHeight: 24,
						backgroundColor: {
							image: dataBg, // 图片的路径
							repeat: 'no-repeat',
						},
						// backgroundColor: 'rgba(87, 51, 51, 0.35)', //透明度0清空文字背景
						// borderWidth: 1.5, //分界线宽度
						// borderRadius: 5,
						borderColor: '#2ab8ff', //分界线颜色
						formatter: function (params) {
							const region = data.find((item) => item.name === params.name);
							if (region) {
								const { value } = region;
								let areaType: any;
								if (layerId === 'spatialDistribution') {
									areaType = targetId;
								} else {
									areaType = type;
								}
								return getDisplayValues(layerId, areaType, value, params);
							} else {
								return null;
							}
						},
						rich: {
							fline: {
								color: '#fff',
								fontSize: 16,
								fontWeight: 1000,
							},
							tline: {
								padding: [0, 10],
								color: '#d9f4f7',
								fontSize: 14,
								fontWeight: 600,
							},
							d: {
								width: 25,
								height: 25,
								padding: [10, 0],
								backgroundColor: {
									image: up,
								},
							},
							upText: {
								padding: [0, -5],
								color: 'red',
								fontSize: 18,
								fontWeight: 1000,
							},
						},
					},
					itemStyle: {
						areaColor: '#374151', // 地图配色
						// areaColor: 'rgba(10, 133, 171, 0.8)',
						opacity: 1,
						borderWidth: 0.4,
						borderColor: '#00FFF8', // 地图边配色
					},
					emphasis: {
						label: { show: !isStreetTownship },
						itemStyle: {
							color: 'rgba(0,254,233,1)',
						},
					},
					data:
						layerId === 'street_discharge' || layerId === 'street_deregulation'
							? customerBatteryCityData
							: getMatchingRegions(customerBatteryCityData),
				},
			],
		};
		return option;
	};

	useEffect(() => {
		if (layerId === 'street_discharge' || layerId === 'street_deregulation')
			getTownJson();
	}, [layerId]);

	useEffect(() => {
		if (!customerBatteryCityData?.length) return;
		if (
			(layerId === 'street_discharge' && !townJson) ||
			(layerId === 'street_deregulation' && !townJson)
		)
			return;
		if (layerId === 'street_discharge' || layerId === 'street_deregulation') {
			echarts.registerMap('beijing3D', townJson);
			isStreetTownship = true;
		} else {
			echarts.registerMap('beijing3D', geoJson);
			isStreetTownship = false;
		}

		echartsChartRef.current = echarts.init(echartsContainerRef.current);
		const option = getOption();
		// if (regionName) {
		//   const region = option.series[0].data.find((item) => item.name === regionName)
		//   region.itemStyle.color = '#09decd'
		//   region.isSelected = true
		// }
		echartsChartRef.current.setOption(option);
		let newOption = JSON.parse(JSON.stringify(option));
		echartsChartRef.current.on('click', (e) => {
			if (!e?.name) return;
			const selectedRegion = newOption.series[0].data.find(
				(item) => item.name === e.name,
			);
			if (selectedRegion.isSelected) {
				selectedRegion.isSelected = false;
				echartsChartRef.current.setOption(option);
				newOption = JSON.parse(JSON.stringify(option));
				setRegionName(null);
			} else {
				selectedRegion.isSelected = true;
				setRegionName(e.name);
			}
		});
		if (!optionDefault) {
			setOptionDefault({ ...option });
		}

		return () => {
			if (echartsChartRef.current) {
				echartsChartRef.current.clear();
				echartsChartRef.current.dispose();
				echartsChartRef.current.off('click');
			}
		};
	}, [customerBatteryCityData, townJson, type]);

	useEffect(() => {
		if (!regionName) return;
		const option = getOption();
		const op = JSON.parse(JSON.stringify(option));
		const selectedRegion = op.series[0].data.find(
			(item) => item.name === regionName,
		);
		if (selectedRegion) {
			selectedRegion.itemStyle.color = '#09decd';
			echartsChartRef.current.setOption(op);
		}
	}, [regionName]);

	useEffect(() => {
		if (!currentPosition || !echartsChartRef.current || !optionDefault) return;
		const option = resetMap();
		const region = option.series[0].data.find(
			(item) => item.name === currentPosition.adm_name,
		);
		region.itemStyle = {
			color: 'rgba(0,254,233,0.4)',
			borderColor: '#0CDEFF',
			borderWidth: 5,
		};
		region.label = {
			show: true,
			color: '#fff', //文字颜色
			fontSize: 18, //文字大小
			position: [20, 0],
			padding: [15, 25],
			alignText: 'center',
			lineHeight: 24,
			backgroundColor: {
				image: dataBg, // 图片的路径
				repeat: 'no-repeat',
			},
			formatter: function (params) {
				const region = data.find((item) => item.name === params.name);
				if (region) {
					const { value } = region;
					return `${params.name} ${value}`;
				} else {
					return null;
				}
			},
		};
		// 更新地图状态
		echartsChartRef.current.setOption(option);
	}, [currentPosition]);

	return (
		<div
			ref={echartsContainerRef}
			style={{
				position: 'absolute',
				width: '100%',
				height: '100%',
				zIndex: 1,
			}}
		/>
	);
};

export default RegistriesDistributionEcharts3d;
