import { useEffect, useState, useContext, useRef } from 'react';
import * as echarts from 'echarts';
import moment from 'moment';
import { isEmpty } from 'lodash';
import Echarts from '@renderer/components/echarts';
import { Space, Button, Dropdown } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { PageContext } from '@renderer/context';
import icon from '@renderer/images/icon';
import industryClassification from '../components/IndustryClassification';
import ScrollListWidthTitle from '@renderer/components/SlidingLayer/DailyActivityLevel/components/ScrollListWidthTitle';
import { getVehicleCumulativeData, getVehicleViolations } from '@renderer/api';
import { CarRightSideStyled, LayoutContainer } from './styles';
import background from '@renderer/images/background';
import pointImg from '@renderer/images/icon/button.png';

type Props = {
	currentSelectedId: string;
	streetShipData: any;
};

const AreaRightSide = (props: Props) => {
	// const {  } = props
	const {
		currentVehicleInfo,
		setCurrentVehicleInfo,
		selectRegionDate,
		setCurrentViolationClueCode,
		setCurrentHistoricalTrackDate,
		historicalStartTime,
		historicalEndTime,
	} = useContext(PageContext);
	const [option, setOption] = useState({});
	const [columns1, setColumns1] = useState([]);
	const [columns2, setColumns2] = useState([]);
	const [data1, setData1] = useState([]);
	const [data2, setData2] = useState([]);
	const [showTable, setShowTable] = useState(false);

	const barRef = useRef(null);
	const barChartRef = useRef(null);

	const getData = async (start_time, end_time) => {
		const { VIN } = currentVehicleInfo;
		const target1 = await getVehicleCumulativeData({
			vid: VIN,
			start_time,
			end_time,
			target: 1,
		});
		const target2 = await getVehicleCumulativeData({
			vid: VIN,
			start_time,
			end_time,
			target: 4,
		});
		const VehicleViolations = await getVehicleViolations({
			vid: VIN,
			start_time,
			end_time,
		});
		try {
			console.log(
				'单车违规查询》》》VehicleViolations??????',
				VehicleViolations,
			);
			if (target2.length < 1) return;
			barChartRef.current = echarts.init(barRef.current);
			const color = [
				[
					{ offset: 0, color: 'rgba(0, 193, 246, 0.6)' },
					{ offset: 0.5, color: 'rgba(0, 248, 184, 1)' },
					{ offset: 0.5, color: 'rgba(0, 193, 246, 0.6)' },
					{ offset: 1, color: 'rgba(0, 248, 184, 1)' },
				],
				[
					{ offset: 0, color: 'rgba(29, 170, 236, 0.6)' },
					{ offset: 0.5, color: 'rgba(29, 142, 236, 1)' },
					{ offset: 0.5, color: 'rgba(29, 170, 236, 0.6)' },
					{ offset: 1, color: 'rgba(29, 142, 236, 1)' },
				],
			];
			const xData = target2.map((item, i) => {
				// const data = VehicleViolations.find(key => key.time == item.name)
				// if (data) {
				//   return {
				//     value: `${item.name}\n ${data.type}` ,
				//     textStyle: {
				//       fontSize: 16,
				//       color: 'red',
				//       fontWeight: 'bold',
				//       align: 'center'
				//     }
				//   }
				// }
				return {
					value: item.name,
					textStyle: {
						fontSize: 15,
						color: '#ffffff',
						align: 'center',
					},
				};
			});
			const data1 = target1.map((item) => Math.floor(item.value));
			const data2 = target2.map((item) => Math.floor(item.value));
			const barOption = {
				timeline: {
					show: false,
					top: 0,
					data: [],
				},
				tooltip: {
					show: true,
				},
				legend: {
					show: true,
					top: '3%',
					textStyle: {
						color: '#CDD7D7',
					},
					data: ['排放量(克)', '运行时长(秒)'],
				},
				grid: [
					// 左边柱子
					{
						show: false,
						left: '10%',
						top: '8%',
						bottom: '20',
						containLabel: true,
						width: '33%',
					},
					// 中间
					{
						show: false,
						left: '51%',
						top: '8.3%',
						bottom: '20',
						width: '0%',
					},
					// 右边柱子
					{
						show: false,
						right: '10%',
						top: '8%',
						bottom: '20',
						containLabel: true,
						width: '33%',
					},
				],
				xAxis: [
					{
						type: 'value',
						inverse: true,
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						position: 'top',
						axisLabel: {
							show: false,
							textStyle: {
								color: '#E2F0FF',
								fontSize: 16,
							},
						},
						splitLine: {
							show: false,
						},
					},
					{
						gridIndex: 1,
						show: false,
					},
					{
						gridIndex: 2,
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						position: 'top',
						axisLabel: {
							show: false,
							textStyle: {
								color: '#E2F0FF',
								fontSize: 16,
							},
						},
						splitLine: {
							show: false,
						},
					},
				],
				yAxis: [
					{
						type: 'category',
						// name: '运行时长(秒)',
						nameTextStyle: {
							padding: [0, 50, 0, 0],
							color: '#ffffff',
						},
						inverse: true,
						position: 'right',
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
						data: xData,
					},
					{
						gridIndex: 1,
						type: 'category',
						inverse: true,
						position: 'center',
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: true,
							align: 'center',
							// color: function (value) {
							//   if (value == '2023-03-03') {
							//     return '#FF4E00'
							//   } else if (value == '2023-03-06') {
							//     return '#FF4E00'
							//   } else {
							//     return '#fff'
							//   }
							// },
							fontSize: 16,
						},
						data: xData,
					},
					{
						gridIndex: 2,
						type: 'category',
						// name: '排放量(千克)',
						nameTextStyle: {
							padding: [0, 50, 0, 0],
							color: '#ffffff',
						},
						inverse: true,
						position: 'left',
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
						data: xData,
					},
				],
				series: [
					{
						name: '排放量(克)',
						type: 'bar',
						xAxisIndex: 0,
						yAxisIndex: 0,
						barWidth: 20,
						showBackground: true,
						itemStyle: {
							borderRadius: 5,
							color: {
								type: 'linear',
								x: 0,
								x2: 0,
								y: 0,
								y2: 1,
								colorStops: color[0],
							},
						},
						label: {
							show: true,
							position: 'left',
							color: '#E2F0FF',
							fontSize: 16,
						},
						data: data1,
					},
					{
						name: '',
						type: 'bar',
						xAxisIndex: 1,
						yAxisIndex: 1,
						barWidth: 20,
						label: {
							show: true,
							color: '#fff',
							position: [-320, 30],
							fontSize: 16,
							fontWeight: 'bold',
							formatter: function (params) {
								const data = VehicleViolations.find(
									(key) => key.time == params.name,
								);
								return data ? `{icon|    车辆违规:${data.type}}` : '';
							},
							rich: {
								icon: {
									fontWeight: 'bold',
									fontSize: 16,
									width: 20,
									height: 20,
									backgroundColor: {
										image: pointImg,
									},
								},
							},
						},
						data: data1.map((item) => 0),
					},
					{
						name: '运行时长(秒)',
						type: 'bar',
						xAxisIndex: 2,
						yAxisIndex: 2,
						barWidth: 20,
						showBackground: true,
						itemStyle: {
							borderRadius: 5,
							color: {
								type: 'linear',
								x: 0,
								x2: 0,
								y: 0,
								y2: 1,
								colorStops: color[1],
							},
						},
						label: {
							show: true,
							position: 'right',
							color: '#E2F0FF',
							fontSize: 16,
						},
						data: data2,
					},
				],
			};
			barChartRef.current.on('click', (e) => {
				if (e?.name) {
					console.log('barChartRef.current click', e);
					setCurrentViolationClueCode(VehicleViolations[e.dataIndex]);
					setCurrentHistoricalTrackDate(e.name);
				}
			});
			barChartRef.current.setOption(barOption);
			barChartRef.current.resize();
			setColumns1([
				{ title: '违规类型', dataIndex: 'type' },
				{ title: '时间', dataIndex: 'time' },
			]);
			setColumns2([
				{ title: '排放量', dataIndex: 'value' },
				{ title: '时间', dataIndex: 'name' },
			]);
			setData1(VehicleViolations);
			setData2(
				target1.map((item) => {
					return {
						value: Math.floor(item.value),
						name: item.name,
					};
				}),
			);
		} catch (error) {}
	};

	useEffect(() => {
		if (isEmpty(currentVehicleInfo)) return;
		const startTime = `${historicalStartTime} 00:00:00`;
		const endTime = `${historicalEndTime} 23:59:59`;
		getData(startTime, endTime);

		return () => {
			if (barChartRef.current) {
				barChartRef.current.dispose();
				barChartRef.current.off('click');
			}
		};
	}, [currentVehicleInfo, historicalStartTime, historicalEndTime]);

	return (
		<CarRightSideStyled>
			<div className="Carcontent">
				<div className="slidingLayer">
					<span
						className="slidtext"
						style={{
							background: `url(${icon.historicalEmissions}) 10px 8px / 20px 20px no-repeat`,
						}}
					>
						历史排放量、运行时长
					</span>
					<div className="line"></div>
				</div>
				<LayoutContainer style={{ height: '95%' }}>
					<div className="btn-table" onClick={() => setShowTable(!showTable)}>
						<p>表格</p>
					</div>
					{/* <div
            className="echarts-ref"
            style={{ width: '100%', height: '100%' }}
            ref={barRef}
          /> */}
					<div
						className="table-container"
						style={{ display: `${showTable ? 'block' : 'none'}` }}
					>
						<div className="table-half">
							<ScrollListWidthTitle
								columns={columns1}
								data={data1}
								OnRowSelection={(record) => {}}
								{...optionTable}
							/>
						</div>
						<div className="table-half">
							<ScrollListWidthTitle
								columns={columns2}
								data={data2}
								OnRowSelection={(record) => {}}
								{...optionTable}
							/>
						</div>
					</div>
					<div
						style={{
							display: `${!showTable ? 'block' : 'none'}`,
							width: '100%',
							height: '100%',
						}}
						className="echarts-ref"
						ref={barRef}
					/>
				</LayoutContainer>
			</div>
		</CarRightSideStyled>
	);
};

export default AreaRightSide;

const optionTable = {
	width: '100%',
	height: '450px',
	fontSize: 18,
	thclassname: 'th_row',
	tablebgcolor: 'rgba(9,30,59,0.7)',
	trheight: '40px',
	thheight: '40px',
	customwidth: true,
	rowbgcolor: [
		'linear-gradient(to right, rgba(58,218,255,0.11) 0%, rgba(9, 30, 47, 0.29) 50%, rgba(58,218,255,0.11) 100%)',
	],
	thbgcolor: '#081F38',
};
