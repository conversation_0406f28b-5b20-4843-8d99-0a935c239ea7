import React, { useEffect, useState } from 'react';
import '@animxyz/core';
import Box from '../../baseUI/Box';
import { XyzTransitionGroup } from '@animxyz/react';
import background from '../../images/background';
import { VariousVehicleMonitoringContent } from './style';

const VariousVehicleMonitoring = () => {
	const [isVisible, setIsVisible] = useState(false);
	useEffect(() => {
		setIsVisible(true);
	});
	return (
		<VariousVehicleMonitoringContent>
			VariousVehicleMonitoring
			<XyzTransitionGroup
				className="item-group"
				appear
				mode="out-in"
				xyz="fade left-100%"
			>
				{isVisible && (
					<div className="square">
						<Box>
							<img src={background.mode1} className="left-bg" />
						</Box>
					</div>
				)}
			</XyzTransitionGroup>
		</VariousVehicleMonitoringContent>
	);
};

export default VariousVehicleMonitoring;
