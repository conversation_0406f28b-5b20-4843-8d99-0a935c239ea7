import { useState, useEffect } from 'react';
import Echarts from '@renderer/components/echarts';
import { EchartsStyle } from './style';

export default function Dashecharts(props) {
	const { title, max, unit, value } = props;
	const [option, setOption] = useState({});
	let count = 0;

	useEffect(() => {
		const option = {
			series: [
				{
					// 进度条
					type: 'gauge',
					splitNumber: 10,
					radius: '100%',
					min: 0,
					max: max,
					pointer: {
						show: true,
						width: 5,
						length: '60%',
						itemStyle: {
							color: 'auto',
						},
					},
					axisLine: {
						show: true,
						lineStyle: {
							width: 12,
							color: [
								[0.3, 'rgba(103,224,227)'],
								[0.7, 'rgba(55,162,218)'],
								[1, 'lightcoral'],
							],
							borderColor: '#000',
							borderWidth: '10',
						},
					},
					axisLabel: {
						show: true,
						color: [
							[0.3, 'rgba(103,224,227)'],
							[0.7, 'rgba(55,162,218)'],
							[1, 'lightcoral'],
						],
						// 刻度显示位置
						distance: 6,
						fontSize: 15,
						formatter: (value) => {
							count++;
							if (count % 2 === 0) {
								if (value >= 140) {
									return '{a|' + value + '}';
								} else if (value >= 60) {
									return '{b|' + value + '}';
								} else {
									return '{c|' + value + '}';
								}
							} else {
								return '';
							}
						},
						rich: {
							a: {
								// 自定义a的样式
								color: 'lightcoral',
								fontSize: 16,
								fontWeight: 'bold',
							},
							b: {
								// 自定义b的样式
								color: 'rgba(55,162,218)',
								fontSize: 16,
								fontWeight: 'bold',
							},
							c: {
								// 自定义b的样式
								color: 'rgba(103,224,227)',
								fontSize: 16,
								fontWeight: 'bold',
							},
						},
					}, //刻度标签
					axisTick: {
						show: false,
					}, //刻度样式
					splitLine: {
						show: false,
						length: '20%',
						lineStyle: {
							color: '#3699FF',
							width: 2,
						},
					}, //分隔线样式
					detail: {
						valueAnimation: true,
						formatter: '{value}',
						textStyle: {
							fontFamily: 'SourceHanSansCN-Regular, SourceHanSansCN',
							fontSize: 36,
							fontWeight: 'bold',
							color: '#fff',
						},
						offsetCenter: ['0', '-25%'],
					},
					title: {
						show: true,
						formatter: '{name}',
						color: '#fff',
						// formatter: unit,
						fontSize: 28,
						offsetCenter: ['0', '25%'],
						fontWeight: 'bold',
					},
					data: [
						{
							value: value,
							name: unit,
						},
					],
				},
				{
					// 彩色部分
					type: 'gauge',
					splitNumber: 10,
					radius: '112%',
					min: 0,
					max: 55,
					pointer: {
						show: false,
						width: 7,
						length: '80%',
						color: 'auto',
					},
					axisLine: {
						show: true,
						lineStyle: {
							width: 25,
							color: [
								[0.3, 'transparent'],
								[0.7, 'transparent'],
								[1, 'transparent'],
							],
							borderColor: 'transparent',
							borderWidth: '1',
						},
					},
					axisLabel: {
						show: false,
					}, //刻度标签。
					axisTick: {
						show: true,
						splitNumber: 5,
						lineStyle: {
							color: '#EBF3FE',
							width: 1,
						},
					}, //刻度样式
					splitLine: {
						show: true,
						length: '12%',
						lineStyle: {
							color: '#fff',
							width: 2,
						},
					},
					detail: {
						show: false,
					},
					title: {
						show: false,
					},
				},
			],
		};

		setOption(option);
	}, [value]);

	return (
		<EchartsStyle>
			<div className="echarts">
				<Echarts option={option} />
			</div>
		</EchartsStyle>
	);
}
